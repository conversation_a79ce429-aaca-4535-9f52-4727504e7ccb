import {
  SpeakerWaveIcon,
  SpeakerXMarkIcon,
  ForwardIcon,
  BackwardIcon,
  PlayIcon,
  PauseIcon
} from "@heroicons/react/24/outline";
import {Slider} from "@/ui/components/slider.tsx"
import React, {useContext, useEffect, useRef, useState} from "react";
import {Button} from "@/ui/components/button.tsx";
import {mediaPath, PlayerContext} from "@/App.tsx";
import {Formater} from "@/business/Formater.ts";

const NEXT = true
const PREV = false

function PlayerBar() {
  const {player} = useContext(PlayerContext);
  const [progress, setProgress] = useState(0)
  const [, forceUpdate] = React.useReducer((x) => x + 1, 0);
  const isSeeking = useRef(false);

  const onForward = () => {
    player.skip(NEXT);
    forceUpdate();
  }

  const onBackward = () => {
    player.skip(PREV);
    forceUpdate();
  }

  const onPlay = () => {
    if (player.isPlaying) {
      player.pause();
    } else {
      player.play(player.songIndex);
    }
    player.onSongChange(() => {
    })
    forceUpdate();
  }

  const onMute = () => {
    if (player.volume() !== 0) {
      onVolumeChange([0])
    } else {
      onVolumeChange([50])
    }
  }

  const onVolumeChange = (volume: [number]) => {
    player.volume(volume[0])
    forceUpdate();
  }

  useEffect(() => {
    player.onProgress((seek) => {
      if (!isSeeking.current) {
        setProgress(seek / player.duration());
      }
    });
  }, [player, player.isPlaying, player.songIndex]);

  const onSeek = (value: [number]) => {
    const seekValue = value[0];
    isSeeking.current = false;
    setProgress(value[0] / player.duration())
    player.seek(seekValue / player.duration());
  }

  const onSeekStart = () => {
    isSeeking.current = true;
  };

  return (
    <div className="bg-bg-primary-color sticky bottom-0 h-18 w-full border-grey">
      <div className="progressbar">
        <Slider step={1}
                max={player.duration()}
                value={[progress * player.duration()]}
                onPointerDown={onSeekStart}
                onValueChange={(value) => setProgress(value[0] / player.duration())}
                onValueCommit={onSeek}
        />
      </div>

      <div className="player-content flex h-full">
        <div className="player-info w-1/3 flex gap-2">
          <div className="song-cover
          w-14 h-14
          ">
            <img src={mediaPath + (player.playlist[player.songIndex]).cover} alt="cover"/>
          </div>
          <div className="song-info">
            <div id="timer" className="text-xs md:text-base lg:text-lg">
              {Formater.formatTime(Math.round(player.duration() * progress))}
            </div>
            <p className="text-xs max-w-14 max-h-8 overflow-hidden truncate whitespace-pre-wrap
            md:text-base md:max-w-max md:whitespace-nowrap  lg:text-lg">
              {(player.playlist[player.songIndex]).title}
            </p>
          </div>
        </div>

        <div className="player-controls w-1/3 flex justify-center items-center gap-2">
          <Button name="backward_song" variant="ghost" onClick={onBackward} className="p-0">
            <BackwardIcon className="h-6 w-6"/>
          </Button>
          <Button name="play_song" variant="ghost" onClick={onPlay} className="p-1" >
            {player.isPlaying ?
              <PauseIcon className="h-6 w-6"/> :
              <PlayIcon className="h-6 w-6"/>}
          </Button>
          <Button name="forward_song" variant="ghost" onClick={onForward} className="p-0">
            <ForwardIcon className="h-6 w-6"/>
          </Button>
        </div>

        <div className="player-actions w-1/3 flex justify-center">
          <div className="volume flex w-full items-center mr-2
          md:w-1/2 md:gap-2 md:mr-0">
            <Button name="mute" variant="ghost" onClick={onMute} className="p-0">
              {(player.volume() === 0) ?
                <SpeakerXMarkIcon className="h-6 w-6"/> :
                <SpeakerWaveIcon className="h-6 w-6"/>}
            </Button>
            <Slider step={1} value={[player.volume()]} onValueChange={onVolumeChange} className="w-full"/>
            <p className="self-start absolute right-2 text-xs md:text-base lg:text-lg">
              {Formater.formatTime(player.duration())}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PlayerBar