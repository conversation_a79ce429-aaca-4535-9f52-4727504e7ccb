import {Link} from "react-router-dom";

type StyledLinkProps = {
  to: string
  text: string
  img?: string | JSX.Element
}

function StyledLink(props: StyledLinkProps) {
  let image
  if (typeof props?.img === "string") {
    image = <img src={props.img} alt="link icon" className="w-6 h-6 inline"/>
  } else {
    image = props.img
  }

  return (
    <Link to={`${props.to}`} className="space-x-1 hover:underline hover:text-bg-primary-color">
      {image}
        <b className="text-sm md:text-base">
          {props.text}
        </b>
    </Link>
  )
}

export default StyledLink