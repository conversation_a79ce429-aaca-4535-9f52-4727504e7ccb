-- CreateTable
CREATE TABLE "Reservation" (
    "id" SERIAL NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "name" TEXT NOT NULL,
    "surname" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phone" TEXT NOT NULL,
    "car" INTEGER NOT NULL,
    "moto" INTEGER NOT NULL,
    "caravan" INTEGER NOT NULL,
    "people" INTEGER NOT NULL,
    "children" INTEGER NOT NULL,
    "dog" INTEGER NOT NULL,
    "tent" INTEGER NOT NULL,
    "electricity" BOOLEAN NOT NULL,
    "street" TEXT NOT NULL,
    "zip" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "country" TEXT NOT NULL,
    "note" TEXT,
    "totalPrice" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Reservation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Price" (
    "id" SERIAL NOT NULL,
    "car" INTEGER NOT NULL,
    "moto" INTEGER NOT NULL,
    "caravan" INTEGER NOT NULL,
    "people" INTEGER NOT NULL,
    "children" INTEGER NOT NULL,
    "dog" INTEGER NOT NULL,
    "tent" INTEGER NOT NULL,
    "electricity" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Price_pkey" PRIMARY KEY ("id")
);
