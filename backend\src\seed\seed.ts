import client from "../client";

const seed = async () => {
  console.log("Start seeding price list...");

  await client.price.create({
    data: {
      car: 150,
      moto: 100,
      caravan: 200,
      people: 100,
      children: 0,
      dog: 50,
      tent: 150,
      electricity: 150,
    },
  });

  console.log("Seeding price list finished.");
};

seed()
  .then(async () => {
    await client.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await client.$disconnect();
    process.exit(1);
  });
