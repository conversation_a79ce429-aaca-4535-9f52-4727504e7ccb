import youtube from "@/data/icons/youtube_icon.png";
import instagram from "@/data/icons/instagram_icon.png";
import facebook from "@/data/icons/facebook_icon.png";
import tiktok from "@/data/icons/tiktok_icon.png";
import twitter from "@/data/icons/twitter_icon.png";
import StyledLink from "../link.tsx";
import {EnvelopeIcon, MapPinIcon, PhoneIcon} from "@heroicons/react/24/outline";
import {socials, contacts, name} from "@/data/content/staticContent.ts";

function Footer() {
  return (
    <div className="bg-bg-secondary-color">
      <div className="p-4 gap-10 flex justify-around">

        <div className="flex flex-col gap-4">
          <h2 className="font-bold text-lg">Contact Me</h2>
          <ul>
            <li key={contacts.mail.name}>
              <StyledLink to={contacts.mail.link} text={contacts.mail.text}
                          img={<EnvelopeIcon className="h-4 w-4 inline"/>}/>
            </li>
            <li key={contacts.address.name}>
              <StyledLink to={contacts.address.link} text={contacts.address.text}
                          img={<MapPinIcon className="h-4 w-4 inline"/>}/>
            </li>
            <li key={contacts.phone.name}>
              <StyledLink to={contacts.phone.link} text={contacts.phone.text}
                          img={<PhoneIcon className="h-4 w-4 inline"/>}/>
            </li>
          </ul>
        </div>

        <div className="flex flex-col gap-4 ">
          <h2 className="font-bold text-lg">Find Me On</h2>
          <ul>
            <li key={socials.yt.name}>
              <StyledLink to={socials.yt.link} text={socials.yt.name} img={youtube}/>
            </li>
            <li key={socials.ig.name}>
              <StyledLink to={socials.ig.link} text={socials.ig.name} img={instagram}/>
            </li>
            <li key={socials.fb.name}>
              <StyledLink to={socials.fb.link} text={socials.fb.name} img={facebook}/>
            </li>
            <li key={socials.tt.name}>
              <StyledLink to={socials.tt.link} text={socials.tt.name} img={tiktok}/>
            </li>
            <li key={socials.tw.name}>
              <StyledLink to={socials.tw.link} text={socials.tw.name} img={twitter}/>
            </li>
          </ul>
        </div>

      </div>
      <p className="text-center">© 2025 {name}</p>
    </div>
  )
}

export default Footer