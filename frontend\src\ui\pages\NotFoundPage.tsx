import {useNavigate} from "react-router-dom";
import {But<PERSON>} from "@/ui/components/button.tsx";

function NotFoundPage() {
  const navigate = useNavigate();
  const goBack = () => navigate("/home");
  return (
    <div className="center-screen text-3xl bg-dark-color text-light-color bg-bg-secondary-color">
      <h1>
        (╯°□°)╯︵ <u>IpunoℲ ʇoᴎI</u>
      </h1>
      <p>Sorry, the page you are looking for could not be found</p>
      <Button variant="default" onClick={goBack} className="text-3xl mt-4">Back to Home</Button>
    </div>
  );
}

export default NotFoundPage