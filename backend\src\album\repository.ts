import client from "../client";
import {Result} from '@badrap/result';
import {NotFoundError} from "../utils";
import {DbResult} from "../types";
import {Album} from "@prisma/client";

export const albumRepository = {

  get: async function (id: string): DbResult<Album> {
    try {
      const result = await client.album.findUnique({
        where: {
          id: id
        },
        include: {
          songs: {
            include: {
              genres: true,
            }
          }
        }
      });

      if (!result) {
        return Result.err(new Error("Album not found"));
      }
      return Result.ok(result);
    } catch (e) {
      return Result.err(new NotFoundError());
    }
  },

  getAll: async function (): DbResult<Album[]> {
    try {
      const result = await client.album.findMany();

      if (!result) {
        return Result.err(new Error("No albums found"));
      }
      return Result.ok(result);

    } catch (e) {
      return Result.err(new NotFoundError());
    }
  }
}