/*
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
var showControllersOnly = false;
var seriesFilter = "";
var filtersOnlySampleSeries = true;

/*
 * Add header in statistics table to group metrics by category
 * format
 *
 */
function summaryTableHeader(header) {
    var newRow = header.insertRow(-1);
    newRow.className = "tablesorter-no-sort";
    var cell = document.createElement('th');
    cell.setAttribute("data-sorter", false);
    cell.colSpan = 1;
    cell.innerHTML = "Requests";
    newRow.appendChild(cell);

    cell = document.createElement('th');
    cell.setAttribute("data-sorter", false);
    cell.colSpan = 3;
    cell.innerHTML = "Executions";
    newRow.appendChild(cell);

    cell = document.createElement('th');
    cell.setAttribute("data-sorter", false);
    cell.colSpan = 7;
    cell.innerHTML = "Response Times (ms)";
    newRow.appendChild(cell);

    cell = document.createElement('th');
    cell.setAttribute("data-sorter", false);
    cell.colSpan = 1;
    cell.innerHTML = "Throughput";
    newRow.appendChild(cell);

    cell = document.createElement('th');
    cell.setAttribute("data-sorter", false);
    cell.colSpan = 2;
    cell.innerHTML = "Network (KB/sec)";
    newRow.appendChild(cell);
}

/*
 * Populates the table identified by id parameter with the specified data and
 * format
 *
 */
function createTable(table, info, formatter, defaultSorts, seriesIndex, headerCreator) {
    var tableRef = table[0];

    // Create header and populate it with data.titles array
    var header = tableRef.createTHead();

    // Call callback is available
    if(headerCreator) {
        headerCreator(header);
    }

    var newRow = header.insertRow(-1);
    for (var index = 0; index < info.titles.length; index++) {
        var cell = document.createElement('th');
        cell.innerHTML = info.titles[index];
        newRow.appendChild(cell);
    }

    var tBody;

    // Create overall body if defined
    if(info.overall){
        tBody = document.createElement('tbody');
        tBody.className = "tablesorter-no-sort";
        tableRef.appendChild(tBody);
        var newRow = tBody.insertRow(-1);
        var data = info.overall.data;
        for(var index=0;index < data.length; index++){
            var cell = newRow.insertCell(-1);
            cell.innerHTML = formatter ? formatter(index, data[index]): data[index];
        }
    }

    // Create regular body
    tBody = document.createElement('tbody');
    tableRef.appendChild(tBody);

    var regexp;
    if(seriesFilter) {
        regexp = new RegExp(seriesFilter, 'i');
    }
    // Populate body with data.items array
    for(var index=0; index < info.items.length; index++){
        var item = info.items[index];
        if((!regexp || filtersOnlySampleSeries && !info.supportsControllersDiscrimination || regexp.test(item.data[seriesIndex]))
                &&
                (!showControllersOnly || !info.supportsControllersDiscrimination || item.isController)){
            if(item.data.length > 0) {
                var newRow = tBody.insertRow(-1);
                for(var col=0; col < item.data.length; col++){
                    var cell = newRow.insertCell(-1);
                    cell.innerHTML = formatter ? formatter(col, item.data[col]) : item.data[col];
                }
            }
        }
    }

    // Add support of columns sort
    table.tablesorter({sortList : defaultSorts});
}

$(document).ready(function() {

    // Customize table sorter default options
    $.extend( $.tablesorter.defaults, {
        theme: 'blue',
        cssInfoBlock: "tablesorter-no-sort",
        widthFixed: true,
        widgets: ['zebra']
    });

    var data = {"OkPercent": 100.0, "KoPercent": 0.0};
    var dataset = [
        {
            "label" : "FAIL",
            "data" : data.KoPercent,
            "color" : "#FF6347"
        },
        {
            "label" : "PASS",
            "data" : data.OkPercent,
            "color" : "#9ACD32"
        }];
    $.plot($("#flot-requests-summary"), dataset, {
        series : {
            pie : {
                show : true,
                radius : 1,
                label : {
                    show : true,
                    radius : 3 / 4,
                    formatter : function(label, series) {
                        return '<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'
                            + label
                            + '<br/>'
                            + Math.round10(series.percent, -2)
                            + '%</div>';
                    },
                    background : {
                        opacity : 0.5,
                        color : '#000'
                    }
                }
            }
        },
        legend : {
            show : true
        }
    });

    // Creates APDEX table
    createTable($("#apdexTable"), {"supportsControllersDiscrimination": true, "overall": {"data": [0.9996521739130435, 500, 2000, "Total"], "isController": false}, "titles": ["Apdex", "T (Toleration threshold)", "F (Frustration threshold)", "Label"], "items": [{"data": [1.0, 500, 2000, "Get all albums"], "isController": false}, {"data": [0.992, 500, 2000, "Home page"], "isController": true}, {"data": [1.0, 500, 2000, "Play songs from home page"], "isController": false}, {"data": [1.0, 500, 2000, "Trending playlist"], "isController": false}, {"data": [1.0, 500, 2000, "Albums page"], "isController": true}, {"data": [1.0, 500, 2000, "Cover 2"], "isController": false}, {"data": [1.0, 500, 2000, "Cover 1"], "isController": false}, {"data": [1.0, 500, 2000, "Cover 4"], "isController": false}, {"data": [1.0, 500, 2000, "Cover 3"], "isController": false}, {"data": [1.0, 500, 2000, "Play songs from albums"], "isController": false}, {"data": [1.0, 500, 2000, "Cover 5"], "isController": false}, {"data": [1.0, 500, 2000, "Search"], "isController": false}, {"data": [1.0, 500, 2000, "Initial song"], "isController": false}, {"data": [1.0, 500, 2000, "Get paths to songs from each album"], "isController": false}]}, function(index, item){
        switch(index){
            case 0:
                item = item.toFixed(3);
                break;
            case 1:
            case 2:
                item = formatDuration(item);
                break;
        }
        return item;
    }, [[0, 0]], 3);

    // Create statistics table
    createTable($("#statisticsTable"), {"supportsControllersDiscrimination": true, "overall": {"data": ["Total", 10500, 0, 0.0, 17.640095238095235, 1, 409, 4.0, 47.0, 64.0, 116.0, 19.99329748503359, 39961.95753007087, 2.945424411887634], "isController": false}, "titles": ["Label", "#Samples", "FAIL", "Error %", "Average", "Min", "Max", "Median", "90th pct", "95th pct", "99th pct", "Transactions/s", "Received", "Sent"], "items": [{"data": ["Get all albums", 500, 0, 0.0, 3.618000000000002, 2, 13, 3.0, 5.0, 6.0, 9.0, 1.4392962417096538, 1.4435129299177873, 0.17007309106139462], "isController": false}, {"data": ["Home page", 500, 0, 0.0, 78.90800000000003, 52, 574, 59.0, 97.90000000000003, 165.79999999999995, 519.97, 8.343345347750635, 72987.93545744476, 8.155945989353892], "isController": true}, {"data": ["Play songs from home page", 1500, 0, 0.0, 57.87666666666664, 27, 272, 46.0, 93.90000000000009, 119.95000000000005, 196.98000000000002, 5.4097136818871965, 37433.379097773584, 0.8994036410528745], "isController": false}, {"data": ["Trending playlist", 500, 0, 0.0, 5.621999999999999, 3, 51, 5.0, 8.0, 10.0, 18.99000000000001, 8.364840900726069, 56.89562194683307, 1.0946178522434502], "isController": false}, {"data": ["Albums page", 500, 0, 0.0, 17.793999999999993, 10, 47, 15.0, 28.0, 31.0, 42.0, 1.4392506685319355, 21.09261209244019, 1.2804271084302667], "isController": true}, {"data": ["Cover 2", 500, 0, 0.0, 4.724000000000003, 2, 42, 3.0, 8.0, 10.0, 35.98000000000002, 8.522824123001397, 3888.6134137532813, 1.156906790133979], "isController": false}, {"data": ["Cover 1", 500, 0, 0.0, 4.3, 2, 34, 3.0, 6.0, 9.0, 28.980000000000018, 8.518177791406861, 3222.1588017266345, 1.1562760869194861], "isController": false}, {"data": ["Cover 4", 500, 0, 0.0, 4.526000000000003, 2, 39, 3.0, 7.0, 10.0, 28.0, 8.53140409848653, 3747.410918810893, 1.1580714547750268], "isController": false}, {"data": ["Cover 3", 500, 0, 0.0, 3.651999999999996, 2, 29, 3.0, 6.0, 7.0, 25.980000000000018, 8.527911855503062, 2668.6367919701183, 1.1575974100731694], "isController": false}, {"data": ["Play songs from albums", 1000, 0, 0.0, 41.90399999999996, 29, 124, 36.0, 58.89999999999998, 67.94999999999993, 100.99000000000001, 2.2980264548805485, 14293.554645758504, 0.3817775981487099], "isController": false}, {"data": ["Cover 5", 500, 0, 0.0, 4.138000000000002, 2, 30, 3.0, 6.0, 9.0, 17.99000000000001, 8.536793580331228, 3014.7219673040804, 1.1588030348301177], "isController": false}, {"data": ["Search", 1500, 0, 0.0, 5.4340000000000055, 2, 22, 4.0, 9.0, 11.0, 16.0, 18.079041569742916, 75.88842481830564, 2.383467394448529], "isController": false}, {"data": ["Initial song", 500, 0, 0.0, 51.94599999999999, 34, 409, 38.0, 68.0, 106.94999999999999, 384.9100000000001, 8.470412847922208, 57610.5782618501, 1.4227646580494333], "isController": false}, {"data": ["Get paths to songs from each album", 2500, 0, 0.0, 2.8352000000000075, 1, 12, 2.0, 5.0, 6.0, 9.0, 7.196398346555516, 19.649540797821505, 1.1103817761286832], "isController": false}]}, function(index, item){
        switch(index){
            // Errors pct
            case 3:
                item = item.toFixed(2) + '%';
                break;
            // Mean
            case 4:
            // Mean
            case 7:
            // Median
            case 8:
            // Percentile 1
            case 9:
            // Percentile 2
            case 10:
            // Percentile 3
            case 11:
            // Throughput
            case 12:
            // Kbytes/s
            case 13:
            // Sent Kbytes/s
                item = item.toFixed(2);
                break;
        }
        return item;
    }, [[0, 0]], 0, summaryTableHeader);

    // Create error table
    createTable($("#errorsTable"), {"supportsControllersDiscrimination": false, "titles": ["Type of error", "Number of errors", "% in errors", "% in all samples"], "items": []}, function(index, item){
        switch(index){
            case 2:
            case 3:
                item = item.toFixed(2) + '%';
                break;
        }
        return item;
    }, [[1, 1]]);

        // Create top5 errors by sampler
    createTable($("#top5ErrorsBySamplerTable"), {"supportsControllersDiscrimination": false, "overall": {"data": ["Total", 10500, 0, "", "", "", "", "", "", "", "", "", ""], "isController": false}, "titles": ["Sample", "#Samples", "#Errors", "Error", "#Errors", "Error", "#Errors", "Error", "#Errors", "Error", "#Errors", "Error", "#Errors"], "items": [{"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}]}, function(index, item){
        return item;
    }, [[0, 0]], 0);

});
