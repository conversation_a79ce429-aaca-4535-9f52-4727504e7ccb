/*
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
$(document).ready(function() {

    $(".click-title").mouseenter( function(    e){
        e.preventDefault();
        this.style.cursor="pointer";
    });
    $(".click-title").mousedown( function(event){
        event.preventDefault();
    });

    // Ugly code while this script is shared among several pages
    try{
        refreshHitsPerSecond(true);
    } catch(e){}
    try{
        refreshResponseTimeOverTime(true);
    } catch(e){}
    try{
        refreshResponseTimePercentiles();
    } catch(e){}
});


var responseTimePercentilesInfos = {
        data: {"result": {"minY": 1.0, "minX": 0.0, "maxY": 574.0, "series": [{"data": [[0.0, 2.0], [0.1, 2.0], [0.2, 2.0], [0.3, 2.0], [0.4, 2.0], [0.5, 2.0], [0.6, 2.0], [0.7, 2.0], [0.8, 2.0], [0.9, 2.0], [1.0, 2.0], [1.1, 2.0], [1.2, 2.0], [1.3, 2.0], [1.4, 2.0], [1.5, 2.0], [1.6, 2.0], [1.7, 2.0], [1.8, 2.0], [1.9, 2.0], [2.0, 2.0], [2.1, 2.0], [2.2, 2.0], [2.3, 2.0], [2.4, 2.0], [2.5, 2.0], [2.6, 2.0], [2.7, 2.0], [2.8, 2.0], [2.9, 2.0], [3.0, 2.0], [3.1, 2.0], [3.2, 2.0], [3.3, 2.0], [3.4, 2.0], [3.5, 2.0], [3.6, 2.0], [3.7, 2.0], [3.8, 2.0], [3.9, 2.0], [4.0, 2.0], [4.1, 2.0], [4.2, 2.0], [4.3, 2.0], [4.4, 2.0], [4.5, 2.0], [4.6, 2.0], [4.7, 2.0], [4.8, 2.0], [4.9, 2.0], [5.0, 2.0], [5.1, 2.0], [5.2, 2.0], [5.3, 2.0], [5.4, 2.0], [5.5, 2.0], [5.6, 2.0], [5.7, 2.0], [5.8, 2.0], [5.9, 2.0], [6.0, 2.0], [6.1, 2.0], [6.2, 2.0], [6.3, 2.0], [6.4, 2.0], [6.5, 2.0], [6.6, 2.0], [6.7, 2.0], [6.8, 2.0], [6.9, 2.0], [7.0, 2.0], [7.1, 2.0], [7.2, 2.0], [7.3, 2.0], [7.4, 2.0], [7.5, 2.0], [7.6, 2.0], [7.7, 2.0], [7.8, 2.0], [7.9, 2.0], [8.0, 2.0], [8.1, 2.0], [8.2, 2.0], [8.3, 2.0], [8.4, 2.0], [8.5, 2.0], [8.6, 2.0], [8.7, 2.0], [8.8, 2.0], [8.9, 2.0], [9.0, 2.0], [9.1, 2.0], [9.2, 2.0], [9.3, 2.0], [9.4, 2.0], [9.5, 2.0], [9.6, 2.0], [9.7, 2.0], [9.8, 2.0], [9.9, 2.0], [10.0, 2.0], [10.1, 2.0], [10.2, 2.0], [10.3, 2.0], [10.4, 3.0], [10.5, 3.0], [10.6, 3.0], [10.7, 3.0], [10.8, 3.0], [10.9, 3.0], [11.0, 3.0], [11.1, 3.0], [11.2, 3.0], [11.3, 3.0], [11.4, 3.0], [11.5, 3.0], [11.6, 3.0], [11.7, 3.0], [11.8, 3.0], [11.9, 3.0], [12.0, 3.0], [12.1, 3.0], [12.2, 3.0], [12.3, 3.0], [12.4, 3.0], [12.5, 3.0], [12.6, 3.0], [12.7, 3.0], [12.8, 3.0], [12.9, 3.0], [13.0, 3.0], [13.1, 3.0], [13.2, 3.0], [13.3, 3.0], [13.4, 3.0], [13.5, 3.0], [13.6, 3.0], [13.7, 3.0], [13.8, 3.0], [13.9, 3.0], [14.0, 3.0], [14.1, 3.0], [14.2, 3.0], [14.3, 3.0], [14.4, 3.0], [14.5, 3.0], [14.6, 3.0], [14.7, 3.0], [14.8, 3.0], [14.9, 3.0], [15.0, 3.0], [15.1, 3.0], [15.2, 3.0], [15.3, 3.0], [15.4, 3.0], [15.5, 3.0], [15.6, 3.0], [15.7, 3.0], [15.8, 3.0], [15.9, 3.0], [16.0, 3.0], [16.1, 3.0], [16.2, 3.0], [16.3, 3.0], [16.4, 3.0], [16.5, 3.0], [16.6, 3.0], [16.7, 3.0], [16.8, 3.0], [16.9, 3.0], [17.0, 3.0], [17.1, 3.0], [17.2, 3.0], [17.3, 3.0], [17.4, 3.0], [17.5, 3.0], [17.6, 3.0], [17.7, 3.0], [17.8, 3.0], [17.9, 3.0], [18.0, 3.0], [18.1, 3.0], [18.2, 3.0], [18.3, 3.0], [18.4, 3.0], [18.5, 3.0], [18.6, 3.0], [18.7, 3.0], [18.8, 3.0], [18.9, 3.0], [19.0, 3.0], [19.1, 3.0], [19.2, 3.0], [19.3, 3.0], [19.4, 3.0], [19.5, 3.0], [19.6, 3.0], [19.7, 3.0], [19.8, 3.0], [19.9, 3.0], [20.0, 3.0], [20.1, 3.0], [20.2, 3.0], [20.3, 3.0], [20.4, 3.0], [20.5, 3.0], [20.6, 3.0], [20.7, 3.0], [20.8, 3.0], [20.9, 3.0], [21.0, 3.0], [21.1, 3.0], [21.2, 3.0], [21.3, 3.0], [21.4, 3.0], [21.5, 3.0], [21.6, 3.0], [21.7, 3.0], [21.8, 3.0], [21.9, 3.0], [22.0, 3.0], [22.1, 3.0], [22.2, 3.0], [22.3, 3.0], [22.4, 3.0], [22.5, 3.0], [22.6, 3.0], [22.7, 3.0], [22.8, 3.0], [22.9, 3.0], [23.0, 3.0], [23.1, 3.0], [23.2, 3.0], [23.3, 3.0], [23.4, 3.0], [23.5, 3.0], [23.6, 3.0], [23.7, 3.0], [23.8, 3.0], [23.9, 3.0], [24.0, 3.0], [24.1, 3.0], [24.2, 3.0], [24.3, 3.0], [24.4, 3.0], [24.5, 3.0], [24.6, 3.0], [24.7, 3.0], [24.8, 3.0], [24.9, 3.0], [25.0, 3.0], [25.1, 3.0], [25.2, 3.0], [25.3, 3.0], [25.4, 3.0], [25.5, 3.0], [25.6, 3.0], [25.7, 3.0], [25.8, 3.0], [25.9, 3.0], [26.0, 3.0], [26.1, 3.0], [26.2, 3.0], [26.3, 3.0], [26.4, 3.0], [26.5, 3.0], [26.6, 3.0], [26.7, 3.0], [26.8, 3.0], [26.9, 3.0], [27.0, 3.0], [27.1, 3.0], [27.2, 3.0], [27.3, 3.0], [27.4, 3.0], [27.5, 3.0], [27.6, 3.0], [27.7, 3.0], [27.8, 3.0], [27.9, 3.0], [28.0, 3.0], [28.1, 3.0], [28.2, 3.0], [28.3, 3.0], [28.4, 3.0], [28.5, 3.0], [28.6, 3.0], [28.7, 3.0], [28.8, 3.0], [28.9, 3.0], [29.0, 3.0], [29.1, 3.0], [29.2, 3.0], [29.3, 3.0], [29.4, 3.0], [29.5, 3.0], [29.6, 3.0], [29.7, 3.0], [29.8, 3.0], [29.9, 3.0], [30.0, 3.0], [30.1, 3.0], [30.2, 3.0], [30.3, 3.0], [30.4, 3.0], [30.5, 3.0], [30.6, 3.0], [30.7, 3.0], [30.8, 3.0], [30.9, 3.0], [31.0, 3.0], [31.1, 3.0], [31.2, 3.0], [31.3, 3.0], [31.4, 3.0], [31.5, 3.0], [31.6, 3.0], [31.7, 3.0], [31.8, 3.0], [31.9, 3.0], [32.0, 3.0], [32.1, 3.0], [32.2, 3.0], [32.3, 3.0], [32.4, 3.0], [32.5, 3.0], [32.6, 3.0], [32.7, 3.0], [32.8, 3.0], [32.9, 3.0], [33.0, 3.0], [33.1, 3.0], [33.2, 3.0], [33.3, 3.0], [33.4, 3.0], [33.5, 3.0], [33.6, 3.0], [33.7, 3.0], [33.8, 3.0], [33.9, 3.0], [34.0, 3.0], [34.1, 3.0], [34.2, 3.0], [34.3, 3.0], [34.4, 3.0], [34.5, 3.0], [34.6, 3.0], [34.7, 3.0], [34.8, 3.0], [34.9, 3.0], [35.0, 3.0], [35.1, 3.0], [35.2, 3.0], [35.3, 3.0], [35.4, 3.0], [35.5, 3.0], [35.6, 3.0], [35.7, 3.0], [35.8, 3.0], [35.9, 3.0], [36.0, 3.0], [36.1, 3.0], [36.2, 3.0], [36.3, 3.0], [36.4, 3.0], [36.5, 3.0], [36.6, 3.0], [36.7, 3.0], [36.8, 3.0], [36.9, 3.0], [37.0, 3.0], [37.1, 3.0], [37.2, 3.0], [37.3, 3.0], [37.4, 3.0], [37.5, 3.0], [37.6, 3.0], [37.7, 3.0], [37.8, 3.0], [37.9, 3.0], [38.0, 3.0], [38.1, 3.0], [38.2, 3.0], [38.3, 3.0], [38.4, 3.0], [38.5, 3.0], [38.6, 3.0], [38.7, 3.0], [38.8, 3.0], [38.9, 3.0], [39.0, 3.0], [39.1, 3.0], [39.2, 3.0], [39.3, 3.0], [39.4, 3.0], [39.5, 3.0], [39.6, 3.0], [39.7, 3.0], [39.8, 3.0], [39.9, 3.0], [40.0, 3.0], [40.1, 3.0], [40.2, 3.0], [40.3, 3.0], [40.4, 3.0], [40.5, 3.0], [40.6, 3.0], [40.7, 3.0], [40.8, 3.0], [40.9, 3.0], [41.0, 3.0], [41.1, 3.0], [41.2, 3.0], [41.3, 3.0], [41.4, 3.0], [41.5, 3.0], [41.6, 3.0], [41.7, 3.0], [41.8, 3.0], [41.9, 3.0], [42.0, 3.0], [42.1, 3.0], [42.2, 3.0], [42.3, 3.0], [42.4, 3.0], [42.5, 3.0], [42.6, 3.0], [42.7, 3.0], [42.8, 3.0], [42.9, 3.0], [43.0, 3.0], [43.1, 3.0], [43.2, 3.0], [43.3, 3.0], [43.4, 3.0], [43.5, 3.0], [43.6, 3.0], [43.7, 3.0], [43.8, 3.0], [43.9, 3.0], [44.0, 3.0], [44.1, 3.0], [44.2, 3.0], [44.3, 3.0], [44.4, 3.0], [44.5, 3.0], [44.6, 3.0], [44.7, 3.0], [44.8, 3.0], [44.9, 3.0], [45.0, 3.0], [45.1, 3.0], [45.2, 3.0], [45.3, 3.0], [45.4, 3.0], [45.5, 3.0], [45.6, 3.0], [45.7, 3.0], [45.8, 3.0], [45.9, 3.0], [46.0, 3.0], [46.1, 3.0], [46.2, 3.0], [46.3, 3.0], [46.4, 3.0], [46.5, 3.0], [46.6, 3.0], [46.7, 3.0], [46.8, 3.0], [46.9, 3.0], [47.0, 3.0], [47.1, 3.0], [47.2, 3.0], [47.3, 3.0], [47.4, 3.0], [47.5, 3.0], [47.6, 3.0], [47.7, 3.0], [47.8, 3.0], [47.9, 3.0], [48.0, 3.0], [48.1, 3.0], [48.2, 3.0], [48.3, 3.0], [48.4, 3.0], [48.5, 3.0], [48.6, 3.0], [48.7, 3.0], [48.8, 3.0], [48.9, 3.0], [49.0, 3.0], [49.1, 3.0], [49.2, 3.0], [49.3, 3.0], [49.4, 3.0], [49.5, 3.0], [49.6, 3.0], [49.7, 3.0], [49.8, 3.0], [49.9, 3.0], [50.0, 3.0], [50.1, 3.0], [50.2, 3.0], [50.3, 3.0], [50.4, 3.0], [50.5, 3.0], [50.6, 3.0], [50.7, 3.0], [50.8, 3.0], [50.9, 3.0], [51.0, 3.0], [51.1, 3.0], [51.2, 3.0], [51.3, 3.0], [51.4, 3.0], [51.5, 3.0], [51.6, 3.0], [51.7, 3.0], [51.8, 3.0], [51.9, 3.0], [52.0, 3.0], [52.1, 3.0], [52.2, 3.0], [52.3, 3.0], [52.4, 3.0], [52.5, 3.0], [52.6, 3.0], [52.7, 3.0], [52.8, 3.0], [52.9, 3.0], [53.0, 3.0], [53.1, 3.0], [53.2, 3.0], [53.3, 3.0], [53.4, 3.0], [53.5, 3.0], [53.6, 3.0], [53.7, 3.0], [53.8, 3.0], [53.9, 3.0], [54.0, 3.0], [54.1, 3.0], [54.2, 3.0], [54.3, 3.0], [54.4, 3.0], [54.5, 3.0], [54.6, 3.0], [54.7, 3.0], [54.8, 3.0], [54.9, 3.0], [55.0, 3.0], [55.1, 3.0], [55.2, 3.0], [55.3, 3.0], [55.4, 3.0], [55.5, 3.0], [55.6, 3.0], [55.7, 3.0], [55.8, 3.0], [55.9, 3.0], [56.0, 3.0], [56.1, 3.0], [56.2, 3.0], [56.3, 3.0], [56.4, 3.0], [56.5, 3.0], [56.6, 3.0], [56.7, 3.0], [56.8, 3.0], [56.9, 3.0], [57.0, 3.0], [57.1, 3.0], [57.2, 3.0], [57.3, 3.0], [57.4, 3.0], [57.5, 3.0], [57.6, 3.0], [57.7, 3.0], [57.8, 3.0], [57.9, 3.0], [58.0, 3.0], [58.1, 3.0], [58.2, 3.0], [58.3, 3.0], [58.4, 3.0], [58.5, 3.0], [58.6, 3.0], [58.7, 3.0], [58.8, 3.0], [58.9, 3.0], [59.0, 3.0], [59.1, 3.0], [59.2, 3.0], [59.3, 3.0], [59.4, 3.0], [59.5, 3.0], [59.6, 3.0], [59.7, 3.0], [59.8, 3.0], [59.9, 3.0], [60.0, 4.0], [60.1, 4.0], [60.2, 4.0], [60.3, 4.0], [60.4, 4.0], [60.5, 4.0], [60.6, 4.0], [60.7, 4.0], [60.8, 4.0], [60.9, 4.0], [61.0, 4.0], [61.1, 4.0], [61.2, 4.0], [61.3, 4.0], [61.4, 4.0], [61.5, 4.0], [61.6, 4.0], [61.7, 4.0], [61.8, 4.0], [61.9, 4.0], [62.0, 4.0], [62.1, 4.0], [62.2, 4.0], [62.3, 4.0], [62.4, 4.0], [62.5, 4.0], [62.6, 4.0], [62.7, 4.0], [62.8, 4.0], [62.9, 4.0], [63.0, 4.0], [63.1, 4.0], [63.2, 4.0], [63.3, 4.0], [63.4, 4.0], [63.5, 4.0], [63.6, 4.0], [63.7, 4.0], [63.8, 4.0], [63.9, 4.0], [64.0, 4.0], [64.1, 4.0], [64.2, 4.0], [64.3, 4.0], [64.4, 4.0], [64.5, 4.0], [64.6, 4.0], [64.7, 4.0], [64.8, 4.0], [64.9, 4.0], [65.0, 4.0], [65.1, 4.0], [65.2, 4.0], [65.3, 4.0], [65.4, 4.0], [65.5, 4.0], [65.6, 4.0], [65.7, 4.0], [65.8, 4.0], [65.9, 4.0], [66.0, 4.0], [66.1, 4.0], [66.2, 4.0], [66.3, 4.0], [66.4, 4.0], [66.5, 4.0], [66.6, 4.0], [66.7, 4.0], [66.8, 4.0], [66.9, 4.0], [67.0, 4.0], [67.1, 4.0], [67.2, 4.0], [67.3, 4.0], [67.4, 4.0], [67.5, 4.0], [67.6, 4.0], [67.7, 4.0], [67.8, 4.0], [67.9, 4.0], [68.0, 4.0], [68.1, 4.0], [68.2, 4.0], [68.3, 4.0], [68.4, 4.0], [68.5, 4.0], [68.6, 4.0], [68.7, 4.0], [68.8, 4.0], [68.9, 4.0], [69.0, 4.0], [69.1, 4.0], [69.2, 4.0], [69.3, 4.0], [69.4, 4.0], [69.5, 4.0], [69.6, 4.0], [69.7, 4.0], [69.8, 4.0], [69.9, 4.0], [70.0, 4.0], [70.1, 4.0], [70.2, 4.0], [70.3, 4.0], [70.4, 4.0], [70.5, 4.0], [70.6, 4.0], [70.7, 4.0], [70.8, 4.0], [70.9, 4.0], [71.0, 4.0], [71.1, 4.0], [71.2, 4.0], [71.3, 4.0], [71.4, 4.0], [71.5, 4.0], [71.6, 4.0], [71.7, 4.0], [71.8, 4.0], [71.9, 4.0], [72.0, 4.0], [72.1, 4.0], [72.2, 4.0], [72.3, 4.0], [72.4, 4.0], [72.5, 4.0], [72.6, 4.0], [72.7, 4.0], [72.8, 4.0], [72.9, 4.0], [73.0, 4.0], [73.1, 4.0], [73.2, 4.0], [73.3, 4.0], [73.4, 4.0], [73.5, 4.0], [73.6, 4.0], [73.7, 4.0], [73.8, 4.0], [73.9, 4.0], [74.0, 4.0], [74.1, 4.0], [74.2, 4.0], [74.3, 4.0], [74.4, 4.0], [74.5, 4.0], [74.6, 4.0], [74.7, 4.0], [74.8, 4.0], [74.9, 4.0], [75.0, 4.0], [75.1, 4.0], [75.2, 4.0], [75.3, 4.0], [75.4, 4.0], [75.5, 4.0], [75.6, 4.0], [75.7, 4.0], [75.8, 4.0], [75.9, 4.0], [76.0, 4.0], [76.1, 4.0], [76.2, 4.0], [76.3, 4.0], [76.4, 4.0], [76.5, 4.0], [76.6, 4.0], [76.7, 4.0], [76.8, 4.0], [76.9, 4.0], [77.0, 4.0], [77.1, 4.0], [77.2, 4.0], [77.3, 4.0], [77.4, 4.0], [77.5, 4.0], [77.6, 4.0], [77.7, 4.0], [77.8, 4.0], [77.9, 4.0], [78.0, 4.0], [78.1, 4.0], [78.2, 4.0], [78.3, 4.0], [78.4, 4.0], [78.5, 4.0], [78.6, 4.0], [78.7, 4.0], [78.8, 4.0], [78.9, 4.0], [79.0, 4.0], [79.1, 4.0], [79.2, 4.0], [79.3, 4.0], [79.4, 4.0], [79.5, 4.0], [79.6, 4.0], [79.7, 4.0], [79.8, 4.0], [79.9, 4.0], [80.0, 4.0], [80.1, 4.0], [80.2, 4.0], [80.3, 4.0], [80.4, 4.0], [80.5, 4.0], [80.6, 4.0], [80.7, 4.0], [80.8, 4.0], [80.9, 4.0], [81.0, 4.0], [81.1, 4.0], [81.2, 4.0], [81.3, 4.0], [81.4, 4.0], [81.5, 4.0], [81.6, 4.0], [81.7, 4.0], [81.8, 4.0], [81.9, 4.0], [82.0, 4.0], [82.1, 4.0], [82.2, 4.0], [82.3, 4.0], [82.4, 4.0], [82.5, 4.0], [82.6, 4.0], [82.7, 4.0], [82.8, 4.0], [82.9, 4.0], [83.0, 4.0], [83.1, 4.0], [83.2, 4.0], [83.3, 4.0], [83.4, 4.0], [83.5, 4.0], [83.6, 4.0], [83.7, 4.0], [83.8, 4.0], [83.9, 4.0], [84.0, 4.0], [84.1, 4.0], [84.2, 4.0], [84.3, 4.0], [84.4, 4.0], [84.5, 4.0], [84.6, 4.0], [84.7, 4.0], [84.8, 4.0], [84.9, 4.0], [85.0, 4.0], [85.1, 4.0], [85.2, 4.0], [85.3, 4.0], [85.4, 4.0], [85.5, 4.0], [85.6, 5.0], [85.7, 5.0], [85.8, 5.0], [85.9, 5.0], [86.0, 5.0], [86.1, 5.0], [86.2, 5.0], [86.3, 5.0], [86.4, 5.0], [86.5, 5.0], [86.6, 5.0], [86.7, 5.0], [86.8, 5.0], [86.9, 5.0], [87.0, 5.0], [87.1, 5.0], [87.2, 5.0], [87.3, 5.0], [87.4, 5.0], [87.5, 5.0], [87.6, 5.0], [87.7, 5.0], [87.8, 5.0], [87.9, 5.0], [88.0, 5.0], [88.1, 5.0], [88.2, 5.0], [88.3, 5.0], [88.4, 5.0], [88.5, 5.0], [88.6, 5.0], [88.7, 5.0], [88.8, 5.0], [88.9, 5.0], [89.0, 5.0], [89.1, 5.0], [89.2, 5.0], [89.3, 5.0], [89.4, 5.0], [89.5, 5.0], [89.6, 5.0], [89.7, 5.0], [89.8, 5.0], [89.9, 5.0], [90.0, 5.0], [90.1, 5.0], [90.2, 5.0], [90.3, 5.0], [90.4, 5.0], [90.5, 5.0], [90.6, 5.0], [90.7, 5.0], [90.8, 5.0], [90.9, 5.0], [91.0, 5.0], [91.1, 5.0], [91.2, 5.0], [91.3, 5.0], [91.4, 5.0], [91.5, 5.0], [91.6, 5.0], [91.7, 5.0], [91.8, 5.0], [91.9, 5.0], [92.0, 5.0], [92.1, 5.0], [92.2, 6.0], [92.3, 6.0], [92.4, 6.0], [92.5, 6.0], [92.6, 6.0], [92.7, 6.0], [92.8, 6.0], [92.9, 6.0], [93.0, 6.0], [93.1, 6.0], [93.2, 6.0], [93.3, 6.0], [93.4, 6.0], [93.5, 6.0], [93.6, 6.0], [93.7, 6.0], [93.8, 6.0], [93.9, 6.0], [94.0, 6.0], [94.1, 6.0], [94.2, 6.0], [94.3, 6.0], [94.4, 6.0], [94.5, 6.0], [94.6, 6.0], [94.7, 6.0], [94.8, 6.0], [94.9, 6.0], [95.0, 6.0], [95.1, 6.0], [95.2, 6.0], [95.3, 6.0], [95.4, 6.0], [95.5, 6.0], [95.6, 7.0], [95.7, 7.0], [95.8, 7.0], [95.9, 7.0], [96.0, 7.0], [96.1, 7.0], [96.2, 7.0], [96.3, 7.0], [96.4, 7.0], [96.5, 7.0], [96.6, 7.0], [96.7, 7.0], [96.8, 7.0], [96.9, 7.0], [97.0, 7.0], [97.1, 7.0], [97.2, 7.0], [97.3, 7.0], [97.4, 7.0], [97.5, 7.0], [97.6, 7.0], [97.7, 7.0], [97.8, 8.0], [97.9, 8.0], [98.0, 8.0], [98.1, 8.0], [98.2, 9.0], [98.3, 9.0], [98.4, 9.0], [98.5, 9.0], [98.6, 9.0], [98.7, 9.0], [98.8, 9.0], [98.9, 9.0], [99.0, 9.0], [99.1, 9.0], [99.2, 10.0], [99.3, 10.0], [99.4, 10.0], [99.5, 10.0], [99.6, 10.0], [99.7, 11.0], [99.8, 11.0], [99.9, 13.0], [100.0, 13.0]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[0.0, 52.0], [0.1, 52.0], [0.2, 52.0], [0.3, 52.0], [0.4, 52.0], [0.5, 52.0], [0.6, 52.0], [0.7, 52.0], [0.8, 52.0], [0.9, 52.0], [1.0, 52.0], [1.1, 52.0], [1.2, 52.0], [1.3, 52.0], [1.4, 53.0], [1.5, 53.0], [1.6, 53.0], [1.7, 53.0], [1.8, 53.0], [1.9, 53.0], [2.0, 53.0], [2.1, 53.0], [2.2, 53.0], [2.3, 53.0], [2.4, 53.0], [2.5, 53.0], [2.6, 53.0], [2.7, 53.0], [2.8, 53.0], [2.9, 53.0], [3.0, 53.0], [3.1, 53.0], [3.2, 53.0], [3.3, 53.0], [3.4, 54.0], [3.5, 54.0], [3.6, 54.0], [3.7, 54.0], [3.8, 54.0], [3.9, 54.0], [4.0, 54.0], [4.1, 54.0], [4.2, 54.0], [4.3, 54.0], [4.4, 54.0], [4.5, 54.0], [4.6, 54.0], [4.7, 54.0], [4.8, 54.0], [4.9, 54.0], [5.0, 54.0], [5.1, 54.0], [5.2, 54.0], [5.3, 54.0], [5.4, 54.0], [5.5, 54.0], [5.6, 54.0], [5.7, 54.0], [5.8, 54.0], [5.9, 54.0], [6.0, 55.0], [6.1, 55.0], [6.2, 55.0], [6.3, 55.0], [6.4, 55.0], [6.5, 55.0], [6.6, 55.0], [6.7, 55.0], [6.8, 55.0], [6.9, 55.0], [7.0, 55.0], [7.1, 55.0], [7.2, 55.0], [7.3, 55.0], [7.4, 55.0], [7.5, 55.0], [7.6, 55.0], [7.7, 55.0], [7.8, 55.0], [7.9, 55.0], [8.0, 55.0], [8.1, 55.0], [8.2, 55.0], [8.3, 55.0], [8.4, 55.0], [8.5, 55.0], [8.6, 55.0], [8.7, 55.0], [8.8, 55.0], [8.9, 55.0], [9.0, 55.0], [9.1, 55.0], [9.2, 55.0], [9.3, 55.0], [9.4, 55.0], [9.5, 55.0], [9.6, 55.0], [9.7, 55.0], [9.8, 55.0], [9.9, 55.0], [10.0, 55.0], [10.1, 55.0], [10.2, 55.0], [10.3, 55.0], [10.4, 55.0], [10.5, 55.0], [10.6, 55.0], [10.7, 55.0], [10.8, 55.0], [10.9, 55.0], [11.0, 55.0], [11.1, 55.0], [11.2, 55.0], [11.3, 55.0], [11.4, 55.0], [11.5, 55.0], [11.6, 55.0], [11.7, 55.0], [11.8, 55.0], [11.9, 55.0], [12.0, 55.0], [12.1, 55.0], [12.2, 55.0], [12.3, 55.0], [12.4, 56.0], [12.5, 56.0], [12.6, 56.0], [12.7, 56.0], [12.8, 56.0], [12.9, 56.0], [13.0, 56.0], [13.1, 56.0], [13.2, 56.0], [13.3, 56.0], [13.4, 56.0], [13.5, 56.0], [13.6, 56.0], [13.7, 56.0], [13.8, 56.0], [13.9, 56.0], [14.0, 56.0], [14.1, 56.0], [14.2, 56.0], [14.3, 56.0], [14.4, 56.0], [14.5, 56.0], [14.6, 56.0], [14.7, 56.0], [14.8, 56.0], [14.9, 56.0], [15.0, 56.0], [15.1, 56.0], [15.2, 56.0], [15.3, 56.0], [15.4, 56.0], [15.5, 56.0], [15.6, 56.0], [15.7, 56.0], [15.8, 56.0], [15.9, 56.0], [16.0, 56.0], [16.1, 56.0], [16.2, 56.0], [16.3, 56.0], [16.4, 56.0], [16.5, 56.0], [16.6, 56.0], [16.7, 56.0], [16.8, 56.0], [16.9, 56.0], [17.0, 56.0], [17.1, 56.0], [17.2, 56.0], [17.3, 56.0], [17.4, 56.0], [17.5, 56.0], [17.6, 56.0], [17.7, 56.0], [17.8, 56.0], [17.9, 56.0], [18.0, 56.0], [18.1, 56.0], [18.2, 56.0], [18.3, 56.0], [18.4, 56.0], [18.5, 56.0], [18.6, 56.0], [18.7, 56.0], [18.8, 56.0], [18.9, 56.0], [19.0, 56.0], [19.1, 56.0], [19.2, 56.0], [19.3, 56.0], [19.4, 56.0], [19.5, 56.0], [19.6, 56.0], [19.7, 56.0], [19.8, 56.0], [19.9, 56.0], [20.0, 56.0], [20.1, 56.0], [20.2, 56.0], [20.3, 56.0], [20.4, 56.0], [20.5, 56.0], [20.6, 56.0], [20.7, 56.0], [20.8, 56.0], [20.9, 56.0], [21.0, 56.0], [21.1, 56.0], [21.2, 56.0], [21.3, 56.0], [21.4, 56.0], [21.5, 56.0], [21.6, 56.0], [21.7, 56.0], [21.8, 56.0], [21.9, 56.0], [22.0, 56.0], [22.1, 56.0], [22.2, 56.0], [22.3, 56.0], [22.4, 56.0], [22.5, 56.0], [22.6, 56.0], [22.7, 56.0], [22.8, 56.0], [22.9, 56.0], [23.0, 56.0], [23.1, 56.0], [23.2, 56.0], [23.3, 56.0], [23.4, 56.0], [23.5, 56.0], [23.6, 56.0], [23.7, 56.0], [23.8, 56.0], [23.9, 56.0], [24.0, 56.0], [24.1, 56.0], [24.2, 56.0], [24.3, 57.0], [24.4, 57.0], [24.5, 57.0], [24.6, 57.0], [24.7, 57.0], [24.8, 57.0], [24.9, 57.0], [25.0, 57.0], [25.1, 57.0], [25.2, 57.0], [25.3, 57.0], [25.4, 57.0], [25.5, 57.0], [25.6, 57.0], [25.7, 57.0], [25.8, 57.0], [25.9, 57.0], [26.0, 57.0], [26.1, 57.0], [26.2, 57.0], [26.3, 57.0], [26.4, 57.0], [26.5, 57.0], [26.6, 57.0], [26.7, 57.0], [26.8, 57.0], [26.9, 57.0], [27.0, 57.0], [27.1, 57.0], [27.2, 57.0], [27.3, 57.0], [27.4, 57.0], [27.5, 57.0], [27.6, 57.0], [27.7, 57.0], [27.8, 57.0], [27.9, 57.0], [28.0, 57.0], [28.1, 57.0], [28.2, 57.0], [28.3, 57.0], [28.4, 57.0], [28.5, 57.0], [28.6, 57.0], [28.7, 57.0], [28.8, 57.0], [28.9, 57.0], [29.0, 57.0], [29.1, 57.0], [29.2, 57.0], [29.3, 57.0], [29.4, 57.0], [29.5, 57.0], [29.6, 57.0], [29.7, 57.0], [29.8, 57.0], [29.9, 57.0], [30.0, 57.0], [30.1, 57.0], [30.2, 57.0], [30.3, 57.0], [30.4, 57.0], [30.5, 57.0], [30.6, 57.0], [30.7, 57.0], [30.8, 57.0], [30.9, 57.0], [31.0, 57.0], [31.1, 57.0], [31.2, 57.0], [31.3, 57.0], [31.4, 57.0], [31.5, 57.0], [31.6, 57.0], [31.7, 57.0], [31.8, 57.0], [31.9, 57.0], [32.0, 57.0], [32.1, 57.0], [32.2, 57.0], [32.3, 57.0], [32.4, 57.0], [32.5, 57.0], [32.6, 57.0], [32.7, 57.0], [32.8, 57.0], [32.9, 57.0], [33.0, 57.0], [33.1, 57.0], [33.2, 57.0], [33.3, 57.0], [33.4, 57.0], [33.5, 57.0], [33.6, 57.0], [33.7, 57.0], [33.8, 57.0], [33.9, 57.0], [34.0, 57.0], [34.1, 57.0], [34.2, 57.0], [34.3, 57.0], [34.4, 57.0], [34.5, 57.0], [34.6, 57.0], [34.7, 57.0], [34.8, 57.0], [34.9, 57.0], [35.0, 58.0], [35.1, 58.0], [35.2, 58.0], [35.3, 58.0], [35.4, 58.0], [35.5, 58.0], [35.6, 58.0], [35.7, 58.0], [35.8, 58.0], [35.9, 58.0], [36.0, 58.0], [36.1, 58.0], [36.2, 58.0], [36.3, 58.0], [36.4, 58.0], [36.5, 58.0], [36.6, 58.0], [36.7, 58.0], [36.8, 58.0], [36.9, 58.0], [37.0, 58.0], [37.1, 58.0], [37.2, 58.0], [37.3, 58.0], [37.4, 58.0], [37.5, 58.0], [37.6, 58.0], [37.7, 58.0], [37.8, 58.0], [37.9, 58.0], [38.0, 58.0], [38.1, 58.0], [38.2, 58.0], [38.3, 58.0], [38.4, 58.0], [38.5, 58.0], [38.6, 58.0], [38.7, 58.0], [38.8, 58.0], [38.9, 58.0], [39.0, 58.0], [39.1, 58.0], [39.2, 58.0], [39.3, 58.0], [39.4, 58.0], [39.5, 58.0], [39.6, 58.0], [39.7, 58.0], [39.8, 58.0], [39.9, 58.0], [40.0, 58.0], [40.1, 58.0], [40.2, 58.0], [40.3, 58.0], [40.4, 58.0], [40.5, 58.0], [40.6, 58.0], [40.7, 58.0], [40.8, 58.0], [40.9, 58.0], [41.0, 58.0], [41.1, 58.0], [41.2, 58.0], [41.3, 58.0], [41.4, 58.0], [41.5, 58.0], [41.6, 58.0], [41.7, 58.0], [41.8, 58.0], [41.9, 58.0], [42.0, 58.0], [42.1, 58.0], [42.2, 58.0], [42.3, 58.0], [42.4, 58.0], [42.5, 58.0], [42.6, 58.0], [42.7, 58.0], [42.8, 58.0], [42.9, 58.0], [43.0, 58.0], [43.1, 58.0], [43.2, 58.0], [43.3, 58.0], [43.4, 58.0], [43.5, 58.0], [43.6, 58.0], [43.7, 58.0], [43.8, 58.0], [43.9, 58.0], [44.0, 58.0], [44.1, 58.0], [44.2, 58.0], [44.3, 58.0], [44.4, 58.0], [44.5, 58.0], [44.6, 58.0], [44.7, 58.0], [44.8, 58.0], [44.9, 58.0], [45.0, 58.0], [45.1, 58.0], [45.2, 59.0], [45.3, 59.0], [45.4, 59.0], [45.5, 59.0], [45.6, 59.0], [45.7, 59.0], [45.8, 59.0], [45.9, 59.0], [46.0, 59.0], [46.1, 59.0], [46.2, 59.0], [46.3, 59.0], [46.4, 59.0], [46.5, 59.0], [46.6, 59.0], [46.7, 59.0], [46.8, 59.0], [46.9, 59.0], [47.0, 59.0], [47.1, 59.0], [47.2, 59.0], [47.3, 59.0], [47.4, 59.0], [47.5, 59.0], [47.6, 59.0], [47.7, 59.0], [47.8, 59.0], [47.9, 59.0], [48.0, 59.0], [48.1, 59.0], [48.2, 59.0], [48.3, 59.0], [48.4, 59.0], [48.5, 59.0], [48.6, 59.0], [48.7, 59.0], [48.8, 59.0], [48.9, 59.0], [49.0, 59.0], [49.1, 59.0], [49.2, 59.0], [49.3, 59.0], [49.4, 59.0], [49.5, 59.0], [49.6, 59.0], [49.7, 59.0], [49.8, 59.0], [49.9, 59.0], [50.0, 59.0], [50.1, 59.0], [50.2, 59.0], [50.3, 59.0], [50.4, 59.0], [50.5, 59.0], [50.6, 59.0], [50.7, 59.0], [50.8, 59.0], [50.9, 59.0], [51.0, 59.0], [51.1, 59.0], [51.2, 59.0], [51.3, 59.0], [51.4, 59.0], [51.5, 59.0], [51.6, 59.0], [51.7, 59.0], [51.8, 59.0], [51.9, 59.0], [52.0, 59.0], [52.1, 59.0], [52.2, 59.0], [52.3, 59.0], [52.4, 59.0], [52.5, 59.0], [52.6, 59.0], [52.7, 59.0], [52.8, 59.0], [52.9, 59.0], [53.0, 59.0], [53.1, 59.0], [53.2, 59.0], [53.3, 59.0], [53.4, 59.0], [53.5, 59.0], [53.6, 59.0], [53.7, 59.0], [53.8, 59.0], [53.9, 59.0], [54.0, 59.0], [54.1, 59.0], [54.2, 59.0], [54.3, 59.0], [54.4, 59.0], [54.5, 59.0], [54.6, 59.0], [54.7, 59.0], [54.8, 59.0], [54.9, 60.0], [55.0, 60.0], [55.1, 60.0], [55.2, 60.0], [55.3, 60.0], [55.4, 60.0], [55.5, 60.0], [55.6, 60.0], [55.7, 60.0], [55.8, 60.0], [55.9, 60.0], [56.0, 60.0], [56.1, 60.0], [56.2, 60.0], [56.3, 60.0], [56.4, 60.0], [56.5, 60.0], [56.6, 60.0], [56.7, 60.0], [56.8, 60.0], [56.9, 60.0], [57.0, 60.0], [57.1, 60.0], [57.2, 60.0], [57.3, 60.0], [57.4, 60.0], [57.5, 60.0], [57.6, 60.0], [57.7, 60.0], [57.8, 60.0], [57.9, 60.0], [58.0, 60.0], [58.1, 60.0], [58.2, 60.0], [58.3, 60.0], [58.4, 60.0], [58.5, 60.0], [58.6, 60.0], [58.7, 60.0], [58.8, 60.0], [58.9, 60.0], [59.0, 60.0], [59.1, 60.0], [59.2, 60.0], [59.3, 60.0], [59.4, 60.0], [59.5, 60.0], [59.6, 60.0], [59.7, 60.0], [59.8, 60.0], [59.9, 60.0], [60.0, 60.0], [60.1, 60.0], [60.2, 60.0], [60.3, 60.0], [60.4, 60.0], [60.5, 60.0], [60.6, 61.0], [60.7, 61.0], [60.8, 61.0], [60.9, 61.0], [61.0, 61.0], [61.1, 61.0], [61.2, 61.0], [61.3, 61.0], [61.4, 61.0], [61.5, 61.0], [61.6, 61.0], [61.7, 61.0], [61.8, 61.0], [61.9, 61.0], [62.0, 61.0], [62.1, 61.0], [62.2, 61.0], [62.3, 61.0], [62.4, 61.0], [62.5, 61.0], [62.6, 61.0], [62.7, 61.0], [62.8, 61.0], [62.9, 61.0], [63.0, 61.0], [63.1, 61.0], [63.2, 61.0], [63.3, 61.0], [63.4, 61.0], [63.5, 61.0], [63.6, 61.0], [63.7, 61.0], [63.8, 61.0], [63.9, 61.0], [64.0, 61.0], [64.1, 61.0], [64.2, 61.0], [64.3, 61.0], [64.4, 61.0], [64.5, 61.0], [64.6, 61.0], [64.7, 61.0], [64.8, 61.0], [64.9, 61.0], [65.0, 61.0], [65.1, 61.0], [65.2, 61.0], [65.3, 61.0], [65.4, 61.0], [65.5, 61.0], [65.6, 61.0], [65.7, 61.0], [65.8, 61.0], [65.9, 61.0], [66.0, 62.0], [66.1, 62.0], [66.2, 62.0], [66.3, 62.0], [66.4, 62.0], [66.5, 62.0], [66.6, 62.0], [66.7, 62.0], [66.8, 62.0], [66.9, 62.0], [67.0, 62.0], [67.1, 62.0], [67.2, 62.0], [67.3, 62.0], [67.4, 62.0], [67.5, 62.0], [67.6, 62.0], [67.7, 62.0], [67.8, 62.0], [67.9, 62.0], [68.0, 63.0], [68.1, 63.0], [68.2, 63.0], [68.3, 63.0], [68.4, 63.0], [68.5, 63.0], [68.6, 63.0], [68.7, 63.0], [68.8, 63.0], [68.9, 63.0], [69.0, 63.0], [69.1, 63.0], [69.2, 63.0], [69.3, 63.0], [69.4, 63.0], [69.5, 63.0], [69.6, 64.0], [69.7, 64.0], [69.8, 64.0], [69.9, 64.0], [70.0, 64.0], [70.1, 64.0], [70.2, 64.0], [70.3, 64.0], [70.4, 64.0], [70.5, 64.0], [70.6, 64.0], [70.7, 64.0], [70.8, 64.0], [70.9, 64.0], [71.0, 64.0], [71.1, 64.0], [71.2, 64.0], [71.3, 64.0], [71.4, 65.0], [71.5, 65.0], [71.6, 65.0], [71.7, 65.0], [71.8, 65.0], [71.9, 65.0], [72.0, 65.0], [72.1, 65.0], [72.2, 65.0], [72.3, 65.0], [72.4, 65.0], [72.5, 65.0], [72.6, 65.0], [72.7, 65.0], [72.8, 66.0], [72.9, 66.0], [73.0, 67.0], [73.1, 67.0], [73.2, 67.0], [73.3, 67.0], [73.4, 68.0], [73.5, 68.0], [73.6, 68.0], [73.7, 68.0], [73.8, 68.0], [73.9, 68.0], [74.0, 68.0], [74.1, 68.0], [74.2, 69.0], [74.3, 69.0], [74.4, 69.0], [74.5, 69.0], [74.6, 69.0], [74.7, 69.0], [74.8, 70.0], [74.9, 70.0], [75.0, 70.0], [75.1, 70.0], [75.2, 70.0], [75.3, 70.0], [75.4, 70.0], [75.5, 70.0], [75.6, 70.0], [75.7, 70.0], [75.8, 70.0], [75.9, 70.0], [76.0, 70.0], [76.1, 70.0], [76.2, 70.0], [76.3, 70.0], [76.4, 71.0], [76.5, 71.0], [76.6, 71.0], [76.7, 71.0], [76.8, 71.0], [76.9, 71.0], [77.0, 71.0], [77.1, 71.0], [77.2, 71.0], [77.3, 71.0], [77.4, 71.0], [77.5, 71.0], [77.6, 71.0], [77.7, 71.0], [77.8, 72.0], [77.9, 72.0], [78.0, 72.0], [78.1, 72.0], [78.2, 72.0], [78.3, 72.0], [78.4, 73.0], [78.5, 73.0], [78.6, 73.0], [78.7, 73.0], [78.8, 73.0], [78.9, 73.0], [79.0, 74.0], [79.1, 74.0], [79.2, 74.0], [79.3, 74.0], [79.4, 75.0], [79.5, 75.0], [79.6, 76.0], [79.7, 76.0], [79.8, 76.0], [79.9, 76.0], [80.0, 77.0], [80.1, 77.0], [80.2, 77.0], [80.3, 77.0], [80.4, 77.0], [80.5, 77.0], [80.6, 78.0], [80.7, 78.0], [80.8, 79.0], [80.9, 79.0], [81.0, 79.0], [81.1, 79.0], [81.2, 79.0], [81.3, 79.0], [81.4, 79.0], [81.5, 79.0], [81.6, 79.0], [81.7, 79.0], [81.8, 80.0], [81.9, 80.0], [82.0, 81.0], [82.1, 81.0], [82.2, 81.0], [82.3, 81.0], [82.4, 81.0], [82.5, 81.0], [82.6, 81.0], [82.7, 81.0], [82.8, 82.0], [82.9, 82.0], [83.0, 83.0], [83.1, 83.0], [83.2, 83.0], [83.3, 83.0], [83.4, 84.0], [83.5, 84.0], [83.6, 84.0], [83.7, 85.0], [83.8, 85.0], [83.9, 85.0], [84.0, 85.0], [84.1, 85.0], [84.2, 86.0], [84.3, 86.0], [84.4, 86.0], [84.5, 86.0], [84.6, 86.0], [84.7, 88.0], [84.8, 88.0], [84.9, 89.0], [85.0, 89.0], [85.1, 89.0], [85.2, 89.0], [85.3, 90.0], [85.4, 90.0], [85.5, 90.0], [85.6, 90.0], [85.7, 90.0], [85.8, 90.0], [85.9, 91.0], [86.0, 91.0], [86.1, 91.0], [86.2, 91.0], [86.3, 92.0], [86.4, 92.0], [86.5, 92.0], [86.6, 92.0], [86.7, 92.0], [86.8, 92.0], [86.9, 93.0], [87.0, 93.0], [87.1, 93.0], [87.2, 93.0], [87.3, 93.0], [87.4, 93.0], [87.5, 93.0], [87.6, 93.0], [87.7, 93.0], [87.8, 93.0], [87.9, 94.0], [88.0, 94.0], [88.1, 94.0], [88.2, 94.0], [88.3, 94.0], [88.4, 95.0], [88.5, 95.0], [88.6, 95.0], [88.7, 95.0], [88.8, 95.0], [88.9, 96.0], [89.0, 96.0], [89.1, 97.0], [89.2, 97.0], [89.3, 97.0], [89.4, 97.0], [89.5, 97.0], [89.6, 97.0], [89.7, 97.0], [89.8, 97.0], [89.9, 97.0], [90.0, 97.0], [90.1, 98.0], [90.2, 98.0], [90.3, 98.0], [90.4, 98.0], [90.5, 98.0], [90.6, 98.0], [90.7, 100.0], [90.8, 100.0], [90.9, 101.0], [91.0, 101.0], [91.1, 102.0], [91.2, 102.0], [91.3, 102.0], [91.4, 102.0], [91.5, 102.0], [91.6, 102.0], [91.7, 103.0], [91.8, 103.0], [91.9, 104.0], [92.0, 104.0], [92.1, 105.0], [92.2, 105.0], [92.3, 114.0], [92.4, 114.0], [92.5, 114.0], [92.6, 114.0], [92.7, 114.0], [92.8, 114.0], [92.9, 116.0], [93.0, 116.0], [93.1, 119.0], [93.2, 119.0], [93.3, 120.0], [93.4, 120.0], [93.5, 123.0], [93.6, 123.0], [93.7, 125.0], [93.8, 125.0], [93.9, 137.0], [94.0, 137.0], [94.1, 138.0], [94.2, 138.0], [94.3, 139.0], [94.4, 139.0], [94.5, 147.0], [94.6, 147.0], [94.7, 151.0], [94.8, 151.0], [94.9, 162.0], [95.0, 162.0], [95.1, 166.0], [95.2, 166.0], [95.3, 170.0], [95.4, 170.0], [95.5, 171.0], [95.6, 171.0], [95.7, 179.0], [95.8, 179.0], [95.9, 179.0], [96.0, 179.0], [96.1, 191.0], [96.2, 191.0], [96.3, 198.0], [96.4, 198.0], [96.5, 202.0], [96.6, 202.0], [96.7, 211.0], [96.8, 211.0], [96.9, 219.0], [97.0, 219.0], [97.1, 261.0], [97.2, 261.0], [97.3, 261.0], [97.4, 261.0], [97.5, 269.0], [97.6, 269.0], [97.7, 290.0], [97.8, 290.0], [97.9, 359.0], [98.0, 359.0], [98.1, 443.0], [98.2, 443.0], [98.3, 498.0], [98.4, 498.0], [98.5, 507.0], [98.6, 507.0], [98.7, 515.0], [98.8, 515.0], [98.9, 517.0], [99.0, 517.0], [99.1, 520.0], [99.2, 520.0], [99.3, 536.0], [99.4, 536.0], [99.5, 539.0], [99.6, 539.0], [99.7, 552.0], [99.8, 552.0], [99.9, 574.0], [100.0, 574.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[0.0, 27.0], [0.1, 27.0], [0.2, 27.0], [0.3, 27.0], [0.4, 28.0], [0.5, 28.0], [0.6, 29.0], [0.7, 29.0], [0.8, 29.0], [0.9, 29.0], [1.0, 29.0], [1.1, 29.0], [1.2, 30.0], [1.3, 30.0], [1.4, 30.0], [1.5, 30.0], [1.6, 30.0], [1.7, 30.0], [1.8, 30.0], [1.9, 30.0], [2.0, 30.0], [2.1, 30.0], [2.2, 30.0], [2.3, 30.0], [2.4, 30.0], [2.5, 30.0], [2.6, 30.0], [2.7, 30.0], [2.8, 31.0], [2.9, 31.0], [3.0, 31.0], [3.1, 31.0], [3.2, 31.0], [3.3, 31.0], [3.4, 31.0], [3.5, 31.0], [3.6, 31.0], [3.7, 31.0], [3.8, 31.0], [3.9, 31.0], [4.0, 31.0], [4.1, 31.0], [4.2, 31.0], [4.3, 31.0], [4.4, 31.0], [4.5, 31.0], [4.6, 32.0], [4.7, 32.0], [4.8, 32.0], [4.9, 32.0], [5.0, 32.0], [5.1, 32.0], [5.2, 32.0], [5.3, 32.0], [5.4, 32.0], [5.5, 32.0], [5.6, 32.0], [5.7, 32.0], [5.8, 32.0], [5.9, 32.0], [6.0, 32.0], [6.1, 32.0], [6.2, 33.0], [6.3, 33.0], [6.4, 33.0], [6.5, 33.0], [6.6, 33.0], [6.7, 33.0], [6.8, 33.0], [6.9, 33.0], [7.0, 33.0], [7.1, 33.0], [7.2, 33.0], [7.3, 33.0], [7.4, 33.0], [7.5, 33.0], [7.6, 33.0], [7.7, 33.0], [7.8, 33.0], [7.9, 33.0], [8.0, 33.0], [8.1, 33.0], [8.2, 33.0], [8.3, 33.0], [8.4, 33.0], [8.5, 33.0], [8.6, 33.0], [8.7, 33.0], [8.8, 33.0], [8.9, 33.0], [9.0, 33.0], [9.1, 33.0], [9.2, 34.0], [9.3, 34.0], [9.4, 34.0], [9.5, 34.0], [9.6, 34.0], [9.7, 34.0], [9.8, 34.0], [9.9, 34.0], [10.0, 34.0], [10.1, 34.0], [10.2, 34.0], [10.3, 34.0], [10.4, 34.0], [10.5, 34.0], [10.6, 34.0], [10.7, 34.0], [10.8, 34.0], [10.9, 34.0], [11.0, 34.0], [11.1, 34.0], [11.2, 34.0], [11.3, 34.0], [11.4, 34.0], [11.5, 34.0], [11.6, 34.0], [11.7, 34.0], [11.8, 34.0], [11.9, 34.0], [12.0, 34.0], [12.1, 34.0], [12.2, 34.0], [12.3, 34.0], [12.4, 34.0], [12.5, 34.0], [12.6, 34.0], [12.7, 34.0], [12.8, 34.0], [12.9, 34.0], [13.0, 34.0], [13.1, 34.0], [13.2, 35.0], [13.3, 35.0], [13.4, 35.0], [13.5, 35.0], [13.6, 35.0], [13.7, 35.0], [13.8, 35.0], [13.9, 35.0], [14.0, 35.0], [14.1, 35.0], [14.2, 35.0], [14.3, 35.0], [14.4, 35.0], [14.5, 35.0], [14.6, 35.0], [14.7, 35.0], [14.8, 35.0], [14.9, 35.0], [15.0, 35.0], [15.1, 35.0], [15.2, 35.0], [15.3, 35.0], [15.4, 35.0], [15.5, 35.0], [15.6, 35.0], [15.7, 35.0], [15.8, 35.0], [15.9, 35.0], [16.0, 35.0], [16.1, 35.0], [16.2, 35.0], [16.3, 35.0], [16.4, 35.0], [16.5, 35.0], [16.6, 35.0], [16.7, 35.0], [16.8, 36.0], [16.9, 36.0], [17.0, 36.0], [17.1, 36.0], [17.2, 36.0], [17.3, 36.0], [17.4, 36.0], [17.5, 36.0], [17.6, 36.0], [17.7, 36.0], [17.8, 36.0], [17.9, 36.0], [18.0, 36.0], [18.1, 36.0], [18.2, 36.0], [18.3, 36.0], [18.4, 36.0], [18.5, 36.0], [18.6, 36.0], [18.7, 36.0], [18.8, 36.0], [18.9, 36.0], [19.0, 36.0], [19.1, 36.0], [19.2, 36.0], [19.3, 36.0], [19.4, 36.0], [19.5, 36.0], [19.6, 36.0], [19.7, 36.0], [19.8, 36.0], [19.9, 36.0], [20.0, 36.0], [20.1, 37.0], [20.2, 37.0], [20.3, 37.0], [20.4, 37.0], [20.5, 37.0], [20.6, 37.0], [20.7, 37.0], [20.8, 37.0], [20.9, 37.0], [21.0, 37.0], [21.1, 37.0], [21.2, 37.0], [21.3, 37.0], [21.4, 37.0], [21.5, 37.0], [21.6, 37.0], [21.7, 37.0], [21.8, 37.0], [21.9, 37.0], [22.0, 37.0], [22.1, 37.0], [22.2, 37.0], [22.3, 37.0], [22.4, 37.0], [22.5, 37.0], [22.6, 37.0], [22.7, 37.0], [22.8, 37.0], [22.9, 37.0], [23.0, 37.0], [23.1, 37.0], [23.2, 37.0], [23.3, 37.0], [23.4, 38.0], [23.5, 38.0], [23.6, 38.0], [23.7, 38.0], [23.8, 38.0], [23.9, 38.0], [24.0, 38.0], [24.1, 38.0], [24.2, 38.0], [24.3, 38.0], [24.4, 38.0], [24.5, 38.0], [24.6, 38.0], [24.7, 38.0], [24.8, 38.0], [24.9, 38.0], [25.0, 38.0], [25.1, 38.0], [25.2, 38.0], [25.3, 38.0], [25.4, 38.0], [25.5, 38.0], [25.6, 38.0], [25.7, 38.0], [25.8, 38.0], [25.9, 38.0], [26.0, 38.0], [26.1, 38.0], [26.2, 38.0], [26.3, 38.0], [26.4, 38.0], [26.5, 38.0], [26.6, 38.0], [26.7, 38.0], [26.8, 38.0], [26.9, 38.0], [27.0, 38.0], [27.1, 38.0], [27.2, 38.0], [27.3, 38.0], [27.4, 38.0], [27.5, 38.0], [27.6, 39.0], [27.7, 39.0], [27.8, 39.0], [27.9, 39.0], [28.0, 39.0], [28.1, 39.0], [28.2, 39.0], [28.3, 39.0], [28.4, 39.0], [28.5, 39.0], [28.6, 39.0], [28.7, 39.0], [28.8, 39.0], [28.9, 39.0], [29.0, 39.0], [29.1, 39.0], [29.2, 39.0], [29.3, 39.0], [29.4, 39.0], [29.5, 39.0], [29.6, 39.0], [29.7, 39.0], [29.8, 40.0], [29.9, 40.0], [30.0, 40.0], [30.1, 40.0], [30.2, 40.0], [30.3, 40.0], [30.4, 40.0], [30.5, 40.0], [30.6, 40.0], [30.7, 40.0], [30.8, 40.0], [30.9, 40.0], [31.0, 40.0], [31.1, 40.0], [31.2, 40.0], [31.3, 40.0], [31.4, 40.0], [31.5, 40.0], [31.6, 40.0], [31.7, 40.0], [31.8, 40.0], [31.9, 40.0], [32.0, 40.0], [32.1, 40.0], [32.2, 40.0], [32.3, 40.0], [32.4, 40.0], [32.5, 40.0], [32.6, 40.0], [32.7, 40.0], [32.8, 40.0], [32.9, 40.0], [33.0, 40.0], [33.1, 40.0], [33.2, 40.0], [33.3, 41.0], [33.4, 41.0], [33.5, 41.0], [33.6, 41.0], [33.7, 41.0], [33.8, 41.0], [33.9, 41.0], [34.0, 41.0], [34.1, 41.0], [34.2, 41.0], [34.3, 41.0], [34.4, 41.0], [34.5, 41.0], [34.6, 41.0], [34.7, 41.0], [34.8, 41.0], [34.9, 41.0], [35.0, 41.0], [35.1, 41.0], [35.2, 41.0], [35.3, 41.0], [35.4, 41.0], [35.5, 41.0], [35.6, 41.0], [35.7, 41.0], [35.8, 41.0], [35.9, 41.0], [36.0, 41.0], [36.1, 41.0], [36.2, 41.0], [36.3, 41.0], [36.4, 41.0], [36.5, 41.0], [36.6, 41.0], [36.7, 41.0], [36.8, 42.0], [36.9, 42.0], [37.0, 42.0], [37.1, 42.0], [37.2, 42.0], [37.3, 42.0], [37.4, 42.0], [37.5, 42.0], [37.6, 42.0], [37.7, 42.0], [37.8, 42.0], [37.9, 42.0], [38.0, 42.0], [38.1, 42.0], [38.2, 42.0], [38.3, 42.0], [38.4, 42.0], [38.5, 42.0], [38.6, 42.0], [38.7, 42.0], [38.8, 42.0], [38.9, 42.0], [39.0, 42.0], [39.1, 42.0], [39.2, 42.0], [39.3, 42.0], [39.4, 42.0], [39.5, 42.0], [39.6, 42.0], [39.7, 42.0], [39.8, 42.0], [39.9, 42.0], [40.0, 42.0], [40.1, 42.0], [40.2, 42.0], [40.3, 43.0], [40.4, 43.0], [40.5, 43.0], [40.6, 43.0], [40.7, 43.0], [40.8, 43.0], [40.9, 43.0], [41.0, 43.0], [41.1, 43.0], [41.2, 43.0], [41.3, 43.0], [41.4, 43.0], [41.5, 43.0], [41.6, 43.0], [41.7, 43.0], [41.8, 43.0], [41.9, 43.0], [42.0, 43.0], [42.1, 43.0], [42.2, 43.0], [42.3, 43.0], [42.4, 43.0], [42.5, 43.0], [42.6, 43.0], [42.7, 43.0], [42.8, 43.0], [42.9, 43.0], [43.0, 43.0], [43.1, 43.0], [43.2, 43.0], [43.3, 43.0], [43.4, 43.0], [43.5, 43.0], [43.6, 44.0], [43.7, 44.0], [43.8, 44.0], [43.9, 44.0], [44.0, 44.0], [44.1, 44.0], [44.2, 44.0], [44.3, 44.0], [44.4, 44.0], [44.5, 44.0], [44.6, 44.0], [44.7, 44.0], [44.8, 44.0], [44.9, 44.0], [45.0, 44.0], [45.1, 44.0], [45.2, 44.0], [45.3, 44.0], [45.4, 44.0], [45.5, 44.0], [45.6, 44.0], [45.7, 44.0], [45.8, 44.0], [45.9, 44.0], [46.0, 44.0], [46.1, 44.0], [46.2, 44.0], [46.3, 45.0], [46.4, 45.0], [46.5, 45.0], [46.6, 45.0], [46.7, 45.0], [46.8, 45.0], [46.9, 45.0], [47.0, 45.0], [47.1, 45.0], [47.2, 45.0], [47.3, 45.0], [47.4, 45.0], [47.5, 45.0], [47.6, 45.0], [47.7, 45.0], [47.8, 45.0], [47.9, 45.0], [48.0, 45.0], [48.1, 45.0], [48.2, 45.0], [48.3, 45.0], [48.4, 45.0], [48.5, 45.0], [48.6, 45.0], [48.7, 45.0], [48.8, 45.0], [48.9, 45.0], [49.0, 45.0], [49.1, 45.0], [49.2, 45.0], [49.3, 45.0], [49.4, 45.0], [49.5, 46.0], [49.6, 46.0], [49.7, 46.0], [49.8, 46.0], [49.9, 46.0], [50.0, 46.0], [50.1, 46.0], [50.2, 46.0], [50.3, 46.0], [50.4, 46.0], [50.5, 46.0], [50.6, 46.0], [50.7, 46.0], [50.8, 46.0], [50.9, 46.0], [51.0, 46.0], [51.1, 46.0], [51.2, 46.0], [51.3, 46.0], [51.4, 46.0], [51.5, 46.0], [51.6, 46.0], [51.7, 46.0], [51.8, 46.0], [51.9, 47.0], [52.0, 47.0], [52.1, 47.0], [52.2, 47.0], [52.3, 47.0], [52.4, 47.0], [52.5, 47.0], [52.6, 47.0], [52.7, 47.0], [52.8, 47.0], [52.9, 47.0], [53.0, 48.0], [53.1, 48.0], [53.2, 48.0], [53.3, 48.0], [53.4, 48.0], [53.5, 48.0], [53.6, 48.0], [53.7, 48.0], [53.8, 48.0], [53.9, 48.0], [54.0, 48.0], [54.1, 48.0], [54.2, 48.0], [54.3, 48.0], [54.4, 48.0], [54.5, 49.0], [54.6, 49.0], [54.7, 49.0], [54.8, 49.0], [54.9, 49.0], [55.0, 49.0], [55.1, 49.0], [55.2, 49.0], [55.3, 49.0], [55.4, 49.0], [55.5, 49.0], [55.6, 49.0], [55.7, 49.0], [55.8, 49.0], [55.9, 49.0], [56.0, 50.0], [56.1, 50.0], [56.2, 50.0], [56.3, 50.0], [56.4, 50.0], [56.5, 50.0], [56.6, 50.0], [56.7, 50.0], [56.8, 50.0], [56.9, 50.0], [57.0, 50.0], [57.1, 50.0], [57.2, 51.0], [57.3, 51.0], [57.4, 51.0], [57.5, 51.0], [57.6, 51.0], [57.7, 51.0], [57.8, 51.0], [57.9, 51.0], [58.0, 51.0], [58.1, 51.0], [58.2, 51.0], [58.3, 51.0], [58.4, 52.0], [58.5, 52.0], [58.6, 52.0], [58.7, 52.0], [58.8, 52.0], [58.9, 52.0], [59.0, 52.0], [59.1, 52.0], [59.2, 52.0], [59.3, 53.0], [59.4, 53.0], [59.5, 53.0], [59.6, 53.0], [59.7, 53.0], [59.8, 53.0], [59.9, 53.0], [60.0, 53.0], [60.1, 53.0], [60.2, 53.0], [60.3, 53.0], [60.4, 53.0], [60.5, 53.0], [60.6, 54.0], [60.7, 54.0], [60.8, 54.0], [60.9, 54.0], [61.0, 54.0], [61.1, 54.0], [61.2, 54.0], [61.3, 54.0], [61.4, 54.0], [61.5, 54.0], [61.6, 54.0], [61.7, 54.0], [61.8, 54.0], [61.9, 55.0], [62.0, 55.0], [62.1, 55.0], [62.2, 55.0], [62.3, 55.0], [62.4, 55.0], [62.5, 55.0], [62.6, 55.0], [62.7, 55.0], [62.8, 55.0], [62.9, 55.0], [63.0, 55.0], [63.1, 55.0], [63.2, 55.0], [63.3, 55.0], [63.4, 55.0], [63.5, 55.0], [63.6, 56.0], [63.7, 56.0], [63.8, 56.0], [63.9, 56.0], [64.0, 56.0], [64.1, 56.0], [64.2, 56.0], [64.3, 56.0], [64.4, 57.0], [64.5, 57.0], [64.6, 57.0], [64.7, 57.0], [64.8, 57.0], [64.9, 57.0], [65.0, 57.0], [65.1, 57.0], [65.2, 57.0], [65.3, 57.0], [65.4, 57.0], [65.5, 57.0], [65.6, 57.0], [65.7, 57.0], [65.8, 58.0], [65.9, 58.0], [66.0, 58.0], [66.1, 58.0], [66.2, 58.0], [66.3, 58.0], [66.4, 58.0], [66.5, 58.0], [66.6, 58.0], [66.7, 58.0], [66.8, 59.0], [66.9, 59.0], [67.0, 59.0], [67.1, 59.0], [67.2, 59.0], [67.3, 59.0], [67.4, 59.0], [67.5, 59.0], [67.6, 60.0], [67.7, 60.0], [67.8, 60.0], [67.9, 60.0], [68.0, 60.0], [68.1, 60.0], [68.2, 60.0], [68.3, 60.0], [68.4, 60.0], [68.5, 61.0], [68.6, 61.0], [68.7, 61.0], [68.8, 61.0], [68.9, 61.0], [69.0, 61.0], [69.1, 61.0], [69.2, 61.0], [69.3, 61.0], [69.4, 61.0], [69.5, 61.0], [69.6, 62.0], [69.7, 62.0], [69.8, 62.0], [69.9, 62.0], [70.0, 62.0], [70.1, 62.0], [70.2, 62.0], [70.3, 62.0], [70.4, 62.0], [70.5, 62.0], [70.6, 63.0], [70.7, 63.0], [70.8, 63.0], [70.9, 63.0], [71.0, 63.0], [71.1, 63.0], [71.2, 63.0], [71.3, 63.0], [71.4, 63.0], [71.5, 63.0], [71.6, 63.0], [71.7, 63.0], [71.8, 64.0], [71.9, 64.0], [72.0, 64.0], [72.1, 64.0], [72.2, 64.0], [72.3, 64.0], [72.4, 64.0], [72.5, 64.0], [72.6, 64.0], [72.7, 64.0], [72.8, 65.0], [72.9, 65.0], [73.0, 65.0], [73.1, 65.0], [73.2, 65.0], [73.3, 65.0], [73.4, 65.0], [73.5, 65.0], [73.6, 66.0], [73.7, 66.0], [73.8, 66.0], [73.9, 66.0], [74.0, 66.0], [74.1, 66.0], [74.2, 66.0], [74.3, 66.0], [74.4, 67.0], [74.5, 67.0], [74.6, 67.0], [74.7, 67.0], [74.8, 67.0], [74.9, 67.0], [75.0, 67.0], [75.1, 67.0], [75.2, 67.0], [75.3, 67.0], [75.4, 67.0], [75.5, 67.0], [75.6, 67.0], [75.7, 67.0], [75.8, 67.0], [75.9, 68.0], [76.0, 68.0], [76.1, 68.0], [76.2, 68.0], [76.3, 68.0], [76.4, 68.0], [76.5, 68.0], [76.6, 68.0], [76.7, 68.0], [76.8, 69.0], [76.9, 69.0], [77.0, 69.0], [77.1, 69.0], [77.2, 69.0], [77.3, 69.0], [77.4, 70.0], [77.5, 70.0], [77.6, 70.0], [77.7, 70.0], [77.8, 70.0], [77.9, 70.0], [78.0, 70.0], [78.1, 71.0], [78.2, 71.0], [78.3, 71.0], [78.4, 71.0], [78.5, 71.0], [78.6, 71.0], [78.7, 71.0], [78.8, 72.0], [78.9, 72.0], [79.0, 72.0], [79.1, 72.0], [79.2, 72.0], [79.3, 72.0], [79.4, 72.0], [79.5, 72.0], [79.6, 73.0], [79.7, 73.0], [79.8, 73.0], [79.9, 73.0], [80.0, 73.0], [80.1, 73.0], [80.2, 74.0], [80.3, 74.0], [80.4, 74.0], [80.5, 74.0], [80.6, 74.0], [80.7, 74.0], [80.8, 75.0], [80.9, 75.0], [81.0, 75.0], [81.1, 75.0], [81.2, 75.0], [81.3, 75.0], [81.4, 76.0], [81.5, 76.0], [81.6, 76.0], [81.7, 76.0], [81.8, 76.0], [81.9, 76.0], [82.0, 76.0], [82.1, 77.0], [82.2, 77.0], [82.3, 77.0], [82.4, 77.0], [82.5, 77.0], [82.6, 77.0], [82.7, 78.0], [82.8, 78.0], [82.9, 78.0], [83.0, 78.0], [83.1, 79.0], [83.2, 79.0], [83.3, 79.0], [83.4, 79.0], [83.5, 79.0], [83.6, 80.0], [83.7, 80.0], [83.8, 80.0], [83.9, 80.0], [84.0, 80.0], [84.1, 80.0], [84.2, 81.0], [84.3, 81.0], [84.4, 81.0], [84.5, 81.0], [84.6, 82.0], [84.7, 82.0], [84.8, 82.0], [84.9, 82.0], [85.0, 82.0], [85.1, 82.0], [85.2, 82.0], [85.3, 83.0], [85.4, 83.0], [85.5, 83.0], [85.6, 83.0], [85.7, 83.0], [85.8, 84.0], [85.9, 84.0], [86.0, 85.0], [86.1, 85.0], [86.2, 85.0], [86.3, 85.0], [86.4, 85.0], [86.5, 85.0], [86.6, 85.0], [86.7, 86.0], [86.8, 86.0], [86.9, 86.0], [87.0, 87.0], [87.1, 87.0], [87.2, 87.0], [87.3, 87.0], [87.4, 87.0], [87.5, 88.0], [87.6, 88.0], [87.7, 88.0], [87.8, 88.0], [87.9, 88.0], [88.0, 88.0], [88.1, 88.0], [88.2, 89.0], [88.3, 89.0], [88.4, 89.0], [88.5, 89.0], [88.6, 90.0], [88.7, 90.0], [88.8, 90.0], [88.9, 91.0], [89.0, 91.0], [89.1, 91.0], [89.2, 91.0], [89.3, 91.0], [89.4, 92.0], [89.5, 92.0], [89.6, 92.0], [89.7, 93.0], [89.8, 93.0], [89.9, 93.0], [90.0, 93.0], [90.1, 94.0], [90.2, 94.0], [90.3, 95.0], [90.4, 95.0], [90.5, 96.0], [90.6, 96.0], [90.7, 97.0], [90.8, 97.0], [90.9, 97.0], [91.0, 98.0], [91.1, 98.0], [91.2, 98.0], [91.3, 98.0], [91.4, 99.0], [91.5, 100.0], [91.6, 100.0], [91.7, 102.0], [91.8, 102.0], [91.9, 103.0], [92.0, 103.0], [92.1, 103.0], [92.2, 104.0], [92.3, 104.0], [92.4, 105.0], [92.5, 105.0], [92.6, 106.0], [92.7, 106.0], [92.8, 106.0], [92.9, 108.0], [93.0, 108.0], [93.1, 109.0], [93.2, 109.0], [93.3, 110.0], [93.4, 111.0], [93.5, 111.0], [93.6, 112.0], [93.7, 113.0], [93.8, 113.0], [93.9, 113.0], [94.0, 114.0], [94.1, 114.0], [94.2, 114.0], [94.3, 114.0], [94.4, 115.0], [94.5, 115.0], [94.6, 116.0], [94.7, 117.0], [94.8, 118.0], [94.9, 119.0], [95.0, 119.0], [95.1, 123.0], [95.2, 123.0], [95.3, 123.0], [95.4, 124.0], [95.5, 124.0], [95.6, 125.0], [95.7, 126.0], [95.8, 126.0], [95.9, 126.0], [96.0, 126.0], [96.1, 128.0], [96.2, 128.0], [96.3, 130.0], [96.4, 131.0], [96.5, 132.0], [96.6, 132.0], [96.7, 135.0], [96.8, 136.0], [96.9, 137.0], [97.0, 137.0], [97.1, 138.0], [97.2, 139.0], [97.3, 144.0], [97.4, 147.0], [97.5, 149.0], [97.6, 152.0], [97.7, 153.0], [97.8, 154.0], [97.9, 160.0], [98.0, 161.0], [98.1, 161.0], [98.2, 161.0], [98.3, 171.0], [98.4, 171.0], [98.5, 180.0], [98.6, 181.0], [98.7, 187.0], [98.8, 193.0], [98.9, 195.0], [99.0, 195.0], [99.1, 199.0], [99.2, 202.0], [99.3, 204.0], [99.4, 210.0], [99.5, 210.0], [99.6, 211.0], [99.7, 224.0], [99.8, 266.0], [99.9, 269.0]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[0.0, 3.0], [0.1, 3.0], [0.2, 3.0], [0.3, 3.0], [0.4, 3.0], [0.5, 3.0], [0.6, 3.0], [0.7, 3.0], [0.8, 3.0], [0.9, 3.0], [1.0, 3.0], [1.1, 3.0], [1.2, 4.0], [1.3, 4.0], [1.4, 4.0], [1.5, 4.0], [1.6, 4.0], [1.7, 4.0], [1.8, 4.0], [1.9, 4.0], [2.0, 4.0], [2.1, 4.0], [2.2, 4.0], [2.3, 4.0], [2.4, 4.0], [2.5, 4.0], [2.6, 4.0], [2.7, 4.0], [2.8, 4.0], [2.9, 4.0], [3.0, 4.0], [3.1, 4.0], [3.2, 4.0], [3.3, 4.0], [3.4, 4.0], [3.5, 4.0], [3.6, 4.0], [3.7, 4.0], [3.8, 4.0], [3.9, 4.0], [4.0, 4.0], [4.1, 4.0], [4.2, 4.0], [4.3, 4.0], [4.4, 4.0], [4.5, 4.0], [4.6, 4.0], [4.7, 4.0], [4.8, 4.0], [4.9, 4.0], [5.0, 4.0], [5.1, 4.0], [5.2, 4.0], [5.3, 4.0], [5.4, 4.0], [5.5, 4.0], [5.6, 4.0], [5.7, 4.0], [5.8, 4.0], [5.9, 4.0], [6.0, 4.0], [6.1, 4.0], [6.2, 4.0], [6.3, 4.0], [6.4, 4.0], [6.5, 4.0], [6.6, 4.0], [6.7, 4.0], [6.8, 4.0], [6.9, 4.0], [7.0, 4.0], [7.1, 4.0], [7.2, 4.0], [7.3, 4.0], [7.4, 4.0], [7.5, 4.0], [7.6, 4.0], [7.7, 4.0], [7.8, 4.0], [7.9, 4.0], [8.0, 4.0], [8.1, 4.0], [8.2, 4.0], [8.3, 4.0], [8.4, 4.0], [8.5, 4.0], [8.6, 4.0], [8.7, 4.0], [8.8, 4.0], [8.9, 4.0], [9.0, 4.0], [9.1, 4.0], [9.2, 4.0], [9.3, 4.0], [9.4, 4.0], [9.5, 4.0], [9.6, 4.0], [9.7, 4.0], [9.8, 4.0], [9.9, 4.0], [10.0, 4.0], [10.1, 4.0], [10.2, 4.0], [10.3, 4.0], [10.4, 4.0], [10.5, 4.0], [10.6, 4.0], [10.7, 4.0], [10.8, 4.0], [10.9, 4.0], [11.0, 4.0], [11.1, 4.0], [11.2, 4.0], [11.3, 4.0], [11.4, 4.0], [11.5, 4.0], [11.6, 4.0], [11.7, 4.0], [11.8, 4.0], [11.9, 4.0], [12.0, 4.0], [12.1, 4.0], [12.2, 4.0], [12.3, 4.0], [12.4, 4.0], [12.5, 4.0], [12.6, 4.0], [12.7, 4.0], [12.8, 4.0], [12.9, 4.0], [13.0, 4.0], [13.1, 4.0], [13.2, 4.0], [13.3, 4.0], [13.4, 4.0], [13.5, 4.0], [13.6, 4.0], [13.7, 4.0], [13.8, 4.0], [13.9, 4.0], [14.0, 4.0], [14.1, 4.0], [14.2, 4.0], [14.3, 4.0], [14.4, 4.0], [14.5, 4.0], [14.6, 4.0], [14.7, 4.0], [14.8, 4.0], [14.9, 4.0], [15.0, 4.0], [15.1, 4.0], [15.2, 4.0], [15.3, 4.0], [15.4, 4.0], [15.5, 4.0], [15.6, 4.0], [15.7, 4.0], [15.8, 4.0], [15.9, 4.0], [16.0, 4.0], [16.1, 4.0], [16.2, 4.0], [16.3, 4.0], [16.4, 4.0], [16.5, 4.0], [16.6, 4.0], [16.7, 4.0], [16.8, 4.0], [16.9, 4.0], [17.0, 4.0], [17.1, 4.0], [17.2, 4.0], [17.3, 4.0], [17.4, 4.0], [17.5, 4.0], [17.6, 4.0], [17.7, 4.0], [17.8, 4.0], [17.9, 4.0], [18.0, 4.0], [18.1, 4.0], [18.2, 4.0], [18.3, 4.0], [18.4, 4.0], [18.5, 4.0], [18.6, 4.0], [18.7, 4.0], [18.8, 4.0], [18.9, 4.0], [19.0, 4.0], [19.1, 4.0], [19.2, 4.0], [19.3, 4.0], [19.4, 4.0], [19.5, 4.0], [19.6, 4.0], [19.7, 4.0], [19.8, 4.0], [19.9, 4.0], [20.0, 4.0], [20.1, 4.0], [20.2, 4.0], [20.3, 4.0], [20.4, 4.0], [20.5, 4.0], [20.6, 4.0], [20.7, 4.0], [20.8, 4.0], [20.9, 4.0], [21.0, 4.0], [21.1, 4.0], [21.2, 4.0], [21.3, 4.0], [21.4, 4.0], [21.5, 4.0], [21.6, 4.0], [21.7, 4.0], [21.8, 4.0], [21.9, 4.0], [22.0, 4.0], [22.1, 4.0], [22.2, 4.0], [22.3, 4.0], [22.4, 4.0], [22.5, 4.0], [22.6, 4.0], [22.7, 4.0], [22.8, 4.0], [22.9, 4.0], [23.0, 4.0], [23.1, 4.0], [23.2, 4.0], [23.3, 4.0], [23.4, 4.0], [23.5, 4.0], [23.6, 4.0], [23.7, 4.0], [23.8, 4.0], [23.9, 4.0], [24.0, 4.0], [24.1, 4.0], [24.2, 4.0], [24.3, 4.0], [24.4, 4.0], [24.5, 4.0], [24.6, 4.0], [24.7, 4.0], [24.8, 4.0], [24.9, 4.0], [25.0, 4.0], [25.1, 4.0], [25.2, 4.0], [25.3, 4.0], [25.4, 4.0], [25.5, 4.0], [25.6, 4.0], [25.7, 4.0], [25.8, 4.0], [25.9, 4.0], [26.0, 4.0], [26.1, 4.0], [26.2, 4.0], [26.3, 4.0], [26.4, 4.0], [26.5, 4.0], [26.6, 4.0], [26.7, 4.0], [26.8, 4.0], [26.9, 4.0], [27.0, 4.0], [27.1, 4.0], [27.2, 4.0], [27.3, 4.0], [27.4, 4.0], [27.5, 4.0], [27.6, 4.0], [27.7, 4.0], [27.8, 4.0], [27.9, 4.0], [28.0, 4.0], [28.1, 4.0], [28.2, 4.0], [28.3, 4.0], [28.4, 4.0], [28.5, 4.0], [28.6, 4.0], [28.7, 4.0], [28.8, 4.0], [28.9, 4.0], [29.0, 4.0], [29.1, 4.0], [29.2, 4.0], [29.3, 4.0], [29.4, 4.0], [29.5, 4.0], [29.6, 4.0], [29.7, 4.0], [29.8, 4.0], [29.9, 4.0], [30.0, 4.0], [30.1, 4.0], [30.2, 4.0], [30.3, 4.0], [30.4, 4.0], [30.5, 4.0], [30.6, 5.0], [30.7, 5.0], [30.8, 5.0], [30.9, 5.0], [31.0, 5.0], [31.1, 5.0], [31.2, 5.0], [31.3, 5.0], [31.4, 5.0], [31.5, 5.0], [31.6, 5.0], [31.7, 5.0], [31.8, 5.0], [31.9, 5.0], [32.0, 5.0], [32.1, 5.0], [32.2, 5.0], [32.3, 5.0], [32.4, 5.0], [32.5, 5.0], [32.6, 5.0], [32.7, 5.0], [32.8, 5.0], [32.9, 5.0], [33.0, 5.0], [33.1, 5.0], [33.2, 5.0], [33.3, 5.0], [33.4, 5.0], [33.5, 5.0], [33.6, 5.0], [33.7, 5.0], [33.8, 5.0], [33.9, 5.0], [34.0, 5.0], [34.1, 5.0], [34.2, 5.0], [34.3, 5.0], [34.4, 5.0], [34.5, 5.0], [34.6, 5.0], [34.7, 5.0], [34.8, 5.0], [34.9, 5.0], [35.0, 5.0], [35.1, 5.0], [35.2, 5.0], [35.3, 5.0], [35.4, 5.0], [35.5, 5.0], [35.6, 5.0], [35.7, 5.0], [35.8, 5.0], [35.9, 5.0], [36.0, 5.0], [36.1, 5.0], [36.2, 5.0], [36.3, 5.0], [36.4, 5.0], [36.5, 5.0], [36.6, 5.0], [36.7, 5.0], [36.8, 5.0], [36.9, 5.0], [37.0, 5.0], [37.1, 5.0], [37.2, 5.0], [37.3, 5.0], [37.4, 5.0], [37.5, 5.0], [37.6, 5.0], [37.7, 5.0], [37.8, 5.0], [37.9, 5.0], [38.0, 5.0], [38.1, 5.0], [38.2, 5.0], [38.3, 5.0], [38.4, 5.0], [38.5, 5.0], [38.6, 5.0], [38.7, 5.0], [38.8, 5.0], [38.9, 5.0], [39.0, 5.0], [39.1, 5.0], [39.2, 5.0], [39.3, 5.0], [39.4, 5.0], [39.5, 5.0], [39.6, 5.0], [39.7, 5.0], [39.8, 5.0], [39.9, 5.0], [40.0, 5.0], [40.1, 5.0], [40.2, 5.0], [40.3, 5.0], [40.4, 5.0], [40.5, 5.0], [40.6, 5.0], [40.7, 5.0], [40.8, 5.0], [40.9, 5.0], [41.0, 5.0], [41.1, 5.0], [41.2, 5.0], [41.3, 5.0], [41.4, 5.0], [41.5, 5.0], [41.6, 5.0], [41.7, 5.0], [41.8, 5.0], [41.9, 5.0], [42.0, 5.0], [42.1, 5.0], [42.2, 5.0], [42.3, 5.0], [42.4, 5.0], [42.5, 5.0], [42.6, 5.0], [42.7, 5.0], [42.8, 5.0], [42.9, 5.0], [43.0, 5.0], [43.1, 5.0], [43.2, 5.0], [43.3, 5.0], [43.4, 5.0], [43.5, 5.0], [43.6, 5.0], [43.7, 5.0], [43.8, 5.0], [43.9, 5.0], [44.0, 5.0], [44.1, 5.0], [44.2, 5.0], [44.3, 5.0], [44.4, 5.0], [44.5, 5.0], [44.6, 5.0], [44.7, 5.0], [44.8, 5.0], [44.9, 5.0], [45.0, 5.0], [45.1, 5.0], [45.2, 5.0], [45.3, 5.0], [45.4, 5.0], [45.5, 5.0], [45.6, 5.0], [45.7, 5.0], [45.8, 5.0], [45.9, 5.0], [46.0, 5.0], [46.1, 5.0], [46.2, 5.0], [46.3, 5.0], [46.4, 5.0], [46.5, 5.0], [46.6, 5.0], [46.7, 5.0], [46.8, 5.0], [46.9, 5.0], [47.0, 5.0], [47.1, 5.0], [47.2, 5.0], [47.3, 5.0], [47.4, 5.0], [47.5, 5.0], [47.6, 5.0], [47.7, 5.0], [47.8, 5.0], [47.9, 5.0], [48.0, 5.0], [48.1, 5.0], [48.2, 5.0], [48.3, 5.0], [48.4, 5.0], [48.5, 5.0], [48.6, 5.0], [48.7, 5.0], [48.8, 5.0], [48.9, 5.0], [49.0, 5.0], [49.1, 5.0], [49.2, 5.0], [49.3, 5.0], [49.4, 5.0], [49.5, 5.0], [49.6, 5.0], [49.7, 5.0], [49.8, 5.0], [49.9, 5.0], [50.0, 5.0], [50.1, 5.0], [50.2, 5.0], [50.3, 5.0], [50.4, 5.0], [50.5, 5.0], [50.6, 5.0], [50.7, 5.0], [50.8, 5.0], [50.9, 5.0], [51.0, 5.0], [51.1, 5.0], [51.2, 5.0], [51.3, 5.0], [51.4, 5.0], [51.5, 5.0], [51.6, 5.0], [51.7, 5.0], [51.8, 5.0], [51.9, 5.0], [52.0, 5.0], [52.1, 5.0], [52.2, 5.0], [52.3, 5.0], [52.4, 5.0], [52.5, 5.0], [52.6, 5.0], [52.7, 5.0], [52.8, 5.0], [52.9, 5.0], [53.0, 5.0], [53.1, 5.0], [53.2, 5.0], [53.3, 5.0], [53.4, 5.0], [53.5, 5.0], [53.6, 5.0], [53.7, 5.0], [53.8, 5.0], [53.9, 5.0], [54.0, 5.0], [54.1, 5.0], [54.2, 5.0], [54.3, 5.0], [54.4, 5.0], [54.5, 5.0], [54.6, 5.0], [54.7, 5.0], [54.8, 5.0], [54.9, 5.0], [55.0, 5.0], [55.1, 5.0], [55.2, 5.0], [55.3, 5.0], [55.4, 5.0], [55.5, 5.0], [55.6, 5.0], [55.7, 5.0], [55.8, 5.0], [55.9, 5.0], [56.0, 5.0], [56.1, 5.0], [56.2, 5.0], [56.3, 5.0], [56.4, 5.0], [56.5, 5.0], [56.6, 5.0], [56.7, 5.0], [56.8, 5.0], [56.9, 5.0], [57.0, 5.0], [57.1, 5.0], [57.2, 5.0], [57.3, 5.0], [57.4, 5.0], [57.5, 5.0], [57.6, 5.0], [57.7, 5.0], [57.8, 5.0], [57.9, 5.0], [58.0, 5.0], [58.1, 5.0], [58.2, 5.0], [58.3, 5.0], [58.4, 5.0], [58.5, 5.0], [58.6, 5.0], [58.7, 5.0], [58.8, 5.0], [58.9, 5.0], [59.0, 5.0], [59.1, 5.0], [59.2, 5.0], [59.3, 5.0], [59.4, 5.0], [59.5, 5.0], [59.6, 5.0], [59.7, 5.0], [59.8, 5.0], [59.9, 5.0], [60.0, 5.0], [60.1, 5.0], [60.2, 5.0], [60.3, 5.0], [60.4, 5.0], [60.5, 5.0], [60.6, 5.0], [60.7, 5.0], [60.8, 5.0], [60.9, 5.0], [61.0, 5.0], [61.1, 5.0], [61.2, 5.0], [61.3, 5.0], [61.4, 5.0], [61.5, 5.0], [61.6, 5.0], [61.7, 5.0], [61.8, 5.0], [61.9, 5.0], [62.0, 5.0], [62.1, 5.0], [62.2, 5.0], [62.3, 5.0], [62.4, 5.0], [62.5, 5.0], [62.6, 5.0], [62.7, 5.0], [62.8, 5.0], [62.9, 5.0], [63.0, 5.0], [63.1, 5.0], [63.2, 5.0], [63.3, 5.0], [63.4, 5.0], [63.5, 5.0], [63.6, 5.0], [63.7, 5.0], [63.8, 5.0], [63.9, 5.0], [64.0, 5.0], [64.1, 5.0], [64.2, 5.0], [64.3, 5.0], [64.4, 5.0], [64.5, 5.0], [64.6, 5.0], [64.7, 5.0], [64.8, 5.0], [64.9, 5.0], [65.0, 5.0], [65.1, 5.0], [65.2, 5.0], [65.3, 5.0], [65.4, 5.0], [65.5, 5.0], [65.6, 5.0], [65.7, 5.0], [65.8, 5.0], [65.9, 5.0], [66.0, 5.0], [66.1, 5.0], [66.2, 5.0], [66.3, 5.0], [66.4, 5.0], [66.5, 5.0], [66.6, 5.0], [66.7, 5.0], [66.8, 5.0], [66.9, 5.0], [67.0, 5.0], [67.1, 5.0], [67.2, 5.0], [67.3, 5.0], [67.4, 5.0], [67.5, 5.0], [67.6, 5.0], [67.7, 5.0], [67.8, 5.0], [67.9, 5.0], [68.0, 5.0], [68.1, 5.0], [68.2, 5.0], [68.3, 5.0], [68.4, 5.0], [68.5, 5.0], [68.6, 5.0], [68.7, 5.0], [68.8, 5.0], [68.9, 5.0], [69.0, 5.0], [69.1, 5.0], [69.2, 5.0], [69.3, 5.0], [69.4, 5.0], [69.5, 5.0], [69.6, 5.0], [69.7, 5.0], [69.8, 5.0], [69.9, 5.0], [70.0, 5.0], [70.1, 5.0], [70.2, 5.0], [70.3, 5.0], [70.4, 5.0], [70.5, 5.0], [70.6, 5.0], [70.7, 5.0], [70.8, 5.0], [70.9, 5.0], [71.0, 5.0], [71.1, 5.0], [71.2, 5.0], [71.3, 5.0], [71.4, 5.0], [71.5, 5.0], [71.6, 5.0], [71.7, 5.0], [71.8, 5.0], [71.9, 5.0], [72.0, 5.0], [72.1, 5.0], [72.2, 5.0], [72.3, 5.0], [72.4, 5.0], [72.5, 5.0], [72.6, 5.0], [72.7, 5.0], [72.8, 5.0], [72.9, 5.0], [73.0, 5.0], [73.1, 5.0], [73.2, 5.0], [73.3, 5.0], [73.4, 5.0], [73.5, 5.0], [73.6, 5.0], [73.7, 5.0], [73.8, 5.0], [73.9, 5.0], [74.0, 5.0], [74.1, 5.0], [74.2, 5.0], [74.3, 5.0], [74.4, 5.0], [74.5, 5.0], [74.6, 5.0], [74.7, 5.0], [74.8, 5.0], [74.9, 5.0], [75.0, 5.0], [75.1, 5.0], [75.2, 5.0], [75.3, 5.0], [75.4, 5.0], [75.5, 5.0], [75.6, 5.0], [75.7, 5.0], [75.8, 5.0], [75.9, 5.0], [76.0, 5.0], [76.1, 5.0], [76.2, 5.0], [76.3, 5.0], [76.4, 5.0], [76.5, 5.0], [76.6, 5.0], [76.7, 5.0], [76.8, 5.0], [76.9, 5.0], [77.0, 5.0], [77.1, 5.0], [77.2, 5.0], [77.3, 5.0], [77.4, 5.0], [77.5, 5.0], [77.6, 6.0], [77.7, 6.0], [77.8, 6.0], [77.9, 6.0], [78.0, 6.0], [78.1, 6.0], [78.2, 6.0], [78.3, 6.0], [78.4, 6.0], [78.5, 6.0], [78.6, 6.0], [78.7, 6.0], [78.8, 6.0], [78.9, 6.0], [79.0, 6.0], [79.1, 6.0], [79.2, 6.0], [79.3, 6.0], [79.4, 6.0], [79.5, 6.0], [79.6, 6.0], [79.7, 6.0], [79.8, 6.0], [79.9, 6.0], [80.0, 6.0], [80.1, 6.0], [80.2, 6.0], [80.3, 6.0], [80.4, 6.0], [80.5, 6.0], [80.6, 6.0], [80.7, 6.0], [80.8, 6.0], [80.9, 6.0], [81.0, 6.0], [81.1, 6.0], [81.2, 6.0], [81.3, 6.0], [81.4, 6.0], [81.5, 6.0], [81.6, 6.0], [81.7, 6.0], [81.8, 6.0], [81.9, 6.0], [82.0, 6.0], [82.1, 6.0], [82.2, 6.0], [82.3, 6.0], [82.4, 6.0], [82.5, 6.0], [82.6, 6.0], [82.7, 6.0], [82.8, 6.0], [82.9, 6.0], [83.0, 6.0], [83.1, 6.0], [83.2, 6.0], [83.3, 6.0], [83.4, 6.0], [83.5, 6.0], [83.6, 6.0], [83.7, 6.0], [83.8, 6.0], [83.9, 6.0], [84.0, 6.0], [84.1, 6.0], [84.2, 6.0], [84.3, 6.0], [84.4, 6.0], [84.5, 6.0], [84.6, 6.0], [84.7, 6.0], [84.8, 6.0], [84.9, 6.0], [85.0, 6.0], [85.1, 6.0], [85.2, 6.0], [85.3, 6.0], [85.4, 6.0], [85.5, 6.0], [85.6, 6.0], [85.7, 6.0], [85.8, 6.0], [85.9, 6.0], [86.0, 6.0], [86.1, 6.0], [86.2, 6.0], [86.3, 6.0], [86.4, 6.0], [86.5, 6.0], [86.6, 6.0], [86.7, 6.0], [86.8, 7.0], [86.9, 7.0], [87.0, 7.0], [87.1, 7.0], [87.2, 7.0], [87.3, 7.0], [87.4, 7.0], [87.5, 7.0], [87.6, 7.0], [87.7, 7.0], [87.8, 7.0], [87.9, 7.0], [88.0, 7.0], [88.1, 7.0], [88.2, 7.0], [88.3, 7.0], [88.4, 7.0], [88.5, 7.0], [88.6, 7.0], [88.7, 7.0], [88.8, 7.0], [88.9, 7.0], [89.0, 7.0], [89.1, 7.0], [89.2, 7.0], [89.3, 7.0], [89.4, 7.0], [89.5, 7.0], [89.6, 8.0], [89.7, 8.0], [89.8, 8.0], [89.9, 8.0], [90.0, 8.0], [90.1, 8.0], [90.2, 8.0], [90.3, 8.0], [90.4, 8.0], [90.5, 8.0], [90.6, 8.0], [90.7, 8.0], [90.8, 8.0], [90.9, 8.0], [91.0, 8.0], [91.1, 8.0], [91.2, 8.0], [91.3, 8.0], [91.4, 9.0], [91.5, 9.0], [91.6, 9.0], [91.7, 9.0], [91.8, 9.0], [91.9, 9.0], [92.0, 9.0], [92.1, 9.0], [92.2, 9.0], [92.3, 9.0], [92.4, 9.0], [92.5, 9.0], [92.6, 9.0], [92.7, 9.0], [92.8, 9.0], [92.9, 9.0], [93.0, 9.0], [93.1, 9.0], [93.2, 9.0], [93.3, 9.0], [93.4, 9.0], [93.5, 9.0], [93.6, 9.0], [93.7, 9.0], [93.8, 10.0], [93.9, 10.0], [94.0, 10.0], [94.1, 10.0], [94.2, 10.0], [94.3, 10.0], [94.4, 10.0], [94.5, 10.0], [94.6, 10.0], [94.7, 10.0], [94.8, 10.0], [94.9, 10.0], [95.0, 10.0], [95.1, 10.0], [95.2, 10.0], [95.3, 10.0], [95.4, 11.0], [95.5, 11.0], [95.6, 11.0], [95.7, 11.0], [95.8, 11.0], [95.9, 11.0], [96.0, 11.0], [96.1, 11.0], [96.2, 11.0], [96.3, 11.0], [96.4, 11.0], [96.5, 11.0], [96.6, 13.0], [96.7, 13.0], [96.8, 13.0], [96.9, 13.0], [97.0, 14.0], [97.1, 14.0], [97.2, 14.0], [97.3, 14.0], [97.4, 14.0], [97.5, 14.0], [97.6, 16.0], [97.7, 16.0], [97.8, 16.0], [97.9, 16.0], [98.0, 17.0], [98.1, 17.0], [98.2, 17.0], [98.3, 17.0], [98.4, 17.0], [98.5, 17.0], [98.6, 17.0], [98.7, 17.0], [98.8, 18.0], [98.9, 18.0], [99.0, 19.0], [99.1, 19.0], [99.2, 22.0], [99.3, 22.0], [99.4, 23.0], [99.5, 23.0], [99.6, 23.0], [99.7, 29.0], [99.8, 29.0], [99.9, 51.0], [100.0, 51.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[0.0, 10.0], [0.1, 10.0], [0.2, 12.0], [0.3, 12.0], [0.4, 12.0], [0.5, 12.0], [0.6, 12.0], [0.7, 12.0], [0.8, 12.0], [0.9, 12.0], [1.0, 12.0], [1.1, 12.0], [1.2, 12.0], [1.3, 12.0], [1.4, 12.0], [1.5, 12.0], [1.6, 12.0], [1.7, 12.0], [1.8, 12.0], [1.9, 12.0], [2.0, 12.0], [2.1, 12.0], [2.2, 12.0], [2.3, 12.0], [2.4, 12.0], [2.5, 12.0], [2.6, 12.0], [2.7, 12.0], [2.8, 12.0], [2.9, 12.0], [3.0, 12.0], [3.1, 12.0], [3.2, 12.0], [3.3, 12.0], [3.4, 12.0], [3.5, 12.0], [3.6, 12.0], [3.7, 12.0], [3.8, 12.0], [3.9, 12.0], [4.0, 12.0], [4.1, 12.0], [4.2, 12.0], [4.3, 12.0], [4.4, 13.0], [4.5, 13.0], [4.6, 13.0], [4.7, 13.0], [4.8, 13.0], [4.9, 13.0], [5.0, 13.0], [5.1, 13.0], [5.2, 13.0], [5.3, 13.0], [5.4, 13.0], [5.5, 13.0], [5.6, 13.0], [5.7, 13.0], [5.8, 13.0], [5.9, 13.0], [6.0, 13.0], [6.1, 13.0], [6.2, 13.0], [6.3, 13.0], [6.4, 13.0], [6.5, 13.0], [6.6, 13.0], [6.7, 13.0], [6.8, 13.0], [6.9, 13.0], [7.0, 13.0], [7.1, 13.0], [7.2, 13.0], [7.3, 13.0], [7.4, 13.0], [7.5, 13.0], [7.6, 13.0], [7.7, 13.0], [7.8, 13.0], [7.9, 13.0], [8.0, 13.0], [8.1, 13.0], [8.2, 13.0], [8.3, 13.0], [8.4, 13.0], [8.5, 13.0], [8.6, 13.0], [8.7, 13.0], [8.8, 13.0], [8.9, 13.0], [9.0, 13.0], [9.1, 13.0], [9.2, 13.0], [9.3, 13.0], [9.4, 13.0], [9.5, 13.0], [9.6, 13.0], [9.7, 13.0], [9.8, 13.0], [9.9, 13.0], [10.0, 13.0], [10.1, 13.0], [10.2, 13.0], [10.3, 13.0], [10.4, 13.0], [10.5, 13.0], [10.6, 13.0], [10.7, 13.0], [10.8, 13.0], [10.9, 13.0], [11.0, 13.0], [11.1, 13.0], [11.2, 13.0], [11.3, 13.0], [11.4, 13.0], [11.5, 13.0], [11.6, 13.0], [11.7, 13.0], [11.8, 13.0], [11.9, 13.0], [12.0, 13.0], [12.1, 13.0], [12.2, 13.0], [12.3, 13.0], [12.4, 13.0], [12.5, 13.0], [12.6, 13.0], [12.7, 13.0], [12.8, 13.0], [12.9, 13.0], [13.0, 13.0], [13.1, 13.0], [13.2, 13.0], [13.3, 13.0], [13.4, 13.0], [13.5, 13.0], [13.6, 13.0], [13.7, 13.0], [13.8, 13.0], [13.9, 13.0], [14.0, 13.0], [14.1, 13.0], [14.2, 13.0], [14.3, 13.0], [14.4, 13.0], [14.5, 13.0], [14.6, 13.0], [14.7, 13.0], [14.8, 13.0], [14.9, 13.0], [15.0, 13.0], [15.1, 13.0], [15.2, 13.0], [15.3, 13.0], [15.4, 13.0], [15.5, 13.0], [15.6, 13.0], [15.7, 13.0], [15.8, 13.0], [15.9, 13.0], [16.0, 13.0], [16.1, 13.0], [16.2, 13.0], [16.3, 13.0], [16.4, 13.0], [16.5, 13.0], [16.6, 13.0], [16.7, 13.0], [16.8, 13.0], [16.9, 13.0], [17.0, 13.0], [17.1, 13.0], [17.2, 13.0], [17.3, 13.0], [17.4, 13.0], [17.5, 13.0], [17.6, 13.0], [17.7, 13.0], [17.8, 13.0], [17.9, 13.0], [18.0, 13.0], [18.1, 13.0], [18.2, 13.0], [18.3, 13.0], [18.4, 13.0], [18.5, 13.0], [18.6, 13.0], [18.7, 13.0], [18.8, 13.0], [18.9, 13.0], [19.0, 13.0], [19.1, 13.0], [19.2, 13.0], [19.3, 13.0], [19.4, 13.0], [19.5, 13.0], [19.6, 13.0], [19.7, 13.0], [19.8, 13.0], [19.9, 13.0], [20.0, 13.0], [20.1, 13.0], [20.2, 13.0], [20.3, 13.0], [20.4, 13.0], [20.5, 13.0], [20.6, 13.0], [20.7, 13.0], [20.8, 13.0], [20.9, 13.0], [21.0, 13.0], [21.1, 13.0], [21.2, 13.0], [21.3, 13.0], [21.4, 13.0], [21.5, 13.0], [21.6, 13.0], [21.7, 13.0], [21.8, 13.0], [21.9, 13.0], [22.0, 14.0], [22.1, 14.0], [22.2, 14.0], [22.3, 14.0], [22.4, 14.0], [22.5, 14.0], [22.6, 14.0], [22.7, 14.0], [22.8, 14.0], [22.9, 14.0], [23.0, 14.0], [23.1, 14.0], [23.2, 14.0], [23.3, 14.0], [23.4, 14.0], [23.5, 14.0], [23.6, 14.0], [23.7, 14.0], [23.8, 14.0], [23.9, 14.0], [24.0, 14.0], [24.1, 14.0], [24.2, 14.0], [24.3, 14.0], [24.4, 14.0], [24.5, 14.0], [24.6, 14.0], [24.7, 14.0], [24.8, 14.0], [24.9, 14.0], [25.0, 14.0], [25.1, 14.0], [25.2, 14.0], [25.3, 14.0], [25.4, 14.0], [25.5, 14.0], [25.6, 14.0], [25.7, 14.0], [25.8, 14.0], [25.9, 14.0], [26.0, 14.0], [26.1, 14.0], [26.2, 14.0], [26.3, 14.0], [26.4, 14.0], [26.5, 14.0], [26.6, 14.0], [26.7, 14.0], [26.8, 14.0], [26.9, 14.0], [27.0, 14.0], [27.1, 14.0], [27.2, 14.0], [27.3, 14.0], [27.4, 14.0], [27.5, 14.0], [27.6, 14.0], [27.7, 14.0], [27.8, 14.0], [27.9, 14.0], [28.0, 14.0], [28.1, 14.0], [28.2, 14.0], [28.3, 14.0], [28.4, 14.0], [28.5, 14.0], [28.6, 14.0], [28.7, 14.0], [28.8, 14.0], [28.9, 14.0], [29.0, 14.0], [29.1, 14.0], [29.2, 14.0], [29.3, 14.0], [29.4, 14.0], [29.5, 14.0], [29.6, 14.0], [29.7, 14.0], [29.8, 14.0], [29.9, 14.0], [30.0, 14.0], [30.1, 14.0], [30.2, 14.0], [30.3, 14.0], [30.4, 14.0], [30.5, 14.0], [30.6, 14.0], [30.7, 14.0], [30.8, 14.0], [30.9, 14.0], [31.0, 14.0], [31.1, 14.0], [31.2, 14.0], [31.3, 14.0], [31.4, 14.0], [31.5, 14.0], [31.6, 14.0], [31.7, 14.0], [31.8, 14.0], [31.9, 14.0], [32.0, 14.0], [32.1, 14.0], [32.2, 14.0], [32.3, 14.0], [32.4, 14.0], [32.5, 14.0], [32.6, 14.0], [32.7, 14.0], [32.8, 14.0], [32.9, 14.0], [33.0, 14.0], [33.1, 14.0], [33.2, 14.0], [33.3, 14.0], [33.4, 14.0], [33.5, 14.0], [33.6, 14.0], [33.7, 14.0], [33.8, 14.0], [33.9, 14.0], [34.0, 14.0], [34.1, 14.0], [34.2, 14.0], [34.3, 14.0], [34.4, 14.0], [34.5, 14.0], [34.6, 14.0], [34.7, 14.0], [34.8, 14.0], [34.9, 14.0], [35.0, 14.0], [35.1, 14.0], [35.2, 14.0], [35.3, 14.0], [35.4, 14.0], [35.5, 14.0], [35.6, 14.0], [35.7, 14.0], [35.8, 14.0], [35.9, 14.0], [36.0, 14.0], [36.1, 14.0], [36.2, 14.0], [36.3, 14.0], [36.4, 14.0], [36.5, 14.0], [36.6, 14.0], [36.7, 14.0], [36.8, 14.0], [36.9, 14.0], [37.0, 14.0], [37.1, 14.0], [37.2, 14.0], [37.3, 14.0], [37.4, 14.0], [37.5, 14.0], [37.6, 14.0], [37.7, 14.0], [37.8, 14.0], [37.9, 14.0], [38.0, 14.0], [38.1, 14.0], [38.2, 14.0], [38.3, 14.0], [38.4, 14.0], [38.5, 14.0], [38.6, 14.0], [38.7, 14.0], [38.8, 14.0], [38.9, 14.0], [39.0, 14.0], [39.1, 14.0], [39.2, 14.0], [39.3, 14.0], [39.4, 14.0], [39.5, 14.0], [39.6, 14.0], [39.7, 14.0], [39.8, 14.0], [39.9, 14.0], [40.0, 14.0], [40.1, 14.0], [40.2, 14.0], [40.3, 14.0], [40.4, 14.0], [40.5, 14.0], [40.6, 14.0], [40.7, 14.0], [40.8, 14.0], [40.9, 14.0], [41.0, 14.0], [41.1, 14.0], [41.2, 14.0], [41.3, 14.0], [41.4, 14.0], [41.5, 14.0], [41.6, 14.0], [41.7, 14.0], [41.8, 14.0], [41.9, 14.0], [42.0, 14.0], [42.1, 14.0], [42.2, 14.0], [42.3, 14.0], [42.4, 14.0], [42.5, 14.0], [42.6, 15.0], [42.7, 15.0], [42.8, 15.0], [42.9, 15.0], [43.0, 15.0], [43.1, 15.0], [43.2, 15.0], [43.3, 15.0], [43.4, 15.0], [43.5, 15.0], [43.6, 15.0], [43.7, 15.0], [43.8, 15.0], [43.9, 15.0], [44.0, 15.0], [44.1, 15.0], [44.2, 15.0], [44.3, 15.0], [44.4, 15.0], [44.5, 15.0], [44.6, 15.0], [44.7, 15.0], [44.8, 15.0], [44.9, 15.0], [45.0, 15.0], [45.1, 15.0], [45.2, 15.0], [45.3, 15.0], [45.4, 15.0], [45.5, 15.0], [45.6, 15.0], [45.7, 15.0], [45.8, 15.0], [45.9, 15.0], [46.0, 15.0], [46.1, 15.0], [46.2, 15.0], [46.3, 15.0], [46.4, 15.0], [46.5, 15.0], [46.6, 15.0], [46.7, 15.0], [46.8, 15.0], [46.9, 15.0], [47.0, 15.0], [47.1, 15.0], [47.2, 15.0], [47.3, 15.0], [47.4, 15.0], [47.5, 15.0], [47.6, 15.0], [47.7, 15.0], [47.8, 15.0], [47.9, 15.0], [48.0, 15.0], [48.1, 15.0], [48.2, 15.0], [48.3, 15.0], [48.4, 15.0], [48.5, 15.0], [48.6, 15.0], [48.7, 15.0], [48.8, 15.0], [48.9, 15.0], [49.0, 15.0], [49.1, 15.0], [49.2, 15.0], [49.3, 15.0], [49.4, 15.0], [49.5, 15.0], [49.6, 15.0], [49.7, 15.0], [49.8, 15.0], [49.9, 15.0], [50.0, 15.0], [50.1, 15.0], [50.2, 15.0], [50.3, 15.0], [50.4, 15.0], [50.5, 15.0], [50.6, 15.0], [50.7, 15.0], [50.8, 15.0], [50.9, 15.0], [51.0, 15.0], [51.1, 15.0], [51.2, 15.0], [51.3, 15.0], [51.4, 15.0], [51.5, 15.0], [51.6, 15.0], [51.7, 15.0], [51.8, 15.0], [51.9, 15.0], [52.0, 15.0], [52.1, 15.0], [52.2, 15.0], [52.3, 15.0], [52.4, 15.0], [52.5, 15.0], [52.6, 15.0], [52.7, 15.0], [52.8, 15.0], [52.9, 15.0], [53.0, 15.0], [53.1, 15.0], [53.2, 15.0], [53.3, 15.0], [53.4, 15.0], [53.5, 15.0], [53.6, 15.0], [53.7, 15.0], [53.8, 15.0], [53.9, 15.0], [54.0, 15.0], [54.1, 15.0], [54.2, 15.0], [54.3, 15.0], [54.4, 15.0], [54.5, 15.0], [54.6, 15.0], [54.7, 15.0], [54.8, 16.0], [54.9, 16.0], [55.0, 16.0], [55.1, 16.0], [55.2, 16.0], [55.3, 16.0], [55.4, 16.0], [55.5, 16.0], [55.6, 16.0], [55.7, 16.0], [55.8, 16.0], [55.9, 16.0], [56.0, 16.0], [56.1, 16.0], [56.2, 16.0], [56.3, 16.0], [56.4, 16.0], [56.5, 16.0], [56.6, 16.0], [56.7, 16.0], [56.8, 16.0], [56.9, 16.0], [57.0, 16.0], [57.1, 16.0], [57.2, 16.0], [57.3, 16.0], [57.4, 16.0], [57.5, 16.0], [57.6, 16.0], [57.7, 16.0], [57.8, 16.0], [57.9, 16.0], [58.0, 16.0], [58.1, 16.0], [58.2, 16.0], [58.3, 16.0], [58.4, 16.0], [58.5, 16.0], [58.6, 16.0], [58.7, 16.0], [58.8, 16.0], [58.9, 16.0], [59.0, 16.0], [59.1, 16.0], [59.2, 16.0], [59.3, 16.0], [59.4, 16.0], [59.5, 16.0], [59.6, 16.0], [59.7, 16.0], [59.8, 16.0], [59.9, 16.0], [60.0, 16.0], [60.1, 16.0], [60.2, 16.0], [60.3, 16.0], [60.4, 16.0], [60.5, 16.0], [60.6, 16.0], [60.7, 16.0], [60.8, 16.0], [60.9, 16.0], [61.0, 16.0], [61.1, 16.0], [61.2, 16.0], [61.3, 16.0], [61.4, 16.0], [61.5, 16.0], [61.6, 16.0], [61.7, 16.0], [61.8, 16.0], [61.9, 16.0], [62.0, 16.0], [62.1, 16.0], [62.2, 16.0], [62.3, 16.0], [62.4, 16.0], [62.5, 16.0], [62.6, 16.0], [62.7, 16.0], [62.8, 16.0], [62.9, 16.0], [63.0, 16.0], [63.1, 16.0], [63.2, 16.0], [63.3, 16.0], [63.4, 16.0], [63.5, 16.0], [63.6, 16.0], [63.7, 16.0], [63.8, 16.0], [63.9, 16.0], [64.0, 16.0], [64.1, 16.0], [64.2, 16.0], [64.3, 16.0], [64.4, 16.0], [64.5, 16.0], [64.6, 17.0], [64.7, 17.0], [64.8, 17.0], [64.9, 17.0], [65.0, 17.0], [65.1, 17.0], [65.2, 17.0], [65.3, 17.0], [65.4, 17.0], [65.5, 17.0], [65.6, 17.0], [65.7, 17.0], [65.8, 17.0], [65.9, 17.0], [66.0, 17.0], [66.1, 17.0], [66.2, 17.0], [66.3, 17.0], [66.4, 17.0], [66.5, 17.0], [66.6, 17.0], [66.7, 17.0], [66.8, 17.0], [66.9, 17.0], [67.0, 17.0], [67.1, 17.0], [67.2, 17.0], [67.3, 17.0], [67.4, 17.0], [67.5, 17.0], [67.6, 17.0], [67.7, 17.0], [67.8, 17.0], [67.9, 17.0], [68.0, 17.0], [68.1, 17.0], [68.2, 17.0], [68.3, 17.0], [68.4, 17.0], [68.5, 17.0], [68.6, 17.0], [68.7, 17.0], [68.8, 17.0], [68.9, 17.0], [69.0, 17.0], [69.1, 17.0], [69.2, 17.0], [69.3, 17.0], [69.4, 17.0], [69.5, 17.0], [69.6, 17.0], [69.7, 17.0], [69.8, 17.0], [69.9, 17.0], [70.0, 17.0], [70.1, 17.0], [70.2, 17.0], [70.3, 17.0], [70.4, 17.0], [70.5, 17.0], [70.6, 17.0], [70.7, 17.0], [70.8, 17.0], [70.9, 17.0], [71.0, 17.0], [71.1, 17.0], [71.2, 17.0], [71.3, 17.0], [71.4, 17.0], [71.5, 17.0], [71.6, 18.0], [71.7, 18.0], [71.8, 18.0], [71.9, 18.0], [72.0, 18.0], [72.1, 18.0], [72.2, 18.0], [72.3, 18.0], [72.4, 18.0], [72.5, 18.0], [72.6, 18.0], [72.7, 18.0], [72.8, 18.0], [72.9, 18.0], [73.0, 18.0], [73.1, 18.0], [73.2, 18.0], [73.3, 18.0], [73.4, 18.0], [73.5, 18.0], [73.6, 18.0], [73.7, 18.0], [73.8, 18.0], [73.9, 18.0], [74.0, 18.0], [74.1, 18.0], [74.2, 18.0], [74.3, 18.0], [74.4, 18.0], [74.5, 18.0], [74.6, 18.0], [74.7, 18.0], [74.8, 18.0], [74.9, 18.0], [75.0, 18.0], [75.1, 18.0], [75.2, 18.0], [75.3, 18.0], [75.4, 19.0], [75.5, 19.0], [75.6, 19.0], [75.7, 19.0], [75.8, 20.0], [75.9, 20.0], [76.0, 20.0], [76.1, 20.0], [76.2, 20.0], [76.3, 20.0], [76.4, 20.0], [76.5, 20.0], [76.6, 20.0], [76.7, 20.0], [76.8, 20.0], [76.9, 20.0], [77.0, 21.0], [77.1, 21.0], [77.2, 21.0], [77.3, 21.0], [77.4, 21.0], [77.5, 21.0], [77.6, 21.0], [77.7, 21.0], [77.8, 21.0], [77.9, 21.0], [78.0, 21.0], [78.1, 21.0], [78.2, 22.0], [78.3, 22.0], [78.4, 22.0], [78.5, 22.0], [78.6, 22.0], [78.7, 22.0], [78.8, 22.0], [78.9, 22.0], [79.0, 22.0], [79.1, 22.0], [79.2, 22.0], [79.3, 22.0], [79.4, 22.0], [79.5, 22.0], [79.6, 22.0], [79.7, 22.0], [79.8, 23.0], [79.9, 23.0], [80.0, 23.0], [80.1, 23.0], [80.2, 23.0], [80.3, 23.0], [80.4, 23.0], [80.5, 23.0], [80.6, 23.0], [80.7, 23.0], [80.8, 23.0], [80.9, 23.0], [81.0, 23.0], [81.1, 23.0], [81.2, 23.0], [81.3, 23.0], [81.4, 23.0], [81.5, 23.0], [81.6, 23.0], [81.7, 23.0], [81.8, 23.0], [81.9, 23.0], [82.0, 24.0], [82.1, 24.0], [82.2, 24.0], [82.3, 24.0], [82.4, 24.0], [82.5, 24.0], [82.6, 24.0], [82.7, 24.0], [82.8, 24.0], [82.9, 24.0], [83.0, 24.0], [83.1, 24.0], [83.2, 24.0], [83.3, 24.0], [83.4, 24.0], [83.5, 24.0], [83.6, 25.0], [83.7, 25.0], [83.8, 25.0], [83.9, 25.0], [84.0, 25.0], [84.1, 25.0], [84.2, 25.0], [84.3, 25.0], [84.4, 25.0], [84.5, 25.0], [84.6, 25.0], [84.7, 25.0], [84.8, 25.0], [84.9, 25.0], [85.0, 25.0], [85.1, 25.0], [85.2, 25.0], [85.3, 25.0], [85.4, 25.0], [85.5, 25.0], [85.6, 25.0], [85.7, 25.0], [85.8, 25.0], [85.9, 25.0], [86.0, 25.0], [86.1, 25.0], [86.2, 25.0], [86.3, 25.0], [86.4, 25.0], [86.5, 25.0], [86.6, 25.0], [86.7, 25.0], [86.8, 25.0], [86.9, 25.0], [87.0, 25.0], [87.1, 25.0], [87.2, 25.0], [87.3, 25.0], [87.4, 26.0], [87.5, 26.0], [87.6, 26.0], [87.7, 26.0], [87.8, 26.0], [87.9, 26.0], [88.0, 26.0], [88.1, 26.0], [88.2, 26.0], [88.3, 26.0], [88.4, 26.0], [88.5, 26.0], [88.6, 27.0], [88.7, 27.0], [88.8, 27.0], [88.9, 27.0], [89.0, 28.0], [89.1, 28.0], [89.2, 28.0], [89.3, 28.0], [89.4, 28.0], [89.5, 28.0], [89.6, 28.0], [89.7, 28.0], [89.8, 28.0], [89.9, 28.0], [90.0, 28.0], [90.1, 28.0], [90.2, 28.0], [90.3, 28.0], [90.4, 28.0], [90.5, 28.0], [90.6, 28.0], [90.7, 28.0], [90.8, 28.0], [90.9, 28.0], [91.0, 28.0], [91.1, 28.0], [91.2, 29.0], [91.3, 29.0], [91.4, 29.0], [91.5, 29.0], [91.6, 29.0], [91.7, 29.0], [91.8, 29.0], [91.9, 29.0], [92.0, 29.0], [92.1, 29.0], [92.2, 29.0], [92.3, 29.0], [92.4, 29.0], [92.5, 29.0], [92.6, 29.0], [92.7, 29.0], [92.8, 29.0], [92.9, 29.0], [93.0, 30.0], [93.1, 30.0], [93.2, 30.0], [93.3, 30.0], [93.4, 30.0], [93.5, 30.0], [93.6, 30.0], [93.7, 30.0], [93.8, 30.0], [93.9, 30.0], [94.0, 31.0], [94.1, 31.0], [94.2, 31.0], [94.3, 31.0], [94.4, 31.0], [94.5, 31.0], [94.6, 31.0], [94.7, 31.0], [94.8, 31.0], [94.9, 31.0], [95.0, 31.0], [95.1, 31.0], [95.2, 32.0], [95.3, 32.0], [95.4, 33.0], [95.5, 33.0], [95.6, 33.0], [95.7, 33.0], [95.8, 33.0], [95.9, 33.0], [96.0, 34.0], [96.1, 34.0], [96.2, 35.0], [96.3, 35.0], [96.4, 35.0], [96.5, 35.0], [96.6, 35.0], [96.7, 35.0], [96.8, 35.0], [96.9, 35.0], [97.0, 36.0], [97.1, 36.0], [97.2, 37.0], [97.3, 37.0], [97.4, 37.0], [97.5, 37.0], [97.6, 37.0], [97.7, 38.0], [97.8, 38.0], [97.9, 39.0], [98.0, 39.0], [98.1, 39.0], [98.2, 39.0], [98.3, 40.0], [98.4, 40.0], [98.5, 40.0], [98.6, 40.0], [98.7, 40.0], [98.8, 40.0], [98.9, 42.0], [99.0, 42.0], [99.1, 42.0], [99.2, 42.0], [99.3, 43.0], [99.4, 43.0], [99.5, 46.0], [99.6, 46.0], [99.7, 47.0], [99.8, 47.0], [99.9, 47.0], [100.0, 47.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[0.0, 2.0], [0.1, 2.0], [0.2, 2.0], [0.3, 2.0], [0.4, 2.0], [0.5, 2.0], [0.6, 2.0], [0.7, 2.0], [0.8, 2.0], [0.9, 2.0], [1.0, 2.0], [1.1, 2.0], [1.2, 2.0], [1.3, 2.0], [1.4, 2.0], [1.5, 2.0], [1.6, 2.0], [1.7, 2.0], [1.8, 2.0], [1.9, 2.0], [2.0, 2.0], [2.1, 2.0], [2.2, 2.0], [2.3, 2.0], [2.4, 2.0], [2.5, 2.0], [2.6, 2.0], [2.7, 2.0], [2.8, 2.0], [2.9, 2.0], [3.0, 2.0], [3.1, 2.0], [3.2, 2.0], [3.3, 2.0], [3.4, 2.0], [3.5, 2.0], [3.6, 2.0], [3.7, 2.0], [3.8, 2.0], [3.9, 2.0], [4.0, 2.0], [4.1, 2.0], [4.2, 2.0], [4.3, 2.0], [4.4, 2.0], [4.5, 2.0], [4.6, 2.0], [4.7, 2.0], [4.8, 3.0], [4.9, 3.0], [5.0, 3.0], [5.1, 3.0], [5.2, 3.0], [5.3, 3.0], [5.4, 3.0], [5.5, 3.0], [5.6, 3.0], [5.7, 3.0], [5.8, 3.0], [5.9, 3.0], [6.0, 3.0], [6.1, 3.0], [6.2, 3.0], [6.3, 3.0], [6.4, 3.0], [6.5, 3.0], [6.6, 3.0], [6.7, 3.0], [6.8, 3.0], [6.9, 3.0], [7.0, 3.0], [7.1, 3.0], [7.2, 3.0], [7.3, 3.0], [7.4, 3.0], [7.5, 3.0], [7.6, 3.0], [7.7, 3.0], [7.8, 3.0], [7.9, 3.0], [8.0, 3.0], [8.1, 3.0], [8.2, 3.0], [8.3, 3.0], [8.4, 3.0], [8.5, 3.0], [8.6, 3.0], [8.7, 3.0], [8.8, 3.0], [8.9, 3.0], [9.0, 3.0], [9.1, 3.0], [9.2, 3.0], [9.3, 3.0], [9.4, 3.0], [9.5, 3.0], [9.6, 3.0], [9.7, 3.0], [9.8, 3.0], [9.9, 3.0], [10.0, 3.0], [10.1, 3.0], [10.2, 3.0], [10.3, 3.0], [10.4, 3.0], [10.5, 3.0], [10.6, 3.0], [10.7, 3.0], [10.8, 3.0], [10.9, 3.0], [11.0, 3.0], [11.1, 3.0], [11.2, 3.0], [11.3, 3.0], [11.4, 3.0], [11.5, 3.0], [11.6, 3.0], [11.7, 3.0], [11.8, 3.0], [11.9, 3.0], [12.0, 3.0], [12.1, 3.0], [12.2, 3.0], [12.3, 3.0], [12.4, 3.0], [12.5, 3.0], [12.6, 3.0], [12.7, 3.0], [12.8, 3.0], [12.9, 3.0], [13.0, 3.0], [13.1, 3.0], [13.2, 3.0], [13.3, 3.0], [13.4, 3.0], [13.5, 3.0], [13.6, 3.0], [13.7, 3.0], [13.8, 3.0], [13.9, 3.0], [14.0, 3.0], [14.1, 3.0], [14.2, 3.0], [14.3, 3.0], [14.4, 3.0], [14.5, 3.0], [14.6, 3.0], [14.7, 3.0], [14.8, 3.0], [14.9, 3.0], [15.0, 3.0], [15.1, 3.0], [15.2, 3.0], [15.3, 3.0], [15.4, 3.0], [15.5, 3.0], [15.6, 3.0], [15.7, 3.0], [15.8, 3.0], [15.9, 3.0], [16.0, 3.0], [16.1, 3.0], [16.2, 3.0], [16.3, 3.0], [16.4, 3.0], [16.5, 3.0], [16.6, 3.0], [16.7, 3.0], [16.8, 3.0], [16.9, 3.0], [17.0, 3.0], [17.1, 3.0], [17.2, 3.0], [17.3, 3.0], [17.4, 3.0], [17.5, 3.0], [17.6, 3.0], [17.7, 3.0], [17.8, 3.0], [17.9, 3.0], [18.0, 3.0], [18.1, 3.0], [18.2, 3.0], [18.3, 3.0], [18.4, 3.0], [18.5, 3.0], [18.6, 3.0], [18.7, 3.0], [18.8, 3.0], [18.9, 3.0], [19.0, 3.0], [19.1, 3.0], [19.2, 3.0], [19.3, 3.0], [19.4, 3.0], [19.5, 3.0], [19.6, 3.0], [19.7, 3.0], [19.8, 3.0], [19.9, 3.0], [20.0, 3.0], [20.1, 3.0], [20.2, 3.0], [20.3, 3.0], [20.4, 3.0], [20.5, 3.0], [20.6, 3.0], [20.7, 3.0], [20.8, 3.0], [20.9, 3.0], [21.0, 3.0], [21.1, 3.0], [21.2, 3.0], [21.3, 3.0], [21.4, 3.0], [21.5, 3.0], [21.6, 3.0], [21.7, 3.0], [21.8, 3.0], [21.9, 3.0], [22.0, 3.0], [22.1, 3.0], [22.2, 3.0], [22.3, 3.0], [22.4, 3.0], [22.5, 3.0], [22.6, 3.0], [22.7, 3.0], [22.8, 3.0], [22.9, 3.0], [23.0, 3.0], [23.1, 3.0], [23.2, 3.0], [23.3, 3.0], [23.4, 3.0], [23.5, 3.0], [23.6, 3.0], [23.7, 3.0], [23.8, 3.0], [23.9, 3.0], [24.0, 3.0], [24.1, 3.0], [24.2, 3.0], [24.3, 3.0], [24.4, 3.0], [24.5, 3.0], [24.6, 3.0], [24.7, 3.0], [24.8, 3.0], [24.9, 3.0], [25.0, 3.0], [25.1, 3.0], [25.2, 3.0], [25.3, 3.0], [25.4, 3.0], [25.5, 3.0], [25.6, 3.0], [25.7, 3.0], [25.8, 3.0], [25.9, 3.0], [26.0, 3.0], [26.1, 3.0], [26.2, 3.0], [26.3, 3.0], [26.4, 3.0], [26.5, 3.0], [26.6, 3.0], [26.7, 3.0], [26.8, 3.0], [26.9, 3.0], [27.0, 3.0], [27.1, 3.0], [27.2, 3.0], [27.3, 3.0], [27.4, 3.0], [27.5, 3.0], [27.6, 3.0], [27.7, 3.0], [27.8, 3.0], [27.9, 3.0], [28.0, 3.0], [28.1, 3.0], [28.2, 3.0], [28.3, 3.0], [28.4, 3.0], [28.5, 3.0], [28.6, 3.0], [28.7, 3.0], [28.8, 3.0], [28.9, 3.0], [29.0, 3.0], [29.1, 3.0], [29.2, 3.0], [29.3, 3.0], [29.4, 3.0], [29.5, 3.0], [29.6, 3.0], [29.7, 3.0], [29.8, 3.0], [29.9, 3.0], [30.0, 3.0], [30.1, 3.0], [30.2, 3.0], [30.3, 3.0], [30.4, 3.0], [30.5, 3.0], [30.6, 3.0], [30.7, 3.0], [30.8, 3.0], [30.9, 3.0], [31.0, 3.0], [31.1, 3.0], [31.2, 3.0], [31.3, 3.0], [31.4, 3.0], [31.5, 3.0], [31.6, 3.0], [31.7, 3.0], [31.8, 3.0], [31.9, 3.0], [32.0, 3.0], [32.1, 3.0], [32.2, 3.0], [32.3, 3.0], [32.4, 3.0], [32.5, 3.0], [32.6, 3.0], [32.7, 3.0], [32.8, 3.0], [32.9, 3.0], [33.0, 3.0], [33.1, 3.0], [33.2, 3.0], [33.3, 3.0], [33.4, 3.0], [33.5, 3.0], [33.6, 3.0], [33.7, 3.0], [33.8, 3.0], [33.9, 3.0], [34.0, 3.0], [34.1, 3.0], [34.2, 3.0], [34.3, 3.0], [34.4, 3.0], [34.5, 3.0], [34.6, 3.0], [34.7, 3.0], [34.8, 3.0], [34.9, 3.0], [35.0, 3.0], [35.1, 3.0], [35.2, 3.0], [35.3, 3.0], [35.4, 3.0], [35.5, 3.0], [35.6, 3.0], [35.7, 3.0], [35.8, 3.0], [35.9, 3.0], [36.0, 3.0], [36.1, 3.0], [36.2, 3.0], [36.3, 3.0], [36.4, 3.0], [36.5, 3.0], [36.6, 3.0], [36.7, 3.0], [36.8, 3.0], [36.9, 3.0], [37.0, 3.0], [37.1, 3.0], [37.2, 3.0], [37.3, 3.0], [37.4, 3.0], [37.5, 3.0], [37.6, 3.0], [37.7, 3.0], [37.8, 3.0], [37.9, 3.0], [38.0, 3.0], [38.1, 3.0], [38.2, 3.0], [38.3, 3.0], [38.4, 3.0], [38.5, 3.0], [38.6, 3.0], [38.7, 3.0], [38.8, 3.0], [38.9, 3.0], [39.0, 3.0], [39.1, 3.0], [39.2, 3.0], [39.3, 3.0], [39.4, 3.0], [39.5, 3.0], [39.6, 3.0], [39.7, 3.0], [39.8, 3.0], [39.9, 3.0], [40.0, 3.0], [40.1, 3.0], [40.2, 3.0], [40.3, 3.0], [40.4, 3.0], [40.5, 3.0], [40.6, 3.0], [40.7, 3.0], [40.8, 3.0], [40.9, 3.0], [41.0, 3.0], [41.1, 3.0], [41.2, 3.0], [41.3, 3.0], [41.4, 3.0], [41.5, 3.0], [41.6, 3.0], [41.7, 3.0], [41.8, 3.0], [41.9, 3.0], [42.0, 3.0], [42.1, 3.0], [42.2, 3.0], [42.3, 3.0], [42.4, 3.0], [42.5, 3.0], [42.6, 3.0], [42.7, 3.0], [42.8, 3.0], [42.9, 3.0], [43.0, 3.0], [43.1, 3.0], [43.2, 3.0], [43.3, 3.0], [43.4, 3.0], [43.5, 3.0], [43.6, 3.0], [43.7, 3.0], [43.8, 3.0], [43.9, 3.0], [44.0, 3.0], [44.1, 3.0], [44.2, 3.0], [44.3, 3.0], [44.4, 3.0], [44.5, 3.0], [44.6, 3.0], [44.7, 3.0], [44.8, 3.0], [44.9, 3.0], [45.0, 3.0], [45.1, 3.0], [45.2, 3.0], [45.3, 3.0], [45.4, 3.0], [45.5, 3.0], [45.6, 3.0], [45.7, 3.0], [45.8, 3.0], [45.9, 3.0], [46.0, 3.0], [46.1, 3.0], [46.2, 3.0], [46.3, 3.0], [46.4, 3.0], [46.5, 3.0], [46.6, 3.0], [46.7, 3.0], [46.8, 3.0], [46.9, 3.0], [47.0, 3.0], [47.1, 3.0], [47.2, 3.0], [47.3, 3.0], [47.4, 3.0], [47.5, 3.0], [47.6, 3.0], [47.7, 3.0], [47.8, 3.0], [47.9, 3.0], [48.0, 3.0], [48.1, 3.0], [48.2, 3.0], [48.3, 3.0], [48.4, 3.0], [48.5, 3.0], [48.6, 3.0], [48.7, 3.0], [48.8, 3.0], [48.9, 3.0], [49.0, 3.0], [49.1, 3.0], [49.2, 3.0], [49.3, 3.0], [49.4, 3.0], [49.5, 3.0], [49.6, 3.0], [49.7, 3.0], [49.8, 3.0], [49.9, 3.0], [50.0, 3.0], [50.1, 3.0], [50.2, 3.0], [50.3, 3.0], [50.4, 3.0], [50.5, 3.0], [50.6, 3.0], [50.7, 3.0], [50.8, 3.0], [50.9, 3.0], [51.0, 3.0], [51.1, 3.0], [51.2, 3.0], [51.3, 3.0], [51.4, 3.0], [51.5, 3.0], [51.6, 3.0], [51.7, 3.0], [51.8, 3.0], [51.9, 3.0], [52.0, 3.0], [52.1, 3.0], [52.2, 3.0], [52.3, 3.0], [52.4, 3.0], [52.5, 3.0], [52.6, 3.0], [52.7, 3.0], [52.8, 3.0], [52.9, 3.0], [53.0, 3.0], [53.1, 3.0], [53.2, 3.0], [53.3, 3.0], [53.4, 3.0], [53.5, 3.0], [53.6, 3.0], [53.7, 3.0], [53.8, 3.0], [53.9, 3.0], [54.0, 3.0], [54.1, 3.0], [54.2, 3.0], [54.3, 3.0], [54.4, 3.0], [54.5, 3.0], [54.6, 3.0], [54.7, 3.0], [54.8, 3.0], [54.9, 3.0], [55.0, 3.0], [55.1, 3.0], [55.2, 3.0], [55.3, 3.0], [55.4, 3.0], [55.5, 3.0], [55.6, 3.0], [55.7, 3.0], [55.8, 3.0], [55.9, 3.0], [56.0, 3.0], [56.1, 3.0], [56.2, 3.0], [56.3, 3.0], [56.4, 3.0], [56.5, 3.0], [56.6, 3.0], [56.7, 3.0], [56.8, 3.0], [56.9, 3.0], [57.0, 3.0], [57.1, 3.0], [57.2, 3.0], [57.3, 3.0], [57.4, 3.0], [57.5, 3.0], [57.6, 3.0], [57.7, 3.0], [57.8, 3.0], [57.9, 3.0], [58.0, 3.0], [58.1, 3.0], [58.2, 3.0], [58.3, 3.0], [58.4, 3.0], [58.5, 3.0], [58.6, 3.0], [58.7, 3.0], [58.8, 3.0], [58.9, 3.0], [59.0, 3.0], [59.1, 3.0], [59.2, 3.0], [59.3, 3.0], [59.4, 3.0], [59.5, 3.0], [59.6, 3.0], [59.7, 3.0], [59.8, 3.0], [59.9, 3.0], [60.0, 3.0], [60.1, 3.0], [60.2, 3.0], [60.3, 3.0], [60.4, 3.0], [60.5, 3.0], [60.6, 4.0], [60.7, 4.0], [60.8, 4.0], [60.9, 4.0], [61.0, 4.0], [61.1, 4.0], [61.2, 4.0], [61.3, 4.0], [61.4, 4.0], [61.5, 4.0], [61.6, 4.0], [61.7, 4.0], [61.8, 4.0], [61.9, 4.0], [62.0, 4.0], [62.1, 4.0], [62.2, 4.0], [62.3, 4.0], [62.4, 4.0], [62.5, 4.0], [62.6, 4.0], [62.7, 4.0], [62.8, 4.0], [62.9, 4.0], [63.0, 4.0], [63.1, 4.0], [63.2, 4.0], [63.3, 4.0], [63.4, 4.0], [63.5, 4.0], [63.6, 4.0], [63.7, 4.0], [63.8, 4.0], [63.9, 4.0], [64.0, 4.0], [64.1, 4.0], [64.2, 4.0], [64.3, 4.0], [64.4, 4.0], [64.5, 4.0], [64.6, 4.0], [64.7, 4.0], [64.8, 4.0], [64.9, 4.0], [65.0, 4.0], [65.1, 4.0], [65.2, 4.0], [65.3, 4.0], [65.4, 4.0], [65.5, 4.0], [65.6, 4.0], [65.7, 4.0], [65.8, 4.0], [65.9, 4.0], [66.0, 4.0], [66.1, 4.0], [66.2, 4.0], [66.3, 4.0], [66.4, 4.0], [66.5, 4.0], [66.6, 4.0], [66.7, 4.0], [66.8, 4.0], [66.9, 4.0], [67.0, 4.0], [67.1, 4.0], [67.2, 4.0], [67.3, 4.0], [67.4, 4.0], [67.5, 4.0], [67.6, 4.0], [67.7, 4.0], [67.8, 4.0], [67.9, 4.0], [68.0, 4.0], [68.1, 4.0], [68.2, 4.0], [68.3, 4.0], [68.4, 4.0], [68.5, 4.0], [68.6, 4.0], [68.7, 4.0], [68.8, 4.0], [68.9, 4.0], [69.0, 4.0], [69.1, 4.0], [69.2, 4.0], [69.3, 4.0], [69.4, 4.0], [69.5, 4.0], [69.6, 4.0], [69.7, 4.0], [69.8, 4.0], [69.9, 4.0], [70.0, 4.0], [70.1, 4.0], [70.2, 4.0], [70.3, 4.0], [70.4, 4.0], [70.5, 4.0], [70.6, 4.0], [70.7, 4.0], [70.8, 4.0], [70.9, 4.0], [71.0, 4.0], [71.1, 4.0], [71.2, 4.0], [71.3, 4.0], [71.4, 4.0], [71.5, 4.0], [71.6, 4.0], [71.7, 4.0], [71.8, 4.0], [71.9, 4.0], [72.0, 4.0], [72.1, 4.0], [72.2, 4.0], [72.3, 4.0], [72.4, 4.0], [72.5, 4.0], [72.6, 4.0], [72.7, 4.0], [72.8, 5.0], [72.9, 5.0], [73.0, 5.0], [73.1, 5.0], [73.2, 5.0], [73.3, 5.0], [73.4, 5.0], [73.5, 5.0], [73.6, 5.0], [73.7, 5.0], [73.8, 5.0], [73.9, 5.0], [74.0, 5.0], [74.1, 5.0], [74.2, 5.0], [74.3, 5.0], [74.4, 5.0], [74.5, 5.0], [74.6, 5.0], [74.7, 5.0], [74.8, 5.0], [74.9, 5.0], [75.0, 5.0], [75.1, 5.0], [75.2, 5.0], [75.3, 5.0], [75.4, 5.0], [75.5, 5.0], [75.6, 5.0], [75.7, 5.0], [75.8, 5.0], [75.9, 5.0], [76.0, 5.0], [76.1, 5.0], [76.2, 5.0], [76.3, 5.0], [76.4, 5.0], [76.5, 5.0], [76.6, 5.0], [76.7, 5.0], [76.8, 5.0], [76.9, 5.0], [77.0, 5.0], [77.1, 5.0], [77.2, 5.0], [77.3, 5.0], [77.4, 5.0], [77.5, 5.0], [77.6, 5.0], [77.7, 5.0], [77.8, 5.0], [77.9, 5.0], [78.0, 5.0], [78.1, 5.0], [78.2, 5.0], [78.3, 5.0], [78.4, 5.0], [78.5, 5.0], [78.6, 5.0], [78.7, 5.0], [78.8, 5.0], [78.9, 5.0], [79.0, 5.0], [79.1, 5.0], [79.2, 5.0], [79.3, 5.0], [79.4, 5.0], [79.5, 5.0], [79.6, 5.0], [79.7, 5.0], [79.8, 5.0], [79.9, 5.0], [80.0, 5.0], [80.1, 5.0], [80.2, 5.0], [80.3, 5.0], [80.4, 5.0], [80.5, 5.0], [80.6, 5.0], [80.7, 5.0], [80.8, 5.0], [80.9, 5.0], [81.0, 5.0], [81.1, 5.0], [81.2, 5.0], [81.3, 5.0], [81.4, 5.0], [81.5, 5.0], [81.6, 5.0], [81.7, 5.0], [81.8, 5.0], [81.9, 5.0], [82.0, 5.0], [82.1, 5.0], [82.2, 5.0], [82.3, 5.0], [82.4, 5.0], [82.5, 5.0], [82.6, 5.0], [82.7, 5.0], [82.8, 5.0], [82.9, 5.0], [83.0, 6.0], [83.1, 6.0], [83.2, 6.0], [83.3, 6.0], [83.4, 6.0], [83.5, 6.0], [83.6, 6.0], [83.7, 6.0], [83.8, 6.0], [83.9, 6.0], [84.0, 6.0], [84.1, 6.0], [84.2, 6.0], [84.3, 6.0], [84.4, 6.0], [84.5, 6.0], [84.6, 6.0], [84.7, 6.0], [84.8, 6.0], [84.9, 6.0], [85.0, 6.0], [85.1, 6.0], [85.2, 6.0], [85.3, 6.0], [85.4, 6.0], [85.5, 6.0], [85.6, 6.0], [85.7, 6.0], [85.8, 6.0], [85.9, 6.0], [86.0, 6.0], [86.1, 6.0], [86.2, 6.0], [86.3, 6.0], [86.4, 6.0], [86.5, 6.0], [86.6, 6.0], [86.7, 6.0], [86.8, 6.0], [86.9, 6.0], [87.0, 6.0], [87.1, 6.0], [87.2, 6.0], [87.3, 6.0], [87.4, 7.0], [87.5, 7.0], [87.6, 7.0], [87.7, 7.0], [87.8, 7.0], [87.9, 7.0], [88.0, 7.0], [88.1, 7.0], [88.2, 7.0], [88.3, 7.0], [88.4, 7.0], [88.5, 7.0], [88.6, 7.0], [88.7, 7.0], [88.8, 7.0], [88.9, 7.0], [89.0, 7.0], [89.1, 7.0], [89.2, 7.0], [89.3, 7.0], [89.4, 7.0], [89.5, 7.0], [89.6, 7.0], [89.7, 8.0], [89.8, 8.0], [89.9, 8.0], [90.0, 8.0], [90.1, 8.0], [90.2, 8.0], [90.3, 8.0], [90.4, 8.0], [90.5, 8.0], [90.6, 8.0], [90.7, 8.0], [90.8, 8.0], [90.9, 8.0], [91.0, 8.0], [91.1, 8.0], [91.2, 8.0], [91.3, 8.0], [91.4, 8.0], [91.5, 8.0], [91.6, 8.0], [91.7, 8.0], [91.8, 8.0], [91.9, 8.0], [92.0, 8.0], [92.1, 8.0], [92.2, 8.0], [92.3, 8.0], [92.4, 8.0], [92.5, 8.0], [92.6, 8.0], [92.7, 8.0], [92.8, 8.0], [92.9, 8.0], [93.0, 8.0], [93.1, 9.0], [93.2, 9.0], [93.3, 9.0], [93.4, 9.0], [93.5, 9.0], [93.6, 9.0], [93.7, 9.0], [93.8, 9.0], [93.9, 9.0], [94.0, 9.0], [94.1, 9.0], [94.2, 9.0], [94.3, 9.0], [94.4, 9.0], [94.5, 10.0], [94.6, 10.0], [94.7, 10.0], [94.8, 10.0], [94.9, 10.0], [95.0, 10.0], [95.1, 10.0], [95.2, 10.0], [95.3, 10.0], [95.4, 10.0], [95.5, 11.0], [95.6, 11.0], [95.7, 11.0], [95.8, 11.0], [95.9, 12.0], [96.0, 12.0], [96.1, 12.0], [96.2, 12.0], [96.3, 13.0], [96.4, 13.0], [96.5, 13.0], [96.6, 13.0], [96.7, 13.0], [96.8, 13.0], [96.9, 14.0], [97.0, 14.0], [97.1, 14.0], [97.2, 14.0], [97.3, 15.0], [97.4, 15.0], [97.5, 17.0], [97.6, 17.0], [97.7, 18.0], [97.8, 18.0], [97.9, 21.0], [98.0, 21.0], [98.1, 23.0], [98.2, 23.0], [98.3, 27.0], [98.4, 27.0], [98.5, 29.0], [98.6, 29.0], [98.7, 33.0], [98.8, 33.0], [98.9, 34.0], [99.0, 34.0], [99.1, 36.0], [99.2, 36.0], [99.3, 36.0], [99.4, 36.0], [99.5, 37.0], [99.6, 37.0], [99.7, 39.0], [99.8, 39.0], [99.9, 42.0], [100.0, 42.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[0.0, 2.0], [0.1, 2.0], [0.2, 2.0], [0.3, 2.0], [0.4, 2.0], [0.5, 2.0], [0.6, 2.0], [0.7, 2.0], [0.8, 2.0], [0.9, 2.0], [1.0, 2.0], [1.1, 2.0], [1.2, 2.0], [1.3, 2.0], [1.4, 2.0], [1.5, 2.0], [1.6, 2.0], [1.7, 2.0], [1.8, 2.0], [1.9, 2.0], [2.0, 2.0], [2.1, 2.0], [2.2, 2.0], [2.3, 2.0], [2.4, 2.0], [2.5, 2.0], [2.6, 3.0], [2.7, 3.0], [2.8, 3.0], [2.9, 3.0], [3.0, 3.0], [3.1, 3.0], [3.2, 3.0], [3.3, 3.0], [3.4, 3.0], [3.5, 3.0], [3.6, 3.0], [3.7, 3.0], [3.8, 3.0], [3.9, 3.0], [4.0, 3.0], [4.1, 3.0], [4.2, 3.0], [4.3, 3.0], [4.4, 3.0], [4.5, 3.0], [4.6, 3.0], [4.7, 3.0], [4.8, 3.0], [4.9, 3.0], [5.0, 3.0], [5.1, 3.0], [5.2, 3.0], [5.3, 3.0], [5.4, 3.0], [5.5, 3.0], [5.6, 3.0], [5.7, 3.0], [5.8, 3.0], [5.9, 3.0], [6.0, 3.0], [6.1, 3.0], [6.2, 3.0], [6.3, 3.0], [6.4, 3.0], [6.5, 3.0], [6.6, 3.0], [6.7, 3.0], [6.8, 3.0], [6.9, 3.0], [7.0, 3.0], [7.1, 3.0], [7.2, 3.0], [7.3, 3.0], [7.4, 3.0], [7.5, 3.0], [7.6, 3.0], [7.7, 3.0], [7.8, 3.0], [7.9, 3.0], [8.0, 3.0], [8.1, 3.0], [8.2, 3.0], [8.3, 3.0], [8.4, 3.0], [8.5, 3.0], [8.6, 3.0], [8.7, 3.0], [8.8, 3.0], [8.9, 3.0], [9.0, 3.0], [9.1, 3.0], [9.2, 3.0], [9.3, 3.0], [9.4, 3.0], [9.5, 3.0], [9.6, 3.0], [9.7, 3.0], [9.8, 3.0], [9.9, 3.0], [10.0, 3.0], [10.1, 3.0], [10.2, 3.0], [10.3, 3.0], [10.4, 3.0], [10.5, 3.0], [10.6, 3.0], [10.7, 3.0], [10.8, 3.0], [10.9, 3.0], [11.0, 3.0], [11.1, 3.0], [11.2, 3.0], [11.3, 3.0], [11.4, 3.0], [11.5, 3.0], [11.6, 3.0], [11.7, 3.0], [11.8, 3.0], [11.9, 3.0], [12.0, 3.0], [12.1, 3.0], [12.2, 3.0], [12.3, 3.0], [12.4, 3.0], [12.5, 3.0], [12.6, 3.0], [12.7, 3.0], [12.8, 3.0], [12.9, 3.0], [13.0, 3.0], [13.1, 3.0], [13.2, 3.0], [13.3, 3.0], [13.4, 3.0], [13.5, 3.0], [13.6, 3.0], [13.7, 3.0], [13.8, 3.0], [13.9, 3.0], [14.0, 3.0], [14.1, 3.0], [14.2, 3.0], [14.3, 3.0], [14.4, 3.0], [14.5, 3.0], [14.6, 3.0], [14.7, 3.0], [14.8, 3.0], [14.9, 3.0], [15.0, 3.0], [15.1, 3.0], [15.2, 3.0], [15.3, 3.0], [15.4, 3.0], [15.5, 3.0], [15.6, 3.0], [15.7, 3.0], [15.8, 3.0], [15.9, 3.0], [16.0, 3.0], [16.1, 3.0], [16.2, 3.0], [16.3, 3.0], [16.4, 3.0], [16.5, 3.0], [16.6, 3.0], [16.7, 3.0], [16.8, 3.0], [16.9, 3.0], [17.0, 3.0], [17.1, 3.0], [17.2, 3.0], [17.3, 3.0], [17.4, 3.0], [17.5, 3.0], [17.6, 3.0], [17.7, 3.0], [17.8, 3.0], [17.9, 3.0], [18.0, 3.0], [18.1, 3.0], [18.2, 3.0], [18.3, 3.0], [18.4, 3.0], [18.5, 3.0], [18.6, 3.0], [18.7, 3.0], [18.8, 3.0], [18.9, 3.0], [19.0, 3.0], [19.1, 3.0], [19.2, 3.0], [19.3, 3.0], [19.4, 3.0], [19.5, 3.0], [19.6, 3.0], [19.7, 3.0], [19.8, 3.0], [19.9, 3.0], [20.0, 3.0], [20.1, 3.0], [20.2, 3.0], [20.3, 3.0], [20.4, 3.0], [20.5, 3.0], [20.6, 3.0], [20.7, 3.0], [20.8, 3.0], [20.9, 3.0], [21.0, 3.0], [21.1, 3.0], [21.2, 3.0], [21.3, 3.0], [21.4, 3.0], [21.5, 3.0], [21.6, 3.0], [21.7, 3.0], [21.8, 3.0], [21.9, 3.0], [22.0, 3.0], [22.1, 3.0], [22.2, 3.0], [22.3, 3.0], [22.4, 3.0], [22.5, 3.0], [22.6, 3.0], [22.7, 3.0], [22.8, 3.0], [22.9, 3.0], [23.0, 3.0], [23.1, 3.0], [23.2, 3.0], [23.3, 3.0], [23.4, 3.0], [23.5, 3.0], [23.6, 3.0], [23.7, 3.0], [23.8, 3.0], [23.9, 3.0], [24.0, 3.0], [24.1, 3.0], [24.2, 3.0], [24.3, 3.0], [24.4, 3.0], [24.5, 3.0], [24.6, 3.0], [24.7, 3.0], [24.8, 3.0], [24.9, 3.0], [25.0, 3.0], [25.1, 3.0], [25.2, 3.0], [25.3, 3.0], [25.4, 3.0], [25.5, 3.0], [25.6, 3.0], [25.7, 3.0], [25.8, 3.0], [25.9, 3.0], [26.0, 3.0], [26.1, 3.0], [26.2, 3.0], [26.3, 3.0], [26.4, 3.0], [26.5, 3.0], [26.6, 3.0], [26.7, 3.0], [26.8, 3.0], [26.9, 3.0], [27.0, 3.0], [27.1, 3.0], [27.2, 3.0], [27.3, 3.0], [27.4, 3.0], [27.5, 3.0], [27.6, 3.0], [27.7, 3.0], [27.8, 3.0], [27.9, 3.0], [28.0, 3.0], [28.1, 3.0], [28.2, 3.0], [28.3, 3.0], [28.4, 3.0], [28.5, 3.0], [28.6, 3.0], [28.7, 3.0], [28.8, 3.0], [28.9, 3.0], [29.0, 3.0], [29.1, 3.0], [29.2, 3.0], [29.3, 3.0], [29.4, 3.0], [29.5, 3.0], [29.6, 3.0], [29.7, 3.0], [29.8, 3.0], [29.9, 3.0], [30.0, 3.0], [30.1, 3.0], [30.2, 3.0], [30.3, 3.0], [30.4, 3.0], [30.5, 3.0], [30.6, 3.0], [30.7, 3.0], [30.8, 3.0], [30.9, 3.0], [31.0, 3.0], [31.1, 3.0], [31.2, 3.0], [31.3, 3.0], [31.4, 3.0], [31.5, 3.0], [31.6, 3.0], [31.7, 3.0], [31.8, 3.0], [31.9, 3.0], [32.0, 3.0], [32.1, 3.0], [32.2, 3.0], [32.3, 3.0], [32.4, 3.0], [32.5, 3.0], [32.6, 3.0], [32.7, 3.0], [32.8, 3.0], [32.9, 3.0], [33.0, 3.0], [33.1, 3.0], [33.2, 3.0], [33.3, 3.0], [33.4, 3.0], [33.5, 3.0], [33.6, 3.0], [33.7, 3.0], [33.8, 3.0], [33.9, 3.0], [34.0, 3.0], [34.1, 3.0], [34.2, 3.0], [34.3, 3.0], [34.4, 3.0], [34.5, 3.0], [34.6, 3.0], [34.7, 3.0], [34.8, 3.0], [34.9, 3.0], [35.0, 3.0], [35.1, 3.0], [35.2, 3.0], [35.3, 3.0], [35.4, 3.0], [35.5, 3.0], [35.6, 3.0], [35.7, 3.0], [35.8, 3.0], [35.9, 3.0], [36.0, 3.0], [36.1, 3.0], [36.2, 3.0], [36.3, 3.0], [36.4, 3.0], [36.5, 3.0], [36.6, 3.0], [36.7, 3.0], [36.8, 3.0], [36.9, 3.0], [37.0, 3.0], [37.1, 3.0], [37.2, 3.0], [37.3, 3.0], [37.4, 3.0], [37.5, 3.0], [37.6, 3.0], [37.7, 3.0], [37.8, 3.0], [37.9, 3.0], [38.0, 3.0], [38.1, 3.0], [38.2, 3.0], [38.3, 3.0], [38.4, 3.0], [38.5, 3.0], [38.6, 3.0], [38.7, 3.0], [38.8, 3.0], [38.9, 3.0], [39.0, 3.0], [39.1, 3.0], [39.2, 3.0], [39.3, 3.0], [39.4, 3.0], [39.5, 3.0], [39.6, 3.0], [39.7, 3.0], [39.8, 3.0], [39.9, 3.0], [40.0, 3.0], [40.1, 3.0], [40.2, 3.0], [40.3, 3.0], [40.4, 3.0], [40.5, 3.0], [40.6, 3.0], [40.7, 3.0], [40.8, 3.0], [40.9, 3.0], [41.0, 3.0], [41.1, 3.0], [41.2, 3.0], [41.3, 3.0], [41.4, 3.0], [41.5, 3.0], [41.6, 3.0], [41.7, 3.0], [41.8, 3.0], [41.9, 3.0], [42.0, 3.0], [42.1, 3.0], [42.2, 3.0], [42.3, 3.0], [42.4, 3.0], [42.5, 3.0], [42.6, 3.0], [42.7, 3.0], [42.8, 3.0], [42.9, 3.0], [43.0, 3.0], [43.1, 3.0], [43.2, 3.0], [43.3, 3.0], [43.4, 3.0], [43.5, 3.0], [43.6, 3.0], [43.7, 3.0], [43.8, 3.0], [43.9, 3.0], [44.0, 3.0], [44.1, 3.0], [44.2, 3.0], [44.3, 3.0], [44.4, 3.0], [44.5, 3.0], [44.6, 3.0], [44.7, 3.0], [44.8, 3.0], [44.9, 3.0], [45.0, 3.0], [45.1, 3.0], [45.2, 3.0], [45.3, 3.0], [45.4, 3.0], [45.5, 3.0], [45.6, 3.0], [45.7, 3.0], [45.8, 3.0], [45.9, 3.0], [46.0, 3.0], [46.1, 3.0], [46.2, 3.0], [46.3, 3.0], [46.4, 3.0], [46.5, 3.0], [46.6, 3.0], [46.7, 3.0], [46.8, 3.0], [46.9, 3.0], [47.0, 3.0], [47.1, 3.0], [47.2, 3.0], [47.3, 3.0], [47.4, 3.0], [47.5, 3.0], [47.6, 3.0], [47.7, 3.0], [47.8, 3.0], [47.9, 3.0], [48.0, 3.0], [48.1, 3.0], [48.2, 3.0], [48.3, 3.0], [48.4, 3.0], [48.5, 3.0], [48.6, 3.0], [48.7, 3.0], [48.8, 3.0], [48.9, 3.0], [49.0, 3.0], [49.1, 3.0], [49.2, 3.0], [49.3, 3.0], [49.4, 3.0], [49.5, 3.0], [49.6, 3.0], [49.7, 3.0], [49.8, 3.0], [49.9, 3.0], [50.0, 3.0], [50.1, 3.0], [50.2, 3.0], [50.3, 3.0], [50.4, 3.0], [50.5, 3.0], [50.6, 3.0], [50.7, 3.0], [50.8, 3.0], [50.9, 3.0], [51.0, 3.0], [51.1, 3.0], [51.2, 3.0], [51.3, 3.0], [51.4, 3.0], [51.5, 3.0], [51.6, 3.0], [51.7, 3.0], [51.8, 3.0], [51.9, 3.0], [52.0, 3.0], [52.1, 3.0], [52.2, 3.0], [52.3, 3.0], [52.4, 3.0], [52.5, 3.0], [52.6, 3.0], [52.7, 3.0], [52.8, 3.0], [52.9, 3.0], [53.0, 3.0], [53.1, 3.0], [53.2, 3.0], [53.3, 3.0], [53.4, 3.0], [53.5, 3.0], [53.6, 3.0], [53.7, 3.0], [53.8, 3.0], [53.9, 3.0], [54.0, 3.0], [54.1, 3.0], [54.2, 3.0], [54.3, 3.0], [54.4, 3.0], [54.5, 3.0], [54.6, 3.0], [54.7, 3.0], [54.8, 3.0], [54.9, 3.0], [55.0, 3.0], [55.1, 3.0], [55.2, 3.0], [55.3, 3.0], [55.4, 3.0], [55.5, 3.0], [55.6, 3.0], [55.7, 3.0], [55.8, 3.0], [55.9, 3.0], [56.0, 3.0], [56.1, 3.0], [56.2, 3.0], [56.3, 3.0], [56.4, 3.0], [56.5, 3.0], [56.6, 3.0], [56.7, 3.0], [56.8, 3.0], [56.9, 3.0], [57.0, 3.0], [57.1, 3.0], [57.2, 3.0], [57.3, 3.0], [57.4, 3.0], [57.5, 3.0], [57.6, 3.0], [57.7, 3.0], [57.8, 3.0], [57.9, 3.0], [58.0, 3.0], [58.1, 3.0], [58.2, 3.0], [58.3, 3.0], [58.4, 3.0], [58.5, 3.0], [58.6, 3.0], [58.7, 3.0], [58.8, 3.0], [58.9, 3.0], [59.0, 3.0], [59.1, 3.0], [59.2, 3.0], [59.3, 3.0], [59.4, 3.0], [59.5, 3.0], [59.6, 3.0], [59.7, 3.0], [59.8, 3.0], [59.9, 3.0], [60.0, 3.0], [60.1, 3.0], [60.2, 3.0], [60.3, 3.0], [60.4, 3.0], [60.5, 3.0], [60.6, 3.0], [60.7, 3.0], [60.8, 3.0], [60.9, 3.0], [61.0, 3.0], [61.1, 3.0], [61.2, 3.0], [61.3, 3.0], [61.4, 3.0], [61.5, 3.0], [61.6, 3.0], [61.7, 3.0], [61.8, 3.0], [61.9, 3.0], [62.0, 3.0], [62.1, 3.0], [62.2, 3.0], [62.3, 3.0], [62.4, 3.0], [62.5, 3.0], [62.6, 3.0], [62.7, 3.0], [62.8, 3.0], [62.9, 3.0], [63.0, 3.0], [63.1, 3.0], [63.2, 3.0], [63.3, 3.0], [63.4, 3.0], [63.5, 3.0], [63.6, 3.0], [63.7, 3.0], [63.8, 3.0], [63.9, 4.0], [64.0, 4.0], [64.1, 4.0], [64.2, 4.0], [64.3, 4.0], [64.4, 4.0], [64.5, 4.0], [64.6, 4.0], [64.7, 4.0], [64.8, 4.0], [64.9, 4.0], [65.0, 4.0], [65.1, 4.0], [65.2, 4.0], [65.3, 4.0], [65.4, 4.0], [65.5, 4.0], [65.6, 4.0], [65.7, 4.0], [65.8, 4.0], [65.9, 4.0], [66.0, 4.0], [66.1, 4.0], [66.2, 4.0], [66.3, 4.0], [66.4, 4.0], [66.5, 4.0], [66.6, 4.0], [66.7, 4.0], [66.8, 4.0], [66.9, 4.0], [67.0, 4.0], [67.1, 4.0], [67.2, 4.0], [67.3, 4.0], [67.4, 4.0], [67.5, 4.0], [67.6, 4.0], [67.7, 4.0], [67.8, 4.0], [67.9, 4.0], [68.0, 4.0], [68.1, 4.0], [68.2, 4.0], [68.3, 4.0], [68.4, 4.0], [68.5, 4.0], [68.6, 4.0], [68.7, 4.0], [68.8, 4.0], [68.9, 4.0], [69.0, 4.0], [69.1, 4.0], [69.2, 4.0], [69.3, 4.0], [69.4, 4.0], [69.5, 4.0], [69.6, 4.0], [69.7, 4.0], [69.8, 4.0], [69.9, 4.0], [70.0, 4.0], [70.1, 4.0], [70.2, 4.0], [70.3, 4.0], [70.4, 4.0], [70.5, 4.0], [70.6, 4.0], [70.7, 4.0], [70.8, 4.0], [70.9, 4.0], [71.0, 4.0], [71.1, 4.0], [71.2, 4.0], [71.3, 4.0], [71.4, 4.0], [71.5, 4.0], [71.6, 4.0], [71.7, 4.0], [71.8, 4.0], [71.9, 4.0], [72.0, 4.0], [72.1, 4.0], [72.2, 4.0], [72.3, 4.0], [72.4, 4.0], [72.5, 4.0], [72.6, 4.0], [72.7, 4.0], [72.8, 4.0], [72.9, 4.0], [73.0, 4.0], [73.1, 4.0], [73.2, 4.0], [73.3, 4.0], [73.4, 4.0], [73.5, 4.0], [73.6, 4.0], [73.7, 4.0], [73.8, 4.0], [73.9, 4.0], [74.0, 4.0], [74.1, 4.0], [74.2, 4.0], [74.3, 4.0], [74.4, 4.0], [74.5, 4.0], [74.6, 4.0], [74.7, 4.0], [74.8, 4.0], [74.9, 4.0], [75.0, 4.0], [75.1, 4.0], [75.2, 4.0], [75.3, 4.0], [75.4, 4.0], [75.5, 4.0], [75.6, 4.0], [75.7, 4.0], [75.8, 4.0], [75.9, 4.0], [76.0, 4.0], [76.1, 4.0], [76.2, 4.0], [76.3, 4.0], [76.4, 4.0], [76.5, 4.0], [76.6, 4.0], [76.7, 4.0], [76.8, 4.0], [76.9, 4.0], [77.0, 4.0], [77.1, 4.0], [77.2, 4.0], [77.3, 4.0], [77.4, 4.0], [77.5, 4.0], [77.6, 4.0], [77.7, 4.0], [77.8, 4.0], [77.9, 4.0], [78.0, 4.0], [78.1, 4.0], [78.2, 4.0], [78.3, 4.0], [78.4, 4.0], [78.5, 4.0], [78.6, 4.0], [78.7, 4.0], [78.8, 4.0], [78.9, 4.0], [79.0, 4.0], [79.1, 4.0], [79.2, 4.0], [79.3, 4.0], [79.4, 4.0], [79.5, 4.0], [79.6, 4.0], [79.7, 4.0], [79.8, 4.0], [79.9, 4.0], [80.0, 5.0], [80.1, 5.0], [80.2, 5.0], [80.3, 5.0], [80.4, 5.0], [80.5, 5.0], [80.6, 5.0], [80.7, 5.0], [80.8, 5.0], [80.9, 5.0], [81.0, 5.0], [81.1, 5.0], [81.2, 5.0], [81.3, 5.0], [81.4, 5.0], [81.5, 5.0], [81.6, 5.0], [81.7, 5.0], [81.8, 5.0], [81.9, 5.0], [82.0, 5.0], [82.1, 5.0], [82.2, 5.0], [82.3, 5.0], [82.4, 5.0], [82.5, 5.0], [82.6, 5.0], [82.7, 5.0], [82.8, 5.0], [82.9, 5.0], [83.0, 5.0], [83.1, 5.0], [83.2, 5.0], [83.3, 5.0], [83.4, 5.0], [83.5, 5.0], [83.6, 5.0], [83.7, 5.0], [83.8, 5.0], [83.9, 5.0], [84.0, 5.0], [84.1, 5.0], [84.2, 5.0], [84.3, 5.0], [84.4, 5.0], [84.5, 5.0], [84.6, 5.0], [84.7, 5.0], [84.8, 5.0], [84.9, 5.0], [85.0, 5.0], [85.1, 5.0], [85.2, 5.0], [85.3, 5.0], [85.4, 5.0], [85.5, 5.0], [85.6, 5.0], [85.7, 5.0], [85.8, 5.0], [85.9, 5.0], [86.0, 5.0], [86.1, 5.0], [86.2, 5.0], [86.3, 5.0], [86.4, 5.0], [86.5, 5.0], [86.6, 5.0], [86.7, 5.0], [86.8, 6.0], [86.9, 6.0], [87.0, 6.0], [87.1, 6.0], [87.2, 6.0], [87.3, 6.0], [87.4, 6.0], [87.5, 6.0], [87.6, 6.0], [87.7, 6.0], [87.8, 6.0], [87.9, 6.0], [88.0, 6.0], [88.1, 6.0], [88.2, 6.0], [88.3, 6.0], [88.4, 6.0], [88.5, 6.0], [88.6, 6.0], [88.7, 6.0], [88.8, 6.0], [88.9, 6.0], [89.0, 6.0], [89.1, 6.0], [89.2, 6.0], [89.3, 6.0], [89.4, 6.0], [89.5, 6.0], [89.6, 6.0], [89.7, 6.0], [89.8, 6.0], [89.9, 6.0], [90.0, 6.0], [90.1, 6.0], [90.2, 6.0], [90.3, 6.0], [90.4, 6.0], [90.5, 6.0], [90.6, 6.0], [90.7, 6.0], [90.8, 6.0], [90.9, 6.0], [91.0, 6.0], [91.1, 6.0], [91.2, 6.0], [91.3, 6.0], [91.4, 6.0], [91.5, 6.0], [91.6, 6.0], [91.7, 6.0], [91.8, 7.0], [91.9, 7.0], [92.0, 7.0], [92.1, 7.0], [92.2, 7.0], [92.3, 7.0], [92.4, 7.0], [92.5, 7.0], [92.6, 7.0], [92.7, 7.0], [92.8, 7.0], [92.9, 7.0], [93.0, 7.0], [93.1, 7.0], [93.2, 7.0], [93.3, 7.0], [93.4, 7.0], [93.5, 7.0], [93.6, 7.0], [93.7, 7.0], [93.8, 7.0], [93.9, 7.0], [94.0, 7.0], [94.1, 7.0], [94.2, 7.0], [94.3, 7.0], [94.4, 8.0], [94.5, 8.0], [94.6, 8.0], [94.7, 8.0], [94.8, 9.0], [94.9, 9.0], [95.0, 9.0], [95.1, 9.0], [95.2, 9.0], [95.3, 9.0], [95.4, 9.0], [95.5, 9.0], [95.6, 10.0], [95.7, 10.0], [95.8, 10.0], [95.9, 10.0], [96.0, 10.0], [96.1, 10.0], [96.2, 11.0], [96.3, 11.0], [96.4, 11.0], [96.5, 11.0], [96.6, 12.0], [96.7, 12.0], [96.8, 13.0], [96.9, 13.0], [97.0, 13.0], [97.1, 13.0], [97.2, 14.0], [97.3, 14.0], [97.4, 14.0], [97.5, 14.0], [97.6, 14.0], [97.7, 14.0], [97.8, 16.0], [97.9, 16.0], [98.0, 18.0], [98.1, 18.0], [98.2, 19.0], [98.3, 19.0], [98.4, 25.0], [98.5, 25.0], [98.6, 25.0], [98.7, 27.0], [98.8, 27.0], [98.9, 27.0], [99.0, 27.0], [99.1, 29.0], [99.2, 29.0], [99.3, 29.0], [99.4, 29.0], [99.5, 30.0], [99.6, 30.0], [99.7, 33.0], [99.8, 33.0], [99.9, 34.0], [100.0, 34.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[0.0, 2.0], [0.1, 2.0], [0.2, 2.0], [0.3, 2.0], [0.4, 2.0], [0.5, 2.0], [0.6, 2.0], [0.7, 2.0], [0.8, 2.0], [0.9, 2.0], [1.0, 2.0], [1.1, 2.0], [1.2, 2.0], [1.3, 2.0], [1.4, 2.0], [1.5, 2.0], [1.6, 2.0], [1.7, 2.0], [1.8, 2.0], [1.9, 2.0], [2.0, 2.0], [2.1, 2.0], [2.2, 2.0], [2.3, 2.0], [2.4, 2.0], [2.5, 2.0], [2.6, 2.0], [2.7, 2.0], [2.8, 2.0], [2.9, 2.0], [3.0, 2.0], [3.1, 2.0], [3.2, 2.0], [3.3, 2.0], [3.4, 2.0], [3.5, 2.0], [3.6, 2.0], [3.7, 2.0], [3.8, 2.0], [3.9, 2.0], [4.0, 2.0], [4.1, 2.0], [4.2, 2.0], [4.3, 2.0], [4.4, 2.0], [4.5, 2.0], [4.6, 2.0], [4.7, 2.0], [4.8, 2.0], [4.9, 2.0], [5.0, 2.0], [5.1, 2.0], [5.2, 3.0], [5.3, 3.0], [5.4, 3.0], [5.5, 3.0], [5.6, 3.0], [5.7, 3.0], [5.8, 3.0], [5.9, 3.0], [6.0, 3.0], [6.1, 3.0], [6.2, 3.0], [6.3, 3.0], [6.4, 3.0], [6.5, 3.0], [6.6, 3.0], [6.7, 3.0], [6.8, 3.0], [6.9, 3.0], [7.0, 3.0], [7.1, 3.0], [7.2, 3.0], [7.3, 3.0], [7.4, 3.0], [7.5, 3.0], [7.6, 3.0], [7.7, 3.0], [7.8, 3.0], [7.9, 3.0], [8.0, 3.0], [8.1, 3.0], [8.2, 3.0], [8.3, 3.0], [8.4, 3.0], [8.5, 3.0], [8.6, 3.0], [8.7, 3.0], [8.8, 3.0], [8.9, 3.0], [9.0, 3.0], [9.1, 3.0], [9.2, 3.0], [9.3, 3.0], [9.4, 3.0], [9.5, 3.0], [9.6, 3.0], [9.7, 3.0], [9.8, 3.0], [9.9, 3.0], [10.0, 3.0], [10.1, 3.0], [10.2, 3.0], [10.3, 3.0], [10.4, 3.0], [10.5, 3.0], [10.6, 3.0], [10.7, 3.0], [10.8, 3.0], [10.9, 3.0], [11.0, 3.0], [11.1, 3.0], [11.2, 3.0], [11.3, 3.0], [11.4, 3.0], [11.5, 3.0], [11.6, 3.0], [11.7, 3.0], [11.8, 3.0], [11.9, 3.0], [12.0, 3.0], [12.1, 3.0], [12.2, 3.0], [12.3, 3.0], [12.4, 3.0], [12.5, 3.0], [12.6, 3.0], [12.7, 3.0], [12.8, 3.0], [12.9, 3.0], [13.0, 3.0], [13.1, 3.0], [13.2, 3.0], [13.3, 3.0], [13.4, 3.0], [13.5, 3.0], [13.6, 3.0], [13.7, 3.0], [13.8, 3.0], [13.9, 3.0], [14.0, 3.0], [14.1, 3.0], [14.2, 3.0], [14.3, 3.0], [14.4, 3.0], [14.5, 3.0], [14.6, 3.0], [14.7, 3.0], [14.8, 3.0], [14.9, 3.0], [15.0, 3.0], [15.1, 3.0], [15.2, 3.0], [15.3, 3.0], [15.4, 3.0], [15.5, 3.0], [15.6, 3.0], [15.7, 3.0], [15.8, 3.0], [15.9, 3.0], [16.0, 3.0], [16.1, 3.0], [16.2, 3.0], [16.3, 3.0], [16.4, 3.0], [16.5, 3.0], [16.6, 3.0], [16.7, 3.0], [16.8, 3.0], [16.9, 3.0], [17.0, 3.0], [17.1, 3.0], [17.2, 3.0], [17.3, 3.0], [17.4, 3.0], [17.5, 3.0], [17.6, 3.0], [17.7, 3.0], [17.8, 3.0], [17.9, 3.0], [18.0, 3.0], [18.1, 3.0], [18.2, 3.0], [18.3, 3.0], [18.4, 3.0], [18.5, 3.0], [18.6, 3.0], [18.7, 3.0], [18.8, 3.0], [18.9, 3.0], [19.0, 3.0], [19.1, 3.0], [19.2, 3.0], [19.3, 3.0], [19.4, 3.0], [19.5, 3.0], [19.6, 3.0], [19.7, 3.0], [19.8, 3.0], [19.9, 3.0], [20.0, 3.0], [20.1, 3.0], [20.2, 3.0], [20.3, 3.0], [20.4, 3.0], [20.5, 3.0], [20.6, 3.0], [20.7, 3.0], [20.8, 3.0], [20.9, 3.0], [21.0, 3.0], [21.1, 3.0], [21.2, 3.0], [21.3, 3.0], [21.4, 3.0], [21.5, 3.0], [21.6, 3.0], [21.7, 3.0], [21.8, 3.0], [21.9, 3.0], [22.0, 3.0], [22.1, 3.0], [22.2, 3.0], [22.3, 3.0], [22.4, 3.0], [22.5, 3.0], [22.6, 3.0], [22.7, 3.0], [22.8, 3.0], [22.9, 3.0], [23.0, 3.0], [23.1, 3.0], [23.2, 3.0], [23.3, 3.0], [23.4, 3.0], [23.5, 3.0], [23.6, 3.0], [23.7, 3.0], [23.8, 3.0], [23.9, 3.0], [24.0, 3.0], [24.1, 3.0], [24.2, 3.0], [24.3, 3.0], [24.4, 3.0], [24.5, 3.0], [24.6, 3.0], [24.7, 3.0], [24.8, 3.0], [24.9, 3.0], [25.0, 3.0], [25.1, 3.0], [25.2, 3.0], [25.3, 3.0], [25.4, 3.0], [25.5, 3.0], [25.6, 3.0], [25.7, 3.0], [25.8, 3.0], [25.9, 3.0], [26.0, 3.0], [26.1, 3.0], [26.2, 3.0], [26.3, 3.0], [26.4, 3.0], [26.5, 3.0], [26.6, 3.0], [26.7, 3.0], [26.8, 3.0], [26.9, 3.0], [27.0, 3.0], [27.1, 3.0], [27.2, 3.0], [27.3, 3.0], [27.4, 3.0], [27.5, 3.0], [27.6, 3.0], [27.7, 3.0], [27.8, 3.0], [27.9, 3.0], [28.0, 3.0], [28.1, 3.0], [28.2, 3.0], [28.3, 3.0], [28.4, 3.0], [28.5, 3.0], [28.6, 3.0], [28.7, 3.0], [28.8, 3.0], [28.9, 3.0], [29.0, 3.0], [29.1, 3.0], [29.2, 3.0], [29.3, 3.0], [29.4, 3.0], [29.5, 3.0], [29.6, 3.0], [29.7, 3.0], [29.8, 3.0], [29.9, 3.0], [30.0, 3.0], [30.1, 3.0], [30.2, 3.0], [30.3, 3.0], [30.4, 3.0], [30.5, 3.0], [30.6, 3.0], [30.7, 3.0], [30.8, 3.0], [30.9, 3.0], [31.0, 3.0], [31.1, 3.0], [31.2, 3.0], [31.3, 3.0], [31.4, 3.0], [31.5, 3.0], [31.6, 3.0], [31.7, 3.0], [31.8, 3.0], [31.9, 3.0], [32.0, 3.0], [32.1, 3.0], [32.2, 3.0], [32.3, 3.0], [32.4, 3.0], [32.5, 3.0], [32.6, 3.0], [32.7, 3.0], [32.8, 3.0], [32.9, 3.0], [33.0, 3.0], [33.1, 3.0], [33.2, 3.0], [33.3, 3.0], [33.4, 3.0], [33.5, 3.0], [33.6, 3.0], [33.7, 3.0], [33.8, 3.0], [33.9, 3.0], [34.0, 3.0], [34.1, 3.0], [34.2, 3.0], [34.3, 3.0], [34.4, 3.0], [34.5, 3.0], [34.6, 3.0], [34.7, 3.0], [34.8, 3.0], [34.9, 3.0], [35.0, 3.0], [35.1, 3.0], [35.2, 3.0], [35.3, 3.0], [35.4, 3.0], [35.5, 3.0], [35.6, 3.0], [35.7, 3.0], [35.8, 3.0], [35.9, 3.0], [36.0, 3.0], [36.1, 3.0], [36.2, 3.0], [36.3, 3.0], [36.4, 3.0], [36.5, 3.0], [36.6, 3.0], [36.7, 3.0], [36.8, 3.0], [36.9, 3.0], [37.0, 3.0], [37.1, 3.0], [37.2, 3.0], [37.3, 3.0], [37.4, 3.0], [37.5, 3.0], [37.6, 3.0], [37.7, 3.0], [37.8, 3.0], [37.9, 3.0], [38.0, 3.0], [38.1, 3.0], [38.2, 3.0], [38.3, 3.0], [38.4, 3.0], [38.5, 3.0], [38.6, 3.0], [38.7, 3.0], [38.8, 3.0], [38.9, 3.0], [39.0, 3.0], [39.1, 3.0], [39.2, 3.0], [39.3, 3.0], [39.4, 3.0], [39.5, 3.0], [39.6, 3.0], [39.7, 3.0], [39.8, 3.0], [39.9, 3.0], [40.0, 3.0], [40.1, 3.0], [40.2, 3.0], [40.3, 3.0], [40.4, 3.0], [40.5, 3.0], [40.6, 3.0], [40.7, 3.0], [40.8, 3.0], [40.9, 3.0], [41.0, 3.0], [41.1, 3.0], [41.2, 3.0], [41.3, 3.0], [41.4, 3.0], [41.5, 3.0], [41.6, 3.0], [41.7, 3.0], [41.8, 3.0], [41.9, 3.0], [42.0, 3.0], [42.1, 3.0], [42.2, 3.0], [42.3, 3.0], [42.4, 3.0], [42.5, 3.0], [42.6, 3.0], [42.7, 3.0], [42.8, 3.0], [42.9, 3.0], [43.0, 3.0], [43.1, 3.0], [43.2, 3.0], [43.3, 3.0], [43.4, 3.0], [43.5, 3.0], [43.6, 3.0], [43.7, 3.0], [43.8, 3.0], [43.9, 3.0], [44.0, 3.0], [44.1, 3.0], [44.2, 3.0], [44.3, 3.0], [44.4, 3.0], [44.5, 3.0], [44.6, 3.0], [44.7, 3.0], [44.8, 3.0], [44.9, 3.0], [45.0, 3.0], [45.1, 3.0], [45.2, 3.0], [45.3, 3.0], [45.4, 3.0], [45.5, 3.0], [45.6, 3.0], [45.7, 3.0], [45.8, 3.0], [45.9, 3.0], [46.0, 3.0], [46.1, 3.0], [46.2, 3.0], [46.3, 3.0], [46.4, 3.0], [46.5, 3.0], [46.6, 3.0], [46.7, 3.0], [46.8, 3.0], [46.9, 3.0], [47.0, 3.0], [47.1, 3.0], [47.2, 3.0], [47.3, 3.0], [47.4, 3.0], [47.5, 3.0], [47.6, 3.0], [47.7, 3.0], [47.8, 3.0], [47.9, 3.0], [48.0, 3.0], [48.1, 3.0], [48.2, 3.0], [48.3, 3.0], [48.4, 3.0], [48.5, 3.0], [48.6, 3.0], [48.7, 3.0], [48.8, 3.0], [48.9, 3.0], [49.0, 3.0], [49.1, 3.0], [49.2, 3.0], [49.3, 3.0], [49.4, 3.0], [49.5, 3.0], [49.6, 3.0], [49.7, 3.0], [49.8, 3.0], [49.9, 3.0], [50.0, 3.0], [50.1, 3.0], [50.2, 3.0], [50.3, 3.0], [50.4, 3.0], [50.5, 3.0], [50.6, 3.0], [50.7, 3.0], [50.8, 3.0], [50.9, 3.0], [51.0, 3.0], [51.1, 3.0], [51.2, 3.0], [51.3, 3.0], [51.4, 3.0], [51.5, 3.0], [51.6, 3.0], [51.7, 3.0], [51.8, 3.0], [51.9, 3.0], [52.0, 3.0], [52.1, 3.0], [52.2, 3.0], [52.3, 3.0], [52.4, 3.0], [52.5, 3.0], [52.6, 3.0], [52.7, 3.0], [52.8, 3.0], [52.9, 3.0], [53.0, 3.0], [53.1, 3.0], [53.2, 3.0], [53.3, 3.0], [53.4, 3.0], [53.5, 3.0], [53.6, 3.0], [53.7, 3.0], [53.8, 3.0], [53.9, 3.0], [54.0, 3.0], [54.1, 3.0], [54.2, 3.0], [54.3, 3.0], [54.4, 3.0], [54.5, 3.0], [54.6, 3.0], [54.7, 3.0], [54.8, 3.0], [54.9, 3.0], [55.0, 3.0], [55.1, 3.0], [55.2, 3.0], [55.3, 3.0], [55.4, 3.0], [55.5, 3.0], [55.6, 3.0], [55.7, 3.0], [55.8, 3.0], [55.9, 3.0], [56.0, 3.0], [56.1, 3.0], [56.2, 3.0], [56.3, 3.0], [56.4, 3.0], [56.5, 3.0], [56.6, 3.0], [56.7, 3.0], [56.8, 3.0], [56.9, 3.0], [57.0, 3.0], [57.1, 3.0], [57.2, 3.0], [57.3, 3.0], [57.4, 3.0], [57.5, 3.0], [57.6, 3.0], [57.7, 3.0], [57.8, 3.0], [57.9, 3.0], [58.0, 3.0], [58.1, 3.0], [58.2, 3.0], [58.3, 3.0], [58.4, 3.0], [58.5, 3.0], [58.6, 3.0], [58.7, 3.0], [58.8, 3.0], [58.9, 3.0], [59.0, 3.0], [59.1, 3.0], [59.2, 3.0], [59.3, 3.0], [59.4, 3.0], [59.5, 3.0], [59.6, 3.0], [59.7, 3.0], [59.8, 3.0], [59.9, 3.0], [60.0, 3.0], [60.1, 3.0], [60.2, 3.0], [60.3, 3.0], [60.4, 3.0], [60.5, 3.0], [60.6, 4.0], [60.7, 4.0], [60.8, 4.0], [60.9, 4.0], [61.0, 4.0], [61.1, 4.0], [61.2, 4.0], [61.3, 4.0], [61.4, 4.0], [61.5, 4.0], [61.6, 4.0], [61.7, 4.0], [61.8, 4.0], [61.9, 4.0], [62.0, 4.0], [62.1, 4.0], [62.2, 4.0], [62.3, 4.0], [62.4, 4.0], [62.5, 4.0], [62.6, 4.0], [62.7, 4.0], [62.8, 4.0], [62.9, 4.0], [63.0, 4.0], [63.1, 4.0], [63.2, 4.0], [63.3, 4.0], [63.4, 4.0], [63.5, 4.0], [63.6, 4.0], [63.7, 4.0], [63.8, 4.0], [63.9, 4.0], [64.0, 4.0], [64.1, 4.0], [64.2, 4.0], [64.3, 4.0], [64.4, 4.0], [64.5, 4.0], [64.6, 4.0], [64.7, 4.0], [64.8, 4.0], [64.9, 4.0], [65.0, 4.0], [65.1, 4.0], [65.2, 4.0], [65.3, 4.0], [65.4, 4.0], [65.5, 4.0], [65.6, 4.0], [65.7, 4.0], [65.8, 4.0], [65.9, 4.0], [66.0, 4.0], [66.1, 4.0], [66.2, 4.0], [66.3, 4.0], [66.4, 4.0], [66.5, 4.0], [66.6, 4.0], [66.7, 4.0], [66.8, 4.0], [66.9, 4.0], [67.0, 4.0], [67.1, 4.0], [67.2, 4.0], [67.3, 4.0], [67.4, 4.0], [67.5, 4.0], [67.6, 4.0], [67.7, 4.0], [67.8, 4.0], [67.9, 4.0], [68.0, 4.0], [68.1, 4.0], [68.2, 4.0], [68.3, 4.0], [68.4, 4.0], [68.5, 4.0], [68.6, 4.0], [68.7, 4.0], [68.8, 4.0], [68.9, 4.0], [69.0, 4.0], [69.1, 4.0], [69.2, 4.0], [69.3, 4.0], [69.4, 4.0], [69.5, 4.0], [69.6, 4.0], [69.7, 4.0], [69.8, 4.0], [69.9, 4.0], [70.0, 4.0], [70.1, 4.0], [70.2, 4.0], [70.3, 4.0], [70.4, 4.0], [70.5, 4.0], [70.6, 4.0], [70.7, 4.0], [70.8, 4.0], [70.9, 4.0], [71.0, 4.0], [71.1, 4.0], [71.2, 4.0], [71.3, 4.0], [71.4, 4.0], [71.5, 4.0], [71.6, 4.0], [71.7, 4.0], [71.8, 4.0], [71.9, 4.0], [72.0, 4.0], [72.1, 4.0], [72.2, 4.0], [72.3, 4.0], [72.4, 4.0], [72.5, 4.0], [72.6, 4.0], [72.7, 4.0], [72.8, 4.0], [72.9, 4.0], [73.0, 4.0], [73.1, 4.0], [73.2, 4.0], [73.3, 4.0], [73.4, 4.0], [73.5, 4.0], [73.6, 4.0], [73.7, 4.0], [73.8, 4.0], [73.9, 4.0], [74.0, 4.0], [74.1, 4.0], [74.2, 4.0], [74.3, 4.0], [74.4, 4.0], [74.5, 4.0], [74.6, 4.0], [74.7, 4.0], [74.8, 4.0], [74.9, 4.0], [75.0, 4.0], [75.1, 4.0], [75.2, 5.0], [75.3, 5.0], [75.4, 5.0], [75.5, 5.0], [75.6, 5.0], [75.7, 5.0], [75.8, 5.0], [75.9, 5.0], [76.0, 5.0], [76.1, 5.0], [76.2, 5.0], [76.3, 5.0], [76.4, 5.0], [76.5, 5.0], [76.6, 5.0], [76.7, 5.0], [76.8, 5.0], [76.9, 5.0], [77.0, 5.0], [77.1, 5.0], [77.2, 5.0], [77.3, 5.0], [77.4, 5.0], [77.5, 5.0], [77.6, 5.0], [77.7, 5.0], [77.8, 5.0], [77.9, 5.0], [78.0, 5.0], [78.1, 5.0], [78.2, 5.0], [78.3, 5.0], [78.4, 5.0], [78.5, 5.0], [78.6, 5.0], [78.7, 5.0], [78.8, 5.0], [78.9, 5.0], [79.0, 5.0], [79.1, 5.0], [79.2, 5.0], [79.3, 5.0], [79.4, 5.0], [79.5, 5.0], [79.6, 5.0], [79.7, 5.0], [79.8, 5.0], [79.9, 5.0], [80.0, 5.0], [80.1, 5.0], [80.2, 5.0], [80.3, 5.0], [80.4, 5.0], [80.5, 5.0], [80.6, 5.0], [80.7, 5.0], [80.8, 5.0], [80.9, 5.0], [81.0, 5.0], [81.1, 5.0], [81.2, 5.0], [81.3, 5.0], [81.4, 5.0], [81.5, 5.0], [81.6, 5.0], [81.7, 5.0], [81.8, 5.0], [81.9, 5.0], [82.0, 5.0], [82.1, 5.0], [82.2, 5.0], [82.3, 5.0], [82.4, 5.0], [82.5, 5.0], [82.6, 5.0], [82.7, 6.0], [82.8, 6.0], [82.9, 6.0], [83.0, 6.0], [83.1, 6.0], [83.2, 6.0], [83.3, 6.0], [83.4, 6.0], [83.5, 6.0], [83.6, 6.0], [83.7, 6.0], [83.8, 6.0], [83.9, 6.0], [84.0, 6.0], [84.1, 6.0], [84.2, 6.0], [84.3, 6.0], [84.4, 6.0], [84.5, 6.0], [84.6, 6.0], [84.7, 6.0], [84.8, 6.0], [84.9, 6.0], [85.0, 6.0], [85.1, 6.0], [85.2, 6.0], [85.3, 6.0], [85.4, 6.0], [85.5, 6.0], [85.6, 6.0], [85.7, 6.0], [85.8, 6.0], [85.9, 6.0], [86.0, 6.0], [86.1, 6.0], [86.2, 6.0], [86.3, 6.0], [86.4, 6.0], [86.5, 6.0], [86.6, 6.0], [86.7, 6.0], [86.8, 6.0], [86.9, 6.0], [87.0, 6.0], [87.1, 6.0], [87.2, 6.0], [87.3, 6.0], [87.4, 6.0], [87.5, 6.0], [87.6, 6.0], [87.7, 7.0], [87.8, 7.0], [87.9, 7.0], [88.0, 7.0], [88.1, 7.0], [88.2, 7.0], [88.3, 7.0], [88.4, 7.0], [88.5, 7.0], [88.6, 7.0], [88.7, 7.0], [88.8, 7.0], [88.9, 7.0], [89.0, 7.0], [89.1, 7.0], [89.2, 7.0], [89.3, 7.0], [89.4, 7.0], [89.5, 7.0], [89.6, 7.0], [89.7, 7.0], [89.8, 7.0], [89.9, 7.0], [90.0, 7.0], [90.1, 7.0], [90.2, 7.0], [90.3, 7.0], [90.4, 7.0], [90.5, 7.0], [90.6, 7.0], [90.7, 7.0], [90.8, 7.0], [90.9, 8.0], [91.0, 8.0], [91.1, 8.0], [91.2, 8.0], [91.3, 8.0], [91.4, 8.0], [91.5, 8.0], [91.6, 8.0], [91.7, 8.0], [91.8, 8.0], [91.9, 8.0], [92.0, 8.0], [92.1, 8.0], [92.2, 8.0], [92.3, 8.0], [92.4, 8.0], [92.5, 8.0], [92.6, 8.0], [92.7, 8.0], [92.8, 8.0], [92.9, 8.0], [93.0, 8.0], [93.1, 9.0], [93.2, 9.0], [93.3, 9.0], [93.4, 9.0], [93.5, 9.0], [93.6, 9.0], [93.7, 9.0], [93.8, 9.0], [93.9, 9.0], [94.0, 9.0], [94.1, 9.0], [94.2, 9.0], [94.3, 9.0], [94.4, 9.0], [94.5, 9.0], [94.6, 9.0], [94.7, 9.0], [94.8, 9.0], [94.9, 10.0], [95.0, 10.0], [95.1, 10.0], [95.2, 10.0], [95.3, 10.0], [95.4, 10.0], [95.5, 10.0], [95.6, 10.0], [95.7, 11.0], [95.8, 11.0], [95.9, 11.0], [96.0, 11.0], [96.1, 11.0], [96.2, 11.0], [96.3, 11.0], [96.4, 12.0], [96.5, 12.0], [96.6, 12.0], [96.7, 13.0], [96.8, 13.0], [96.9, 13.0], [97.0, 13.0], [97.1, 13.0], [97.2, 13.0], [97.3, 13.0], [97.4, 14.0], [97.5, 14.0], [97.6, 14.0], [97.7, 14.0], [97.8, 14.0], [97.9, 15.0], [98.0, 15.0], [98.1, 18.0], [98.2, 18.0], [98.3, 18.0], [98.4, 18.0], [98.5, 23.0], [98.6, 23.0], [98.7, 25.0], [98.8, 25.0], [98.9, 28.0], [99.0, 28.0], [99.1, 28.0], [99.2, 28.0], [99.3, 33.0], [99.4, 33.0], [99.5, 33.0], [99.6, 33.0], [99.7, 36.0], [99.8, 36.0], [99.9, 39.0], [100.0, 39.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[0.0, 2.0], [0.1, 2.0], [0.2, 2.0], [0.3, 2.0], [0.4, 2.0], [0.5, 2.0], [0.6, 2.0], [0.7, 2.0], [0.8, 2.0], [0.9, 2.0], [1.0, 2.0], [1.1, 2.0], [1.2, 2.0], [1.3, 2.0], [1.4, 2.0], [1.5, 2.0], [1.6, 2.0], [1.7, 2.0], [1.8, 2.0], [1.9, 2.0], [2.0, 2.0], [2.1, 2.0], [2.2, 2.0], [2.3, 2.0], [2.4, 2.0], [2.5, 2.0], [2.6, 2.0], [2.7, 2.0], [2.8, 2.0], [2.9, 2.0], [3.0, 2.0], [3.1, 2.0], [3.2, 2.0], [3.3, 2.0], [3.4, 2.0], [3.5, 2.0], [3.6, 2.0], [3.7, 2.0], [3.8, 2.0], [3.9, 2.0], [4.0, 2.0], [4.1, 2.0], [4.2, 2.0], [4.3, 2.0], [4.4, 2.0], [4.5, 2.0], [4.6, 2.0], [4.7, 2.0], [4.8, 2.0], [4.9, 2.0], [5.0, 2.0], [5.1, 2.0], [5.2, 2.0], [5.3, 2.0], [5.4, 2.0], [5.5, 2.0], [5.6, 2.0], [5.7, 2.0], [5.8, 2.0], [5.9, 2.0], [6.0, 2.0], [6.1, 2.0], [6.2, 2.0], [6.3, 2.0], [6.4, 2.0], [6.5, 2.0], [6.6, 2.0], [6.7, 2.0], [6.8, 2.0], [6.9, 2.0], [7.0, 2.0], [7.1, 2.0], [7.2, 2.0], [7.3, 2.0], [7.4, 2.0], [7.5, 2.0], [7.6, 2.0], [7.7, 2.0], [7.8, 2.0], [7.9, 2.0], [8.0, 2.0], [8.1, 2.0], [8.2, 2.0], [8.3, 2.0], [8.4, 2.0], [8.5, 2.0], [8.6, 2.0], [8.7, 2.0], [8.8, 2.0], [8.9, 2.0], [9.0, 2.0], [9.1, 2.0], [9.2, 2.0], [9.3, 2.0], [9.4, 2.0], [9.5, 2.0], [9.6, 2.0], [9.7, 2.0], [9.8, 2.0], [9.9, 2.0], [10.0, 2.0], [10.1, 2.0], [10.2, 2.0], [10.3, 2.0], [10.4, 2.0], [10.5, 2.0], [10.6, 2.0], [10.7, 2.0], [10.8, 2.0], [10.9, 2.0], [11.0, 2.0], [11.1, 2.0], [11.2, 2.0], [11.3, 2.0], [11.4, 2.0], [11.5, 2.0], [11.6, 2.0], [11.7, 2.0], [11.8, 2.0], [11.9, 2.0], [12.0, 2.0], [12.1, 2.0], [12.2, 2.0], [12.3, 2.0], [12.4, 2.0], [12.5, 2.0], [12.6, 2.0], [12.7, 2.0], [12.8, 2.0], [12.9, 2.0], [13.0, 2.0], [13.1, 2.0], [13.2, 2.0], [13.3, 2.0], [13.4, 2.0], [13.5, 2.0], [13.6, 2.0], [13.7, 2.0], [13.8, 2.0], [13.9, 2.0], [14.0, 2.0], [14.1, 2.0], [14.2, 2.0], [14.3, 2.0], [14.4, 2.0], [14.5, 2.0], [14.6, 2.0], [14.7, 2.0], [14.8, 2.0], [14.9, 2.0], [15.0, 2.0], [15.1, 2.0], [15.2, 2.0], [15.3, 2.0], [15.4, 2.0], [15.5, 2.0], [15.6, 2.0], [15.7, 2.0], [15.8, 2.0], [15.9, 2.0], [16.0, 2.0], [16.1, 2.0], [16.2, 2.0], [16.3, 2.0], [16.4, 2.0], [16.5, 2.0], [16.6, 2.0], [16.7, 2.0], [16.8, 2.0], [16.9, 2.0], [17.0, 2.0], [17.1, 2.0], [17.2, 2.0], [17.3, 2.0], [17.4, 2.0], [17.5, 2.0], [17.6, 2.0], [17.7, 2.0], [17.8, 2.0], [17.9, 2.0], [18.0, 2.0], [18.1, 2.0], [18.2, 2.0], [18.3, 2.0], [18.4, 2.0], [18.5, 2.0], [18.6, 2.0], [18.7, 2.0], [18.8, 2.0], [18.9, 2.0], [19.0, 2.0], [19.1, 2.0], [19.2, 2.0], [19.3, 2.0], [19.4, 2.0], [19.5, 2.0], [19.6, 2.0], [19.7, 2.0], [19.8, 2.0], [19.9, 2.0], [20.0, 2.0], [20.1, 2.0], [20.2, 2.0], [20.3, 2.0], [20.4, 2.0], [20.5, 2.0], [20.6, 2.0], [20.7, 2.0], [20.8, 2.0], [20.9, 2.0], [21.0, 2.0], [21.1, 2.0], [21.2, 2.0], [21.3, 2.0], [21.4, 2.0], [21.5, 2.0], [21.6, 2.0], [21.7, 2.0], [21.8, 2.0], [21.9, 2.0], [22.0, 2.0], [22.1, 2.0], [22.2, 2.0], [22.3, 2.0], [22.4, 2.0], [22.5, 2.0], [22.6, 2.0], [22.7, 2.0], [22.8, 2.0], [22.9, 2.0], [23.0, 2.0], [23.1, 2.0], [23.2, 2.0], [23.3, 2.0], [23.4, 2.0], [23.5, 2.0], [23.6, 2.0], [23.7, 2.0], [23.8, 2.0], [23.9, 2.0], [24.0, 2.0], [24.1, 2.0], [24.2, 2.0], [24.3, 2.0], [24.4, 2.0], [24.5, 2.0], [24.6, 2.0], [24.7, 2.0], [24.8, 2.0], [24.9, 2.0], [25.0, 2.0], [25.1, 2.0], [25.2, 2.0], [25.3, 2.0], [25.4, 2.0], [25.5, 2.0], [25.6, 2.0], [25.7, 2.0], [25.8, 2.0], [25.9, 2.0], [26.0, 2.0], [26.1, 2.0], [26.2, 2.0], [26.3, 2.0], [26.4, 2.0], [26.5, 2.0], [26.6, 2.0], [26.7, 2.0], [26.8, 2.0], [26.9, 2.0], [27.0, 2.0], [27.1, 2.0], [27.2, 2.0], [27.3, 2.0], [27.4, 2.0], [27.5, 2.0], [27.6, 2.0], [27.7, 2.0], [27.8, 2.0], [27.9, 2.0], [28.0, 2.0], [28.1, 2.0], [28.2, 2.0], [28.3, 2.0], [28.4, 2.0], [28.5, 2.0], [28.6, 2.0], [28.7, 2.0], [28.8, 2.0], [28.9, 2.0], [29.0, 2.0], [29.1, 2.0], [29.2, 2.0], [29.3, 2.0], [29.4, 2.0], [29.5, 2.0], [29.6, 2.0], [29.7, 2.0], [29.8, 2.0], [29.9, 2.0], [30.0, 2.0], [30.1, 2.0], [30.2, 2.0], [30.3, 2.0], [30.4, 2.0], [30.5, 2.0], [30.6, 2.0], [30.7, 2.0], [30.8, 2.0], [30.9, 2.0], [31.0, 2.0], [31.1, 2.0], [31.2, 2.0], [31.3, 2.0], [31.4, 2.0], [31.5, 2.0], [31.6, 2.0], [31.7, 2.0], [31.8, 2.0], [31.9, 2.0], [32.0, 2.0], [32.1, 2.0], [32.2, 2.0], [32.3, 2.0], [32.4, 2.0], [32.5, 2.0], [32.6, 2.0], [32.7, 2.0], [32.8, 2.0], [32.9, 2.0], [33.0, 2.0], [33.1, 2.0], [33.2, 2.0], [33.3, 2.0], [33.4, 2.0], [33.5, 2.0], [33.6, 2.0], [33.7, 2.0], [33.8, 2.0], [33.9, 2.0], [34.0, 2.0], [34.1, 2.0], [34.2, 2.0], [34.3, 2.0], [34.4, 2.0], [34.5, 2.0], [34.6, 2.0], [34.7, 2.0], [34.8, 2.0], [34.9, 2.0], [35.0, 2.0], [35.1, 2.0], [35.2, 2.0], [35.3, 2.0], [35.4, 2.0], [35.5, 2.0], [35.6, 2.0], [35.7, 2.0], [35.8, 2.0], [35.9, 2.0], [36.0, 2.0], [36.1, 2.0], [36.2, 2.0], [36.3, 2.0], [36.4, 2.0], [36.5, 2.0], [36.6, 2.0], [36.7, 2.0], [36.8, 2.0], [36.9, 2.0], [37.0, 2.0], [37.1, 2.0], [37.2, 2.0], [37.3, 2.0], [37.4, 2.0], [37.5, 2.0], [37.6, 2.0], [37.7, 2.0], [37.8, 2.0], [37.9, 2.0], [38.0, 2.0], [38.1, 2.0], [38.2, 2.0], [38.3, 2.0], [38.4, 3.0], [38.5, 3.0], [38.6, 3.0], [38.7, 3.0], [38.8, 3.0], [38.9, 3.0], [39.0, 3.0], [39.1, 3.0], [39.2, 3.0], [39.3, 3.0], [39.4, 3.0], [39.5, 3.0], [39.6, 3.0], [39.7, 3.0], [39.8, 3.0], [39.9, 3.0], [40.0, 3.0], [40.1, 3.0], [40.2, 3.0], [40.3, 3.0], [40.4, 3.0], [40.5, 3.0], [40.6, 3.0], [40.7, 3.0], [40.8, 3.0], [40.9, 3.0], [41.0, 3.0], [41.1, 3.0], [41.2, 3.0], [41.3, 3.0], [41.4, 3.0], [41.5, 3.0], [41.6, 3.0], [41.7, 3.0], [41.8, 3.0], [41.9, 3.0], [42.0, 3.0], [42.1, 3.0], [42.2, 3.0], [42.3, 3.0], [42.4, 3.0], [42.5, 3.0], [42.6, 3.0], [42.7, 3.0], [42.8, 3.0], [42.9, 3.0], [43.0, 3.0], [43.1, 3.0], [43.2, 3.0], [43.3, 3.0], [43.4, 3.0], [43.5, 3.0], [43.6, 3.0], [43.7, 3.0], [43.8, 3.0], [43.9, 3.0], [44.0, 3.0], [44.1, 3.0], [44.2, 3.0], [44.3, 3.0], [44.4, 3.0], [44.5, 3.0], [44.6, 3.0], [44.7, 3.0], [44.8, 3.0], [44.9, 3.0], [45.0, 3.0], [45.1, 3.0], [45.2, 3.0], [45.3, 3.0], [45.4, 3.0], [45.5, 3.0], [45.6, 3.0], [45.7, 3.0], [45.8, 3.0], [45.9, 3.0], [46.0, 3.0], [46.1, 3.0], [46.2, 3.0], [46.3, 3.0], [46.4, 3.0], [46.5, 3.0], [46.6, 3.0], [46.7, 3.0], [46.8, 3.0], [46.9, 3.0], [47.0, 3.0], [47.1, 3.0], [47.2, 3.0], [47.3, 3.0], [47.4, 3.0], [47.5, 3.0], [47.6, 3.0], [47.7, 3.0], [47.8, 3.0], [47.9, 3.0], [48.0, 3.0], [48.1, 3.0], [48.2, 3.0], [48.3, 3.0], [48.4, 3.0], [48.5, 3.0], [48.6, 3.0], [48.7, 3.0], [48.8, 3.0], [48.9, 3.0], [49.0, 3.0], [49.1, 3.0], [49.2, 3.0], [49.3, 3.0], [49.4, 3.0], [49.5, 3.0], [49.6, 3.0], [49.7, 3.0], [49.8, 3.0], [49.9, 3.0], [50.0, 3.0], [50.1, 3.0], [50.2, 3.0], [50.3, 3.0], [50.4, 3.0], [50.5, 3.0], [50.6, 3.0], [50.7, 3.0], [50.8, 3.0], [50.9, 3.0], [51.0, 3.0], [51.1, 3.0], [51.2, 3.0], [51.3, 3.0], [51.4, 3.0], [51.5, 3.0], [51.6, 3.0], [51.7, 3.0], [51.8, 3.0], [51.9, 3.0], [52.0, 3.0], [52.1, 3.0], [52.2, 3.0], [52.3, 3.0], [52.4, 3.0], [52.5, 3.0], [52.6, 3.0], [52.7, 3.0], [52.8, 3.0], [52.9, 3.0], [53.0, 3.0], [53.1, 3.0], [53.2, 3.0], [53.3, 3.0], [53.4, 3.0], [53.5, 3.0], [53.6, 3.0], [53.7, 3.0], [53.8, 3.0], [53.9, 3.0], [54.0, 3.0], [54.1, 3.0], [54.2, 3.0], [54.3, 3.0], [54.4, 3.0], [54.5, 3.0], [54.6, 3.0], [54.7, 3.0], [54.8, 3.0], [54.9, 3.0], [55.0, 3.0], [55.1, 3.0], [55.2, 3.0], [55.3, 3.0], [55.4, 3.0], [55.5, 3.0], [55.6, 3.0], [55.7, 3.0], [55.8, 3.0], [55.9, 3.0], [56.0, 3.0], [56.1, 3.0], [56.2, 3.0], [56.3, 3.0], [56.4, 3.0], [56.5, 3.0], [56.6, 3.0], [56.7, 3.0], [56.8, 3.0], [56.9, 3.0], [57.0, 3.0], [57.1, 3.0], [57.2, 3.0], [57.3, 3.0], [57.4, 3.0], [57.5, 3.0], [57.6, 3.0], [57.7, 3.0], [57.8, 3.0], [57.9, 3.0], [58.0, 3.0], [58.1, 3.0], [58.2, 3.0], [58.3, 3.0], [58.4, 3.0], [58.5, 3.0], [58.6, 3.0], [58.7, 3.0], [58.8, 3.0], [58.9, 3.0], [59.0, 3.0], [59.1, 3.0], [59.2, 3.0], [59.3, 3.0], [59.4, 3.0], [59.5, 3.0], [59.6, 3.0], [59.7, 3.0], [59.8, 3.0], [59.9, 3.0], [60.0, 3.0], [60.1, 3.0], [60.2, 3.0], [60.3, 3.0], [60.4, 3.0], [60.5, 3.0], [60.6, 3.0], [60.7, 3.0], [60.8, 3.0], [60.9, 3.0], [61.0, 3.0], [61.1, 3.0], [61.2, 3.0], [61.3, 3.0], [61.4, 3.0], [61.5, 3.0], [61.6, 3.0], [61.7, 3.0], [61.8, 3.0], [61.9, 3.0], [62.0, 3.0], [62.1, 3.0], [62.2, 3.0], [62.3, 3.0], [62.4, 3.0], [62.5, 3.0], [62.6, 3.0], [62.7, 3.0], [62.8, 3.0], [62.9, 3.0], [63.0, 3.0], [63.1, 3.0], [63.2, 3.0], [63.3, 3.0], [63.4, 3.0], [63.5, 3.0], [63.6, 3.0], [63.7, 3.0], [63.8, 3.0], [63.9, 3.0], [64.0, 3.0], [64.1, 3.0], [64.2, 3.0], [64.3, 3.0], [64.4, 3.0], [64.5, 3.0], [64.6, 3.0], [64.7, 3.0], [64.8, 3.0], [64.9, 3.0], [65.0, 3.0], [65.1, 3.0], [65.2, 3.0], [65.3, 3.0], [65.4, 3.0], [65.5, 3.0], [65.6, 3.0], [65.7, 3.0], [65.8, 3.0], [65.9, 3.0], [66.0, 3.0], [66.1, 3.0], [66.2, 3.0], [66.3, 3.0], [66.4, 3.0], [66.5, 3.0], [66.6, 3.0], [66.7, 3.0], [66.8, 3.0], [66.9, 3.0], [67.0, 3.0], [67.1, 3.0], [67.2, 3.0], [67.3, 3.0], [67.4, 3.0], [67.5, 3.0], [67.6, 3.0], [67.7, 3.0], [67.8, 3.0], [67.9, 3.0], [68.0, 3.0], [68.1, 3.0], [68.2, 3.0], [68.3, 3.0], [68.4, 3.0], [68.5, 3.0], [68.6, 3.0], [68.7, 3.0], [68.8, 3.0], [68.9, 3.0], [69.0, 3.0], [69.1, 3.0], [69.2, 3.0], [69.3, 3.0], [69.4, 3.0], [69.5, 3.0], [69.6, 3.0], [69.7, 3.0], [69.8, 3.0], [69.9, 3.0], [70.0, 3.0], [70.1, 3.0], [70.2, 3.0], [70.3, 3.0], [70.4, 3.0], [70.5, 3.0], [70.6, 3.0], [70.7, 3.0], [70.8, 3.0], [70.9, 3.0], [71.0, 3.0], [71.1, 3.0], [71.2, 3.0], [71.3, 3.0], [71.4, 3.0], [71.5, 3.0], [71.6, 3.0], [71.7, 3.0], [71.8, 3.0], [71.9, 3.0], [72.0, 3.0], [72.1, 3.0], [72.2, 3.0], [72.3, 3.0], [72.4, 3.0], [72.5, 3.0], [72.6, 3.0], [72.7, 3.0], [72.8, 3.0], [72.9, 3.0], [73.0, 3.0], [73.1, 3.0], [73.2, 3.0], [73.3, 3.0], [73.4, 3.0], [73.5, 3.0], [73.6, 3.0], [73.7, 3.0], [73.8, 3.0], [73.9, 3.0], [74.0, 3.0], [74.1, 3.0], [74.2, 3.0], [74.3, 3.0], [74.4, 3.0], [74.5, 3.0], [74.6, 3.0], [74.7, 3.0], [74.8, 3.0], [74.9, 3.0], [75.0, 4.0], [75.1, 4.0], [75.2, 4.0], [75.3, 4.0], [75.4, 4.0], [75.5, 4.0], [75.6, 4.0], [75.7, 4.0], [75.8, 4.0], [75.9, 4.0], [76.0, 4.0], [76.1, 4.0], [76.2, 4.0], [76.3, 4.0], [76.4, 4.0], [76.5, 4.0], [76.6, 4.0], [76.7, 4.0], [76.8, 4.0], [76.9, 4.0], [77.0, 4.0], [77.1, 4.0], [77.2, 4.0], [77.3, 4.0], [77.4, 4.0], [77.5, 4.0], [77.6, 4.0], [77.7, 4.0], [77.8, 4.0], [77.9, 4.0], [78.0, 4.0], [78.1, 4.0], [78.2, 4.0], [78.3, 4.0], [78.4, 4.0], [78.5, 4.0], [78.6, 4.0], [78.7, 4.0], [78.8, 4.0], [78.9, 4.0], [79.0, 4.0], [79.1, 4.0], [79.2, 4.0], [79.3, 4.0], [79.4, 4.0], [79.5, 4.0], [79.6, 4.0], [79.7, 4.0], [79.8, 4.0], [79.9, 4.0], [80.0, 4.0], [80.1, 4.0], [80.2, 4.0], [80.3, 4.0], [80.4, 4.0], [80.5, 4.0], [80.6, 4.0], [80.7, 4.0], [80.8, 4.0], [80.9, 4.0], [81.0, 4.0], [81.1, 4.0], [81.2, 4.0], [81.3, 4.0], [81.4, 4.0], [81.5, 4.0], [81.6, 4.0], [81.7, 4.0], [81.8, 4.0], [81.9, 4.0], [82.0, 4.0], [82.1, 4.0], [82.2, 4.0], [82.3, 4.0], [82.4, 4.0], [82.5, 4.0], [82.6, 4.0], [82.7, 4.0], [82.8, 4.0], [82.9, 4.0], [83.0, 4.0], [83.1, 4.0], [83.2, 4.0], [83.3, 4.0], [83.4, 4.0], [83.5, 4.0], [83.6, 4.0], [83.7, 4.0], [83.8, 5.0], [83.9, 5.0], [84.0, 5.0], [84.1, 5.0], [84.2, 5.0], [84.3, 5.0], [84.4, 5.0], [84.5, 5.0], [84.6, 5.0], [84.7, 5.0], [84.8, 5.0], [84.9, 5.0], [85.0, 5.0], [85.1, 5.0], [85.2, 5.0], [85.3, 5.0], [85.4, 5.0], [85.5, 5.0], [85.6, 5.0], [85.7, 5.0], [85.8, 5.0], [85.9, 5.0], [86.0, 5.0], [86.1, 5.0], [86.2, 5.0], [86.3, 5.0], [86.4, 5.0], [86.5, 5.0], [86.6, 5.0], [86.7, 5.0], [86.8, 5.0], [86.9, 5.0], [87.0, 5.0], [87.1, 5.0], [87.2, 5.0], [87.3, 5.0], [87.4, 5.0], [87.5, 5.0], [87.6, 5.0], [87.7, 5.0], [87.8, 5.0], [87.9, 5.0], [88.0, 5.0], [88.1, 5.0], [88.2, 5.0], [88.3, 5.0], [88.4, 5.0], [88.5, 5.0], [88.6, 5.0], [88.7, 5.0], [88.8, 5.0], [88.9, 5.0], [89.0, 5.0], [89.1, 5.0], [89.2, 5.0], [89.3, 5.0], [89.4, 5.0], [89.5, 5.0], [89.6, 6.0], [89.7, 6.0], [89.8, 6.0], [89.9, 6.0], [90.0, 6.0], [90.1, 6.0], [90.2, 6.0], [90.3, 6.0], [90.4, 6.0], [90.5, 6.0], [90.6, 6.0], [90.7, 6.0], [90.8, 6.0], [90.9, 6.0], [91.0, 6.0], [91.1, 6.0], [91.2, 6.0], [91.3, 6.0], [91.4, 6.0], [91.5, 6.0], [91.6, 6.0], [91.7, 6.0], [91.8, 6.0], [91.9, 6.0], [92.0, 6.0], [92.1, 6.0], [92.2, 6.0], [92.3, 6.0], [92.4, 6.0], [92.5, 6.0], [92.6, 6.0], [92.7, 6.0], [92.8, 6.0], [92.9, 6.0], [93.0, 7.0], [93.1, 7.0], [93.2, 7.0], [93.3, 7.0], [93.4, 7.0], [93.5, 7.0], [93.6, 7.0], [93.7, 7.0], [93.8, 7.0], [93.9, 7.0], [94.0, 7.0], [94.1, 7.0], [94.2, 7.0], [94.3, 7.0], [94.4, 7.0], [94.5, 7.0], [94.6, 7.0], [94.7, 7.0], [94.8, 7.0], [94.9, 7.0], [95.0, 7.0], [95.1, 7.0], [95.2, 8.0], [95.3, 8.0], [95.4, 8.0], [95.5, 8.0], [95.6, 8.0], [95.7, 8.0], [95.8, 8.0], [95.9, 8.0], [96.0, 8.0], [96.1, 8.0], [96.2, 8.0], [96.3, 8.0], [96.4, 8.0], [96.5, 8.0], [96.6, 8.0], [96.7, 8.0], [96.8, 9.0], [96.9, 9.0], [97.0, 9.0], [97.1, 9.0], [97.2, 10.0], [97.3, 10.0], [97.4, 12.0], [97.5, 12.0], [97.6, 12.0], [97.7, 19.0], [97.8, 19.0], [97.9, 19.0], [98.0, 19.0], [98.1, 21.0], [98.2, 21.0], [98.3, 21.0], [98.4, 21.0], [98.5, 22.0], [98.6, 22.0], [98.7, 23.0], [98.8, 23.0], [98.9, 24.0], [99.0, 24.0], [99.1, 26.0], [99.2, 26.0], [99.3, 28.0], [99.4, 28.0], [99.5, 28.0], [99.6, 28.0], [99.7, 29.0], [99.8, 29.0], [99.9, 29.0], [100.0, 29.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[0.0, 29.0], [0.1, 29.0], [0.2, 30.0], [0.3, 30.0], [0.4, 30.0], [0.5, 30.0], [0.6, 30.0], [0.7, 30.0], [0.8, 31.0], [0.9, 31.0], [1.0, 31.0], [1.1, 31.0], [1.2, 31.0], [1.3, 31.0], [1.4, 31.0], [1.5, 31.0], [1.6, 31.0], [1.7, 31.0], [1.8, 31.0], [1.9, 31.0], [2.0, 31.0], [2.1, 31.0], [2.2, 31.0], [2.3, 31.0], [2.4, 31.0], [2.5, 31.0], [2.6, 31.0], [2.7, 31.0], [2.8, 32.0], [2.9, 32.0], [3.0, 32.0], [3.1, 32.0], [3.2, 32.0], [3.3, 32.0], [3.4, 32.0], [3.5, 32.0], [3.6, 32.0], [3.7, 32.0], [3.8, 32.0], [3.9, 32.0], [4.0, 32.0], [4.1, 32.0], [4.2, 32.0], [4.3, 32.0], [4.4, 32.0], [4.5, 32.0], [4.6, 32.0], [4.7, 32.0], [4.8, 32.0], [4.9, 32.0], [5.0, 32.0], [5.1, 32.0], [5.2, 32.0], [5.3, 32.0], [5.4, 32.0], [5.5, 32.0], [5.6, 32.0], [5.7, 32.0], [5.8, 32.0], [5.9, 32.0], [6.0, 32.0], [6.1, 32.0], [6.2, 32.0], [6.3, 32.0], [6.4, 32.0], [6.5, 32.0], [6.6, 32.0], [6.7, 32.0], [6.8, 32.0], [6.9, 32.0], [7.0, 32.0], [7.1, 33.0], [7.2, 33.0], [7.3, 33.0], [7.4, 33.0], [7.5, 33.0], [7.6, 33.0], [7.7, 33.0], [7.8, 33.0], [7.9, 33.0], [8.0, 33.0], [8.1, 33.0], [8.2, 33.0], [8.3, 33.0], [8.4, 33.0], [8.5, 33.0], [8.6, 33.0], [8.7, 33.0], [8.8, 33.0], [8.9, 33.0], [9.0, 33.0], [9.1, 33.0], [9.2, 33.0], [9.3, 33.0], [9.4, 33.0], [9.5, 33.0], [9.6, 33.0], [9.7, 33.0], [9.8, 33.0], [9.9, 33.0], [10.0, 33.0], [10.1, 33.0], [10.2, 33.0], [10.3, 33.0], [10.4, 33.0], [10.5, 33.0], [10.6, 33.0], [10.7, 33.0], [10.8, 33.0], [10.9, 33.0], [11.0, 33.0], [11.1, 33.0], [11.2, 33.0], [11.3, 33.0], [11.4, 33.0], [11.5, 33.0], [11.6, 33.0], [11.7, 33.0], [11.8, 33.0], [11.9, 33.0], [12.0, 33.0], [12.1, 33.0], [12.2, 33.0], [12.3, 33.0], [12.4, 33.0], [12.5, 33.0], [12.6, 33.0], [12.7, 33.0], [12.8, 33.0], [12.9, 33.0], [13.0, 33.0], [13.1, 33.0], [13.2, 33.0], [13.3, 33.0], [13.4, 33.0], [13.5, 33.0], [13.6, 33.0], [13.7, 33.0], [13.8, 33.0], [13.9, 33.0], [14.0, 33.0], [14.1, 33.0], [14.2, 33.0], [14.3, 33.0], [14.4, 33.0], [14.5, 33.0], [14.6, 33.0], [14.7, 33.0], [14.8, 33.0], [14.9, 33.0], [15.0, 33.0], [15.1, 33.0], [15.2, 33.0], [15.3, 33.0], [15.4, 33.0], [15.5, 34.0], [15.6, 34.0], [15.7, 34.0], [15.8, 34.0], [15.9, 34.0], [16.0, 34.0], [16.1, 34.0], [16.2, 34.0], [16.3, 34.0], [16.4, 34.0], [16.5, 34.0], [16.6, 34.0], [16.7, 34.0], [16.8, 34.0], [16.9, 34.0], [17.0, 34.0], [17.1, 34.0], [17.2, 34.0], [17.3, 34.0], [17.4, 34.0], [17.5, 34.0], [17.6, 34.0], [17.7, 34.0], [17.8, 34.0], [17.9, 34.0], [18.0, 34.0], [18.1, 34.0], [18.2, 34.0], [18.3, 34.0], [18.4, 34.0], [18.5, 34.0], [18.6, 34.0], [18.7, 34.0], [18.8, 34.0], [18.9, 34.0], [19.0, 34.0], [19.1, 34.0], [19.2, 34.0], [19.3, 34.0], [19.4, 34.0], [19.5, 34.0], [19.6, 34.0], [19.7, 34.0], [19.8, 34.0], [19.9, 34.0], [20.0, 34.0], [20.1, 34.0], [20.2, 34.0], [20.3, 34.0], [20.4, 34.0], [20.5, 34.0], [20.6, 34.0], [20.7, 34.0], [20.8, 34.0], [20.9, 34.0], [21.0, 34.0], [21.1, 34.0], [21.2, 34.0], [21.3, 34.0], [21.4, 34.0], [21.5, 34.0], [21.6, 34.0], [21.7, 34.0], [21.8, 34.0], [21.9, 34.0], [22.0, 34.0], [22.1, 34.0], [22.2, 34.0], [22.3, 34.0], [22.4, 34.0], [22.5, 34.0], [22.6, 34.0], [22.7, 34.0], [22.8, 34.0], [22.9, 34.0], [23.0, 34.0], [23.1, 34.0], [23.2, 34.0], [23.3, 34.0], [23.4, 34.0], [23.5, 34.0], [23.6, 34.0], [23.7, 34.0], [23.8, 34.0], [23.9, 34.0], [24.0, 34.0], [24.1, 34.0], [24.2, 34.0], [24.3, 34.0], [24.4, 34.0], [24.5, 34.0], [24.6, 34.0], [24.7, 34.0], [24.8, 34.0], [24.9, 34.0], [25.0, 34.0], [25.1, 34.0], [25.2, 34.0], [25.3, 34.0], [25.4, 35.0], [25.5, 35.0], [25.6, 35.0], [25.7, 35.0], [25.8, 35.0], [25.9, 35.0], [26.0, 35.0], [26.1, 35.0], [26.2, 35.0], [26.3, 35.0], [26.4, 35.0], [26.5, 35.0], [26.6, 35.0], [26.7, 35.0], [26.8, 35.0], [26.9, 35.0], [27.0, 35.0], [27.1, 35.0], [27.2, 35.0], [27.3, 35.0], [27.4, 35.0], [27.5, 35.0], [27.6, 35.0], [27.7, 35.0], [27.8, 35.0], [27.9, 35.0], [28.0, 35.0], [28.1, 35.0], [28.2, 35.0], [28.3, 35.0], [28.4, 35.0], [28.5, 35.0], [28.6, 35.0], [28.7, 35.0], [28.8, 35.0], [28.9, 35.0], [29.0, 35.0], [29.1, 35.0], [29.2, 35.0], [29.3, 35.0], [29.4, 35.0], [29.5, 35.0], [29.6, 35.0], [29.7, 35.0], [29.8, 35.0], [29.9, 35.0], [30.0, 35.0], [30.1, 35.0], [30.2, 35.0], [30.3, 35.0], [30.4, 35.0], [30.5, 35.0], [30.6, 35.0], [30.7, 35.0], [30.8, 35.0], [30.9, 35.0], [31.0, 35.0], [31.1, 35.0], [31.2, 35.0], [31.3, 35.0], [31.4, 35.0], [31.5, 35.0], [31.6, 35.0], [31.7, 35.0], [31.8, 35.0], [31.9, 35.0], [32.0, 35.0], [32.1, 35.0], [32.2, 35.0], [32.3, 35.0], [32.4, 35.0], [32.5, 35.0], [32.6, 35.0], [32.7, 35.0], [32.8, 35.0], [32.9, 35.0], [33.0, 35.0], [33.1, 35.0], [33.2, 35.0], [33.3, 35.0], [33.4, 35.0], [33.5, 35.0], [33.6, 35.0], [33.7, 35.0], [33.8, 35.0], [33.9, 35.0], [34.0, 35.0], [34.1, 35.0], [34.2, 35.0], [34.3, 35.0], [34.4, 35.0], [34.5, 35.0], [34.6, 35.0], [34.7, 35.0], [34.8, 35.0], [34.9, 35.0], [35.0, 35.0], [35.1, 35.0], [35.2, 35.0], [35.3, 35.0], [35.4, 35.0], [35.5, 35.0], [35.6, 35.0], [35.7, 35.0], [35.8, 35.0], [35.9, 35.0], [36.0, 35.0], [36.1, 35.0], [36.2, 35.0], [36.3, 35.0], [36.4, 35.0], [36.5, 35.0], [36.6, 35.0], [36.7, 35.0], [36.8, 35.0], [36.9, 35.0], [37.0, 35.0], [37.1, 35.0], [37.2, 35.0], [37.3, 35.0], [37.4, 35.0], [37.5, 35.0], [37.6, 35.0], [37.7, 35.0], [37.8, 35.0], [37.9, 35.0], [38.0, 35.0], [38.1, 35.0], [38.2, 35.0], [38.3, 35.0], [38.4, 35.0], [38.5, 35.0], [38.6, 35.0], [38.7, 35.0], [38.8, 35.0], [38.9, 35.0], [39.0, 35.0], [39.1, 35.0], [39.2, 35.0], [39.3, 35.0], [39.4, 35.0], [39.5, 35.0], [39.6, 35.0], [39.7, 35.0], [39.8, 35.0], [39.9, 35.0], [40.0, 35.0], [40.1, 35.0], [40.2, 35.0], [40.3, 35.0], [40.4, 35.0], [40.5, 35.0], [40.6, 35.0], [40.7, 35.0], [40.8, 35.0], [40.9, 35.0], [41.0, 35.0], [41.1, 35.0], [41.2, 35.0], [41.3, 35.0], [41.4, 35.0], [41.5, 35.0], [41.6, 35.0], [41.7, 36.0], [41.8, 36.0], [41.9, 36.0], [42.0, 36.0], [42.1, 36.0], [42.2, 36.0], [42.3, 36.0], [42.4, 36.0], [42.5, 36.0], [42.6, 36.0], [42.7, 36.0], [42.8, 36.0], [42.9, 36.0], [43.0, 36.0], [43.1, 36.0], [43.2, 36.0], [43.3, 36.0], [43.4, 36.0], [43.5, 36.0], [43.6, 36.0], [43.7, 36.0], [43.8, 36.0], [43.9, 36.0], [44.0, 36.0], [44.1, 36.0], [44.2, 36.0], [44.3, 36.0], [44.4, 36.0], [44.5, 36.0], [44.6, 36.0], [44.7, 36.0], [44.8, 36.0], [44.9, 36.0], [45.0, 36.0], [45.1, 36.0], [45.2, 36.0], [45.3, 36.0], [45.4, 36.0], [45.5, 36.0], [45.6, 36.0], [45.7, 36.0], [45.8, 36.0], [45.9, 36.0], [46.0, 36.0], [46.1, 36.0], [46.2, 36.0], [46.3, 36.0], [46.4, 36.0], [46.5, 36.0], [46.6, 36.0], [46.7, 36.0], [46.8, 36.0], [46.9, 36.0], [47.0, 36.0], [47.1, 36.0], [47.2, 36.0], [47.3, 36.0], [47.4, 36.0], [47.5, 36.0], [47.6, 36.0], [47.7, 36.0], [47.8, 36.0], [47.9, 36.0], [48.0, 36.0], [48.1, 36.0], [48.2, 36.0], [48.3, 36.0], [48.4, 36.0], [48.5, 36.0], [48.6, 36.0], [48.7, 36.0], [48.8, 36.0], [48.9, 36.0], [49.0, 36.0], [49.1, 36.0], [49.2, 36.0], [49.3, 36.0], [49.4, 36.0], [49.5, 36.0], [49.6, 36.0], [49.7, 36.0], [49.8, 36.0], [49.9, 36.0], [50.0, 36.0], [50.1, 36.0], [50.2, 36.0], [50.3, 36.0], [50.4, 36.0], [50.5, 36.0], [50.6, 37.0], [50.7, 37.0], [50.8, 37.0], [50.9, 37.0], [51.0, 37.0], [51.1, 37.0], [51.2, 37.0], [51.3, 37.0], [51.4, 37.0], [51.5, 37.0], [51.6, 37.0], [51.7, 37.0], [51.8, 37.0], [51.9, 37.0], [52.0, 37.0], [52.1, 37.0], [52.2, 37.0], [52.3, 37.0], [52.4, 37.0], [52.5, 37.0], [52.6, 37.0], [52.7, 37.0], [52.8, 37.0], [52.9, 37.0], [53.0, 37.0], [53.1, 37.0], [53.2, 37.0], [53.3, 37.0], [53.4, 37.0], [53.5, 37.0], [53.6, 37.0], [53.7, 37.0], [53.8, 37.0], [53.9, 37.0], [54.0, 37.0], [54.1, 37.0], [54.2, 37.0], [54.3, 37.0], [54.4, 37.0], [54.5, 37.0], [54.6, 37.0], [54.7, 37.0], [54.8, 37.0], [54.9, 37.0], [55.0, 37.0], [55.1, 37.0], [55.2, 37.0], [55.3, 37.0], [55.4, 37.0], [55.5, 37.0], [55.6, 37.0], [55.7, 37.0], [55.8, 37.0], [55.9, 37.0], [56.0, 37.0], [56.1, 37.0], [56.2, 37.0], [56.3, 37.0], [56.4, 37.0], [56.5, 37.0], [56.6, 37.0], [56.7, 37.0], [56.8, 37.0], [56.9, 37.0], [57.0, 37.0], [57.1, 37.0], [57.2, 37.0], [57.3, 37.0], [57.4, 37.0], [57.5, 37.0], [57.6, 37.0], [57.7, 37.0], [57.8, 37.0], [57.9, 37.0], [58.0, 37.0], [58.1, 37.0], [58.2, 37.0], [58.3, 37.0], [58.4, 37.0], [58.5, 37.0], [58.6, 37.0], [58.7, 37.0], [58.8, 37.0], [58.9, 37.0], [59.0, 37.0], [59.1, 37.0], [59.2, 37.0], [59.3, 37.0], [59.4, 37.0], [59.5, 37.0], [59.6, 37.0], [59.7, 37.0], [59.8, 37.0], [59.9, 37.0], [60.0, 37.0], [60.1, 37.0], [60.2, 37.0], [60.3, 37.0], [60.4, 37.0], [60.5, 37.0], [60.6, 37.0], [60.7, 37.0], [60.8, 38.0], [60.9, 38.0], [61.0, 38.0], [61.1, 38.0], [61.2, 38.0], [61.3, 38.0], [61.4, 38.0], [61.5, 38.0], [61.6, 38.0], [61.7, 38.0], [61.8, 38.0], [61.9, 38.0], [62.0, 38.0], [62.1, 38.0], [62.2, 38.0], [62.3, 38.0], [62.4, 38.0], [62.5, 38.0], [62.6, 38.0], [62.7, 38.0], [62.8, 38.0], [62.9, 38.0], [63.0, 38.0], [63.1, 38.0], [63.2, 38.0], [63.3, 38.0], [63.4, 38.0], [63.5, 38.0], [63.6, 38.0], [63.7, 38.0], [63.8, 38.0], [63.9, 38.0], [64.0, 38.0], [64.1, 38.0], [64.2, 38.0], [64.3, 38.0], [64.4, 38.0], [64.5, 38.0], [64.6, 38.0], [64.7, 38.0], [64.8, 38.0], [64.9, 38.0], [65.0, 38.0], [65.1, 38.0], [65.2, 38.0], [65.3, 39.0], [65.4, 39.0], [65.5, 39.0], [65.6, 39.0], [65.7, 39.0], [65.8, 39.0], [65.9, 39.0], [66.0, 39.0], [66.1, 39.0], [66.2, 39.0], [66.3, 39.0], [66.4, 39.0], [66.5, 39.0], [66.6, 39.0], [66.7, 39.0], [66.8, 39.0], [66.9, 39.0], [67.0, 39.0], [67.1, 39.0], [67.2, 39.0], [67.3, 39.0], [67.4, 39.0], [67.5, 39.0], [67.6, 40.0], [67.7, 40.0], [67.8, 40.0], [67.9, 40.0], [68.0, 40.0], [68.1, 40.0], [68.2, 40.0], [68.3, 40.0], [68.4, 40.0], [68.5, 40.0], [68.6, 40.0], [68.7, 40.0], [68.8, 40.0], [68.9, 40.0], [69.0, 40.0], [69.1, 40.0], [69.2, 40.0], [69.3, 40.0], [69.4, 40.0], [69.5, 40.0], [69.6, 40.0], [69.7, 40.0], [69.8, 41.0], [69.9, 41.0], [70.0, 41.0], [70.1, 41.0], [70.2, 41.0], [70.3, 41.0], [70.4, 41.0], [70.5, 41.0], [70.6, 41.0], [70.7, 41.0], [70.8, 41.0], [70.9, 41.0], [71.0, 41.0], [71.1, 41.0], [71.2, 41.0], [71.3, 41.0], [71.4, 41.0], [71.5, 41.0], [71.6, 41.0], [71.7, 41.0], [71.8, 41.0], [71.9, 42.0], [72.0, 42.0], [72.1, 42.0], [72.2, 42.0], [72.3, 42.0], [72.4, 42.0], [72.5, 42.0], [72.6, 42.0], [72.7, 42.0], [72.8, 42.0], [72.9, 42.0], [73.0, 43.0], [73.1, 43.0], [73.2, 43.0], [73.3, 43.0], [73.4, 43.0], [73.5, 43.0], [73.6, 43.0], [73.7, 43.0], [73.8, 43.0], [73.9, 44.0], [74.0, 44.0], [74.1, 44.0], [74.2, 44.0], [74.3, 44.0], [74.4, 44.0], [74.5, 44.0], [74.6, 44.0], [74.7, 44.0], [74.8, 44.0], [74.9, 44.0], [75.0, 44.0], [75.1, 45.0], [75.2, 45.0], [75.3, 45.0], [75.4, 45.0], [75.5, 45.0], [75.6, 45.0], [75.7, 45.0], [75.8, 45.0], [75.9, 45.0], [76.0, 45.0], [76.1, 45.0], [76.2, 45.0], [76.3, 45.0], [76.4, 46.0], [76.5, 46.0], [76.6, 46.0], [76.7, 46.0], [76.8, 46.0], [76.9, 46.0], [77.0, 46.0], [77.1, 46.0], [77.2, 46.0], [77.3, 46.0], [77.4, 47.0], [77.5, 47.0], [77.6, 47.0], [77.7, 47.0], [77.8, 47.0], [77.9, 48.0], [78.0, 48.0], [78.1, 48.0], [78.2, 48.0], [78.3, 48.0], [78.4, 48.0], [78.5, 48.0], [78.6, 48.0], [78.7, 48.0], [78.8, 48.0], [78.9, 48.0], [79.0, 48.0], [79.1, 49.0], [79.2, 49.0], [79.3, 49.0], [79.4, 49.0], [79.5, 49.0], [79.6, 49.0], [79.7, 49.0], [79.8, 49.0], [79.9, 49.0], [80.0, 49.0], [80.1, 49.0], [80.2, 49.0], [80.3, 50.0], [80.4, 50.0], [80.5, 50.0], [80.6, 50.0], [80.7, 50.0], [80.8, 51.0], [80.9, 51.0], [81.0, 51.0], [81.1, 51.0], [81.2, 51.0], [81.3, 51.0], [81.4, 52.0], [81.5, 52.0], [81.6, 52.0], [81.7, 52.0], [81.8, 52.0], [81.9, 52.0], [82.0, 52.0], [82.1, 52.0], [82.2, 52.0], [82.3, 52.0], [82.4, 53.0], [82.5, 53.0], [82.6, 53.0], [82.7, 53.0], [82.8, 53.0], [82.9, 53.0], [83.0, 53.0], [83.1, 53.0], [83.2, 53.0], [83.3, 53.0], [83.4, 53.0], [83.5, 54.0], [83.6, 54.0], [83.7, 54.0], [83.8, 54.0], [83.9, 54.0], [84.0, 54.0], [84.1, 54.0], [84.2, 54.0], [84.3, 55.0], [84.4, 55.0], [84.5, 55.0], [84.6, 55.0], [84.7, 55.0], [84.8, 55.0], [84.9, 55.0], [85.0, 55.0], [85.1, 55.0], [85.2, 55.0], [85.3, 55.0], [85.4, 55.0], [85.5, 55.0], [85.6, 55.0], [85.7, 55.0], [85.8, 55.0], [85.9, 56.0], [86.0, 56.0], [86.1, 56.0], [86.2, 56.0], [86.3, 56.0], [86.4, 56.0], [86.5, 56.0], [86.6, 56.0], [86.7, 56.0], [86.8, 56.0], [86.9, 56.0], [87.0, 56.0], [87.1, 56.0], [87.2, 56.0], [87.3, 56.0], [87.4, 56.0], [87.5, 57.0], [87.6, 57.0], [87.7, 57.0], [87.8, 57.0], [87.9, 57.0], [88.0, 57.0], [88.1, 57.0], [88.2, 57.0], [88.3, 57.0], [88.4, 57.0], [88.5, 57.0], [88.6, 57.0], [88.7, 57.0], [88.8, 57.0], [88.9, 58.0], [89.0, 58.0], [89.1, 58.0], [89.2, 58.0], [89.3, 58.0], [89.4, 58.0], [89.5, 58.0], [89.6, 58.0], [89.7, 58.0], [89.8, 58.0], [89.9, 58.0], [90.0, 59.0], [90.1, 59.0], [90.2, 59.0], [90.3, 59.0], [90.4, 59.0], [90.5, 59.0], [90.6, 59.0], [90.7, 59.0], [90.8, 60.0], [90.9, 60.0], [91.0, 60.0], [91.1, 60.0], [91.2, 60.0], [91.3, 60.0], [91.4, 60.0], [91.5, 60.0], [91.6, 60.0], [91.7, 60.0], [91.8, 61.0], [91.9, 61.0], [92.0, 61.0], [92.1, 61.0], [92.2, 61.0], [92.3, 61.0], [92.4, 61.0], [92.5, 61.0], [92.6, 61.0], [92.7, 62.0], [92.8, 62.0], [92.9, 62.0], [93.0, 62.0], [93.1, 62.0], [93.2, 63.0], [93.3, 63.0], [93.4, 63.0], [93.5, 63.0], [93.6, 63.0], [93.7, 63.0], [93.8, 63.0], [93.9, 64.0], [94.0, 64.0], [94.1, 65.0], [94.2, 65.0], [94.3, 65.0], [94.4, 65.0], [94.5, 65.0], [94.6, 66.0], [94.7, 66.0], [94.8, 66.0], [94.9, 67.0], [95.0, 68.0], [95.1, 68.0], [95.2, 68.0], [95.3, 68.0], [95.4, 69.0], [95.5, 70.0], [95.6, 70.0], [95.7, 70.0], [95.8, 71.0], [95.9, 71.0], [96.0, 71.0], [96.1, 71.0], [96.2, 71.0], [96.3, 73.0], [96.4, 74.0], [96.5, 74.0], [96.6, 74.0], [96.7, 77.0], [96.8, 78.0], [96.9, 78.0], [97.0, 78.0], [97.1, 78.0], [97.2, 79.0], [97.3, 79.0], [97.4, 79.0], [97.5, 81.0], [97.6, 81.0], [97.7, 82.0], [97.8, 83.0], [97.9, 85.0], [98.0, 87.0], [98.1, 89.0], [98.2, 90.0], [98.3, 92.0], [98.4, 92.0], [98.5, 93.0], [98.6, 94.0], [98.7, 97.0], [98.8, 98.0], [98.9, 100.0], [99.0, 101.0], [99.1, 103.0], [99.2, 106.0], [99.3, 107.0], [99.4, 110.0], [99.5, 112.0], [99.6, 115.0], [99.7, 118.0], [99.8, 120.0], [99.9, 124.0]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[0.0, 2.0], [0.1, 2.0], [0.2, 2.0], [0.3, 2.0], [0.4, 2.0], [0.5, 2.0], [0.6, 2.0], [0.7, 2.0], [0.8, 2.0], [0.9, 2.0], [1.0, 2.0], [1.1, 2.0], [1.2, 2.0], [1.3, 2.0], [1.4, 2.0], [1.5, 2.0], [1.6, 2.0], [1.7, 2.0], [1.8, 2.0], [1.9, 2.0], [2.0, 2.0], [2.1, 2.0], [2.2, 2.0], [2.3, 2.0], [2.4, 2.0], [2.5, 2.0], [2.6, 2.0], [2.7, 2.0], [2.8, 2.0], [2.9, 2.0], [3.0, 2.0], [3.1, 2.0], [3.2, 2.0], [3.3, 2.0], [3.4, 2.0], [3.5, 2.0], [3.6, 2.0], [3.7, 2.0], [3.8, 2.0], [3.9, 2.0], [4.0, 2.0], [4.1, 2.0], [4.2, 2.0], [4.3, 2.0], [4.4, 2.0], [4.5, 2.0], [4.6, 2.0], [4.7, 2.0], [4.8, 2.0], [4.9, 2.0], [5.0, 2.0], [5.1, 2.0], [5.2, 2.0], [5.3, 2.0], [5.4, 2.0], [5.5, 2.0], [5.6, 2.0], [5.7, 2.0], [5.8, 2.0], [5.9, 2.0], [6.0, 2.0], [6.1, 2.0], [6.2, 2.0], [6.3, 2.0], [6.4, 2.0], [6.5, 2.0], [6.6, 2.0], [6.7, 2.0], [6.8, 2.0], [6.9, 2.0], [7.0, 2.0], [7.1, 2.0], [7.2, 2.0], [7.3, 2.0], [7.4, 2.0], [7.5, 2.0], [7.6, 2.0], [7.7, 2.0], [7.8, 2.0], [7.9, 2.0], [8.0, 2.0], [8.1, 2.0], [8.2, 2.0], [8.3, 2.0], [8.4, 2.0], [8.5, 2.0], [8.6, 2.0], [8.7, 2.0], [8.8, 2.0], [8.9, 2.0], [9.0, 2.0], [9.1, 2.0], [9.2, 2.0], [9.3, 2.0], [9.4, 2.0], [9.5, 2.0], [9.6, 2.0], [9.7, 2.0], [9.8, 2.0], [9.9, 2.0], [10.0, 2.0], [10.1, 2.0], [10.2, 2.0], [10.3, 2.0], [10.4, 2.0], [10.5, 2.0], [10.6, 2.0], [10.7, 2.0], [10.8, 2.0], [10.9, 2.0], [11.0, 2.0], [11.1, 2.0], [11.2, 2.0], [11.3, 2.0], [11.4, 2.0], [11.5, 2.0], [11.6, 2.0], [11.7, 2.0], [11.8, 2.0], [11.9, 2.0], [12.0, 2.0], [12.1, 2.0], [12.2, 2.0], [12.3, 2.0], [12.4, 2.0], [12.5, 2.0], [12.6, 2.0], [12.7, 2.0], [12.8, 2.0], [12.9, 2.0], [13.0, 2.0], [13.1, 2.0], [13.2, 2.0], [13.3, 2.0], [13.4, 2.0], [13.5, 2.0], [13.6, 2.0], [13.7, 2.0], [13.8, 2.0], [13.9, 2.0], [14.0, 2.0], [14.1, 2.0], [14.2, 2.0], [14.3, 2.0], [14.4, 2.0], [14.5, 2.0], [14.6, 2.0], [14.7, 2.0], [14.8, 2.0], [14.9, 2.0], [15.0, 2.0], [15.1, 2.0], [15.2, 2.0], [15.3, 2.0], [15.4, 2.0], [15.5, 2.0], [15.6, 2.0], [15.7, 2.0], [15.8, 2.0], [15.9, 2.0], [16.0, 2.0], [16.1, 2.0], [16.2, 2.0], [16.3, 2.0], [16.4, 2.0], [16.5, 2.0], [16.6, 2.0], [16.7, 2.0], [16.8, 2.0], [16.9, 2.0], [17.0, 2.0], [17.1, 2.0], [17.2, 2.0], [17.3, 2.0], [17.4, 2.0], [17.5, 2.0], [17.6, 2.0], [17.7, 2.0], [17.8, 2.0], [17.9, 2.0], [18.0, 2.0], [18.1, 2.0], [18.2, 2.0], [18.3, 2.0], [18.4, 2.0], [18.5, 2.0], [18.6, 2.0], [18.7, 2.0], [18.8, 2.0], [18.9, 2.0], [19.0, 2.0], [19.1, 2.0], [19.2, 2.0], [19.3, 2.0], [19.4, 2.0], [19.5, 2.0], [19.6, 2.0], [19.7, 2.0], [19.8, 3.0], [19.9, 3.0], [20.0, 3.0], [20.1, 3.0], [20.2, 3.0], [20.3, 3.0], [20.4, 3.0], [20.5, 3.0], [20.6, 3.0], [20.7, 3.0], [20.8, 3.0], [20.9, 3.0], [21.0, 3.0], [21.1, 3.0], [21.2, 3.0], [21.3, 3.0], [21.4, 3.0], [21.5, 3.0], [21.6, 3.0], [21.7, 3.0], [21.8, 3.0], [21.9, 3.0], [22.0, 3.0], [22.1, 3.0], [22.2, 3.0], [22.3, 3.0], [22.4, 3.0], [22.5, 3.0], [22.6, 3.0], [22.7, 3.0], [22.8, 3.0], [22.9, 3.0], [23.0, 3.0], [23.1, 3.0], [23.2, 3.0], [23.3, 3.0], [23.4, 3.0], [23.5, 3.0], [23.6, 3.0], [23.7, 3.0], [23.8, 3.0], [23.9, 3.0], [24.0, 3.0], [24.1, 3.0], [24.2, 3.0], [24.3, 3.0], [24.4, 3.0], [24.5, 3.0], [24.6, 3.0], [24.7, 3.0], [24.8, 3.0], [24.9, 3.0], [25.0, 3.0], [25.1, 3.0], [25.2, 3.0], [25.3, 3.0], [25.4, 3.0], [25.5, 3.0], [25.6, 3.0], [25.7, 3.0], [25.8, 3.0], [25.9, 3.0], [26.0, 3.0], [26.1, 3.0], [26.2, 3.0], [26.3, 3.0], [26.4, 3.0], [26.5, 3.0], [26.6, 3.0], [26.7, 3.0], [26.8, 3.0], [26.9, 3.0], [27.0, 3.0], [27.1, 3.0], [27.2, 3.0], [27.3, 3.0], [27.4, 3.0], [27.5, 3.0], [27.6, 3.0], [27.7, 3.0], [27.8, 3.0], [27.9, 3.0], [28.0, 3.0], [28.1, 3.0], [28.2, 3.0], [28.3, 3.0], [28.4, 3.0], [28.5, 3.0], [28.6, 3.0], [28.7, 3.0], [28.8, 3.0], [28.9, 3.0], [29.0, 3.0], [29.1, 3.0], [29.2, 3.0], [29.3, 3.0], [29.4, 3.0], [29.5, 3.0], [29.6, 3.0], [29.7, 3.0], [29.8, 3.0], [29.9, 3.0], [30.0, 3.0], [30.1, 3.0], [30.2, 3.0], [30.3, 3.0], [30.4, 3.0], [30.5, 3.0], [30.6, 3.0], [30.7, 3.0], [30.8, 3.0], [30.9, 3.0], [31.0, 3.0], [31.1, 3.0], [31.2, 3.0], [31.3, 3.0], [31.4, 3.0], [31.5, 3.0], [31.6, 3.0], [31.7, 3.0], [31.8, 3.0], [31.9, 3.0], [32.0, 3.0], [32.1, 3.0], [32.2, 3.0], [32.3, 3.0], [32.4, 3.0], [32.5, 3.0], [32.6, 3.0], [32.7, 3.0], [32.8, 3.0], [32.9, 3.0], [33.0, 3.0], [33.1, 3.0], [33.2, 3.0], [33.3, 3.0], [33.4, 3.0], [33.5, 3.0], [33.6, 3.0], [33.7, 3.0], [33.8, 3.0], [33.9, 3.0], [34.0, 3.0], [34.1, 3.0], [34.2, 3.0], [34.3, 3.0], [34.4, 3.0], [34.5, 3.0], [34.6, 3.0], [34.7, 3.0], [34.8, 3.0], [34.9, 3.0], [35.0, 3.0], [35.1, 3.0], [35.2, 3.0], [35.3, 3.0], [35.4, 3.0], [35.5, 3.0], [35.6, 3.0], [35.7, 3.0], [35.8, 3.0], [35.9, 3.0], [36.0, 3.0], [36.1, 3.0], [36.2, 3.0], [36.3, 3.0], [36.4, 3.0], [36.5, 3.0], [36.6, 3.0], [36.7, 3.0], [36.8, 3.0], [36.9, 3.0], [37.0, 3.0], [37.1, 3.0], [37.2, 3.0], [37.3, 3.0], [37.4, 3.0], [37.5, 3.0], [37.6, 3.0], [37.7, 3.0], [37.8, 3.0], [37.9, 3.0], [38.0, 3.0], [38.1, 3.0], [38.2, 3.0], [38.3, 3.0], [38.4, 3.0], [38.5, 3.0], [38.6, 3.0], [38.7, 3.0], [38.8, 3.0], [38.9, 3.0], [39.0, 3.0], [39.1, 3.0], [39.2, 3.0], [39.3, 3.0], [39.4, 3.0], [39.5, 3.0], [39.6, 3.0], [39.7, 3.0], [39.8, 3.0], [39.9, 3.0], [40.0, 3.0], [40.1, 3.0], [40.2, 3.0], [40.3, 3.0], [40.4, 3.0], [40.5, 3.0], [40.6, 3.0], [40.7, 3.0], [40.8, 3.0], [40.9, 3.0], [41.0, 3.0], [41.1, 3.0], [41.2, 3.0], [41.3, 3.0], [41.4, 3.0], [41.5, 3.0], [41.6, 3.0], [41.7, 3.0], [41.8, 3.0], [41.9, 3.0], [42.0, 3.0], [42.1, 3.0], [42.2, 3.0], [42.3, 3.0], [42.4, 3.0], [42.5, 3.0], [42.6, 3.0], [42.7, 3.0], [42.8, 3.0], [42.9, 3.0], [43.0, 3.0], [43.1, 3.0], [43.2, 3.0], [43.3, 3.0], [43.4, 3.0], [43.5, 3.0], [43.6, 3.0], [43.7, 3.0], [43.8, 3.0], [43.9, 3.0], [44.0, 3.0], [44.1, 3.0], [44.2, 3.0], [44.3, 3.0], [44.4, 3.0], [44.5, 3.0], [44.6, 3.0], [44.7, 3.0], [44.8, 3.0], [44.9, 3.0], [45.0, 3.0], [45.1, 3.0], [45.2, 3.0], [45.3, 3.0], [45.4, 3.0], [45.5, 3.0], [45.6, 3.0], [45.7, 3.0], [45.8, 3.0], [45.9, 3.0], [46.0, 3.0], [46.1, 3.0], [46.2, 3.0], [46.3, 3.0], [46.4, 3.0], [46.5, 3.0], [46.6, 3.0], [46.7, 3.0], [46.8, 3.0], [46.9, 3.0], [47.0, 3.0], [47.1, 3.0], [47.2, 3.0], [47.3, 3.0], [47.4, 3.0], [47.5, 3.0], [47.6, 3.0], [47.7, 3.0], [47.8, 3.0], [47.9, 3.0], [48.0, 3.0], [48.1, 3.0], [48.2, 3.0], [48.3, 3.0], [48.4, 3.0], [48.5, 3.0], [48.6, 3.0], [48.7, 3.0], [48.8, 3.0], [48.9, 3.0], [49.0, 3.0], [49.1, 3.0], [49.2, 3.0], [49.3, 3.0], [49.4, 3.0], [49.5, 3.0], [49.6, 3.0], [49.7, 3.0], [49.8, 3.0], [49.9, 3.0], [50.0, 3.0], [50.1, 3.0], [50.2, 3.0], [50.3, 3.0], [50.4, 3.0], [50.5, 3.0], [50.6, 3.0], [50.7, 3.0], [50.8, 3.0], [50.9, 3.0], [51.0, 3.0], [51.1, 3.0], [51.2, 3.0], [51.3, 3.0], [51.4, 3.0], [51.5, 3.0], [51.6, 3.0], [51.7, 3.0], [51.8, 3.0], [51.9, 3.0], [52.0, 3.0], [52.1, 3.0], [52.2, 3.0], [52.3, 3.0], [52.4, 3.0], [52.5, 3.0], [52.6, 3.0], [52.7, 3.0], [52.8, 3.0], [52.9, 3.0], [53.0, 3.0], [53.1, 3.0], [53.2, 3.0], [53.3, 3.0], [53.4, 3.0], [53.5, 3.0], [53.6, 3.0], [53.7, 3.0], [53.8, 3.0], [53.9, 3.0], [54.0, 3.0], [54.1, 3.0], [54.2, 3.0], [54.3, 3.0], [54.4, 3.0], [54.5, 3.0], [54.6, 3.0], [54.7, 3.0], [54.8, 3.0], [54.9, 3.0], [55.0, 3.0], [55.1, 3.0], [55.2, 3.0], [55.3, 3.0], [55.4, 3.0], [55.5, 3.0], [55.6, 3.0], [55.7, 3.0], [55.8, 3.0], [55.9, 3.0], [56.0, 3.0], [56.1, 3.0], [56.2, 3.0], [56.3, 3.0], [56.4, 3.0], [56.5, 3.0], [56.6, 3.0], [56.7, 3.0], [56.8, 3.0], [56.9, 3.0], [57.0, 3.0], [57.1, 3.0], [57.2, 3.0], [57.3, 3.0], [57.4, 3.0], [57.5, 3.0], [57.6, 3.0], [57.7, 3.0], [57.8, 3.0], [57.9, 3.0], [58.0, 3.0], [58.1, 3.0], [58.2, 3.0], [58.3, 3.0], [58.4, 3.0], [58.5, 3.0], [58.6, 3.0], [58.7, 3.0], [58.8, 4.0], [58.9, 4.0], [59.0, 4.0], [59.1, 4.0], [59.2, 4.0], [59.3, 4.0], [59.4, 4.0], [59.5, 4.0], [59.6, 4.0], [59.7, 4.0], [59.8, 4.0], [59.9, 4.0], [60.0, 4.0], [60.1, 4.0], [60.2, 4.0], [60.3, 4.0], [60.4, 4.0], [60.5, 4.0], [60.6, 4.0], [60.7, 4.0], [60.8, 4.0], [60.9, 4.0], [61.0, 4.0], [61.1, 4.0], [61.2, 4.0], [61.3, 4.0], [61.4, 4.0], [61.5, 4.0], [61.6, 4.0], [61.7, 4.0], [61.8, 4.0], [61.9, 4.0], [62.0, 4.0], [62.1, 4.0], [62.2, 4.0], [62.3, 4.0], [62.4, 4.0], [62.5, 4.0], [62.6, 4.0], [62.7, 4.0], [62.8, 4.0], [62.9, 4.0], [63.0, 4.0], [63.1, 4.0], [63.2, 4.0], [63.3, 4.0], [63.4, 4.0], [63.5, 4.0], [63.6, 4.0], [63.7, 4.0], [63.8, 4.0], [63.9, 4.0], [64.0, 4.0], [64.1, 4.0], [64.2, 4.0], [64.3, 4.0], [64.4, 4.0], [64.5, 4.0], [64.6, 4.0], [64.7, 4.0], [64.8, 4.0], [64.9, 4.0], [65.0, 4.0], [65.1, 4.0], [65.2, 4.0], [65.3, 4.0], [65.4, 4.0], [65.5, 4.0], [65.6, 4.0], [65.7, 4.0], [65.8, 4.0], [65.9, 4.0], [66.0, 4.0], [66.1, 4.0], [66.2, 4.0], [66.3, 4.0], [66.4, 4.0], [66.5, 4.0], [66.6, 4.0], [66.7, 4.0], [66.8, 4.0], [66.9, 4.0], [67.0, 4.0], [67.1, 4.0], [67.2, 4.0], [67.3, 4.0], [67.4, 4.0], [67.5, 4.0], [67.6, 4.0], [67.7, 4.0], [67.8, 4.0], [67.9, 4.0], [68.0, 4.0], [68.1, 4.0], [68.2, 4.0], [68.3, 4.0], [68.4, 4.0], [68.5, 4.0], [68.6, 4.0], [68.7, 4.0], [68.8, 4.0], [68.9, 4.0], [69.0, 4.0], [69.1, 4.0], [69.2, 4.0], [69.3, 4.0], [69.4, 4.0], [69.5, 4.0], [69.6, 4.0], [69.7, 4.0], [69.8, 4.0], [69.9, 4.0], [70.0, 4.0], [70.1, 4.0], [70.2, 4.0], [70.3, 4.0], [70.4, 4.0], [70.5, 4.0], [70.6, 4.0], [70.7, 4.0], [70.8, 4.0], [70.9, 4.0], [71.0, 4.0], [71.1, 4.0], [71.2, 4.0], [71.3, 4.0], [71.4, 4.0], [71.5, 4.0], [71.6, 4.0], [71.7, 4.0], [71.8, 4.0], [71.9, 4.0], [72.0, 4.0], [72.1, 4.0], [72.2, 4.0], [72.3, 4.0], [72.4, 4.0], [72.5, 4.0], [72.6, 4.0], [72.7, 4.0], [72.8, 4.0], [72.9, 4.0], [73.0, 4.0], [73.1, 4.0], [73.2, 4.0], [73.3, 4.0], [73.4, 4.0], [73.5, 4.0], [73.6, 4.0], [73.7, 4.0], [73.8, 4.0], [73.9, 4.0], [74.0, 4.0], [74.1, 4.0], [74.2, 4.0], [74.3, 4.0], [74.4, 5.0], [74.5, 5.0], [74.6, 5.0], [74.7, 5.0], [74.8, 5.0], [74.9, 5.0], [75.0, 5.0], [75.1, 5.0], [75.2, 5.0], [75.3, 5.0], [75.4, 5.0], [75.5, 5.0], [75.6, 5.0], [75.7, 5.0], [75.8, 5.0], [75.9, 5.0], [76.0, 5.0], [76.1, 5.0], [76.2, 5.0], [76.3, 5.0], [76.4, 5.0], [76.5, 5.0], [76.6, 5.0], [76.7, 5.0], [76.8, 5.0], [76.9, 5.0], [77.0, 5.0], [77.1, 5.0], [77.2, 5.0], [77.3, 5.0], [77.4, 5.0], [77.5, 5.0], [77.6, 5.0], [77.7, 5.0], [77.8, 5.0], [77.9, 5.0], [78.0, 5.0], [78.1, 5.0], [78.2, 5.0], [78.3, 5.0], [78.4, 5.0], [78.5, 5.0], [78.6, 5.0], [78.7, 5.0], [78.8, 5.0], [78.9, 5.0], [79.0, 5.0], [79.1, 5.0], [79.2, 5.0], [79.3, 5.0], [79.4, 5.0], [79.5, 5.0], [79.6, 5.0], [79.7, 5.0], [79.8, 5.0], [79.9, 5.0], [80.0, 5.0], [80.1, 5.0], [80.2, 5.0], [80.3, 5.0], [80.4, 5.0], [80.5, 5.0], [80.6, 5.0], [80.7, 5.0], [80.8, 5.0], [80.9, 5.0], [81.0, 5.0], [81.1, 5.0], [81.2, 5.0], [81.3, 5.0], [81.4, 5.0], [81.5, 5.0], [81.6, 5.0], [81.7, 5.0], [81.8, 5.0], [81.9, 5.0], [82.0, 5.0], [82.1, 5.0], [82.2, 5.0], [82.3, 5.0], [82.4, 5.0], [82.5, 5.0], [82.6, 5.0], [82.7, 5.0], [82.8, 5.0], [82.9, 5.0], [83.0, 5.0], [83.1, 5.0], [83.2, 5.0], [83.3, 5.0], [83.4, 5.0], [83.5, 5.0], [83.6, 5.0], [83.7, 5.0], [83.8, 5.0], [83.9, 5.0], [84.0, 5.0], [84.1, 5.0], [84.2, 5.0], [84.3, 5.0], [84.4, 6.0], [84.5, 6.0], [84.6, 6.0], [84.7, 6.0], [84.8, 6.0], [84.9, 6.0], [85.0, 6.0], [85.1, 6.0], [85.2, 6.0], [85.3, 6.0], [85.4, 6.0], [85.5, 6.0], [85.6, 6.0], [85.7, 6.0], [85.8, 6.0], [85.9, 6.0], [86.0, 6.0], [86.1, 6.0], [86.2, 6.0], [86.3, 6.0], [86.4, 6.0], [86.5, 6.0], [86.6, 6.0], [86.7, 6.0], [86.8, 6.0], [86.9, 6.0], [87.0, 6.0], [87.1, 6.0], [87.2, 6.0], [87.3, 6.0], [87.4, 6.0], [87.5, 6.0], [87.6, 6.0], [87.7, 6.0], [87.8, 6.0], [87.9, 6.0], [88.0, 6.0], [88.1, 6.0], [88.2, 6.0], [88.3, 6.0], [88.4, 6.0], [88.5, 6.0], [88.6, 6.0], [88.7, 6.0], [88.8, 6.0], [88.9, 6.0], [89.0, 6.0], [89.1, 6.0], [89.2, 6.0], [89.3, 6.0], [89.4, 6.0], [89.5, 6.0], [89.6, 6.0], [89.7, 6.0], [89.8, 6.0], [89.9, 6.0], [90.0, 6.0], [90.1, 6.0], [90.2, 6.0], [90.3, 6.0], [90.4, 7.0], [90.5, 7.0], [90.6, 7.0], [90.7, 7.0], [90.8, 7.0], [90.9, 7.0], [91.0, 7.0], [91.1, 7.0], [91.2, 7.0], [91.3, 7.0], [91.4, 7.0], [91.5, 7.0], [91.6, 7.0], [91.7, 7.0], [91.8, 7.0], [91.9, 7.0], [92.0, 7.0], [92.1, 7.0], [92.2, 7.0], [92.3, 7.0], [92.4, 7.0], [92.5, 7.0], [92.6, 7.0], [92.7, 7.0], [92.8, 8.0], [92.9, 8.0], [93.0, 8.0], [93.1, 8.0], [93.2, 8.0], [93.3, 8.0], [93.4, 8.0], [93.5, 8.0], [93.6, 8.0], [93.7, 8.0], [93.8, 8.0], [93.9, 8.0], [94.0, 8.0], [94.1, 8.0], [94.2, 8.0], [94.3, 8.0], [94.4, 8.0], [94.5, 8.0], [94.6, 9.0], [94.7, 9.0], [94.8, 9.0], [94.9, 9.0], [95.0, 9.0], [95.1, 9.0], [95.2, 9.0], [95.3, 9.0], [95.4, 9.0], [95.5, 9.0], [95.6, 9.0], [95.7, 9.0], [95.8, 9.0], [95.9, 9.0], [96.0, 10.0], [96.1, 10.0], [96.2, 10.0], [96.3, 10.0], [96.4, 11.0], [96.5, 11.0], [96.6, 11.0], [96.7, 11.0], [96.8, 11.0], [96.9, 12.0], [97.0, 12.0], [97.1, 13.0], [97.2, 13.0], [97.3, 13.0], [97.4, 13.0], [97.5, 14.0], [97.6, 14.0], [97.7, 14.0], [97.8, 14.0], [97.9, 14.0], [98.0, 14.0], [98.1, 15.0], [98.2, 15.0], [98.3, 15.0], [98.4, 15.0], [98.5, 16.0], [98.6, 16.0], [98.7, 16.0], [98.8, 16.0], [98.9, 17.0], [99.0, 17.0], [99.1, 18.0], [99.2, 18.0], [99.3, 21.0], [99.4, 21.0], [99.5, 26.0], [99.6, 26.0], [99.7, 29.0], [99.8, 29.0], [99.9, 30.0], [100.0, 30.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[0.0, 2.0], [0.1, 2.0], [0.2, 2.0], [0.3, 2.0], [0.4, 2.0], [0.5, 2.0], [0.6, 2.0], [0.7, 2.0], [0.8, 2.0], [0.9, 2.0], [1.0, 2.0], [1.1, 2.0], [1.2, 2.0], [1.3, 2.0], [1.4, 2.0], [1.5, 2.0], [1.6, 2.0], [1.7, 2.0], [1.8, 2.0], [1.9, 2.0], [2.0, 2.0], [2.1, 2.0], [2.2, 2.0], [2.3, 2.0], [2.4, 2.0], [2.5, 2.0], [2.6, 2.0], [2.7, 2.0], [2.8, 2.0], [2.9, 2.0], [3.0, 2.0], [3.1, 2.0], [3.2, 2.0], [3.3, 2.0], [3.4, 2.0], [3.5, 2.0], [3.6, 2.0], [3.7, 2.0], [3.8, 2.0], [3.9, 2.0], [4.0, 2.0], [4.1, 2.0], [4.2, 2.0], [4.3, 2.0], [4.4, 2.0], [4.5, 2.0], [4.6, 3.0], [4.7, 3.0], [4.8, 3.0], [4.9, 3.0], [5.0, 3.0], [5.1, 3.0], [5.2, 3.0], [5.3, 3.0], [5.4, 3.0], [5.5, 3.0], [5.6, 3.0], [5.7, 3.0], [5.8, 3.0], [5.9, 3.0], [6.0, 3.0], [6.1, 3.0], [6.2, 3.0], [6.3, 3.0], [6.4, 3.0], [6.5, 3.0], [6.6, 3.0], [6.7, 3.0], [6.8, 3.0], [6.9, 3.0], [7.0, 3.0], [7.1, 3.0], [7.2, 3.0], [7.3, 3.0], [7.4, 3.0], [7.5, 3.0], [7.6, 3.0], [7.7, 3.0], [7.8, 3.0], [7.9, 3.0], [8.0, 3.0], [8.1, 3.0], [8.2, 3.0], [8.3, 3.0], [8.4, 3.0], [8.5, 3.0], [8.6, 3.0], [8.7, 3.0], [8.8, 3.0], [8.9, 3.0], [9.0, 3.0], [9.1, 3.0], [9.2, 3.0], [9.3, 3.0], [9.4, 3.0], [9.5, 3.0], [9.6, 3.0], [9.7, 3.0], [9.8, 3.0], [9.9, 3.0], [10.0, 3.0], [10.1, 3.0], [10.2, 3.0], [10.3, 3.0], [10.4, 3.0], [10.5, 3.0], [10.6, 3.0], [10.7, 3.0], [10.8, 3.0], [10.9, 3.0], [11.0, 3.0], [11.1, 3.0], [11.2, 3.0], [11.3, 3.0], [11.4, 3.0], [11.5, 3.0], [11.6, 3.0], [11.7, 3.0], [11.8, 3.0], [11.9, 3.0], [12.0, 3.0], [12.1, 3.0], [12.2, 3.0], [12.3, 3.0], [12.4, 3.0], [12.5, 3.0], [12.6, 3.0], [12.7, 3.0], [12.8, 3.0], [12.9, 3.0], [13.0, 3.0], [13.1, 3.0], [13.2, 3.0], [13.3, 3.0], [13.4, 3.0], [13.5, 3.0], [13.6, 3.0], [13.7, 3.0], [13.8, 3.0], [13.9, 3.0], [14.0, 3.0], [14.1, 3.0], [14.2, 3.0], [14.3, 3.0], [14.4, 3.0], [14.5, 3.0], [14.6, 3.0], [14.7, 3.0], [14.8, 3.0], [14.9, 3.0], [15.0, 3.0], [15.1, 3.0], [15.2, 3.0], [15.3, 3.0], [15.4, 3.0], [15.5, 3.0], [15.6, 3.0], [15.7, 3.0], [15.8, 3.0], [15.9, 3.0], [16.0, 3.0], [16.1, 3.0], [16.2, 3.0], [16.3, 3.0], [16.4, 3.0], [16.5, 3.0], [16.6, 3.0], [16.7, 3.0], [16.8, 3.0], [16.9, 3.0], [17.0, 3.0], [17.1, 3.0], [17.2, 3.0], [17.3, 3.0], [17.4, 3.0], [17.5, 3.0], [17.6, 3.0], [17.7, 3.0], [17.8, 3.0], [17.9, 3.0], [18.0, 3.0], [18.1, 3.0], [18.2, 3.0], [18.3, 3.0], [18.4, 3.0], [18.5, 3.0], [18.6, 3.0], [18.7, 3.0], [18.8, 3.0], [18.9, 3.0], [19.0, 3.0], [19.1, 3.0], [19.2, 3.0], [19.3, 3.0], [19.4, 3.0], [19.5, 3.0], [19.6, 3.0], [19.7, 3.0], [19.8, 3.0], [19.9, 3.0], [20.0, 3.0], [20.1, 3.0], [20.2, 3.0], [20.3, 3.0], [20.4, 3.0], [20.5, 3.0], [20.6, 3.0], [20.7, 3.0], [20.8, 3.0], [20.9, 3.0], [21.0, 3.0], [21.1, 3.0], [21.2, 3.0], [21.3, 3.0], [21.4, 3.0], [21.5, 3.0], [21.6, 3.0], [21.7, 3.0], [21.8, 3.0], [21.9, 3.0], [22.0, 3.0], [22.1, 3.0], [22.2, 3.0], [22.3, 3.0], [22.4, 3.0], [22.5, 3.0], [22.6, 3.0], [22.7, 3.0], [22.8, 3.0], [22.9, 3.0], [23.0, 3.0], [23.1, 3.0], [23.2, 3.0], [23.3, 3.0], [23.4, 3.0], [23.5, 3.0], [23.6, 3.0], [23.7, 3.0], [23.8, 3.0], [23.9, 3.0], [24.0, 3.0], [24.1, 3.0], [24.2, 3.0], [24.3, 3.0], [24.4, 3.0], [24.5, 3.0], [24.6, 3.0], [24.7, 3.0], [24.8, 3.0], [24.9, 4.0], [25.0, 4.0], [25.1, 4.0], [25.2, 4.0], [25.3, 4.0], [25.4, 4.0], [25.5, 4.0], [25.6, 4.0], [25.7, 4.0], [25.8, 4.0], [25.9, 4.0], [26.0, 4.0], [26.1, 4.0], [26.2, 4.0], [26.3, 4.0], [26.4, 4.0], [26.5, 4.0], [26.6, 4.0], [26.7, 4.0], [26.8, 4.0], [26.9, 4.0], [27.0, 4.0], [27.1, 4.0], [27.2, 4.0], [27.3, 4.0], [27.4, 4.0], [27.5, 4.0], [27.6, 4.0], [27.7, 4.0], [27.8, 4.0], [27.9, 4.0], [28.0, 4.0], [28.1, 4.0], [28.2, 4.0], [28.3, 4.0], [28.4, 4.0], [28.5, 4.0], [28.6, 4.0], [28.7, 4.0], [28.8, 4.0], [28.9, 4.0], [29.0, 4.0], [29.1, 4.0], [29.2, 4.0], [29.3, 4.0], [29.4, 4.0], [29.5, 4.0], [29.6, 4.0], [29.7, 4.0], [29.8, 4.0], [29.9, 4.0], [30.0, 4.0], [30.1, 4.0], [30.2, 4.0], [30.3, 4.0], [30.4, 4.0], [30.5, 4.0], [30.6, 4.0], [30.7, 4.0], [30.8, 4.0], [30.9, 4.0], [31.0, 4.0], [31.1, 4.0], [31.2, 4.0], [31.3, 4.0], [31.4, 4.0], [31.5, 4.0], [31.6, 4.0], [31.7, 4.0], [31.8, 4.0], [31.9, 4.0], [32.0, 4.0], [32.1, 4.0], [32.2, 4.0], [32.3, 4.0], [32.4, 4.0], [32.5, 4.0], [32.6, 4.0], [32.7, 4.0], [32.8, 4.0], [32.9, 4.0], [33.0, 4.0], [33.1, 4.0], [33.2, 4.0], [33.3, 4.0], [33.4, 4.0], [33.5, 4.0], [33.6, 4.0], [33.7, 4.0], [33.8, 4.0], [33.9, 4.0], [34.0, 4.0], [34.1, 4.0], [34.2, 4.0], [34.3, 4.0], [34.4, 4.0], [34.5, 4.0], [34.6, 4.0], [34.7, 4.0], [34.8, 4.0], [34.9, 4.0], [35.0, 4.0], [35.1, 4.0], [35.2, 4.0], [35.3, 4.0], [35.4, 4.0], [35.5, 4.0], [35.6, 4.0], [35.7, 4.0], [35.8, 4.0], [35.9, 4.0], [36.0, 4.0], [36.1, 4.0], [36.2, 4.0], [36.3, 4.0], [36.4, 4.0], [36.5, 4.0], [36.6, 4.0], [36.7, 4.0], [36.8, 4.0], [36.9, 4.0], [37.0, 4.0], [37.1, 4.0], [37.2, 4.0], [37.3, 4.0], [37.4, 4.0], [37.5, 4.0], [37.6, 4.0], [37.7, 4.0], [37.8, 4.0], [37.9, 4.0], [38.0, 4.0], [38.1, 4.0], [38.2, 4.0], [38.3, 4.0], [38.4, 4.0], [38.5, 4.0], [38.6, 4.0], [38.7, 4.0], [38.8, 4.0], [38.9, 4.0], [39.0, 4.0], [39.1, 4.0], [39.2, 4.0], [39.3, 4.0], [39.4, 4.0], [39.5, 4.0], [39.6, 4.0], [39.7, 4.0], [39.8, 4.0], [39.9, 4.0], [40.0, 4.0], [40.1, 4.0], [40.2, 4.0], [40.3, 4.0], [40.4, 4.0], [40.5, 4.0], [40.6, 4.0], [40.7, 4.0], [40.8, 4.0], [40.9, 4.0], [41.0, 4.0], [41.1, 4.0], [41.2, 4.0], [41.3, 4.0], [41.4, 4.0], [41.5, 4.0], [41.6, 4.0], [41.7, 4.0], [41.8, 4.0], [41.9, 4.0], [42.0, 4.0], [42.1, 4.0], [42.2, 4.0], [42.3, 4.0], [42.4, 4.0], [42.5, 4.0], [42.6, 4.0], [42.7, 4.0], [42.8, 4.0], [42.9, 4.0], [43.0, 4.0], [43.1, 4.0], [43.2, 4.0], [43.3, 4.0], [43.4, 4.0], [43.5, 4.0], [43.6, 4.0], [43.7, 4.0], [43.8, 4.0], [43.9, 4.0], [44.0, 4.0], [44.1, 4.0], [44.2, 4.0], [44.3, 4.0], [44.4, 4.0], [44.5, 4.0], [44.6, 4.0], [44.7, 4.0], [44.8, 4.0], [44.9, 4.0], [45.0, 4.0], [45.1, 4.0], [45.2, 4.0], [45.3, 4.0], [45.4, 4.0], [45.5, 4.0], [45.6, 4.0], [45.7, 4.0], [45.8, 4.0], [45.9, 4.0], [46.0, 4.0], [46.1, 4.0], [46.2, 4.0], [46.3, 4.0], [46.4, 4.0], [46.5, 4.0], [46.6, 4.0], [46.7, 4.0], [46.8, 4.0], [46.9, 4.0], [47.0, 4.0], [47.1, 4.0], [47.2, 4.0], [47.3, 4.0], [47.4, 4.0], [47.5, 4.0], [47.6, 4.0], [47.7, 4.0], [47.8, 4.0], [47.9, 4.0], [48.0, 4.0], [48.1, 4.0], [48.2, 4.0], [48.3, 4.0], [48.4, 4.0], [48.5, 4.0], [48.6, 4.0], [48.7, 4.0], [48.8, 4.0], [48.9, 4.0], [49.0, 4.0], [49.1, 4.0], [49.2, 4.0], [49.3, 4.0], [49.4, 4.0], [49.5, 4.0], [49.6, 4.0], [49.7, 4.0], [49.8, 4.0], [49.9, 4.0], [50.0, 4.0], [50.1, 4.0], [50.2, 4.0], [50.3, 4.0], [50.4, 4.0], [50.5, 4.0], [50.6, 4.0], [50.7, 4.0], [50.8, 5.0], [50.9, 5.0], [51.0, 5.0], [51.1, 5.0], [51.2, 5.0], [51.3, 5.0], [51.4, 5.0], [51.5, 5.0], [51.6, 5.0], [51.7, 5.0], [51.8, 5.0], [51.9, 5.0], [52.0, 5.0], [52.1, 5.0], [52.2, 5.0], [52.3, 5.0], [52.4, 5.0], [52.5, 5.0], [52.6, 5.0], [52.7, 5.0], [52.8, 5.0], [52.9, 5.0], [53.0, 5.0], [53.1, 5.0], [53.2, 5.0], [53.3, 5.0], [53.4, 5.0], [53.5, 5.0], [53.6, 5.0], [53.7, 5.0], [53.8, 5.0], [53.9, 5.0], [54.0, 5.0], [54.1, 5.0], [54.2, 5.0], [54.3, 5.0], [54.4, 5.0], [54.5, 5.0], [54.6, 5.0], [54.7, 5.0], [54.8, 5.0], [54.9, 5.0], [55.0, 5.0], [55.1, 5.0], [55.2, 5.0], [55.3, 5.0], [55.4, 5.0], [55.5, 5.0], [55.6, 5.0], [55.7, 5.0], [55.8, 5.0], [55.9, 5.0], [56.0, 5.0], [56.1, 5.0], [56.2, 5.0], [56.3, 5.0], [56.4, 5.0], [56.5, 5.0], [56.6, 5.0], [56.7, 5.0], [56.8, 5.0], [56.9, 5.0], [57.0, 5.0], [57.1, 5.0], [57.2, 5.0], [57.3, 5.0], [57.4, 5.0], [57.5, 5.0], [57.6, 5.0], [57.7, 5.0], [57.8, 5.0], [57.9, 5.0], [58.0, 5.0], [58.1, 5.0], [58.2, 5.0], [58.3, 5.0], [58.4, 5.0], [58.5, 5.0], [58.6, 5.0], [58.7, 5.0], [58.8, 5.0], [58.9, 5.0], [59.0, 5.0], [59.1, 5.0], [59.2, 5.0], [59.3, 5.0], [59.4, 5.0], [59.5, 5.0], [59.6, 5.0], [59.7, 5.0], [59.8, 5.0], [59.9, 5.0], [60.0, 5.0], [60.1, 5.0], [60.2, 5.0], [60.3, 5.0], [60.4, 5.0], [60.5, 5.0], [60.6, 5.0], [60.7, 5.0], [60.8, 5.0], [60.9, 5.0], [61.0, 5.0], [61.1, 5.0], [61.2, 5.0], [61.3, 5.0], [61.4, 5.0], [61.5, 5.0], [61.6, 5.0], [61.7, 5.0], [61.8, 5.0], [61.9, 5.0], [62.0, 5.0], [62.1, 5.0], [62.2, 5.0], [62.3, 5.0], [62.4, 5.0], [62.5, 5.0], [62.6, 5.0], [62.7, 5.0], [62.8, 5.0], [62.9, 5.0], [63.0, 5.0], [63.1, 5.0], [63.2, 5.0], [63.3, 5.0], [63.4, 5.0], [63.5, 5.0], [63.6, 5.0], [63.7, 5.0], [63.8, 5.0], [63.9, 5.0], [64.0, 5.0], [64.1, 5.0], [64.2, 6.0], [64.3, 6.0], [64.4, 6.0], [64.5, 6.0], [64.6, 6.0], [64.7, 6.0], [64.8, 6.0], [64.9, 6.0], [65.0, 6.0], [65.1, 6.0], [65.2, 6.0], [65.3, 6.0], [65.4, 6.0], [65.5, 6.0], [65.6, 6.0], [65.7, 6.0], [65.8, 6.0], [65.9, 6.0], [66.0, 6.0], [66.1, 6.0], [66.2, 6.0], [66.3, 6.0], [66.4, 6.0], [66.5, 6.0], [66.6, 6.0], [66.7, 6.0], [66.8, 6.0], [66.9, 6.0], [67.0, 6.0], [67.1, 6.0], [67.2, 6.0], [67.3, 6.0], [67.4, 6.0], [67.5, 6.0], [67.6, 6.0], [67.7, 6.0], [67.8, 6.0], [67.9, 6.0], [68.0, 6.0], [68.1, 6.0], [68.2, 6.0], [68.3, 6.0], [68.4, 6.0], [68.5, 6.0], [68.6, 6.0], [68.7, 6.0], [68.8, 6.0], [68.9, 6.0], [69.0, 6.0], [69.1, 6.0], [69.2, 6.0], [69.3, 6.0], [69.4, 6.0], [69.5, 6.0], [69.6, 6.0], [69.7, 6.0], [69.8, 6.0], [69.9, 6.0], [70.0, 6.0], [70.1, 6.0], [70.2, 6.0], [70.3, 6.0], [70.4, 6.0], [70.5, 6.0], [70.6, 6.0], [70.7, 6.0], [70.8, 6.0], [70.9, 6.0], [71.0, 6.0], [71.1, 6.0], [71.2, 6.0], [71.3, 6.0], [71.4, 6.0], [71.5, 6.0], [71.6, 6.0], [71.7, 6.0], [71.8, 6.0], [71.9, 6.0], [72.0, 6.0], [72.1, 6.0], [72.2, 6.0], [72.3, 6.0], [72.4, 6.0], [72.5, 6.0], [72.6, 6.0], [72.7, 6.0], [72.8, 6.0], [72.9, 6.0], [73.0, 6.0], [73.1, 6.0], [73.2, 6.0], [73.3, 6.0], [73.4, 7.0], [73.5, 7.0], [73.6, 7.0], [73.7, 7.0], [73.8, 7.0], [73.9, 7.0], [74.0, 7.0], [74.1, 7.0], [74.2, 7.0], [74.3, 7.0], [74.4, 7.0], [74.5, 7.0], [74.6, 7.0], [74.7, 7.0], [74.8, 7.0], [74.9, 7.0], [75.0, 7.0], [75.1, 7.0], [75.2, 7.0], [75.3, 7.0], [75.4, 7.0], [75.5, 7.0], [75.6, 7.0], [75.7, 7.0], [75.8, 7.0], [75.9, 7.0], [76.0, 7.0], [76.1, 7.0], [76.2, 7.0], [76.3, 7.0], [76.4, 7.0], [76.5, 7.0], [76.6, 7.0], [76.7, 7.0], [76.8, 7.0], [76.9, 7.0], [77.0, 7.0], [77.1, 7.0], [77.2, 7.0], [77.3, 7.0], [77.4, 7.0], [77.5, 7.0], [77.6, 7.0], [77.7, 7.0], [77.8, 7.0], [77.9, 7.0], [78.0, 7.0], [78.1, 7.0], [78.2, 7.0], [78.3, 7.0], [78.4, 7.0], [78.5, 7.0], [78.6, 7.0], [78.7, 7.0], [78.8, 7.0], [78.9, 7.0], [79.0, 7.0], [79.1, 7.0], [79.2, 7.0], [79.3, 7.0], [79.4, 7.0], [79.5, 7.0], [79.6, 7.0], [79.7, 7.0], [79.8, 7.0], [79.9, 7.0], [80.0, 7.0], [80.1, 7.0], [80.2, 7.0], [80.3, 7.0], [80.4, 7.0], [80.5, 7.0], [80.6, 7.0], [80.7, 7.0], [80.8, 8.0], [80.9, 8.0], [81.0, 8.0], [81.1, 8.0], [81.2, 8.0], [81.3, 8.0], [81.4, 8.0], [81.5, 8.0], [81.6, 8.0], [81.7, 8.0], [81.8, 8.0], [81.9, 8.0], [82.0, 8.0], [82.1, 8.0], [82.2, 8.0], [82.3, 8.0], [82.4, 8.0], [82.5, 8.0], [82.6, 8.0], [82.7, 8.0], [82.8, 8.0], [82.9, 8.0], [83.0, 8.0], [83.1, 8.0], [83.2, 8.0], [83.3, 8.0], [83.4, 8.0], [83.5, 8.0], [83.6, 8.0], [83.7, 8.0], [83.8, 8.0], [83.9, 8.0], [84.0, 8.0], [84.1, 8.0], [84.2, 8.0], [84.3, 8.0], [84.4, 8.0], [84.5, 8.0], [84.6, 8.0], [84.7, 8.0], [84.8, 8.0], [84.9, 8.0], [85.0, 8.0], [85.1, 8.0], [85.2, 8.0], [85.3, 8.0], [85.4, 8.0], [85.5, 8.0], [85.6, 8.0], [85.7, 8.0], [85.8, 8.0], [85.9, 8.0], [86.0, 8.0], [86.1, 8.0], [86.2, 8.0], [86.3, 8.0], [86.4, 8.0], [86.5, 8.0], [86.6, 8.0], [86.7, 8.0], [86.8, 8.0], [86.9, 8.0], [87.0, 8.0], [87.1, 8.0], [87.2, 9.0], [87.3, 9.0], [87.4, 9.0], [87.5, 9.0], [87.6, 9.0], [87.7, 9.0], [87.8, 9.0], [87.9, 9.0], [88.0, 9.0], [88.1, 9.0], [88.2, 9.0], [88.3, 9.0], [88.4, 9.0], [88.5, 9.0], [88.6, 9.0], [88.7, 9.0], [88.8, 9.0], [88.9, 9.0], [89.0, 9.0], [89.1, 9.0], [89.2, 9.0], [89.3, 9.0], [89.4, 9.0], [89.5, 9.0], [89.6, 9.0], [89.7, 9.0], [89.8, 9.0], [89.9, 9.0], [90.0, 9.0], [90.1, 9.0], [90.2, 9.0], [90.3, 9.0], [90.4, 9.0], [90.5, 9.0], [90.6, 9.0], [90.7, 9.0], [90.8, 9.0], [90.9, 9.0], [91.0, 9.0], [91.1, 9.0], [91.2, 9.0], [91.3, 9.0], [91.4, 9.0], [91.5, 9.0], [91.6, 9.0], [91.7, 10.0], [91.8, 10.0], [91.9, 10.0], [92.0, 10.0], [92.1, 10.0], [92.2, 10.0], [92.3, 10.0], [92.4, 10.0], [92.5, 10.0], [92.6, 10.0], [92.7, 10.0], [92.8, 10.0], [92.9, 10.0], [93.0, 10.0], [93.1, 10.0], [93.2, 10.0], [93.3, 10.0], [93.4, 10.0], [93.5, 10.0], [93.6, 10.0], [93.7, 10.0], [93.8, 10.0], [93.9, 10.0], [94.0, 10.0], [94.1, 10.0], [94.2, 10.0], [94.3, 11.0], [94.4, 11.0], [94.5, 11.0], [94.6, 11.0], [94.7, 11.0], [94.8, 11.0], [94.9, 11.0], [95.0, 11.0], [95.1, 11.0], [95.2, 11.0], [95.3, 11.0], [95.4, 11.0], [95.5, 11.0], [95.6, 11.0], [95.7, 11.0], [95.8, 12.0], [95.9, 12.0], [96.0, 12.0], [96.1, 12.0], [96.2, 12.0], [96.3, 12.0], [96.4, 12.0], [96.5, 12.0], [96.6, 12.0], [96.7, 13.0], [96.8, 13.0], [96.9, 13.0], [97.0, 13.0], [97.1, 13.0], [97.2, 13.0], [97.3, 13.0], [97.4, 13.0], [97.5, 13.0], [97.6, 13.0], [97.7, 13.0], [97.8, 13.0], [97.9, 13.0], [98.0, 14.0], [98.1, 14.0], [98.2, 14.0], [98.3, 14.0], [98.4, 14.0], [98.5, 15.0], [98.6, 15.0], [98.7, 15.0], [98.8, 15.0], [98.9, 15.0], [99.0, 16.0], [99.1, 17.0], [99.2, 17.0], [99.3, 17.0], [99.4, 18.0], [99.5, 19.0], [99.6, 19.0], [99.7, 20.0], [99.8, 20.0], [99.9, 21.0], [100.0, 22.0]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[0.0, 34.0], [0.1, 34.0], [0.2, 34.0], [0.3, 34.0], [0.4, 34.0], [0.5, 34.0], [0.6, 34.0], [0.7, 34.0], [0.8, 34.0], [0.9, 34.0], [1.0, 34.0], [1.1, 34.0], [1.2, 34.0], [1.3, 34.0], [1.4, 34.0], [1.5, 34.0], [1.6, 34.0], [1.7, 34.0], [1.8, 34.0], [1.9, 34.0], [2.0, 35.0], [2.1, 35.0], [2.2, 35.0], [2.3, 35.0], [2.4, 35.0], [2.5, 35.0], [2.6, 35.0], [2.7, 35.0], [2.8, 35.0], [2.9, 35.0], [3.0, 35.0], [3.1, 35.0], [3.2, 35.0], [3.3, 35.0], [3.4, 35.0], [3.5, 35.0], [3.6, 35.0], [3.7, 35.0], [3.8, 35.0], [3.9, 35.0], [4.0, 35.0], [4.1, 35.0], [4.2, 35.0], [4.3, 35.0], [4.4, 35.0], [4.5, 35.0], [4.6, 35.0], [4.7, 35.0], [4.8, 35.0], [4.9, 35.0], [5.0, 35.0], [5.1, 35.0], [5.2, 35.0], [5.3, 35.0], [5.4, 35.0], [5.5, 35.0], [5.6, 35.0], [5.7, 35.0], [5.8, 35.0], [5.9, 35.0], [6.0, 35.0], [6.1, 35.0], [6.2, 35.0], [6.3, 35.0], [6.4, 35.0], [6.5, 35.0], [6.6, 35.0], [6.7, 35.0], [6.8, 35.0], [6.9, 35.0], [7.0, 35.0], [7.1, 35.0], [7.2, 35.0], [7.3, 35.0], [7.4, 35.0], [7.5, 35.0], [7.6, 35.0], [7.7, 35.0], [7.8, 35.0], [7.9, 35.0], [8.0, 35.0], [8.1, 35.0], [8.2, 35.0], [8.3, 35.0], [8.4, 35.0], [8.5, 35.0], [8.6, 35.0], [8.7, 35.0], [8.8, 35.0], [8.9, 35.0], [9.0, 35.0], [9.1, 35.0], [9.2, 35.0], [9.3, 35.0], [9.4, 35.0], [9.5, 35.0], [9.6, 35.0], [9.7, 35.0], [9.8, 35.0], [9.9, 35.0], [10.0, 35.0], [10.1, 35.0], [10.2, 35.0], [10.3, 35.0], [10.4, 35.0], [10.5, 35.0], [10.6, 35.0], [10.7, 35.0], [10.8, 35.0], [10.9, 35.0], [11.0, 35.0], [11.1, 35.0], [11.2, 35.0], [11.3, 35.0], [11.4, 35.0], [11.5, 35.0], [11.6, 35.0], [11.7, 35.0], [11.8, 35.0], [11.9, 35.0], [12.0, 35.0], [12.1, 35.0], [12.2, 35.0], [12.3, 35.0], [12.4, 35.0], [12.5, 35.0], [12.6, 35.0], [12.7, 35.0], [12.8, 35.0], [12.9, 35.0], [13.0, 35.0], [13.1, 35.0], [13.2, 35.0], [13.3, 35.0], [13.4, 35.0], [13.5, 35.0], [13.6, 35.0], [13.7, 35.0], [13.8, 35.0], [13.9, 35.0], [14.0, 35.0], [14.1, 35.0], [14.2, 35.0], [14.3, 35.0], [14.4, 35.0], [14.5, 35.0], [14.6, 35.0], [14.7, 35.0], [14.8, 35.0], [14.9, 35.0], [15.0, 35.0], [15.1, 35.0], [15.2, 35.0], [15.3, 35.0], [15.4, 35.0], [15.5, 35.0], [15.6, 35.0], [15.7, 35.0], [15.8, 36.0], [15.9, 36.0], [16.0, 36.0], [16.1, 36.0], [16.2, 36.0], [16.3, 36.0], [16.4, 36.0], [16.5, 36.0], [16.6, 36.0], [16.7, 36.0], [16.8, 36.0], [16.9, 36.0], [17.0, 36.0], [17.1, 36.0], [17.2, 36.0], [17.3, 36.0], [17.4, 36.0], [17.5, 36.0], [17.6, 36.0], [17.7, 36.0], [17.8, 36.0], [17.9, 36.0], [18.0, 36.0], [18.1, 36.0], [18.2, 36.0], [18.3, 36.0], [18.4, 36.0], [18.5, 36.0], [18.6, 36.0], [18.7, 36.0], [18.8, 36.0], [18.9, 36.0], [19.0, 36.0], [19.1, 36.0], [19.2, 36.0], [19.3, 36.0], [19.4, 36.0], [19.5, 36.0], [19.6, 36.0], [19.7, 36.0], [19.8, 36.0], [19.9, 36.0], [20.0, 36.0], [20.1, 36.0], [20.2, 36.0], [20.3, 36.0], [20.4, 36.0], [20.5, 36.0], [20.6, 36.0], [20.7, 36.0], [20.8, 36.0], [20.9, 36.0], [21.0, 36.0], [21.1, 36.0], [21.2, 36.0], [21.3, 36.0], [21.4, 36.0], [21.5, 36.0], [21.6, 36.0], [21.7, 36.0], [21.8, 36.0], [21.9, 36.0], [22.0, 36.0], [22.1, 36.0], [22.2, 36.0], [22.3, 36.0], [22.4, 36.0], [22.5, 36.0], [22.6, 36.0], [22.7, 36.0], [22.8, 36.0], [22.9, 36.0], [23.0, 36.0], [23.1, 36.0], [23.2, 36.0], [23.3, 36.0], [23.4, 36.0], [23.5, 36.0], [23.6, 36.0], [23.7, 36.0], [23.8, 36.0], [23.9, 36.0], [24.0, 36.0], [24.1, 36.0], [24.2, 36.0], [24.3, 36.0], [24.4, 36.0], [24.5, 36.0], [24.6, 36.0], [24.7, 36.0], [24.8, 36.0], [24.9, 36.0], [25.0, 36.0], [25.1, 36.0], [25.2, 36.0], [25.3, 36.0], [25.4, 36.0], [25.5, 36.0], [25.6, 36.0], [25.7, 36.0], [25.8, 36.0], [25.9, 36.0], [26.0, 36.0], [26.1, 36.0], [26.2, 36.0], [26.3, 36.0], [26.4, 36.0], [26.5, 36.0], [26.6, 36.0], [26.7, 36.0], [26.8, 36.0], [26.9, 36.0], [27.0, 36.0], [27.1, 36.0], [27.2, 36.0], [27.3, 36.0], [27.4, 36.0], [27.5, 36.0], [27.6, 36.0], [27.7, 36.0], [27.8, 36.0], [27.9, 36.0], [28.0, 36.0], [28.1, 36.0], [28.2, 36.0], [28.3, 36.0], [28.4, 36.0], [28.5, 36.0], [28.6, 36.0], [28.7, 36.0], [28.8, 36.0], [28.9, 36.0], [29.0, 36.0], [29.1, 36.0], [29.2, 36.0], [29.3, 36.0], [29.4, 36.0], [29.5, 36.0], [29.6, 36.0], [29.7, 36.0], [29.8, 36.0], [29.9, 36.0], [30.0, 36.0], [30.1, 36.0], [30.2, 36.0], [30.3, 36.0], [30.4, 36.0], [30.5, 36.0], [30.6, 37.0], [30.7, 37.0], [30.8, 37.0], [30.9, 37.0], [31.0, 37.0], [31.1, 37.0], [31.2, 37.0], [31.3, 37.0], [31.4, 37.0], [31.5, 37.0], [31.6, 37.0], [31.7, 37.0], [31.8, 37.0], [31.9, 37.0], [32.0, 37.0], [32.1, 37.0], [32.2, 37.0], [32.3, 37.0], [32.4, 37.0], [32.5, 37.0], [32.6, 37.0], [32.7, 37.0], [32.8, 37.0], [32.9, 37.0], [33.0, 37.0], [33.1, 37.0], [33.2, 37.0], [33.3, 37.0], [33.4, 37.0], [33.5, 37.0], [33.6, 37.0], [33.7, 37.0], [33.8, 37.0], [33.9, 37.0], [34.0, 37.0], [34.1, 37.0], [34.2, 37.0], [34.3, 37.0], [34.4, 37.0], [34.5, 37.0], [34.6, 37.0], [34.7, 37.0], [34.8, 37.0], [34.9, 37.0], [35.0, 37.0], [35.1, 37.0], [35.2, 37.0], [35.3, 37.0], [35.4, 37.0], [35.5, 37.0], [35.6, 37.0], [35.7, 37.0], [35.8, 37.0], [35.9, 37.0], [36.0, 37.0], [36.1, 37.0], [36.2, 37.0], [36.3, 37.0], [36.4, 37.0], [36.5, 37.0], [36.6, 37.0], [36.7, 37.0], [36.8, 37.0], [36.9, 37.0], [37.0, 37.0], [37.1, 37.0], [37.2, 37.0], [37.3, 37.0], [37.4, 37.0], [37.5, 37.0], [37.6, 37.0], [37.7, 37.0], [37.8, 37.0], [37.9, 37.0], [38.0, 37.0], [38.1, 37.0], [38.2, 37.0], [38.3, 37.0], [38.4, 37.0], [38.5, 37.0], [38.6, 37.0], [38.7, 37.0], [38.8, 37.0], [38.9, 37.0], [39.0, 37.0], [39.1, 37.0], [39.2, 37.0], [39.3, 37.0], [39.4, 37.0], [39.5, 37.0], [39.6, 37.0], [39.7, 37.0], [39.8, 37.0], [39.9, 37.0], [40.0, 37.0], [40.1, 37.0], [40.2, 37.0], [40.3, 37.0], [40.4, 37.0], [40.5, 37.0], [40.6, 37.0], [40.7, 37.0], [40.8, 37.0], [40.9, 37.0], [41.0, 37.0], [41.1, 37.0], [41.2, 37.0], [41.3, 37.0], [41.4, 37.0], [41.5, 37.0], [41.6, 37.0], [41.7, 37.0], [41.8, 37.0], [41.9, 37.0], [42.0, 37.0], [42.1, 37.0], [42.2, 37.0], [42.3, 37.0], [42.4, 37.0], [42.5, 37.0], [42.6, 37.0], [42.7, 37.0], [42.8, 37.0], [42.9, 37.0], [43.0, 37.0], [43.1, 37.0], [43.2, 37.0], [43.3, 37.0], [43.4, 37.0], [43.5, 37.0], [43.6, 37.0], [43.7, 37.0], [43.8, 37.0], [43.9, 37.0], [44.0, 37.0], [44.1, 37.0], [44.2, 37.0], [44.3, 37.0], [44.4, 37.0], [44.5, 37.0], [44.6, 37.0], [44.7, 37.0], [44.8, 37.0], [44.9, 37.0], [45.0, 37.0], [45.1, 37.0], [45.2, 37.0], [45.3, 37.0], [45.4, 37.0], [45.5, 37.0], [45.6, 37.0], [45.7, 37.0], [45.8, 37.0], [45.9, 37.0], [46.0, 37.0], [46.1, 37.0], [46.2, 37.0], [46.3, 37.0], [46.4, 37.0], [46.5, 37.0], [46.6, 37.0], [46.7, 37.0], [46.8, 37.0], [46.9, 37.0], [47.0, 37.0], [47.1, 37.0], [47.2, 38.0], [47.3, 38.0], [47.4, 38.0], [47.5, 38.0], [47.6, 38.0], [47.7, 38.0], [47.8, 38.0], [47.9, 38.0], [48.0, 38.0], [48.1, 38.0], [48.2, 38.0], [48.3, 38.0], [48.4, 38.0], [48.5, 38.0], [48.6, 38.0], [48.7, 38.0], [48.8, 38.0], [48.9, 38.0], [49.0, 38.0], [49.1, 38.0], [49.2, 38.0], [49.3, 38.0], [49.4, 38.0], [49.5, 38.0], [49.6, 38.0], [49.7, 38.0], [49.8, 38.0], [49.9, 38.0], [50.0, 38.0], [50.1, 38.0], [50.2, 38.0], [50.3, 38.0], [50.4, 38.0], [50.5, 38.0], [50.6, 38.0], [50.7, 38.0], [50.8, 38.0], [50.9, 38.0], [51.0, 38.0], [51.1, 38.0], [51.2, 38.0], [51.3, 38.0], [51.4, 38.0], [51.5, 38.0], [51.6, 38.0], [51.7, 38.0], [51.8, 38.0], [51.9, 38.0], [52.0, 38.0], [52.1, 38.0], [52.2, 38.0], [52.3, 38.0], [52.4, 38.0], [52.5, 38.0], [52.6, 38.0], [52.7, 38.0], [52.8, 38.0], [52.9, 38.0], [53.0, 38.0], [53.1, 38.0], [53.2, 38.0], [53.3, 38.0], [53.4, 38.0], [53.5, 38.0], [53.6, 38.0], [53.7, 38.0], [53.8, 38.0], [53.9, 38.0], [54.0, 38.0], [54.1, 38.0], [54.2, 38.0], [54.3, 38.0], [54.4, 38.0], [54.5, 38.0], [54.6, 38.0], [54.7, 38.0], [54.8, 38.0], [54.9, 38.0], [55.0, 38.0], [55.1, 38.0], [55.2, 38.0], [55.3, 38.0], [55.4, 38.0], [55.5, 38.0], [55.6, 38.0], [55.7, 38.0], [55.8, 38.0], [55.9, 38.0], [56.0, 38.0], [56.1, 38.0], [56.2, 38.0], [56.3, 38.0], [56.4, 38.0], [56.5, 38.0], [56.6, 38.0], [56.7, 38.0], [56.8, 38.0], [56.9, 38.0], [57.0, 38.0], [57.1, 38.0], [57.2, 38.0], [57.3, 38.0], [57.4, 38.0], [57.5, 38.0], [57.6, 38.0], [57.7, 38.0], [57.8, 38.0], [57.9, 38.0], [58.0, 38.0], [58.1, 38.0], [58.2, 38.0], [58.3, 38.0], [58.4, 38.0], [58.5, 38.0], [58.6, 38.0], [58.7, 38.0], [58.8, 38.0], [58.9, 38.0], [59.0, 38.0], [59.1, 38.0], [59.2, 38.0], [59.3, 38.0], [59.4, 38.0], [59.5, 38.0], [59.6, 38.0], [59.7, 38.0], [59.8, 38.0], [59.9, 38.0], [60.0, 38.0], [60.1, 38.0], [60.2, 38.0], [60.3, 38.0], [60.4, 38.0], [60.5, 38.0], [60.6, 38.0], [60.7, 38.0], [60.8, 38.0], [60.9, 39.0], [61.0, 39.0], [61.1, 39.0], [61.2, 39.0], [61.3, 39.0], [61.4, 39.0], [61.5, 39.0], [61.6, 39.0], [61.7, 39.0], [61.8, 39.0], [61.9, 39.0], [62.0, 39.0], [62.1, 39.0], [62.2, 39.0], [62.3, 39.0], [62.4, 39.0], [62.5, 39.0], [62.6, 39.0], [62.7, 39.0], [62.8, 39.0], [62.9, 39.0], [63.0, 39.0], [63.1, 39.0], [63.2, 39.0], [63.3, 39.0], [63.4, 39.0], [63.5, 39.0], [63.6, 39.0], [63.7, 39.0], [63.8, 39.0], [63.9, 39.0], [64.0, 39.0], [64.1, 39.0], [64.2, 39.0], [64.3, 39.0], [64.4, 39.0], [64.5, 39.0], [64.6, 39.0], [64.7, 39.0], [64.8, 39.0], [64.9, 39.0], [65.0, 39.0], [65.1, 39.0], [65.2, 39.0], [65.3, 39.0], [65.4, 39.0], [65.5, 39.0], [65.6, 39.0], [65.7, 39.0], [65.8, 39.0], [65.9, 39.0], [66.0, 39.0], [66.1, 39.0], [66.2, 39.0], [66.3, 39.0], [66.4, 39.0], [66.5, 39.0], [66.6, 39.0], [66.7, 39.0], [66.8, 39.0], [66.9, 39.0], [67.0, 39.0], [67.1, 39.0], [67.2, 39.0], [67.3, 39.0], [67.4, 39.0], [67.5, 39.0], [67.6, 39.0], [67.7, 39.0], [67.8, 39.0], [67.9, 39.0], [68.0, 39.0], [68.1, 39.0], [68.2, 39.0], [68.3, 39.0], [68.4, 39.0], [68.5, 39.0], [68.6, 39.0], [68.7, 39.0], [68.8, 39.0], [68.9, 39.0], [69.0, 39.0], [69.1, 39.0], [69.2, 39.0], [69.3, 39.0], [69.4, 40.0], [69.5, 40.0], [69.6, 40.0], [69.7, 40.0], [69.8, 40.0], [69.9, 40.0], [70.0, 40.0], [70.1, 40.0], [70.2, 40.0], [70.3, 40.0], [70.4, 40.0], [70.5, 40.0], [70.6, 40.0], [70.7, 40.0], [70.8, 40.0], [70.9, 40.0], [71.0, 40.0], [71.1, 40.0], [71.2, 40.0], [71.3, 40.0], [71.4, 40.0], [71.5, 40.0], [71.6, 40.0], [71.7, 40.0], [71.8, 40.0], [71.9, 40.0], [72.0, 40.0], [72.1, 40.0], [72.2, 40.0], [72.3, 40.0], [72.4, 40.0], [72.5, 40.0], [72.6, 40.0], [72.7, 40.0], [72.8, 40.0], [72.9, 40.0], [73.0, 41.0], [73.1, 41.0], [73.2, 41.0], [73.3, 41.0], [73.4, 41.0], [73.5, 41.0], [73.6, 41.0], [73.7, 41.0], [73.8, 41.0], [73.9, 41.0], [74.0, 41.0], [74.1, 41.0], [74.2, 41.0], [74.3, 41.0], [74.4, 41.0], [74.5, 41.0], [74.6, 41.0], [74.7, 41.0], [74.8, 42.0], [74.9, 42.0], [75.0, 42.0], [75.1, 42.0], [75.2, 42.0], [75.3, 42.0], [75.4, 42.0], [75.5, 42.0], [75.6, 43.0], [75.7, 43.0], [75.8, 43.0], [75.9, 43.0], [76.0, 43.0], [76.1, 43.0], [76.2, 43.0], [76.3, 43.0], [76.4, 43.0], [76.5, 43.0], [76.6, 43.0], [76.7, 43.0], [76.8, 43.0], [76.9, 43.0], [77.0, 43.0], [77.1, 43.0], [77.2, 44.0], [77.3, 44.0], [77.4, 44.0], [77.5, 44.0], [77.6, 45.0], [77.7, 45.0], [77.8, 45.0], [77.9, 45.0], [78.0, 45.0], [78.1, 45.0], [78.2, 45.0], [78.3, 45.0], [78.4, 45.0], [78.5, 45.0], [78.6, 46.0], [78.7, 46.0], [78.8, 46.0], [78.9, 46.0], [79.0, 47.0], [79.1, 47.0], [79.2, 47.0], [79.3, 47.0], [79.4, 47.0], [79.5, 47.0], [79.6, 48.0], [79.7, 48.0], [79.8, 48.0], [79.9, 48.0], [80.0, 49.0], [80.1, 49.0], [80.2, 49.0], [80.3, 49.0], [80.4, 49.0], [80.5, 49.0], [80.6, 49.0], [80.7, 49.0], [80.8, 49.0], [80.9, 49.0], [81.0, 50.0], [81.1, 50.0], [81.2, 50.0], [81.3, 50.0], [81.4, 50.0], [81.5, 50.0], [81.6, 50.0], [81.7, 50.0], [81.8, 50.0], [81.9, 50.0], [82.0, 51.0], [82.1, 51.0], [82.2, 51.0], [82.3, 51.0], [82.4, 51.0], [82.5, 51.0], [82.6, 51.0], [82.7, 51.0], [82.8, 51.0], [82.9, 51.0], [83.0, 52.0], [83.1, 52.0], [83.2, 52.0], [83.3, 52.0], [83.4, 52.0], [83.5, 52.0], [83.6, 52.0], [83.7, 52.0], [83.8, 52.0], [83.9, 52.0], [84.0, 53.0], [84.1, 53.0], [84.2, 53.0], [84.3, 53.0], [84.4, 53.0], [84.5, 53.0], [84.6, 53.0], [84.7, 53.0], [84.8, 53.0], [84.9, 53.0], [85.0, 54.0], [85.1, 54.0], [85.2, 54.0], [85.3, 54.0], [85.4, 54.0], [85.5, 54.0], [85.6, 54.0], [85.7, 54.0], [85.8, 55.0], [85.9, 55.0], [86.0, 56.0], [86.1, 56.0], [86.2, 56.0], [86.3, 56.0], [86.4, 56.0], [86.5, 56.0], [86.6, 56.0], [86.7, 56.0], [86.8, 57.0], [86.9, 57.0], [87.0, 58.0], [87.1, 58.0], [87.2, 58.0], [87.3, 58.0], [87.4, 58.0], [87.5, 58.0], [87.6, 60.0], [87.7, 60.0], [87.8, 60.0], [87.9, 60.0], [88.0, 61.0], [88.1, 61.0], [88.2, 61.0], [88.3, 61.0], [88.4, 64.0], [88.5, 64.0], [88.6, 64.0], [88.7, 65.0], [88.8, 65.0], [88.9, 65.0], [89.0, 65.0], [89.1, 65.0], [89.2, 65.0], [89.3, 65.0], [89.4, 65.0], [89.5, 65.0], [89.6, 65.0], [89.7, 68.0], [89.8, 68.0], [89.9, 68.0], [90.0, 68.0], [90.1, 68.0], [90.2, 68.0], [90.3, 68.0], [90.4, 69.0], [90.5, 69.0], [90.6, 69.0], [90.7, 69.0], [90.8, 69.0], [90.9, 70.0], [91.0, 70.0], [91.1, 70.0], [91.2, 70.0], [91.3, 70.0], [91.4, 70.0], [91.5, 70.0], [91.6, 70.0], [91.7, 71.0], [91.8, 71.0], [91.9, 75.0], [92.0, 75.0], [92.1, 76.0], [92.2, 76.0], [92.3, 77.0], [92.4, 77.0], [92.5, 78.0], [92.6, 78.0], [92.7, 78.0], [92.8, 78.0], [92.9, 80.0], [93.0, 80.0], [93.1, 84.0], [93.2, 84.0], [93.3, 90.0], [93.4, 90.0], [93.5, 94.0], [93.6, 94.0], [93.7, 95.0], [93.8, 95.0], [93.9, 95.0], [94.0, 95.0], [94.1, 96.0], [94.2, 96.0], [94.3, 98.0], [94.4, 98.0], [94.5, 99.0], [94.6, 99.0], [94.7, 105.0], [94.8, 105.0], [94.9, 106.0], [95.0, 106.0], [95.1, 107.0], [95.2, 107.0], [95.3, 110.0], [95.4, 110.0], [95.5, 110.0], [95.6, 110.0], [95.7, 111.0], [95.8, 111.0], [95.9, 122.0], [96.0, 122.0], [96.1, 127.0], [96.2, 127.0], [96.3, 130.0], [96.4, 130.0], [96.5, 137.0], [96.6, 137.0], [96.7, 163.0], [96.8, 163.0], [96.9, 166.0], [97.0, 166.0], [97.1, 180.0], [97.2, 180.0], [97.3, 204.0], [97.4, 204.0], [97.5, 211.0], [97.6, 211.0], [97.7, 215.0], [97.8, 215.0], [97.9, 293.0], [98.0, 293.0], [98.1, 335.0], [98.2, 335.0], [98.3, 351.0], [98.4, 351.0], [98.5, 362.0], [98.6, 362.0], [98.7, 368.0], [98.8, 368.0], [98.9, 376.0], [99.0, 376.0], [99.1, 385.0], [99.2, 385.0], [99.3, 390.0], [99.4, 390.0], [99.5, 399.0], [99.6, 399.0], [99.7, 399.0], [99.8, 399.0], [99.9, 409.0], [100.0, 409.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[0.0, 1.0], [0.1, 1.0], [0.2, 1.0], [0.3, 1.0], [0.4, 1.0], [0.5, 2.0], [0.6, 2.0], [0.7, 2.0], [0.8, 2.0], [0.9, 2.0], [1.0, 2.0], [1.1, 2.0], [1.2, 2.0], [1.3, 2.0], [1.4, 2.0], [1.5, 2.0], [1.6, 2.0], [1.7, 2.0], [1.8, 2.0], [1.9, 2.0], [2.0, 2.0], [2.1, 2.0], [2.2, 2.0], [2.3, 2.0], [2.4, 2.0], [2.5, 2.0], [2.6, 2.0], [2.7, 2.0], [2.8, 2.0], [2.9, 2.0], [3.0, 2.0], [3.1, 2.0], [3.2, 2.0], [3.3, 2.0], [3.4, 2.0], [3.5, 2.0], [3.6, 2.0], [3.7, 2.0], [3.8, 2.0], [3.9, 2.0], [4.0, 2.0], [4.1, 2.0], [4.2, 2.0], [4.3, 2.0], [4.4, 2.0], [4.5, 2.0], [4.6, 2.0], [4.7, 2.0], [4.8, 2.0], [4.9, 2.0], [5.0, 2.0], [5.1, 2.0], [5.2, 2.0], [5.3, 2.0], [5.4, 2.0], [5.5, 2.0], [5.6, 2.0], [5.7, 2.0], [5.8, 2.0], [5.9, 2.0], [6.0, 2.0], [6.1, 2.0], [6.2, 2.0], [6.3, 2.0], [6.4, 2.0], [6.5, 2.0], [6.6, 2.0], [6.7, 2.0], [6.8, 2.0], [6.9, 2.0], [7.0, 2.0], [7.1, 2.0], [7.2, 2.0], [7.3, 2.0], [7.4, 2.0], [7.5, 2.0], [7.6, 2.0], [7.7, 2.0], [7.8, 2.0], [7.9, 2.0], [8.0, 2.0], [8.1, 2.0], [8.2, 2.0], [8.3, 2.0], [8.4, 2.0], [8.5, 2.0], [8.6, 2.0], [8.7, 2.0], [8.8, 2.0], [8.9, 2.0], [9.0, 2.0], [9.1, 2.0], [9.2, 2.0], [9.3, 2.0], [9.4, 2.0], [9.5, 2.0], [9.6, 2.0], [9.7, 2.0], [9.8, 2.0], [9.9, 2.0], [10.0, 2.0], [10.1, 2.0], [10.2, 2.0], [10.3, 2.0], [10.4, 2.0], [10.5, 2.0], [10.6, 2.0], [10.7, 2.0], [10.8, 2.0], [10.9, 2.0], [11.0, 2.0], [11.1, 2.0], [11.2, 2.0], [11.3, 2.0], [11.4, 2.0], [11.5, 2.0], [11.6, 2.0], [11.7, 2.0], [11.8, 2.0], [11.9, 2.0], [12.0, 2.0], [12.1, 2.0], [12.2, 2.0], [12.3, 2.0], [12.4, 2.0], [12.5, 2.0], [12.6, 2.0], [12.7, 2.0], [12.8, 2.0], [12.9, 2.0], [13.0, 2.0], [13.1, 2.0], [13.2, 2.0], [13.3, 2.0], [13.4, 2.0], [13.5, 2.0], [13.6, 2.0], [13.7, 2.0], [13.8, 2.0], [13.9, 2.0], [14.0, 2.0], [14.1, 2.0], [14.2, 2.0], [14.3, 2.0], [14.4, 2.0], [14.5, 2.0], [14.6, 2.0], [14.7, 2.0], [14.8, 2.0], [14.9, 2.0], [15.0, 2.0], [15.1, 2.0], [15.2, 2.0], [15.3, 2.0], [15.4, 2.0], [15.5, 2.0], [15.6, 2.0], [15.7, 2.0], [15.8, 2.0], [15.9, 2.0], [16.0, 2.0], [16.1, 2.0], [16.2, 2.0], [16.3, 2.0], [16.4, 2.0], [16.5, 2.0], [16.6, 2.0], [16.7, 2.0], [16.8, 2.0], [16.9, 2.0], [17.0, 2.0], [17.1, 2.0], [17.2, 2.0], [17.3, 2.0], [17.4, 2.0], [17.5, 2.0], [17.6, 2.0], [17.7, 2.0], [17.8, 2.0], [17.9, 2.0], [18.0, 2.0], [18.1, 2.0], [18.2, 2.0], [18.3, 2.0], [18.4, 2.0], [18.5, 2.0], [18.6, 2.0], [18.7, 2.0], [18.8, 2.0], [18.9, 2.0], [19.0, 2.0], [19.1, 2.0], [19.2, 2.0], [19.3, 2.0], [19.4, 2.0], [19.5, 2.0], [19.6, 2.0], [19.7, 2.0], [19.8, 2.0], [19.9, 2.0], [20.0, 2.0], [20.1, 2.0], [20.2, 2.0], [20.3, 2.0], [20.4, 2.0], [20.5, 2.0], [20.6, 2.0], [20.7, 2.0], [20.8, 2.0], [20.9, 2.0], [21.0, 2.0], [21.1, 2.0], [21.2, 2.0], [21.3, 2.0], [21.4, 2.0], [21.5, 2.0], [21.6, 2.0], [21.7, 2.0], [21.8, 2.0], [21.9, 2.0], [22.0, 2.0], [22.1, 2.0], [22.2, 2.0], [22.3, 2.0], [22.4, 2.0], [22.5, 2.0], [22.6, 2.0], [22.7, 2.0], [22.8, 2.0], [22.9, 2.0], [23.0, 2.0], [23.1, 2.0], [23.2, 2.0], [23.3, 2.0], [23.4, 2.0], [23.5, 2.0], [23.6, 2.0], [23.7, 2.0], [23.8, 2.0], [23.9, 2.0], [24.0, 2.0], [24.1, 2.0], [24.2, 2.0], [24.3, 2.0], [24.4, 2.0], [24.5, 2.0], [24.6, 2.0], [24.7, 2.0], [24.8, 2.0], [24.9, 2.0], [25.0, 2.0], [25.1, 2.0], [25.2, 2.0], [25.3, 2.0], [25.4, 2.0], [25.5, 2.0], [25.6, 2.0], [25.7, 2.0], [25.8, 2.0], [25.9, 2.0], [26.0, 2.0], [26.1, 2.0], [26.2, 2.0], [26.3, 2.0], [26.4, 2.0], [26.5, 2.0], [26.6, 2.0], [26.7, 2.0], [26.8, 2.0], [26.9, 2.0], [27.0, 2.0], [27.1, 2.0], [27.2, 2.0], [27.3, 2.0], [27.4, 2.0], [27.5, 2.0], [27.6, 2.0], [27.7, 2.0], [27.8, 2.0], [27.9, 2.0], [28.0, 2.0], [28.1, 2.0], [28.2, 2.0], [28.3, 2.0], [28.4, 2.0], [28.5, 2.0], [28.6, 2.0], [28.7, 2.0], [28.8, 2.0], [28.9, 2.0], [29.0, 2.0], [29.1, 2.0], [29.2, 2.0], [29.3, 2.0], [29.4, 2.0], [29.5, 2.0], [29.6, 2.0], [29.7, 2.0], [29.8, 2.0], [29.9, 2.0], [30.0, 2.0], [30.1, 2.0], [30.2, 2.0], [30.3, 2.0], [30.4, 2.0], [30.5, 2.0], [30.6, 2.0], [30.7, 2.0], [30.8, 2.0], [30.9, 2.0], [31.0, 2.0], [31.1, 2.0], [31.2, 2.0], [31.3, 2.0], [31.4, 2.0], [31.5, 2.0], [31.6, 2.0], [31.7, 2.0], [31.8, 2.0], [31.9, 2.0], [32.0, 2.0], [32.1, 2.0], [32.2, 2.0], [32.3, 2.0], [32.4, 2.0], [32.5, 2.0], [32.6, 2.0], [32.7, 2.0], [32.8, 2.0], [32.9, 2.0], [33.0, 2.0], [33.1, 2.0], [33.2, 2.0], [33.3, 2.0], [33.4, 2.0], [33.5, 2.0], [33.6, 2.0], [33.7, 2.0], [33.8, 2.0], [33.9, 2.0], [34.0, 2.0], [34.1, 2.0], [34.2, 2.0], [34.3, 2.0], [34.4, 2.0], [34.5, 2.0], [34.6, 2.0], [34.7, 2.0], [34.8, 2.0], [34.9, 2.0], [35.0, 2.0], [35.1, 2.0], [35.2, 2.0], [35.3, 2.0], [35.4, 2.0], [35.5, 2.0], [35.6, 2.0], [35.7, 2.0], [35.8, 2.0], [35.9, 2.0], [36.0, 2.0], [36.1, 2.0], [36.2, 2.0], [36.3, 2.0], [36.4, 2.0], [36.5, 2.0], [36.6, 2.0], [36.7, 2.0], [36.8, 2.0], [36.9, 2.0], [37.0, 2.0], [37.1, 2.0], [37.2, 2.0], [37.3, 2.0], [37.4, 2.0], [37.5, 2.0], [37.6, 2.0], [37.7, 2.0], [37.8, 2.0], [37.9, 2.0], [38.0, 2.0], [38.1, 2.0], [38.2, 2.0], [38.3, 2.0], [38.4, 2.0], [38.5, 2.0], [38.6, 2.0], [38.7, 2.0], [38.8, 2.0], [38.9, 2.0], [39.0, 2.0], [39.1, 2.0], [39.2, 2.0], [39.3, 2.0], [39.4, 2.0], [39.5, 2.0], [39.6, 2.0], [39.7, 2.0], [39.8, 2.0], [39.9, 2.0], [40.0, 2.0], [40.1, 2.0], [40.2, 2.0], [40.3, 2.0], [40.4, 2.0], [40.5, 2.0], [40.6, 2.0], [40.7, 2.0], [40.8, 2.0], [40.9, 2.0], [41.0, 2.0], [41.1, 2.0], [41.2, 2.0], [41.3, 2.0], [41.4, 2.0], [41.5, 2.0], [41.6, 2.0], [41.7, 2.0], [41.8, 2.0], [41.9, 2.0], [42.0, 2.0], [42.1, 2.0], [42.2, 2.0], [42.3, 2.0], [42.4, 2.0], [42.5, 2.0], [42.6, 2.0], [42.7, 2.0], [42.8, 2.0], [42.9, 2.0], [43.0, 2.0], [43.1, 2.0], [43.2, 2.0], [43.3, 2.0], [43.4, 2.0], [43.5, 2.0], [43.6, 2.0], [43.7, 2.0], [43.8, 2.0], [43.9, 2.0], [44.0, 2.0], [44.1, 2.0], [44.2, 2.0], [44.3, 2.0], [44.4, 2.0], [44.5, 2.0], [44.6, 2.0], [44.7, 2.0], [44.8, 2.0], [44.9, 2.0], [45.0, 2.0], [45.1, 2.0], [45.2, 2.0], [45.3, 2.0], [45.4, 2.0], [45.5, 2.0], [45.6, 2.0], [45.7, 2.0], [45.8, 2.0], [45.9, 2.0], [46.0, 2.0], [46.1, 2.0], [46.2, 2.0], [46.3, 2.0], [46.4, 2.0], [46.5, 2.0], [46.6, 2.0], [46.7, 2.0], [46.8, 2.0], [46.9, 2.0], [47.0, 2.0], [47.1, 2.0], [47.2, 2.0], [47.3, 2.0], [47.4, 2.0], [47.5, 2.0], [47.6, 2.0], [47.7, 2.0], [47.8, 2.0], [47.9, 2.0], [48.0, 2.0], [48.1, 2.0], [48.2, 2.0], [48.3, 2.0], [48.4, 2.0], [48.5, 2.0], [48.6, 2.0], [48.7, 2.0], [48.8, 2.0], [48.9, 2.0], [49.0, 2.0], [49.1, 2.0], [49.2, 2.0], [49.3, 2.0], [49.4, 2.0], [49.5, 2.0], [49.6, 2.0], [49.7, 2.0], [49.8, 2.0], [49.9, 2.0], [50.0, 2.0], [50.1, 2.0], [50.2, 2.0], [50.3, 2.0], [50.4, 2.0], [50.5, 2.0], [50.6, 2.0], [50.7, 2.0], [50.8, 2.0], [50.9, 2.0], [51.0, 2.0], [51.1, 2.0], [51.2, 2.0], [51.3, 2.0], [51.4, 2.0], [51.5, 2.0], [51.6, 2.0], [51.7, 2.0], [51.8, 2.0], [51.9, 2.0], [52.0, 2.0], [52.1, 2.0], [52.2, 2.0], [52.3, 2.0], [52.4, 2.0], [52.5, 2.0], [52.6, 2.0], [52.7, 2.0], [52.8, 2.0], [52.9, 2.0], [53.0, 2.0], [53.1, 2.0], [53.2, 2.0], [53.3, 2.0], [53.4, 2.0], [53.5, 2.0], [53.6, 2.0], [53.7, 2.0], [53.8, 2.0], [53.9, 2.0], [54.0, 2.0], [54.1, 2.0], [54.2, 2.0], [54.3, 2.0], [54.4, 2.0], [54.5, 2.0], [54.6, 2.0], [54.7, 2.0], [54.8, 2.0], [54.9, 2.0], [55.0, 2.0], [55.1, 2.0], [55.2, 2.0], [55.3, 2.0], [55.4, 2.0], [55.5, 2.0], [55.6, 2.0], [55.7, 2.0], [55.8, 2.0], [55.9, 2.0], [56.0, 2.0], [56.1, 2.0], [56.2, 2.0], [56.3, 2.0], [56.4, 2.0], [56.5, 2.0], [56.6, 2.0], [56.7, 2.0], [56.8, 2.0], [56.9, 2.0], [57.0, 2.0], [57.1, 2.0], [57.2, 2.0], [57.3, 2.0], [57.4, 2.0], [57.5, 2.0], [57.6, 2.0], [57.7, 2.0], [57.8, 2.0], [57.9, 2.0], [58.0, 2.0], [58.1, 2.0], [58.2, 2.0], [58.3, 2.0], [58.4, 2.0], [58.5, 2.0], [58.6, 2.0], [58.7, 2.0], [58.8, 2.0], [58.9, 2.0], [59.0, 2.0], [59.1, 3.0], [59.2, 3.0], [59.3, 3.0], [59.4, 3.0], [59.5, 3.0], [59.6, 3.0], [59.7, 3.0], [59.8, 3.0], [59.9, 3.0], [60.0, 3.0], [60.1, 3.0], [60.2, 3.0], [60.3, 3.0], [60.4, 3.0], [60.5, 3.0], [60.6, 3.0], [60.7, 3.0], [60.8, 3.0], [60.9, 3.0], [61.0, 3.0], [61.1, 3.0], [61.2, 3.0], [61.3, 3.0], [61.4, 3.0], [61.5, 3.0], [61.6, 3.0], [61.7, 3.0], [61.8, 3.0], [61.9, 3.0], [62.0, 3.0], [62.1, 3.0], [62.2, 3.0], [62.3, 3.0], [62.4, 3.0], [62.5, 3.0], [62.6, 3.0], [62.7, 3.0], [62.8, 3.0], [62.9, 3.0], [63.0, 3.0], [63.1, 3.0], [63.2, 3.0], [63.3, 3.0], [63.4, 3.0], [63.5, 3.0], [63.6, 3.0], [63.7, 3.0], [63.8, 3.0], [63.9, 3.0], [64.0, 3.0], [64.1, 3.0], [64.2, 3.0], [64.3, 3.0], [64.4, 3.0], [64.5, 3.0], [64.6, 3.0], [64.7, 3.0], [64.8, 3.0], [64.9, 3.0], [65.0, 3.0], [65.1, 3.0], [65.2, 3.0], [65.3, 3.0], [65.4, 3.0], [65.5, 3.0], [65.6, 3.0], [65.7, 3.0], [65.8, 3.0], [65.9, 3.0], [66.0, 3.0], [66.1, 3.0], [66.2, 3.0], [66.3, 3.0], [66.4, 3.0], [66.5, 3.0], [66.6, 3.0], [66.7, 3.0], [66.8, 3.0], [66.9, 3.0], [67.0, 3.0], [67.1, 3.0], [67.2, 3.0], [67.3, 3.0], [67.4, 3.0], [67.5, 3.0], [67.6, 3.0], [67.7, 3.0], [67.8, 3.0], [67.9, 3.0], [68.0, 3.0], [68.1, 3.0], [68.2, 3.0], [68.3, 3.0], [68.4, 3.0], [68.5, 3.0], [68.6, 3.0], [68.7, 3.0], [68.8, 3.0], [68.9, 3.0], [69.0, 3.0], [69.1, 3.0], [69.2, 3.0], [69.3, 3.0], [69.4, 3.0], [69.5, 3.0], [69.6, 3.0], [69.7, 3.0], [69.8, 3.0], [69.9, 3.0], [70.0, 3.0], [70.1, 3.0], [70.2, 3.0], [70.3, 3.0], [70.4, 3.0], [70.5, 3.0], [70.6, 3.0], [70.7, 3.0], [70.8, 3.0], [70.9, 3.0], [71.0, 3.0], [71.1, 3.0], [71.2, 3.0], [71.3, 3.0], [71.4, 3.0], [71.5, 3.0], [71.6, 3.0], [71.7, 3.0], [71.8, 3.0], [71.9, 3.0], [72.0, 3.0], [72.1, 3.0], [72.2, 3.0], [72.3, 3.0], [72.4, 3.0], [72.5, 3.0], [72.6, 3.0], [72.7, 3.0], [72.8, 3.0], [72.9, 3.0], [73.0, 3.0], [73.1, 3.0], [73.2, 3.0], [73.3, 3.0], [73.4, 3.0], [73.5, 3.0], [73.6, 3.0], [73.7, 3.0], [73.8, 3.0], [73.9, 3.0], [74.0, 3.0], [74.1, 3.0], [74.2, 3.0], [74.3, 3.0], [74.4, 3.0], [74.5, 3.0], [74.6, 3.0], [74.7, 3.0], [74.8, 3.0], [74.9, 3.0], [75.0, 3.0], [75.1, 3.0], [75.2, 3.0], [75.3, 3.0], [75.4, 3.0], [75.5, 3.0], [75.6, 3.0], [75.7, 3.0], [75.8, 3.0], [75.9, 3.0], [76.0, 3.0], [76.1, 3.0], [76.2, 3.0], [76.3, 3.0], [76.4, 3.0], [76.5, 3.0], [76.6, 3.0], [76.7, 3.0], [76.8, 3.0], [76.9, 3.0], [77.0, 3.0], [77.1, 3.0], [77.2, 3.0], [77.3, 3.0], [77.4, 3.0], [77.5, 3.0], [77.6, 3.0], [77.7, 3.0], [77.8, 3.0], [77.9, 3.0], [78.0, 3.0], [78.1, 3.0], [78.2, 3.0], [78.3, 3.0], [78.4, 3.0], [78.5, 3.0], [78.6, 3.0], [78.7, 3.0], [78.8, 3.0], [78.9, 3.0], [79.0, 3.0], [79.1, 3.0], [79.2, 3.0], [79.3, 3.0], [79.4, 3.0], [79.5, 3.0], [79.6, 3.0], [79.7, 3.0], [79.8, 3.0], [79.9, 3.0], [80.0, 3.0], [80.1, 3.0], [80.2, 3.0], [80.3, 3.0], [80.4, 3.0], [80.5, 3.0], [80.6, 3.0], [80.7, 3.0], [80.8, 3.0], [80.9, 3.0], [81.0, 3.0], [81.1, 3.0], [81.2, 4.0], [81.3, 4.0], [81.4, 4.0], [81.5, 4.0], [81.6, 4.0], [81.7, 4.0], [81.8, 4.0], [81.9, 4.0], [82.0, 4.0], [82.1, 4.0], [82.2, 4.0], [82.3, 4.0], [82.4, 4.0], [82.5, 4.0], [82.6, 4.0], [82.7, 4.0], [82.8, 4.0], [82.9, 4.0], [83.0, 4.0], [83.1, 4.0], [83.2, 4.0], [83.3, 4.0], [83.4, 4.0], [83.5, 4.0], [83.6, 4.0], [83.7, 4.0], [83.8, 4.0], [83.9, 4.0], [84.0, 4.0], [84.1, 4.0], [84.2, 4.0], [84.3, 4.0], [84.4, 4.0], [84.5, 4.0], [84.6, 4.0], [84.7, 4.0], [84.8, 4.0], [84.9, 4.0], [85.0, 4.0], [85.1, 4.0], [85.2, 4.0], [85.3, 4.0], [85.4, 4.0], [85.5, 4.0], [85.6, 4.0], [85.7, 4.0], [85.8, 4.0], [85.9, 4.0], [86.0, 4.0], [86.1, 4.0], [86.2, 4.0], [86.3, 4.0], [86.4, 4.0], [86.5, 4.0], [86.6, 4.0], [86.7, 4.0], [86.8, 4.0], [86.9, 4.0], [87.0, 4.0], [87.1, 4.0], [87.2, 4.0], [87.3, 4.0], [87.4, 4.0], [87.5, 4.0], [87.6, 4.0], [87.7, 4.0], [87.8, 4.0], [87.9, 4.0], [88.0, 4.0], [88.1, 4.0], [88.2, 4.0], [88.3, 4.0], [88.4, 4.0], [88.5, 4.0], [88.6, 4.0], [88.7, 4.0], [88.8, 4.0], [88.9, 4.0], [89.0, 5.0], [89.1, 5.0], [89.2, 5.0], [89.3, 5.0], [89.4, 5.0], [89.5, 5.0], [89.6, 5.0], [89.7, 5.0], [89.8, 5.0], [89.9, 5.0], [90.0, 5.0], [90.1, 5.0], [90.2, 5.0], [90.3, 5.0], [90.4, 5.0], [90.5, 5.0], [90.6, 5.0], [90.7, 5.0], [90.8, 5.0], [90.9, 5.0], [91.0, 5.0], [91.1, 5.0], [91.2, 5.0], [91.3, 5.0], [91.4, 5.0], [91.5, 5.0], [91.6, 5.0], [91.7, 5.0], [91.8, 5.0], [91.9, 5.0], [92.0, 5.0], [92.1, 5.0], [92.2, 5.0], [92.3, 5.0], [92.4, 5.0], [92.5, 5.0], [92.6, 5.0], [92.7, 5.0], [92.8, 5.0], [92.9, 5.0], [93.0, 5.0], [93.1, 5.0], [93.2, 5.0], [93.3, 5.0], [93.4, 5.0], [93.5, 5.0], [93.6, 5.0], [93.7, 6.0], [93.8, 6.0], [93.9, 6.0], [94.0, 6.0], [94.1, 6.0], [94.2, 6.0], [94.3, 6.0], [94.4, 6.0], [94.5, 6.0], [94.6, 6.0], [94.7, 6.0], [94.8, 6.0], [94.9, 6.0], [95.0, 6.0], [95.1, 6.0], [95.2, 6.0], [95.3, 6.0], [95.4, 6.0], [95.5, 6.0], [95.6, 6.0], [95.7, 6.0], [95.8, 6.0], [95.9, 6.0], [96.0, 6.0], [96.1, 6.0], [96.2, 6.0], [96.3, 6.0], [96.4, 6.0], [96.5, 7.0], [96.6, 7.0], [96.7, 7.0], [96.8, 7.0], [96.9, 7.0], [97.0, 7.0], [97.1, 7.0], [97.2, 7.0], [97.3, 7.0], [97.4, 7.0], [97.5, 7.0], [97.6, 7.0], [97.7, 7.0], [97.8, 7.0], [97.9, 7.0], [98.0, 7.0], [98.1, 7.0], [98.2, 8.0], [98.3, 8.0], [98.4, 8.0], [98.5, 8.0], [98.6, 8.0], [98.7, 8.0], [98.8, 8.0], [98.9, 8.0], [99.0, 9.0], [99.1, 9.0], [99.2, 9.0], [99.3, 9.0], [99.4, 9.0], [99.5, 9.0], [99.6, 9.0], [99.7, 10.0], [99.8, 10.0], [99.9, 10.0], [100.0, 12.0]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 100.0, "title": "Response Time Percentiles"}},
        getOptions: function() {
            return {
                series: {
                    points: { show: false }
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentiles'
                },
                xaxis: {
                    tickDecimals: 1,
                    axisLabel: "Percentiles",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Percentile value in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : %x.2 percentile was %y ms"
                },
                selection: { mode: "xy" },
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentiles"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesPercentiles"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesPercentiles"), dataset, prepareOverviewOptions(options));
        }
};

/**
 * @param elementId Id of element where we display message
 */
function setEmptyGraph(elementId) {
    $(function() {
        $(elementId).text("No graph series with filter="+seriesFilter);
    });
}

// Response times percentiles
function refreshResponseTimePercentiles() {
    var infos = responseTimePercentilesInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimePercentiles");
        return;
    }
    if (isGraph($("#flotResponseTimesPercentiles"))){
        infos.createGraph();
    } else {
        var choiceContainer = $("#choicesResponseTimePercentiles");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesPercentiles", "#overviewResponseTimesPercentiles");
        $('#bodyResponseTimePercentiles .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimeDistributionInfos = {
        data: {"result": {"minY": 1.0, "minX": 0.0, "maxY": 2500.0, "series": [{"data": [[0.0, 500.0]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[0.0, 453.0], [300.0, 1.0], [200.0, 7.0], [400.0, 2.0], [100.0, 29.0], [500.0, 8.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[0.0, 1372.0], [100.0, 115.0], [200.0, 13.0]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[0.0, 500.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[0.0, 500.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[0.0, 500.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[0.0, 500.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[0.0, 500.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[0.0, 500.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[0.0, 989.0], [100.0, 11.0]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[0.0, 500.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[0.0, 1500.0]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[0.0, 473.0], [300.0, 9.0], [400.0, 1.0], [200.0, 4.0], [100.0, 13.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[0.0, 2500.0]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 100, "maxX": 500.0, "title": "Response Time Distribution"}},
        getOptions: function() {
            var granularity = this.data.result.granularity;
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    barWidth: this.data.result.granularity
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " responses for " + label + " were between " + xval + " and " + (xval + granularity) + " ms";
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimeDistribution"), prepareData(data.result.series, $("#choicesResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshResponseTimeDistribution() {
    var infos = responseTimeDistributionInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeDistribution");
        return;
    }
    if (isGraph($("#flotResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var syntheticResponseTimeDistributionInfos = {
        data: {"result": {"minY": 10500.0, "minX": 0.0, "ticks": [[0, "Requests having \nresponse time <= 500ms"], [1, "Requests having \nresponse time > 500ms and <= 2 000ms"], [2, "Requests having \nresponse time > 2 000ms"], [3, "Requests in error"]], "maxY": 10500.0, "series": [{"data": [[0.0, 10500.0]], "color": "#9ACD32", "isOverall": false, "label": "Requests having \nresponse time <= 500ms", "isController": false}, {"data": [], "color": "yellow", "isOverall": false, "label": "Requests having \nresponse time > 500ms and <= 2 000ms", "isController": false}, {"data": [], "color": "orange", "isOverall": false, "label": "Requests having \nresponse time > 2 000ms", "isController": false}, {"data": [], "color": "#FF6347", "isOverall": false, "label": "Requests in error", "isController": false}], "supportsControllersDiscrimination": false, "maxX": 4.9E-324, "title": "Synthetic Response Times Distribution"}},
        getOptions: function() {
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendSyntheticResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times ranges",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                    tickLength:0,
                    min:-0.5,
                    max:3.5
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    align: "center",
                    barWidth: 0.25,
                    fill:.75
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " " + label;
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            options.xaxis.ticks = data.result.ticks;
            $.plot($("#flotSyntheticResponseTimeDistribution"), prepareData(data.result.series, $("#choicesSyntheticResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshSyntheticResponseTimeDistribution() {
    var infos = syntheticResponseTimeDistributionInfos;
    prepareSeries(infos.data, true);
    if (isGraph($("#flotSyntheticResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerSyntheticResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var activeThreadsOverTimeInfos = {
        data: {"result": {"minY": 106.95454545454545, "minX": 1.74674562E12, "maxY": 500.0, "series": [{"data": [[1.74674586E12, 492.20984162895945], [1.74674568E12, 468.41203526255265], [1.74674574E12, 500.0], [1.74674604E12, 238.11842105263156], [1.74674562E12, 208.57266387726688], [1.7467461E12, 106.95454545454545], [1.74674592E12, 455.6319018404908], [1.74674598E12, 381.3429158110887], [1.7467458E12, 499.69258987527496]], "isOverall": false, "label": "Thread Group", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.7467461E12, "title": "Active Threads Over Time"}},
        getOptions: function() {
            return {
                series: {
                    stack: true,
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 6,
                    show: true,
                    container: '#legendActiveThreadsOverTime'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                selection: {
                    mode: 'xy'
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : At %x there were %y active threads"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesActiveThreadsOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotActiveThreadsOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewActiveThreadsOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Active Threads Over Time
function refreshActiveThreadsOverTime(fixTimestamps) {
    var infos = activeThreadsOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotActiveThreadsOverTime"))) {
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesActiveThreadsOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotActiveThreadsOverTime", "#overviewActiveThreadsOverTime");
        $('#footerActiveThreadsOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var timeVsThreadsInfos = {
        data: {"result": {"minY": 1.8, "minX": 2.0, "maxY": 529.75, "series": [{"data": [[263.0, 4.0], [313.0, 3.0], [328.0, 5.0], [346.0, 3.0], [363.0, 3.0], [360.0, 4.0], [383.0, 2.5], [380.0, 4.0], [378.0, 3.5], [373.0, 4.0], [371.0, 3.0], [370.0, 3.0], [369.0, 4.0], [399.0, 3.0], [397.0, 4.0], [391.0, 3.0], [388.0, 3.0], [415.0, 4.0], [414.0, 3.0], [412.0, 3.0], [411.0, 3.0], [410.0, 3.5], [406.0, 3.25], [405.0, 3.25], [403.0, 3.0], [430.0, 3.0], [426.0, 4.0], [425.0, 2.0], [422.0, 3.0], [421.0, 3.0], [420.0, 3.0], [418.0, 3.0], [417.0, 4.6], [447.0, 3.0], [446.0, 3.0], [445.0, 3.4], [444.0, 4.0], [442.0, 4.0], [437.0, 3.0], [436.0, 3.3333333333333335], [462.0, 3.0], [461.0, 3.6666666666666665], [460.0, 6.0], [459.0, 3.0], [456.0, 4.0], [452.0, 3.333333333333333], [451.0, 3.0], [449.0, 3.0], [467.0, 2.5], [479.0, 4.0], [478.0, 3.1428571428571432], [475.0, 3.5], [473.0, 3.6666666666666665], [471.0, 3.0], [470.0, 3.4000000000000004], [468.0, 5.25], [466.0, 2.75], [465.0, 3.25], [464.0, 3.4], [494.0, 3.439024390243903], [495.0, 3.5294117647058827], [493.0, 3.8000000000000003], [492.0, 3.6], [491.0, 3.4], [490.0, 3.6666666666666665], [489.0, 3.3333333333333335], [488.0, 4.0], [487.0, 4.0], [481.0, 3.0], [480.0, 4.0], [483.0, 4.0], [482.0, 3.4], [486.0, 2.6666666666666665], [485.0, 3.5], [484.0, 4.0], [500.0, 3.8278145695364247], [498.0, 3.714285714285714], [497.0, 3.666666666666666]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[478.8199999999998, 3.618000000000002]], "isOverall": false, "label": "Get all albums-Aggregated", "isController": false}, {"data": [[12.0, 529.75], [13.0, 481.5], [14.0, 217.5], [15.0, 61.0], [16.0, 59.0], [17.0, 64.0], [18.0, 65.0], [19.0, 60.0], [20.0, 64.0], [21.0, 61.0], [22.0, 57.0], [23.0, 61.0], [24.0, 60.0], [25.0, 59.0], [26.0, 70.0], [27.0, 62.0], [28.0, 58.0], [29.0, 60.0], [30.0, 63.0], [31.0, 62.0], [32.0, 61.0], [33.0, 62.0], [34.0, 63.0], [35.0, 61.0], [36.0, 59.0], [37.0, 55.0], [38.0, 60.0], [39.0, 64.0], [41.0, 60.0], [40.0, 58.0], [42.0, 62.0], [43.0, 57.0], [44.0, 61.0], [45.0, 59.0], [46.0, 59.0], [47.0, 59.0], [48.0, 59.0], [49.0, 61.0], [50.0, 56.0], [51.0, 60.0], [53.0, 63.0], [52.0, 61.0], [55.0, 59.0], [54.0, 58.0], [57.0, 58.0], [56.0, 59.0], [58.0, 57.0], [59.0, 64.0], [61.0, 60.0], [60.0, 60.0], [62.0, 56.0], [63.0, 56.0], [65.0, 56.0], [66.0, 57.0], [67.0, 65.0], [64.0, 59.0], [71.0, 57.0], [69.0, 62.0], [68.0, 57.0], [70.0, 61.0], [73.0, 60.0], [72.0, 59.0], [74.0, 60.0], [75.0, 61.0], [79.0, 56.0], [77.0, 58.0], [76.0, 69.0], [78.0, 60.0], [80.0, 56.0], [82.0, 56.0], [83.0, 61.0], [81.0, 57.0], [84.0, 73.0], [86.0, 57.0], [87.0, 54.0], [85.0, 57.0], [88.0, 58.0], [89.0, 55.0], [90.0, 60.0], [91.0, 55.0], [94.0, 57.0], [92.0, 61.0], [95.0, 63.0], [93.0, 61.0], [96.0, 55.0], [99.0, 58.0], [98.0, 57.0], [97.0, 58.0], [101.0, 59.0], [102.0, 56.0], [103.0, 58.0], [100.0, 57.0], [106.0, 55.0], [104.0, 57.0], [107.0, 62.0], [105.0, 59.0], [109.0, 71.0], [111.0, 57.0], [110.0, 58.0], [108.0, 58.0], [113.0, 70.0], [112.0, 61.0], [114.0, 57.0], [115.0, 55.0], [117.0, 59.0], [119.0, 58.0], [116.0, 67.0], [118.0, 58.0], [123.0, 59.0], [120.0, 59.0], [122.0, 54.0], [121.0, 58.0], [125.0, 56.0], [124.0, 59.0], [127.0, 57.0], [126.0, 61.0], [129.0, 67.0], [135.0, 58.0], [131.0, 56.0], [134.0, 71.0], [128.0, 59.0], [133.0, 56.0], [132.0, 58.0], [130.0, 57.0], [136.0, 59.0], [141.0, 58.0], [137.0, 61.0], [142.0, 55.0], [138.0, 57.0], [143.0, 59.0], [139.0, 58.0], [140.0, 56.0], [144.0, 56.0], [148.0, 57.0], [150.0, 58.0], [151.0, 56.0], [145.0, 56.0], [149.0, 57.0], [147.0, 56.0], [146.0, 56.0], [154.0, 55.0], [153.0, 56.0], [157.0, 53.0], [155.0, 57.0], [152.0, 58.0], [158.0, 58.0], [156.0, 55.0], [159.0, 71.0], [162.0, 56.0], [166.0, 55.0], [164.0, 55.0], [161.0, 58.0], [165.0, 57.0], [167.0, 58.0], [160.0, 56.0], [163.0, 58.0], [168.0, 57.0], [172.0, 55.0], [169.0, 57.0], [173.0, 56.0], [174.0, 58.0], [170.0, 57.0], [171.0, 59.0], [175.0, 56.0], [181.0, 57.0], [177.0, 59.0], [182.0, 56.0], [180.0, 55.0], [178.0, 60.0], [183.0, 58.0], [176.0, 58.0], [179.0, 58.0], [185.0, 59.0], [187.0, 57.0], [184.0, 68.0], [188.0, 53.0], [191.0, 60.0], [189.0, 56.0], [190.0, 60.0], [186.0, 56.0], [192.0, 59.0], [199.0, 57.0], [195.0, 57.0], [196.0, 56.0], [193.0, 55.0], [194.0, 57.0], [198.0, 55.0], [197.0, 59.0], [204.0, 59.0], [207.0, 57.0], [203.0, 58.0], [205.0, 57.0], [202.0, 56.0], [206.0, 57.0], [201.0, 59.0], [200.0, 58.0], [211.0, 56.0], [210.0, 59.0], [209.0, 70.0], [212.0, 55.0], [215.0, 55.0], [213.0, 60.0], [214.0, 59.0], [208.0, 57.0], [218.0, 58.0], [216.0, 59.0], [220.0, 58.0], [223.0, 59.0], [221.0, 55.0], [219.0, 56.0], [217.0, 60.0], [222.0, 56.0], [229.0, 57.0], [225.0, 60.0], [228.0, 59.0], [224.0, 55.0], [230.0, 52.0], [231.0, 58.0], [227.0, 58.0], [226.0, 58.0], [239.0, 58.0], [233.0, 54.0], [235.0, 54.0], [236.0, 56.0], [237.0, 52.0], [238.0, 60.0], [232.0, 57.0], [234.0, 70.0], [247.0, 56.0], [241.0, 52.0], [242.0, 57.0], [240.0, 56.0], [245.0, 58.0], [246.0, 59.0], [243.0, 57.0], [244.0, 56.0], [254.0, 55.0], [255.0, 56.0], [253.0, 57.0], [249.0, 57.0], [252.0, 55.0], [250.0, 55.0], [251.0, 56.0], [248.0, 65.0], [268.0, 56.0], [262.0, 55.0], [260.0, 57.0], [271.0, 60.0], [267.0, 56.0], [270.0, 54.0], [269.0, 59.0], [266.0, 83.0], [264.0, 55.0], [265.0, 57.0], [261.0, 58.0], [259.0, 71.0], [258.0, 57.0], [263.0, 57.0], [256.0, 54.0], [257.0, 56.0], [284.0, 59.0], [278.0, 63.0], [276.0, 59.0], [277.0, 56.0], [279.0, 54.0], [272.0, 56.0], [273.0, 52.0], [282.0, 61.0], [283.0, 59.0], [287.0, 57.0], [280.0, 91.0], [281.0, 63.0], [285.0, 56.0], [286.0, 64.0], [274.0, 56.0], [275.0, 54.0], [303.0, 65.0], [295.0, 59.0], [297.0, 57.0], [288.0, 57.0], [289.0, 93.0], [290.0, 53.0], [291.0, 58.0], [301.0, 59.0], [302.0, 61.0], [300.0, 59.0], [298.0, 85.0], [299.0, 61.0], [294.0, 57.0], [296.0, 94.0], [293.0, 81.0], [292.0, 55.0], [306.0, 64.0], [308.0, 70.0], [309.0, 62.0], [313.0, 62.0], [315.0, 74.0], [314.0, 72.0], [310.0, 63.0], [318.0, 79.0], [312.0, 93.0], [319.0, 56.0], [304.0, 63.0], [311.0, 58.0], [305.0, 60.0], [316.0, 66.0], [307.0, 61.0], [317.0, 84.0], [320.0, 55.0], [326.0, 95.0], [331.0, 60.0], [330.0, 60.0], [329.0, 59.0], [321.0, 58.0], [322.0, 64.0], [323.0, 53.0], [332.0, 52.0], [327.0, 89.0], [328.0, 61.0], [324.0, 102.0], [325.0, 56.0], [333.0, 58.0], [335.0, 57.0], [334.0, 55.0], [350.0, 68.0], [338.0, 162.5], [349.0, 139.5], [339.0, 58.0], [351.0, 58.0], [341.0, 62.0], [342.0, 70.0], [343.0, 73.0], [345.0, 56.0], [347.0, 57.0], [346.0, 59.0], [336.0, 71.0], [340.0, 59.0], [344.0, 64.0], [365.0, 56.0], [360.0, 56.0], [367.0, 202.0], [358.0, 62.0], [354.0, 55.0], [353.0, 79.0], [352.0, 98.0], [359.0, 65.0], [356.0, 86.0], [357.0, 56.0], [364.0, 85.0], [355.0, 92.0], [361.0, 56.0], [362.0, 58.0], [363.0, 65.0], [371.0, 59.0], [375.0, 56.0], [373.0, 91.0], [374.0, 71.0], [372.0, 70.0], [376.0, 75.0], [383.0, 54.0], [382.0, 93.0], [370.0, 55.0], [369.0, 175.66666666666666], [380.0, 86.0], [381.0, 72.0], [377.0, 97.0], [378.0, 97.0], [379.0, 53.0], [386.0, 61.0], [398.0, 113.5], [395.0, 98.0], [394.0, 98.0], [399.0, 56.0], [392.0, 119.5], [393.0, 53.0], [388.0, 102.0], [389.0, 54.0], [390.0, 77.0], [387.0, 72.0], [396.0, 90.0], [385.0, 64.0], [384.0, 93.0], [402.0, 61.0], [415.0, 121.0], [403.0, 55.0], [409.0, 94.0], [411.0, 68.0], [410.0, 59.0], [405.0, 55.0], [407.0, 85.0], [406.0, 57.0], [404.0, 60.0], [408.0, 71.0], [412.0, 60.0], [414.0, 198.0], [401.0, 52.0], [400.0, 90.0], [418.0, 89.0], [422.0, 82.0], [420.0, 76.0], [426.0, 61.0], [425.0, 53.0], [427.0, 56.0], [430.0, 78.0], [429.0, 101.0], [419.0, 57.0], [431.0, 59.0], [423.0, 57.0], [416.0, 56.0], [417.0, 59.0], [424.0, 60.0], [421.0, 101.0], [446.0, 61.0], [433.0, 106.5], [437.0, 60.0], [436.0, 57.0], [435.0, 58.0], [445.0, 94.0], [444.0, 97.0], [443.0, 105.0], [442.0, 178.33333333333334], [439.0, 166.0], [441.0, 290.0], [447.0, 92.0], [434.0, 93.0], [450.0, 79.0], [455.0, 69.0], [452.0, 61.0], [448.0, 81.0], [449.0, 92.0], [458.0, 110.0], [457.0, 219.0], [459.0, 56.0], [451.0, 52.0], [462.0, 61.0], [461.0, 60.0], [460.0, 76.0], [453.0, 81.0], [454.0, 65.0], [464.0, 101.0], [467.0, 90.5], [471.0, 58.0], [470.0, 96.0], [474.0, 98.0], [469.0, 54.0], [465.0, 97.0], [475.0, 56.0], [479.0, 54.0], [476.0, 55.0], [478.0, 53.0], [477.0, 53.0], [473.0, 191.0], [468.0, 56.0], [482.0, 68.0], [480.0, 95.0], [481.0, 81.0], [494.0, 77.0], [493.0, 90.0], [483.0, 58.0], [492.0, 56.0], [486.0, 53.0], [495.0, 56.0], [484.0, 70.0], [485.0, 73.0], [488.0, 114.0], [490.0, 122.0], [491.0, 54.0], [487.0, 58.0], [498.0, 97.5], [500.0, 58.0], [499.0, 60.0], [497.0, 120.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[250.7, 78.90800000000003]], "isOverall": false, "label": "Home page-Aggregated", "isController": true}, {"data": [[266.0, 71.0], [271.0, 36.0], [280.0, 86.0], [281.0, 88.0], [289.0, 68.5], [293.0, 59.0], [294.0, 55.0], [296.0, 62.0], [298.0, 54.0], [299.0, 45.0], [312.0, 66.5], [314.0, 55.0], [316.0, 36.0], [317.0, 58.0], [318.0, 51.0], [320.0, 34.0], [322.0, 42.0], [324.0, 71.5], [326.0, 58.5], [327.0, 60.0], [329.0, 36.0], [330.0, 51.0], [331.0, 36.0], [337.0, 113.0], [336.0, 46.0], [338.0, 142.33333333333334], [340.0, 38.0], [342.0, 38.0], [343.0, 53.0], [344.0, 55.0], [348.0, 102.5], [349.0, 91.0], [350.0, 46.0], [353.0, 63.0], [352.0, 85.0], [355.0, 66.0], [356.0, 77.0], [358.0, 38.0], [359.0, 38.0], [364.0, 72.0], [366.0, 63.0], [367.0, 156.33333333333334], [380.0, 67.0], [368.0, 205.66666666666666], [369.0, 152.0], [372.0, 46.0], [373.0, 64.0], [374.0, 45.0], [376.0, 54.0], [377.0, 71.0], [378.0, 87.0], [381.0, 53.0], [382.0, 90.5], [385.0, 39.0], [384.0, 72.0], [387.0, 45.5], [388.0, 83.5], [389.0, 35.0], [391.0, 147.0], [394.0, 49.0], [395.0, 82.0], [397.0, 105.5], [398.0, 79.0], [399.0, 40.0], [413.0, 96.5], [400.0, 70.0], [405.0, 42.0], [407.0, 63.0], [408.0, 52.0], [409.0, 61.0], [410.0, 40.0], [414.0, 175.0], [415.0, 68.0], [423.0, 44.0], [418.0, 76.0], [420.0, 61.0], [421.0, 50.0], [422.0, 58.5], [424.0, 31.5], [426.0, 44.0], [428.0, 69.0], [429.0, 64.0], [430.0, 68.0], [431.0, 60.0], [435.0, 39.0], [433.0, 88.0], [432.0, 75.0], [434.0, 61.0], [438.0, 40.0], [439.0, 131.5], [440.0, 174.5], [446.0, 82.0], [447.0, 72.0], [444.0, 58.5], [445.0, 81.0], [441.0, 219.0], [442.0, 76.0], [443.0, 75.0], [449.0, 78.0], [448.0, 58.0], [450.0, 61.0], [460.0, 63.0], [453.0, 52.0], [454.0, 50.0], [455.0, 45.0], [456.0, 57.5], [457.0, 171.66666666666666], [458.0, 94.0], [467.0, 52.25], [464.0, 66.0], [465.0, 52.75], [466.0, 103.0], [470.0, 58.0], [468.0, 45.0], [471.0, 43.0], [472.0, 106.0], [478.0, 64.4], [473.0, 128.75], [474.0, 51.5], [475.0, 48.5], [481.0, 45.0], [480.0, 48.0], [482.0, 45.5], [483.0, 53.0], [484.0, 43.0], [485.0, 53.8], [486.0, 47.666666666666664], [488.0, 57.0], [495.0, 45.07142857142857], [493.0, 46.0], [492.0, 65.0], [494.0, 52.519999999999996], [489.0, 82.4], [490.0, 49.0], [491.0, 45.0], [496.0, 58.5], [497.0, 49.913043478260875], [498.0, 46.6], [499.0, 39.5], [500.0, 55.24321959755037]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[486.67133333333305, 57.87666666666664]], "isOverall": false, "label": "Play songs from home page-Aggregated", "isController": false}, {"data": [[2.0, 40.0], [3.0, 7.0], [4.0, 6.0], [5.0, 5.0], [6.0, 6.0], [7.0, 6.0], [8.0, 6.0], [9.0, 22.0], [10.0, 13.0], [11.0, 16.0], [12.0, 16.0], [13.0, 19.0], [14.0, 10.0], [15.0, 5.0], [16.0, 5.0], [17.0, 5.0], [18.0, 5.0], [19.0, 5.0], [20.0, 4.0], [21.0, 5.0], [22.0, 5.0], [23.0, 6.0], [24.0, 5.0], [25.0, 5.0], [26.0, 5.0], [27.0, 5.0], [28.0, 5.0], [29.0, 5.0], [30.0, 5.0], [31.0, 5.0], [32.0, 5.0], [33.0, 5.0], [34.0, 5.0], [35.0, 5.0], [36.0, 5.0], [37.0, 4.0], [38.0, 5.0], [39.0, 5.0], [40.0, 5.0], [41.0, 5.0], [42.0, 5.0], [43.0, 4.0], [44.0, 5.0], [45.0, 5.0], [46.0, 4.0], [47.0, 5.0], [48.0, 5.0], [49.0, 5.0], [50.0, 5.0], [51.0, 5.0], [52.0, 5.0], [53.0, 5.0], [54.0, 4.0], [55.0, 5.0], [56.0, 4.0], [57.0, 5.0], [58.0, 5.0], [59.0, 5.0], [60.0, 4.0], [61.0, 4.0], [62.0, 5.0], [63.0, 5.0], [64.0, 6.0], [65.0, 5.0], [66.0, 5.0], [67.0, 5.0], [68.0, 5.0], [69.0, 5.0], [70.0, 5.0], [71.0, 5.0], [72.0, 5.0], [73.0, 6.0], [74.0, 5.0], [75.0, 6.0], [76.0, 6.0], [77.0, 4.0], [78.0, 6.0], [79.0, 6.0], [80.0, 5.0], [81.0, 5.0], [82.0, 5.0], [83.0, 5.0], [84.0, 6.0], [85.0, 4.0], [86.0, 4.0], [87.0, 5.0], [88.0, 5.0], [89.0, 5.0], [90.0, 6.0], [91.0, 6.0], [92.0, 6.0], [93.0, 5.0], [94.0, 5.0], [95.0, 8.0], [96.0, 5.0], [97.0, 5.0], [98.0, 5.0], [99.0, 5.0], [100.0, 4.0], [101.0, 5.0], [102.0, 5.0], [103.0, 6.0], [104.0, 5.0], [105.0, 5.0], [106.0, 4.0], [107.0, 6.0], [108.0, 5.0], [109.0, 5.0], [110.0, 4.0], [111.0, 5.0], [112.0, 5.0], [113.0, 5.0], [114.0, 4.0], [115.0, 5.0], [116.0, 5.0], [117.0, 5.0], [118.0, 5.0], [119.0, 5.0], [120.0, 5.0], [121.0, 5.0], [122.0, 5.0], [123.0, 7.0], [124.0, 5.0], [125.0, 4.0], [126.0, 5.0], [127.0, 5.0], [128.0, 5.0], [129.0, 5.0], [130.0, 5.0], [131.0, 5.0], [132.0, 5.0], [133.0, 5.0], [134.0, 6.0], [135.0, 5.0], [136.0, 5.0], [137.0, 7.0], [138.0, 5.0], [139.0, 5.0], [140.0, 4.0], [141.0, 6.0], [142.0, 4.0], [143.0, 5.0], [144.0, 5.0], [145.0, 5.0], [146.0, 4.0], [147.0, 5.0], [148.0, 5.0], [149.0, 6.0], [150.0, 5.0], [151.0, 5.0], [152.0, 6.0], [153.0, 5.0], [154.0, 5.0], [155.0, 4.0], [156.0, 5.0], [157.0, 4.0], [158.0, 5.0], [159.0, 6.0], [160.0, 5.0], [161.0, 5.0], [162.0, 5.0], [163.0, 5.0], [164.0, 5.0], [165.0, 4.0], [166.0, 5.0], [167.0, 5.0], [168.0, 5.0], [169.0, 4.0], [170.0, 4.0], [171.0, 5.0], [172.0, 5.0], [173.0, 5.0], [174.0, 5.0], [175.0, 6.0], [176.0, 4.0], [177.0, 5.0], [178.0, 4.0], [179.0, 5.0], [180.0, 5.0], [181.0, 5.0], [182.0, 4.0], [183.0, 5.0], [184.0, 5.0], [185.0, 7.0], [186.0, 4.0], [187.0, 4.0], [188.0, 5.0], [189.0, 4.0], [190.0, 5.0], [191.0, 5.0], [192.0, 5.0], [193.0, 5.0], [194.0, 5.0], [195.0, 5.0], [196.0, 4.0], [197.0, 6.0], [198.0, 4.0], [199.0, 4.0], [200.0, 4.0], [201.0, 5.0], [202.0, 5.0], [203.0, 4.0], [204.0, 5.0], [205.0, 6.0], [206.0, 6.0], [207.0, 5.0], [208.0, 5.0], [209.0, 6.0], [210.0, 5.0], [211.0, 4.0], [212.0, 5.0], [213.0, 4.0], [214.0, 5.0], [215.0, 5.0], [216.0, 5.0], [217.0, 5.0], [218.0, 4.0], [219.0, 4.0], [220.0, 5.0], [221.0, 5.0], [222.0, 4.0], [223.0, 5.0], [224.0, 4.0], [225.0, 5.0], [226.0, 4.0], [227.0, 4.0], [228.0, 4.0], [229.0, 5.0], [230.0, 4.0], [231.0, 5.0], [232.0, 5.0], [233.0, 4.0], [234.0, 5.0], [235.0, 5.0], [236.0, 4.0], [237.0, 4.0], [238.0, 6.0], [239.0, 5.0], [240.0, 5.0], [241.0, 4.0], [242.0, 4.0], [243.0, 6.0], [244.0, 5.0], [245.0, 5.0], [246.0, 4.0], [247.0, 4.0], [248.0, 5.0], [249.0, 5.0], [250.0, 4.0], [251.0, 5.0], [252.0, 4.0], [253.0, 4.0], [254.0, 5.0], [255.0, 5.0], [257.0, 5.0], [256.0, 5.0], [258.0, 4.0], [259.0, 5.0], [260.0, 4.0], [261.0, 5.0], [262.0, 5.0], [263.0, 5.0], [264.0, 5.0], [270.0, 4.0], [271.0, 9.0], [268.0, 5.0], [269.0, 4.0], [265.0, 5.0], [266.0, 6.0], [267.0, 4.0], [273.0, 5.0], [272.0, 5.0], [274.0, 4.0], [275.0, 5.0], [276.0, 4.0], [277.0, 4.0], [278.0, 5.0], [279.0, 4.0], [280.0, 4.0], [286.0, 5.0], [287.0, 5.0], [284.0, 6.0], [285.0, 5.0], [281.0, 9.0], [282.0, 5.0], [283.0, 5.0], [289.0, 6.0], [288.0, 4.0], [290.0, 3.0], [291.0, 4.0], [292.0, 5.0], [293.0, 6.0], [294.0, 4.0], [295.0, 4.0], [296.0, 5.0], [302.0, 5.0], [303.0, 6.0], [300.0, 5.0], [301.0, 5.0], [297.0, 5.0], [298.0, 4.0], [299.0, 8.0], [305.0, 4.0], [304.0, 4.0], [306.0, 5.0], [307.0, 5.0], [308.0, 5.0], [309.0, 4.0], [310.0, 4.0], [311.0, 4.0], [312.0, 11.0], [318.0, 4.0], [319.0, 5.0], [316.0, 6.0], [317.0, 5.0], [313.0, 4.0], [314.0, 4.0], [315.0, 5.0], [321.0, 5.0], [320.0, 6.0], [322.0, 4.0], [323.0, 4.0], [324.0, 13.0], [325.0, 4.0], [326.0, 9.0], [327.0, 5.0], [328.0, 5.0], [334.0, 5.0], [335.0, 4.0], [332.0, 4.0], [333.0, 5.0], [329.0, 5.0], [330.0, 4.0], [331.0, 9.0], [337.0, 11.0], [336.0, 4.0], [338.0, 17.0], [339.0, 5.0], [340.0, 5.0], [341.0, 6.0], [342.0, 6.0], [343.0, 6.0], [344.0, 6.0], [350.0, 5.0], [351.0, 4.0], [348.0, 23.0], [349.0, 7.0], [345.0, 4.0], [346.0, 4.0], [347.0, 4.0], [353.0, 5.0], [352.0, 4.0], [354.0, 4.0], [355.0, 5.0], [356.0, 4.0], [357.0, 4.0], [358.0, 4.0], [359.0, 11.0], [360.0, 5.0], [366.0, 7.0], [367.0, 14.0], [364.0, 4.0], [365.0, 4.0], [361.0, 5.0], [362.0, 4.0], [363.0, 4.0], [369.0, 10.0], [368.0, 17.0], [370.0, 4.0], [371.0, 4.0], [372.0, 5.0], [373.0, 4.0], [374.0, 5.0], [375.0, 4.0], [376.0, 5.0], [382.0, 10.0], [383.0, 4.0], [380.0, 4.0], [381.0, 4.0], [377.0, 5.0], [378.0, 5.0], [379.0, 4.0], [385.0, 8.0], [384.0, 5.0], [386.0, 4.0], [387.0, 7.0], [388.0, 4.0], [389.0, 4.0], [390.0, 4.0], [391.0, 17.0], [392.0, 5.0], [398.0, 7.0], [399.0, 4.0], [396.0, 3.0], [397.0, 10.0], [393.0, 4.0], [394.0, 7.0], [395.0, 5.0], [401.0, 4.0], [400.0, 5.0], [402.0, 5.0], [403.0, 3.0], [404.0, 4.0], [405.0, 3.0], [406.0, 4.0], [407.0, 5.0], [408.0, 5.0], [414.0, 10.0], [415.0, 14.0], [412.0, 4.0], [413.0, 9.0], [409.0, 8.0], [410.0, 5.0], [411.0, 4.0], [417.0, 5.0], [416.0, 4.0], [418.0, 4.0], [419.0, 4.0], [420.0, 9.0], [421.0, 8.0], [422.0, 9.0], [423.0, 6.0], [424.0, 4.0], [430.0, 4.0], [431.0, 6.0], [428.0, 4.0], [429.0, 10.0], [425.0, 3.0], [426.0, 7.0], [427.0, 5.0], [433.0, 6.0], [432.0, 9.0], [434.0, 8.0], [435.0, 5.0], [436.0, 5.0], [437.0, 4.0], [438.0, 5.0], [439.0, 11.0], [440.0, 14.0], [446.0, 6.0], [447.0, 4.0], [444.0, 5.0], [445.0, 4.0], [441.0, 11.0], [442.0, 9.0], [443.0, 4.0], [449.0, 6.0], [448.0, 5.0], [450.0, 9.0], [451.0, 4.0], [452.0, 5.0], [453.0, 7.0], [454.0, 4.0], [455.0, 4.0], [456.0, 8.0], [462.0, 4.0], [463.0, 4.0], [460.0, 8.0], [461.0, 5.0], [457.0, 18.0], [458.0, 7.0], [459.0, 4.0], [465.0, 8.0], [464.0, 17.0], [466.0, 5.0], [467.0, 5.0], [468.0, 4.0], [469.0, 3.0], [470.0, 9.0], [471.0, 5.0], [472.0, 4.0], [478.0, 5.0], [479.0, 4.0], [476.0, 4.0], [477.0, 4.0], [473.0, 10.0], [474.0, 5.0], [475.0, 4.0], [481.0, 4.0], [480.0, 5.0], [482.0, 6.0], [483.0, 5.0], [484.0, 5.0], [485.0, 5.0], [486.0, 4.0], [487.0, 4.0], [488.0, 7.0], [494.0, 5.0], [495.0, 4.0], [492.0, 4.0], [493.0, 4.0], [489.0, 11.0], [490.0, 10.0], [491.0, 4.0], [496.0, 7.0], [497.0, 9.0], [498.0, 5.0], [499.0, 4.0], [500.0, 5.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[250.5020000000001, 5.621999999999999]], "isOverall": false, "label": "Trending playlist-Aggregated", "isController": false}, {"data": [[263.0, 15.0], [313.0, 14.0], [328.0, 18.0], [346.0, 17.0], [360.0, 20.5], [363.0, 18.0], [383.0, 14.0], [378.0, 19.0], [371.0, 14.0], [370.0, 14.5], [369.0, 16.0], [373.0, 17.0], [380.0, 17.0], [397.0, 15.0], [388.0, 15.0], [399.0, 18.0], [391.0, 15.0], [412.0, 14.0], [415.0, 20.5], [414.0, 13.0], [410.0, 14.0], [411.0, 14.0], [406.0, 19.0], [405.0, 15.0], [403.0, 15.0], [430.0, 14.0], [422.0, 13.0], [426.0, 30.0], [425.0, 12.0], [417.0, 20.6], [420.0, 14.0], [421.0, 13.0], [418.0, 13.0], [445.0, 14.6], [446.0, 12.0], [444.0, 20.5], [447.0, 15.0], [437.0, 15.5], [435.0, 16.0], [442.0, 16.0], [436.0, 14.0], [459.0, 17.5], [461.0, 15.666666666666666], [462.0, 15.0], [456.0, 16.0], [460.0, 33.0], [452.0, 16.666666666666664], [451.0, 15.5], [449.0, 15.5], [466.0, 19.25], [479.0, 14.0], [478.0, 18.428571428571427], [467.0, 15.0], [475.0, 17.333333333333332], [473.0, 20.5], [471.0, 13.0], [470.0, 15.5], [468.0, 16.25], [465.0, 13.333333333333334], [464.0, 14.833333333333334], [494.0, 18.292682926829276], [495.0, 17.235294117647058], [492.0, 14.4], [491.0, 16.4], [493.0, 18.1], [490.0, 18.666666666666668], [489.0, 15.333333333333332], [488.0, 20.0], [485.0, 15.666666666666666], [484.0, 20.0], [486.0, 18.0], [487.0, 14.0], [481.0, 16.0], [480.0, 18.75], [483.0, 15.0], [482.0, 19.2], [500.0, 18.90728476821192], [498.0, 20.0], [497.0, 18.128205128205135]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[478.81600000000003, 17.793999999999993]], "isOverall": false, "label": "Albums page-Aggregated", "isController": true}, {"data": [[11.0, 35.0], [12.0, 33.85714285714286], [13.0, 14.0], [14.0, 9.333333333333334], [15.0, 4.0], [16.0, 5.0], [17.0, 4.0], [18.0, 4.0], [19.0, 3.0], [20.0, 7.0], [21.0, 3.0], [22.0, 3.0], [23.0, 3.0], [24.0, 3.0], [25.0, 3.0], [26.0, 3.0], [27.0, 3.0], [28.0, 3.0], [29.0, 3.0], [30.0, 4.0], [31.0, 3.0], [32.0, 3.0], [33.0, 3.0], [34.0, 3.0], [35.0, 3.0], [36.0, 4.0], [37.0, 3.0], [38.0, 8.0], [39.0, 5.0], [40.0, 3.0], [41.0, 3.0], [42.0, 4.0], [43.0, 3.0], [44.0, 4.0], [45.0, 3.0], [46.0, 3.0], [47.0, 3.0], [48.0, 3.0], [49.0, 3.0], [50.0, 3.0], [51.0, 3.0], [52.0, 4.0], [53.0, 5.0], [54.0, 3.0], [55.0, 3.0], [56.0, 4.0], [57.0, 3.0], [58.0, 3.0], [59.0, 4.0], [60.0, 3.0], [61.0, 3.0], [62.0, 3.0], [63.0, 3.0], [64.0, 3.0], [65.0, 3.0], [66.0, 3.0], [67.0, 4.0], [68.0, 3.0], [69.0, 3.0], [70.0, 5.0], [71.0, 3.0], [72.0, 3.0], [73.0, 3.0], [74.0, 3.0], [75.0, 3.0], [76.0, 4.0], [77.0, 3.0], [78.0, 3.0], [79.0, 3.0], [80.0, 3.0], [81.0, 3.0], [82.0, 3.0], [83.0, 3.0], [84.0, 5.0], [85.0, 4.0], [86.0, 3.0], [87.0, 3.0], [88.0, 3.0], [89.0, 3.0], [90.0, 3.0], [91.0, 3.0], [92.0, 4.0], [93.0, 2.0], [94.0, 3.0], [95.0, 5.0], [96.0, 3.0], [97.0, 4.0], [98.0, 2.0], [99.0, 4.0], [100.0, 3.0], [101.0, 3.0], [102.0, 2.0], [103.0, 3.0], [104.0, 3.0], [105.0, 3.0], [106.0, 5.0], [107.0, 4.0], [108.0, 4.0], [109.0, 3.0], [110.0, 3.0], [111.0, 4.0], [112.0, 2.0], [113.0, 3.0], [114.0, 3.0], [115.0, 3.0], [116.0, 3.0], [117.0, 4.0], [118.0, 3.0], [119.0, 3.0], [120.0, 4.0], [121.0, 4.0], [122.0, 3.0], [123.0, 3.0], [124.0, 3.0], [125.0, 3.0], [126.0, 3.0], [127.0, 2.0], [128.0, 3.0], [129.0, 3.0], [130.0, 4.0], [131.0, 3.0], [132.0, 3.0], [133.0, 3.0], [134.0, 3.0], [135.0, 3.0], [136.0, 3.0], [137.0, 3.0], [138.0, 3.0], [139.0, 5.0], [140.0, 4.0], [141.0, 3.0], [142.0, 3.0], [143.0, 3.0], [144.0, 3.0], [145.0, 4.0], [146.0, 2.0], [147.0, 5.0], [148.0, 3.0], [149.0, 3.0], [150.0, 3.0], [151.0, 3.0], [152.0, 3.0], [153.0, 3.0], [154.0, 3.0], [155.0, 3.0], [156.0, 2.0], [157.0, 3.0], [158.0, 3.0], [159.0, 3.0], [160.0, 5.0], [161.0, 3.0], [162.0, 3.0], [163.0, 5.0], [164.0, 2.0], [165.0, 3.0], [166.0, 5.0], [167.0, 3.0], [168.0, 3.0], [169.0, 3.0], [170.0, 3.0], [171.0, 4.0], [172.0, 5.0], [173.0, 3.0], [174.0, 3.0], [175.0, 5.0], [176.0, 4.0], [177.0, 3.0], [178.0, 5.0], [179.0, 3.0], [180.0, 3.0], [181.0, 3.0], [182.0, 6.0], [183.0, 3.0], [184.0, 3.0], [185.0, 3.0], [186.0, 3.0], [187.0, 5.0], [188.0, 2.0], [189.0, 3.0], [190.0, 3.0], [191.0, 3.0], [192.0, 3.0], [193.0, 2.0], [194.0, 3.0], [195.0, 5.0], [196.0, 5.0], [197.0, 3.0], [198.0, 3.0], [199.0, 3.0], [200.0, 4.0], [201.0, 4.0], [202.0, 4.0], [203.0, 3.0], [204.0, 3.0], [205.0, 3.0], [206.0, 3.0], [207.0, 2.0], [208.0, 3.0], [209.0, 3.0], [210.0, 4.0], [211.0, 5.0], [212.0, 3.0], [213.0, 4.0], [214.0, 5.0], [215.0, 3.0], [216.0, 3.0], [217.0, 3.0], [218.0, 3.0], [219.0, 3.0], [220.0, 3.0], [221.0, 3.0], [222.0, 4.0], [223.0, 5.0], [224.0, 3.0], [225.0, 3.0], [226.0, 3.0], [227.0, 3.0], [228.0, 4.0], [229.0, 4.0], [230.0, 3.0], [231.0, 3.0], [232.0, 4.0], [233.0, 3.0], [234.0, 2.0], [235.0, 2.0], [236.0, 3.0], [237.0, 3.0], [238.0, 5.0], [239.0, 5.0], [240.0, 3.0], [241.0, 3.0], [242.0, 3.0], [243.0, 3.0], [244.0, 3.0], [245.0, 3.0], [246.0, 5.0], [247.0, 3.0], [248.0, 3.0], [249.0, 3.0], [250.0, 3.0], [251.0, 5.0], [252.0, 3.0], [253.0, 3.0], [254.0, 3.0], [255.0, 2.0], [257.0, 3.0], [256.0, 3.0], [258.0, 3.0], [259.0, 3.0], [260.0, 3.0], [261.0, 3.0], [262.0, 3.0], [263.0, 5.0], [264.0, 3.0], [270.0, 3.0], [271.0, 3.0], [268.0, 2.0], [269.0, 3.0], [265.0, 3.0], [266.0, 5.0], [267.0, 3.0], [273.0, 2.0], [272.0, 3.0], [274.0, 2.0], [275.0, 3.0], [276.0, 3.0], [277.0, 3.0], [278.0, 3.0], [279.0, 3.0], [280.0, 8.0], [286.0, 3.0], [287.0, 3.0], [284.0, 3.0], [285.0, 3.0], [281.0, 4.0], [282.0, 7.0], [283.0, 4.0], [289.0, 14.0], [288.0, 3.0], [290.0, 5.0], [291.0, 5.0], [292.0, 2.0], [293.0, 7.0], [294.0, 3.0], [295.0, 3.0], [296.0, 10.0], [302.0, 3.0], [303.0, 4.0], [300.0, 5.0], [301.0, 3.0], [297.0, 3.0], [298.0, 9.0], [299.0, 3.0], [305.0, 4.0], [304.0, 6.0], [306.0, 3.0], [307.0, 3.0], [308.0, 6.0], [309.0, 5.0], [310.0, 3.0], [311.0, 3.0], [312.0, 3.0], [318.0, 5.0], [319.0, 3.0], [316.0, 3.0], [317.0, 4.0], [313.0, 4.0], [314.0, 6.0], [315.0, 6.0], [321.0, 3.0], [320.0, 3.0], [322.0, 5.0], [323.0, 3.0], [324.0, 3.0], [325.0, 5.0], [326.0, 3.0], [327.0, 5.0], [328.0, 3.0], [334.0, 3.0], [335.0, 3.0], [332.0, 3.0], [333.0, 3.0], [329.0, 4.0], [330.0, 3.0], [331.0, 3.0], [338.0, 5.5], [336.0, 9.0], [339.0, 3.0], [349.0, 6.5], [350.0, 8.0], [351.0, 3.0], [340.0, 5.0], [341.0, 5.0], [342.0, 4.0], [343.0, 3.0], [344.0, 3.0], [345.0, 3.0], [346.0, 7.0], [347.0, 3.0], [353.0, 3.0], [352.0, 8.0], [354.0, 3.0], [355.0, 3.0], [356.0, 8.0], [357.0, 3.0], [358.0, 3.0], [359.0, 3.0], [360.0, 3.0], [367.0, 21.0], [364.0, 5.0], [365.0, 5.0], [361.0, 2.0], [362.0, 4.0], [363.0, 3.0], [370.0, 3.0], [369.0, 6.0], [371.0, 3.0], [380.0, 5.0], [381.0, 8.0], [382.0, 4.0], [383.0, 3.0], [372.0, 6.0], [373.0, 8.0], [374.0, 8.0], [375.0, 3.0], [376.0, 8.0], [377.0, 17.0], [378.0, 3.0], [379.0, 3.0], [385.0, 3.0], [384.0, 9.0], [386.0, 3.0], [387.0, 4.0], [388.0, 7.0], [389.0, 4.0], [390.0, 3.0], [392.0, 4.5], [398.0, 3.0], [399.0, 4.0], [396.0, 10.0], [397.0, 3.0], [393.0, 2.0], [394.0, 4.0], [395.0, 3.0], [401.0, 3.0], [400.0, 3.0], [402.0, 4.0], [403.0, 3.0], [404.0, 4.0], [405.0, 3.0], [406.0, 3.0], [407.0, 3.0], [408.0, 5.0], [415.0, 5.0], [412.0, 3.0], [414.0, 8.5], [409.0, 3.0], [410.0, 3.0], [411.0, 5.0], [417.0, 3.0], [416.0, 4.0], [418.0, 7.0], [419.0, 3.0], [420.0, 3.0], [421.0, 9.0], [422.0, 8.0], [423.0, 3.0], [424.0, 6.0], [430.0, 7.0], [431.0, 4.0], [428.0, 7.0], [429.0, 5.0], [425.0, 3.0], [426.0, 3.0], [427.0, 3.0], [434.0, 3.0], [433.0, 6.0], [435.0, 3.0], [444.0, 8.0], [445.0, 13.0], [446.0, 4.0], [447.0, 6.0], [436.0, 3.0], [437.0, 6.0], [439.0, 7.0], [440.0, 29.0], [442.0, 6.666666666666667], [443.0, 13.0], [449.0, 5.0], [448.0, 5.0], [450.0, 3.0], [451.0, 3.0], [452.0, 3.0], [453.0, 6.0], [454.0, 6.0], [455.0, 6.0], [457.0, 14.0], [462.0, 3.0], [463.0, 10.0], [460.0, 3.0], [461.0, 5.0], [458.0, 6.0], [459.0, 3.0], [465.0, 8.0], [464.0, 3.0], [466.0, 5.0], [467.0, 3.0], [468.0, 3.0], [469.0, 4.0], [470.0, 8.0], [471.0, 3.0], [473.0, 12.0], [478.0, 2.0], [479.0, 3.0], [476.0, 5.0], [477.0, 3.0], [474.0, 4.0], [475.0, 3.0], [481.0, 6.0], [480.0, 8.0], [482.0, 3.0], [483.0, 4.0], [484.0, 6.0], [485.0, 8.0], [486.0, 3.0], [487.0, 6.0], [488.0, 8.0], [494.0, 9.0], [495.0, 3.0], [492.0, 2.0], [493.0, 6.0], [490.0, 6.0], [491.0, 3.0], [496.0, 18.0], [497.0, 3.0], [498.0, 6.0], [499.0, 3.0], [500.0, 3.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[250.67599999999985, 4.724000000000003]], "isOverall": false, "label": "Cover 2-Aggregated", "isController": false}, {"data": [[11.0, 27.857142857142858], [12.0, 29.0], [13.0, 13.0], [14.0, 7.0], [15.0, 4.0], [16.0, 3.0], [17.0, 4.0], [18.0, 3.0], [19.0, 3.0], [20.0, 3.0], [21.0, 3.0], [22.0, 4.0], [23.0, 3.0], [24.0, 3.0], [25.0, 3.0], [26.0, 4.0], [27.0, 3.0], [28.0, 5.0], [29.0, 3.0], [30.0, 5.0], [31.0, 3.0], [32.0, 4.0], [33.0, 3.0], [34.0, 4.0], [35.0, 3.0], [36.0, 5.0], [37.0, 3.0], [38.0, 3.0], [39.0, 3.0], [40.0, 3.0], [41.0, 3.0], [42.0, 4.0], [43.0, 3.0], [44.0, 3.0], [45.0, 3.0], [46.0, 3.0], [47.0, 3.0], [48.0, 3.0], [49.0, 3.0], [50.0, 3.0], [51.0, 3.0], [52.0, 3.0], [53.0, 3.0], [54.0, 4.0], [55.0, 2.0], [56.0, 5.0], [57.0, 3.0], [58.0, 3.0], [59.0, 3.0], [60.0, 3.0], [61.0, 3.0], [62.0, 5.0], [63.0, 3.0], [64.0, 3.0], [65.0, 4.0], [66.0, 3.0], [67.0, 4.0], [68.0, 3.0], [69.0, 3.0], [70.0, 4.0], [71.0, 3.0], [72.0, 3.0], [73.0, 3.0], [74.0, 3.0], [75.0, 3.0], [76.0, 4.0], [77.0, 4.0], [78.0, 4.0], [79.0, 3.0], [80.0, 4.0], [81.0, 3.0], [82.0, 3.0], [83.0, 4.0], [84.0, 4.0], [85.0, 3.0], [86.0, 3.0], [87.0, 3.0], [88.0, 3.0], [89.0, 4.0], [90.0, 3.0], [91.0, 3.0], [92.0, 3.0], [93.0, 3.0], [94.0, 3.0], [95.0, 4.0], [96.0, 3.0], [97.0, 3.0], [98.0, 3.0], [99.0, 3.0], [100.0, 4.0], [101.0, 3.0], [102.0, 2.0], [103.0, 3.0], [104.0, 3.0], [105.0, 3.0], [106.0, 3.0], [107.0, 5.0], [108.0, 5.0], [109.0, 4.0], [110.0, 3.0], [111.0, 3.0], [112.0, 4.0], [113.0, 3.0], [114.0, 3.0], [115.0, 2.0], [116.0, 3.0], [117.0, 3.0], [118.0, 3.0], [119.0, 3.0], [120.0, 5.0], [121.0, 5.0], [122.0, 3.0], [123.0, 3.0], [124.0, 3.0], [125.0, 5.0], [126.0, 3.0], [127.0, 3.0], [128.0, 3.0], [129.0, 4.0], [130.0, 3.0], [131.0, 3.0], [132.0, 3.0], [133.0, 3.0], [134.0, 4.0], [135.0, 3.0], [136.0, 4.0], [137.0, 3.0], [138.0, 3.0], [139.0, 3.0], [140.0, 4.0], [141.0, 3.0], [142.0, 3.0], [143.0, 4.0], [144.0, 3.0], [145.0, 4.0], [146.0, 3.0], [147.0, 3.0], [148.0, 3.0], [149.0, 3.0], [150.0, 3.0], [151.0, 3.0], [152.0, 3.0], [153.0, 3.0], [154.0, 5.0], [155.0, 3.0], [156.0, 3.0], [157.0, 3.0], [158.0, 3.0], [159.0, 3.0], [160.0, 3.0], [161.0, 4.0], [162.0, 4.0], [163.0, 3.0], [164.0, 2.0], [165.0, 5.0], [166.0, 3.0], [167.0, 4.0], [168.0, 3.0], [169.0, 4.0], [170.0, 3.0], [171.0, 4.0], [172.0, 4.0], [173.0, 3.0], [174.0, 3.0], [175.0, 4.0], [176.0, 3.0], [177.0, 3.0], [178.0, 3.0], [179.0, 3.0], [180.0, 3.0], [181.0, 3.0], [182.0, 3.0], [183.0, 3.0], [184.0, 4.0], [185.0, 3.0], [186.0, 5.0], [187.0, 3.0], [188.0, 3.0], [189.0, 3.0], [190.0, 6.0], [191.0, 3.0], [192.0, 3.0], [193.0, 4.0], [194.0, 3.0], [195.0, 3.0], [196.0, 3.0], [197.0, 3.0], [198.0, 4.0], [199.0, 3.0], [200.0, 4.0], [201.0, 4.0], [202.0, 3.0], [203.0, 3.0], [204.0, 3.0], [205.0, 3.0], [206.0, 3.0], [207.0, 2.0], [208.0, 3.0], [209.0, 4.0], [210.0, 3.0], [211.0, 3.0], [212.0, 3.0], [213.0, 3.0], [214.0, 4.0], [215.0, 4.0], [216.0, 3.0], [217.0, 3.0], [218.0, 3.0], [219.0, 3.0], [220.0, 3.0], [221.0, 3.0], [222.0, 4.0], [223.0, 3.0], [224.0, 3.0], [225.0, 5.0], [226.0, 3.0], [227.0, 3.0], [228.0, 5.0], [229.0, 3.0], [230.0, 3.0], [231.0, 3.0], [232.0, 3.0], [233.0, 3.0], [234.0, 3.0], [235.0, 2.0], [236.0, 3.0], [237.0, 3.0], [238.0, 4.0], [239.0, 3.0], [240.0, 3.0], [241.0, 3.0], [242.0, 4.0], [243.0, 3.0], [244.0, 3.0], [245.0, 3.0], [246.0, 4.0], [247.0, 2.0], [248.0, 6.0], [249.0, 3.0], [250.0, 3.0], [251.0, 3.0], [252.0, 3.0], [253.0, 3.0], [254.0, 3.0], [255.0, 3.0], [257.0, 3.0], [256.0, 3.0], [258.0, 3.0], [259.0, 4.0], [260.0, 3.0], [261.0, 3.0], [262.0, 3.0], [263.0, 3.0], [264.0, 3.0], [270.0, 4.0], [271.0, 3.0], [268.0, 3.0], [269.0, 3.0], [265.0, 3.0], [266.0, 4.0], [267.0, 3.0], [273.0, 3.0], [272.0, 3.0], [274.0, 3.0], [275.0, 3.0], [276.0, 3.0], [277.0, 3.0], [278.0, 3.0], [279.0, 3.0], [280.0, 14.0], [286.0, 4.0], [287.0, 3.0], [284.0, 3.0], [285.0, 3.0], [281.0, 3.0], [282.0, 4.0], [283.0, 3.0], [289.0, 7.0], [288.0, 3.0], [290.0, 4.0], [291.0, 3.0], [292.0, 3.0], [293.0, 9.0], [294.0, 3.0], [295.0, 3.0], [296.0, 6.0], [302.0, 3.0], [303.0, 3.0], [300.0, 4.0], [301.0, 3.0], [297.0, 3.0], [298.0, 7.0], [299.0, 3.0], [305.0, 3.0], [304.0, 4.0], [306.0, 4.0], [307.0, 3.0], [308.0, 4.0], [309.0, 3.0], [310.0, 4.0], [311.0, 3.0], [312.0, 3.0], [318.0, 6.0], [319.0, 3.0], [316.0, 3.0], [317.0, 5.0], [313.0, 3.0], [314.0, 5.0], [315.0, 3.0], [321.0, 3.0], [320.0, 3.0], [322.0, 2.0], [323.0, 3.0], [324.0, 3.0], [325.0, 4.0], [326.0, 3.0], [327.0, 6.0], [328.0, 3.0], [334.0, 3.0], [335.0, 3.0], [332.0, 2.0], [333.0, 6.0], [329.0, 3.0], [330.0, 3.0], [331.0, 3.0], [338.0, 5.5], [336.0, 6.0], [339.0, 3.0], [349.0, 6.5], [350.0, 3.0], [351.0, 3.0], [340.0, 3.0], [341.0, 3.0], [342.0, 5.0], [343.0, 3.0], [344.0, 5.0], [345.0, 3.0], [346.0, 3.0], [347.0, 3.0], [353.0, 3.0], [352.0, 9.0], [354.0, 2.0], [355.0, 5.0], [356.0, 7.0], [357.0, 3.0], [358.0, 3.0], [359.0, 3.0], [360.0, 4.0], [366.0, 12.0], [364.0, 7.0], [365.0, 4.0], [361.0, 4.0], [362.0, 6.0], [363.0, 3.0], [369.0, 4.0], [368.0, 7.0], [370.0, 3.0], [371.0, 3.0], [372.0, 4.0], [373.0, 7.0], [374.0, 6.0], [375.0, 3.0], [376.0, 7.0], [382.0, 3.0], [383.0, 3.0], [380.0, 6.0], [381.0, 7.0], [377.0, 5.0], [378.0, 3.0], [379.0, 3.0], [385.0, 3.0], [384.0, 5.0], [386.0, 3.0], [387.0, 2.0], [388.0, 10.0], [389.0, 3.0], [390.0, 3.0], [392.0, 4.5], [398.0, 4.0], [399.0, 3.0], [396.0, 7.0], [397.0, 3.0], [393.0, 2.0], [394.0, 4.0], [395.0, 4.0], [401.0, 3.0], [400.0, 3.0], [402.0, 3.0], [403.0, 5.0], [404.0, 4.0], [405.0, 3.0], [406.0, 3.0], [407.0, 3.0], [408.0, 3.0], [415.0, 5.0], [412.0, 3.0], [414.0, 9.0], [409.0, 3.0], [410.0, 3.0], [411.0, 6.0], [417.0, 3.0], [416.0, 3.0], [418.0, 5.0], [419.0, 4.0], [420.0, 3.0], [421.0, 6.0], [422.0, 6.0], [423.0, 3.0], [424.0, 4.0], [430.0, 6.0], [431.0, 3.0], [428.0, 8.0], [429.0, 4.0], [425.0, 3.0], [426.0, 3.0], [427.0, 3.0], [433.0, 3.0], [432.0, 6.0], [434.0, 3.0], [435.0, 4.0], [436.0, 3.0], [437.0, 3.0], [439.0, 9.0], [440.0, 13.0], [446.0, 3.0], [447.0, 7.0], [444.0, 7.0], [445.0, 6.0], [441.0, 5.0], [442.0, 5.5], [443.0, 5.0], [449.0, 5.0], [448.0, 10.0], [450.0, 3.0], [451.0, 3.0], [452.0, 3.0], [453.0, 4.0], [454.0, 3.0], [455.0, 3.0], [457.0, 10.0], [462.0, 3.0], [463.0, 16.0], [460.0, 4.0], [461.0, 3.0], [458.0, 4.0], [459.0, 3.0], [465.0, 6.0], [464.0, 3.0], [466.0, 7.0], [467.0, 3.0], [468.0, 3.0], [469.0, 3.0], [470.0, 11.0], [471.0, 2.0], [473.0, 14.0], [478.0, 3.0], [479.0, 3.0], [476.0, 3.0], [477.0, 3.0], [474.0, 3.0], [475.0, 3.0], [481.0, 5.0], [480.0, 4.0], [482.0, 3.0], [483.0, 3.0], [484.0, 4.0], [485.0, 6.0], [486.0, 3.0], [487.0, 4.0], [488.0, 6.0], [494.0, 6.0], [495.0, 3.0], [492.0, 4.0], [493.0, 3.0], [489.0, 5.0], [490.0, 5.0], [491.0, 3.0], [496.0, 6.0], [497.0, 6.0], [498.0, 4.0], [499.0, 3.0], [500.0, 6.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[250.65400000000008, 4.3]], "isOverall": false, "label": "Cover 1-Aggregated", "isController": false}, {"data": [[12.0, 30.625], [13.0, 11.666666666666666], [14.0, 6.666666666666667], [15.0, 5.0], [16.0, 3.0], [17.0, 4.0], [18.0, 4.0], [19.0, 4.0], [20.0, 5.0], [21.0, 3.0], [22.0, 3.0], [23.0, 4.0], [24.0, 3.0], [25.0, 3.0], [26.0, 5.0], [27.0, 3.0], [28.0, 3.0], [29.0, 3.0], [30.0, 4.0], [31.0, 3.0], [32.0, 4.0], [33.0, 4.0], [34.0, 3.0], [35.0, 3.0], [36.0, 3.0], [37.0, 3.0], [38.0, 3.0], [39.0, 4.0], [40.0, 5.0], [41.0, 6.0], [42.0, 4.0], [43.0, 3.0], [44.0, 3.0], [45.0, 3.0], [46.0, 6.0], [47.0, 3.0], [48.0, 3.0], [49.0, 4.0], [50.0, 3.0], [51.0, 3.0], [52.0, 4.0], [53.0, 3.0], [54.0, 4.0], [55.0, 5.0], [56.0, 3.0], [57.0, 3.0], [58.0, 3.0], [59.0, 3.0], [60.0, 4.0], [61.0, 4.0], [62.0, 3.0], [63.0, 3.0], [64.0, 3.0], [65.0, 3.0], [66.0, 3.0], [67.0, 4.0], [68.0, 3.0], [69.0, 3.0], [70.0, 3.0], [71.0, 3.0], [72.0, 3.0], [73.0, 3.0], [74.0, 3.0], [75.0, 4.0], [76.0, 4.0], [77.0, 3.0], [78.0, 3.0], [79.0, 3.0], [80.0, 3.0], [81.0, 3.0], [82.0, 3.0], [83.0, 3.0], [84.0, 3.0], [85.0, 4.0], [86.0, 4.0], [87.0, 2.0], [88.0, 4.0], [89.0, 3.0], [90.0, 3.0], [91.0, 3.0], [92.0, 4.0], [93.0, 3.0], [94.0, 3.0], [95.0, 3.0], [96.0, 4.0], [97.0, 3.0], [98.0, 3.0], [99.0, 3.0], [100.0, 3.0], [101.0, 3.0], [102.0, 5.0], [103.0, 6.0], [104.0, 6.0], [105.0, 6.0], [106.0, 3.0], [107.0, 3.0], [108.0, 3.0], [109.0, 3.0], [110.0, 3.0], [111.0, 3.0], [112.0, 5.0], [113.0, 4.0], [114.0, 3.0], [115.0, 3.0], [116.0, 3.0], [117.0, 3.0], [118.0, 3.0], [119.0, 3.0], [120.0, 3.0], [121.0, 3.0], [122.0, 3.0], [123.0, 3.0], [124.0, 3.0], [125.0, 3.0], [126.0, 3.0], [127.0, 3.0], [128.0, 4.0], [129.0, 3.0], [130.0, 2.0], [131.0, 3.0], [132.0, 3.0], [133.0, 3.0], [134.0, 4.0], [135.0, 3.0], [136.0, 3.0], [137.0, 3.0], [138.0, 2.0], [139.0, 3.0], [140.0, 3.0], [141.0, 4.0], [142.0, 2.0], [143.0, 3.0], [144.0, 3.0], [145.0, 2.0], [146.0, 3.0], [147.0, 3.0], [148.0, 6.0], [149.0, 3.0], [150.0, 3.0], [151.0, 3.0], [152.0, 3.0], [153.0, 3.0], [154.0, 3.0], [155.0, 4.0], [156.0, 3.0], [157.0, 3.0], [158.0, 3.0], [159.0, 3.0], [160.0, 3.0], [161.0, 3.0], [162.0, 3.0], [163.0, 3.0], [164.0, 4.0], [165.0, 4.0], [166.0, 3.0], [167.0, 3.0], [168.0, 3.0], [169.0, 3.0], [170.0, 3.0], [171.0, 3.0], [172.0, 2.0], [173.0, 3.0], [174.0, 3.0], [175.0, 3.0], [176.0, 3.0], [177.0, 3.0], [178.0, 5.0], [179.0, 5.0], [180.0, 2.0], [181.0, 3.0], [182.0, 3.0], [183.0, 3.0], [184.0, 3.0], [185.0, 3.0], [186.0, 3.0], [187.0, 4.0], [188.0, 3.0], [189.0, 3.0], [190.0, 5.0], [191.0, 4.0], [192.0, 4.0], [193.0, 5.0], [194.0, 4.0], [195.0, 4.0], [196.0, 3.0], [197.0, 6.0], [198.0, 3.0], [199.0, 3.0], [200.0, 3.0], [201.0, 3.0], [202.0, 3.0], [203.0, 4.0], [204.0, 4.0], [205.0, 3.0], [206.0, 3.0], [207.0, 3.0], [208.0, 4.0], [209.0, 3.0], [210.0, 3.0], [211.0, 3.0], [212.0, 3.0], [213.0, 4.0], [214.0, 4.0], [215.0, 3.0], [216.0, 3.0], [217.0, 6.0], [218.0, 3.0], [219.0, 3.0], [220.0, 3.0], [221.0, 3.0], [222.0, 3.0], [223.0, 4.0], [224.0, 3.0], [225.0, 5.0], [226.0, 5.0], [227.0, 2.0], [228.0, 4.0], [229.0, 3.0], [230.0, 3.0], [231.0, 3.0], [232.0, 3.0], [233.0, 3.0], [234.0, 3.0], [235.0, 3.0], [236.0, 3.0], [237.0, 2.0], [238.0, 3.0], [239.0, 3.0], [240.0, 6.0], [241.0, 3.0], [242.0, 3.0], [243.0, 5.0], [244.0, 3.0], [245.0, 3.0], [246.0, 4.0], [247.0, 3.0], [248.0, 6.0], [249.0, 3.0], [250.0, 3.0], [251.0, 3.0], [252.0, 3.0], [253.0, 3.0], [254.0, 3.0], [255.0, 3.0], [257.0, 3.0], [256.0, 3.0], [258.0, 3.0], [259.0, 3.0], [260.0, 2.0], [261.0, 3.0], [262.0, 3.0], [263.0, 3.0], [264.0, 3.0], [270.0, 3.0], [271.0, 3.0], [268.0, 4.0], [269.0, 4.0], [265.0, 4.0], [266.0, 7.0], [267.0, 5.0], [273.0, 2.0], [272.0, 3.0], [274.0, 3.0], [275.0, 2.0], [276.0, 4.0], [277.0, 3.0], [278.0, 3.0], [279.0, 3.0], [280.0, 15.0], [286.0, 4.0], [287.0, 3.0], [284.0, 4.0], [285.0, 5.0], [281.0, 3.0], [282.0, 3.0], [283.0, 3.0], [289.0, 10.0], [288.0, 3.0], [290.0, 2.0], [291.0, 3.0], [292.0, 5.0], [293.0, 8.0], [294.0, 3.0], [295.0, 3.0], [296.0, 11.0], [302.0, 3.0], [303.0, 3.0], [300.0, 3.0], [301.0, 3.0], [297.0, 3.0], [298.0, 6.0], [299.0, 3.0], [305.0, 3.0], [304.0, 4.0], [306.0, 3.0], [307.0, 3.0], [308.0, 4.0], [309.0, 3.0], [310.0, 5.0], [311.0, 4.0], [312.0, 2.0], [318.0, 13.0], [319.0, 4.0], [316.0, 3.0], [317.0, 3.0], [313.0, 6.0], [314.0, 4.0], [315.0, 3.0], [321.0, 3.0], [320.0, 3.0], [322.0, 7.0], [323.0, 3.0], [324.0, 3.0], [325.0, 3.0], [326.0, 4.0], [327.0, 9.0], [328.0, 3.0], [334.0, 3.0], [335.0, 3.0], [332.0, 3.0], [333.0, 3.0], [329.0, 5.0], [330.0, 7.0], [331.0, 3.0], [338.0, 5.0], [336.0, 5.0], [339.0, 4.0], [349.0, 7.0], [350.0, 6.0], [351.0, 4.0], [340.0, 3.0], [341.0, 5.0], [342.0, 3.0], [343.0, 2.0], [344.0, 3.0], [345.0, 3.0], [346.0, 3.0], [347.0, 5.0], [353.0, 4.0], [352.0, 11.0], [354.0, 3.0], [355.0, 3.0], [356.0, 6.0], [357.0, 5.0], [358.0, 7.0], [359.0, 5.0], [360.0, 3.0], [367.0, 18.0], [364.0, 7.0], [365.0, 3.0], [361.0, 3.0], [362.0, 3.0], [363.0, 3.0], [370.0, 3.0], [369.0, 5.666666666666667], [371.0, 3.0], [380.0, 5.0], [381.0, 6.0], [382.0, 3.0], [383.0, 2.0], [372.0, 8.0], [373.0, 6.0], [374.0, 4.0], [375.0, 4.0], [376.0, 5.0], [377.0, 5.0], [378.0, 2.0], [379.0, 2.0], [385.0, 3.0], [384.0, 6.0], [386.0, 7.0], [387.0, 3.0], [388.0, 9.0], [389.0, 3.0], [390.0, 9.0], [392.0, 6.5], [399.0, 3.0], [396.0, 10.0], [398.0, 3.5], [393.0, 3.0], [394.0, 5.0], [395.0, 3.0], [401.0, 3.0], [400.0, 3.0], [402.0, 4.0], [403.0, 3.0], [404.0, 3.0], [405.0, 3.0], [406.0, 4.0], [407.0, 2.0], [408.0, 7.0], [415.0, 5.5], [412.0, 4.0], [414.0, 10.0], [409.0, 5.0], [410.0, 4.0], [411.0, 5.0], [417.0, 4.0], [416.0, 3.0], [418.0, 5.0], [419.0, 3.0], [420.0, 3.0], [421.0, 6.0], [422.0, 8.0], [423.0, 3.0], [424.0, 3.0], [430.0, 7.0], [431.0, 4.0], [428.0, 9.0], [429.0, 6.0], [425.0, 3.0], [426.0, 7.0], [427.0, 3.0], [434.0, 3.0], [433.0, 6.0], [435.0, 3.0], [444.0, 6.0], [445.0, 12.0], [446.0, 3.0], [447.0, 8.0], [436.0, 3.0], [437.0, 3.0], [439.0, 13.0], [441.0, 18.0], [442.0, 7.0], [443.0, 9.0], [449.0, 2.0], [448.0, 3.0], [450.0, 3.0], [451.0, 2.0], [452.0, 5.0], [453.0, 9.0], [454.0, 8.0], [455.0, 6.0], [457.0, 8.0], [462.0, 6.0], [460.0, 3.0], [461.0, 4.0], [458.0, 5.0], [459.0, 3.0], [465.0, 3.0], [464.0, 6.5], [467.0, 3.0], [476.0, 3.0], [477.0, 2.0], [478.0, 3.0], [479.0, 2.0], [468.0, 3.0], [469.0, 3.0], [470.0, 7.0], [471.0, 3.0], [473.0, 13.0], [474.0, 3.5], [475.0, 5.0], [481.0, 6.0], [480.0, 4.0], [482.0, 5.0], [483.0, 3.0], [484.0, 6.0], [485.0, 5.0], [486.0, 3.0], [487.0, 4.0], [488.0, 14.0], [494.0, 6.0], [495.0, 3.0], [492.0, 3.0], [493.0, 3.0], [490.0, 8.0], [491.0, 3.0], [496.0, 7.0], [497.0, 3.0], [498.0, 4.0], [499.0, 5.0], [500.0, 3.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[250.6920000000001, 4.526000000000003]], "isOverall": false, "label": "Cover 4-Aggregated", "isController": false}, {"data": [[12.0, 25.25], [13.0, 11.0], [14.0, 4.0], [15.0, 2.0], [16.0, 2.0], [17.0, 3.0], [18.0, 5.0], [19.0, 3.0], [20.0, 3.0], [21.0, 3.0], [22.0, 3.0], [23.0, 2.0], [24.0, 2.0], [25.0, 3.0], [26.0, 4.0], [27.0, 3.0], [28.0, 2.0], [29.0, 2.0], [30.0, 3.0], [31.0, 3.0], [32.0, 3.0], [33.0, 3.0], [34.0, 2.0], [35.0, 3.0], [36.0, 3.0], [37.0, 2.0], [38.0, 2.0], [39.0, 3.0], [40.0, 2.0], [41.0, 2.0], [42.0, 3.0], [43.0, 3.0], [44.0, 5.0], [45.0, 3.0], [46.0, 2.0], [47.0, 3.0], [48.0, 5.0], [49.0, 5.0], [50.0, 2.0], [51.0, 3.0], [52.0, 3.0], [53.0, 3.0], [54.0, 3.0], [55.0, 3.0], [56.0, 3.0], [57.0, 3.0], [58.0, 2.0], [59.0, 3.0], [60.0, 3.0], [61.0, 3.0], [62.0, 2.0], [63.0, 2.0], [64.0, 4.0], [65.0, 2.0], [66.0, 2.0], [67.0, 5.0], [68.0, 2.0], [69.0, 2.0], [70.0, 3.0], [71.0, 2.0], [72.0, 3.0], [73.0, 4.0], [74.0, 3.0], [75.0, 3.0], [76.0, 3.0], [77.0, 5.0], [78.0, 3.0], [79.0, 2.0], [80.0, 2.0], [81.0, 2.0], [82.0, 3.0], [83.0, 5.0], [84.0, 3.0], [85.0, 2.0], [86.0, 3.0], [87.0, 2.0], [88.0, 3.0], [89.0, 2.0], [90.0, 3.0], [91.0, 2.0], [92.0, 3.0], [93.0, 3.0], [94.0, 2.0], [95.0, 3.0], [96.0, 2.0], [97.0, 2.0], [98.0, 2.0], [99.0, 3.0], [100.0, 2.0], [101.0, 2.0], [102.0, 2.0], [103.0, 2.0], [104.0, 2.0], [105.0, 3.0], [106.0, 2.0], [107.0, 3.0], [108.0, 3.0], [109.0, 3.0], [110.0, 3.0], [111.0, 3.0], [112.0, 3.0], [113.0, 2.0], [114.0, 3.0], [115.0, 2.0], [116.0, 3.0], [117.0, 2.0], [118.0, 2.0], [119.0, 2.0], [120.0, 3.0], [121.0, 2.0], [122.0, 2.0], [123.0, 2.0], [124.0, 2.0], [125.0, 2.0], [126.0, 2.0], [127.0, 2.0], [128.0, 2.0], [129.0, 3.0], [130.0, 2.0], [131.0, 2.0], [132.0, 3.0], [133.0, 2.0], [134.0, 2.0], [135.0, 3.0], [136.0, 2.0], [137.0, 2.0], [138.0, 3.0], [139.0, 3.0], [140.0, 3.0], [141.0, 4.0], [142.0, 2.0], [143.0, 3.0], [144.0, 2.0], [145.0, 2.0], [146.0, 3.0], [147.0, 3.0], [148.0, 3.0], [149.0, 2.0], [150.0, 3.0], [151.0, 2.0], [152.0, 2.0], [153.0, 3.0], [154.0, 2.0], [155.0, 4.0], [156.0, 2.0], [157.0, 2.0], [158.0, 3.0], [159.0, 2.0], [160.0, 2.0], [161.0, 2.0], [162.0, 2.0], [163.0, 3.0], [164.0, 2.0], [165.0, 3.0], [166.0, 2.0], [167.0, 4.0], [168.0, 3.0], [169.0, 2.0], [170.0, 3.0], [171.0, 3.0], [172.0, 2.0], [173.0, 3.0], [174.0, 2.0], [175.0, 2.0], [176.0, 3.0], [177.0, 3.0], [178.0, 3.0], [179.0, 3.0], [180.0, 2.0], [181.0, 3.0], [182.0, 2.0], [183.0, 3.0], [184.0, 2.0], [185.0, 3.0], [186.0, 2.0], [187.0, 2.0], [188.0, 2.0], [189.0, 3.0], [190.0, 3.0], [191.0, 3.0], [192.0, 3.0], [193.0, 2.0], [194.0, 2.0], [195.0, 2.0], [196.0, 3.0], [197.0, 2.0], [198.0, 3.0], [199.0, 2.0], [200.0, 5.0], [201.0, 3.0], [202.0, 4.0], [203.0, 3.0], [204.0, 4.0], [205.0, 2.0], [206.0, 3.0], [207.0, 3.0], [208.0, 4.0], [209.0, 2.0], [210.0, 2.0], [211.0, 2.0], [212.0, 2.0], [213.0, 4.0], [214.0, 2.0], [215.0, 2.0], [216.0, 3.0], [217.0, 3.0], [218.0, 3.0], [219.0, 3.0], [220.0, 2.0], [221.0, 2.0], [222.0, 3.0], [223.0, 3.0], [224.0, 3.0], [225.0, 2.0], [226.0, 3.0], [227.0, 3.0], [228.0, 2.0], [229.0, 2.0], [230.0, 2.0], [231.0, 2.0], [232.0, 2.0], [233.0, 2.0], [234.0, 4.0], [235.0, 2.0], [236.0, 2.0], [237.0, 2.0], [238.0, 3.0], [239.0, 3.0], [240.0, 2.0], [241.0, 2.0], [242.0, 2.0], [243.0, 2.0], [244.0, 2.0], [245.0, 2.0], [246.0, 3.0], [247.0, 4.0], [248.0, 3.0], [249.0, 3.0], [250.0, 3.0], [251.0, 2.0], [252.0, 2.0], [253.0, 3.0], [254.0, 3.0], [255.0, 2.0], [257.0, 2.0], [256.0, 2.0], [258.0, 5.0], [259.0, 2.0], [260.0, 2.0], [261.0, 3.0], [262.0, 3.0], [263.0, 3.0], [264.0, 3.0], [270.0, 2.0], [271.0, 3.0], [268.0, 2.0], [269.0, 3.0], [265.0, 2.0], [266.0, 5.0], [267.0, 2.0], [273.0, 3.0], [272.0, 3.0], [274.0, 2.0], [275.0, 3.0], [276.0, 2.0], [277.0, 2.0], [278.0, 3.0], [279.0, 2.0], [280.0, 6.0], [286.0, 2.0], [287.0, 2.0], [284.0, 3.0], [285.0, 2.0], [281.0, 3.0], [282.0, 2.0], [283.0, 4.0], [289.0, 5.0], [288.0, 3.0], [290.0, 2.0], [291.0, 4.0], [292.0, 2.0], [293.0, 5.0], [294.0, 3.0], [295.0, 2.0], [296.0, 6.0], [302.0, 3.0], [303.0, 3.0], [300.0, 2.0], [301.0, 2.0], [297.0, 3.0], [298.0, 4.0], [299.0, 3.0], [305.0, 3.0], [304.0, 3.0], [306.0, 6.0], [307.0, 3.0], [308.0, 5.0], [309.0, 4.0], [310.0, 3.0], [311.0, 3.0], [312.0, 2.0], [318.0, 4.0], [319.0, 4.0], [316.0, 3.0], [317.0, 2.0], [313.0, 4.0], [314.0, 6.0], [315.0, 3.0], [321.0, 2.0], [320.0, 2.0], [322.0, 5.0], [323.0, 2.0], [324.0, 3.0], [325.0, 2.0], [326.0, 4.0], [327.0, 4.0], [328.0, 3.0], [334.0, 2.0], [335.0, 3.0], [332.0, 2.0], [333.0, 3.0], [329.0, 2.0], [330.0, 2.0], [331.0, 2.0], [338.0, 4.5], [336.0, 4.0], [339.0, 3.0], [349.0, 4.0], [350.0, 6.0], [351.0, 2.0], [340.0, 3.0], [341.0, 3.0], [342.0, 2.0], [343.0, 3.0], [344.0, 3.0], [345.0, 2.0], [346.0, 3.0], [347.0, 3.0], [353.0, 2.0], [352.0, 7.0], [354.0, 4.0], [355.0, 3.0], [356.0, 6.0], [357.0, 4.0], [358.0, 3.0], [359.0, 3.0], [360.0, 2.0], [367.0, 28.0], [364.0, 4.0], [365.0, 3.0], [361.0, 3.0], [362.0, 2.0], [363.0, 2.0], [370.0, 2.0], [369.0, 4.0], [371.0, 2.0], [380.0, 5.0], [381.0, 5.0], [382.0, 2.0], [383.0, 2.0], [372.0, 5.0], [373.0, 7.0], [374.0, 4.0], [375.0, 4.0], [376.0, 8.0], [377.0, 7.0], [378.0, 2.0], [379.0, 3.0], [385.0, 2.0], [384.0, 4.0], [386.0, 3.0], [387.0, 2.0], [388.0, 8.0], [389.0, 2.0], [390.0, 3.0], [392.0, 3.0], [398.0, 5.0], [399.0, 4.0], [396.0, 9.0], [397.0, 3.0], [393.0, 2.0], [394.0, 6.0], [395.0, 3.0], [401.0, 2.0], [400.0, 5.0], [402.0, 2.0], [403.0, 2.0], [404.0, 3.0], [405.0, 2.0], [406.0, 2.0], [407.0, 2.0], [408.0, 5.0], [415.0, 6.0], [412.0, 3.0], [414.0, 19.0], [409.0, 2.0], [410.0, 2.0], [411.0, 3.0], [417.0, 3.0], [416.0, 4.0], [418.0, 4.0], [419.0, 3.0], [420.0, 2.0], [421.0, 6.0], [422.0, 8.0], [423.0, 3.0], [424.0, 2.0], [430.0, 4.0], [431.0, 2.0], [428.0, 7.0], [429.0, 10.0], [425.0, 2.0], [426.0, 2.0], [427.0, 3.0], [434.0, 2.0], [433.0, 4.0], [435.0, 2.0], [444.0, 12.0], [445.0, 8.0], [446.0, 4.0], [447.0, 6.0], [436.0, 4.0], [437.0, 2.0], [439.0, 8.0], [441.0, 21.0], [442.0, 5.0], [443.0, 7.0], [449.0, 3.0], [448.0, 2.0], [450.0, 3.0], [451.0, 2.0], [452.0, 3.0], [453.0, 6.0], [454.0, 5.0], [455.0, 8.0], [457.0, 7.0], [462.0, 4.0], [463.0, 7.0], [460.0, 3.0], [461.0, 4.0], [458.0, 4.0], [459.0, 3.0], [465.0, 4.0], [464.0, 3.0], [466.0, 4.0], [467.0, 3.0], [468.0, 3.0], [469.0, 2.0], [470.0, 5.0], [471.0, 3.0], [473.0, 9.0], [478.0, 2.0], [479.0, 3.0], [476.0, 2.0], [477.0, 3.0], [474.0, 2.5], [475.0, 3.0], [481.0, 6.0], [480.0, 4.0], [482.0, 3.0], [483.0, 3.0], [484.0, 5.0], [485.0, 4.0], [486.0, 2.0], [487.0, 2.0], [488.0, 5.0], [494.0, 5.0], [495.0, 3.0], [492.0, 2.0], [493.0, 7.0], [490.0, 6.0], [491.0, 3.0], [496.0, 6.0], [497.0, 3.0], [498.0, 7.0], [499.0, 3.0], [500.0, 3.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[250.68600000000006, 3.651999999999996]], "isOverall": false, "label": "Cover 3-Aggregated", "isController": false}, {"data": [[52.0, 37.0], [67.0, 39.0], [76.0, 37.0], [85.0, 40.0], [89.0, 41.0], [98.0, 38.0], [103.0, 35.0], [101.0, 37.0], [100.0, 40.0], [111.0, 37.0], [109.0, 33.0], [113.0, 41.0], [112.0, 38.0], [116.0, 39.0], [121.0, 38.0], [131.0, 37.0], [129.0, 34.0], [141.0, 37.0], [136.0, 34.0], [161.0, 37.0], [175.0, 38.0], [171.0, 36.0], [182.0, 38.0], [176.0, 34.0], [191.0, 36.0], [190.0, 41.0], [189.0, 35.0], [187.0, 65.0], [186.0, 39.0], [185.0, 39.0], [194.0, 39.0], [206.0, 37.0], [205.0, 47.75], [203.0, 36.0], [209.0, 37.0], [208.0, 38.0], [221.0, 33.0], [220.0, 34.0], [225.0, 36.5], [238.0, 36.0], [237.0, 35.0], [247.0, 37.0], [244.0, 36.0], [242.0, 35.0], [254.0, 37.5], [253.0, 36.0], [250.0, 37.0], [248.0, 37.0], [259.0, 37.0], [271.0, 38.5], [268.0, 66.5], [264.0, 38.0], [263.0, 35.0], [262.0, 37.5], [260.0, 37.0], [258.0, 37.0], [257.0, 41.0], [256.0, 37.0], [283.0, 35.0], [282.0, 37.0], [281.0, 39.0], [280.0, 37.5], [275.0, 39.0], [274.0, 37.0], [289.0, 37.0], [307.0, 39.0], [317.0, 51.5], [316.0, 39.0], [314.0, 36.666666666666664], [313.0, 36.5], [311.0, 37.0], [309.0, 44.0], [308.0, 37.0], [306.0, 49.0], [304.0, 40.0], [334.0, 42.0], [332.0, 42.0], [329.0, 37.0], [328.0, 37.0], [323.0, 38.0], [320.0, 37.5], [350.0, 36.5], [349.0, 36.666666666666664], [347.0, 38.0], [346.0, 35.0], [345.0, 51.0], [344.0, 47.0], [343.0, 36.0], [340.0, 48.8], [339.0, 61.0], [366.0, 33.0], [367.0, 39.125], [365.0, 34.0], [355.0, 36.0], [354.0, 42.333333333333336], [353.0, 36.0], [363.0, 32.5], [362.0, 35.0], [361.0, 35.0], [360.0, 40.5], [359.0, 32.8], [358.0, 36.0], [357.0, 36.75], [371.0, 35.0], [383.0, 32.75], [381.0, 49.666666666666664], [379.0, 35.0], [378.0, 35.142857142857146], [377.0, 34.0], [373.0, 34.0], [372.0, 34.0], [369.0, 41.875], [368.0, 37.166666666666664], [397.0, 39.83333333333333], [399.0, 36.5], [396.0, 72.0], [387.0, 54.5], [395.0, 42.5], [393.0, 48.333333333333336], [392.0, 35.0], [391.0, 35.0], [389.0, 45.4], [388.0, 36.4], [403.0, 42.0], [413.0, 35.0], [415.0, 34.66666666666667], [412.0, 49.25], [410.0, 49.333333333333336], [409.0, 36.0], [407.0, 37.0], [406.0, 36.6], [405.0, 40.83333333333333], [402.0, 35.5], [400.0, 35.0], [419.0, 35.0], [431.0, 39.0], [428.0, 47.333333333333336], [427.0, 35.0], [426.0, 39.75], [425.0, 44.333333333333336], [422.0, 36.0], [421.0, 54.125], [417.0, 34.285714285714285], [416.0, 35.0], [444.0, 41.42857142857143], [446.0, 37.0], [447.0, 35.16666666666667], [445.0, 34.4], [442.0, 34.166666666666664], [441.0, 47.25], [439.0, 30.0], [432.0, 32.0], [435.0, 55.333333333333336], [434.0, 32.0], [438.0, 35.5], [437.0, 43.57142857142857], [436.0, 42.142857142857146], [461.0, 34.0], [462.0, 35.0], [460.0, 59.666666666666664], [459.0, 39.54545454545455], [458.0, 35.5], [457.0, 36.0], [456.0, 40.125], [452.0, 38.35294117647059], [448.0, 38.5], [450.0, 38.25], [449.0, 34.333333333333336], [451.0, 38.63636363636363], [478.0, 51.06451612903226], [479.0, 45.4], [476.0, 49.333333333333336], [467.0, 44.0], [466.0, 44.0], [465.0, 45.57142857142857], [464.0, 43.705882352941174], [475.0, 35.64705882352941], [474.0, 33.0], [473.0, 52.166666666666664], [472.0, 31.0], [471.0, 43.2], [470.0, 39.16666666666667], [468.0, 40.08695652173913], [494.0, 46.55357142857143], [495.0, 40.241379310344826], [493.0, 38.1], [492.0, 37.666666666666664], [491.0, 34.5], [490.0, 47.0], [489.0, 36.75], [487.0, 42.75], [481.0, 48.16666666666667], [480.0, 35.3125], [483.0, 38.0], [482.0, 34.6], [486.0, 34.0], [485.0, 43.722222222222214], [484.0, 33.2], [500.0, 42.609090909090895], [499.0, 35.5], [498.0, 44.833333333333336], [497.0, 47.1604938271605], [496.0, 32.0]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[431.54600000000033, 41.90399999999996]], "isOverall": false, "label": "Play songs from albums-Aggregated", "isController": false}, {"data": [[12.0, 20.0], [13.0, 11.0], [14.0, 6.25], [15.0, 3.0], [16.0, 3.0], [17.0, 4.0], [18.0, 5.0], [19.0, 3.0], [20.0, 3.0], [21.0, 5.0], [22.0, 3.0], [23.0, 3.0], [24.0, 5.0], [25.0, 6.0], [26.0, 3.0], [27.0, 6.0], [28.0, 3.0], [29.0, 7.0], [30.0, 5.0], [31.0, 6.0], [32.0, 3.0], [33.0, 5.0], [34.0, 3.0], [35.0, 6.0], [36.0, 3.0], [37.0, 3.0], [38.0, 3.0], [39.0, 3.0], [40.0, 3.0], [41.0, 4.0], [42.0, 4.0], [43.0, 4.0], [44.0, 4.0], [45.0, 5.0], [46.0, 3.0], [47.0, 3.0], [48.0, 2.0], [49.0, 3.0], [50.0, 3.0], [51.0, 4.0], [52.0, 3.0], [53.0, 3.0], [54.0, 3.0], [55.0, 3.0], [56.0, 4.0], [57.0, 4.0], [58.0, 3.0], [59.0, 2.0], [60.0, 4.0], [61.0, 5.0], [62.0, 3.0], [63.0, 2.0], [64.0, 3.0], [65.0, 3.0], [66.0, 4.0], [67.0, 5.0], [68.0, 3.0], [69.0, 6.0], [70.0, 5.0], [71.0, 4.0], [72.0, 4.0], [73.0, 5.0], [74.0, 3.0], [75.0, 2.0], [76.0, 6.0], [77.0, 3.0], [78.0, 3.0], [79.0, 2.0], [80.0, 2.0], [81.0, 3.0], [82.0, 3.0], [83.0, 3.0], [84.0, 3.0], [85.0, 3.0], [86.0, 3.0], [87.0, 2.0], [88.0, 2.0], [89.0, 4.0], [90.0, 3.0], [91.0, 2.0], [92.0, 3.0], [93.0, 6.0], [94.0, 3.0], [95.0, 3.0], [96.0, 2.0], [97.0, 2.0], [98.0, 3.0], [99.0, 2.0], [100.0, 3.0], [101.0, 4.0], [102.0, 2.0], [103.0, 3.0], [104.0, 3.0], [105.0, 4.0], [106.0, 3.0], [107.0, 5.0], [108.0, 3.0], [109.0, 3.0], [110.0, 3.0], [111.0, 2.0], [112.0, 2.0], [113.0, 4.0], [114.0, 4.0], [115.0, 3.0], [116.0, 3.0], [117.0, 4.0], [118.0, 4.0], [119.0, 4.0], [120.0, 5.0], [121.0, 4.0], [122.0, 3.0], [123.0, 4.0], [124.0, 4.0], [125.0, 4.0], [126.0, 5.0], [127.0, 4.0], [128.0, 5.0], [129.0, 5.0], [130.0, 3.0], [131.0, 4.0], [132.0, 4.0], [133.0, 3.0], [134.0, 3.0], [135.0, 3.0], [136.0, 3.0], [137.0, 4.0], [138.0, 4.0], [139.0, 4.0], [140.0, 3.0], [141.0, 3.0], [142.0, 6.0], [143.0, 3.0], [144.0, 2.0], [145.0, 2.0], [146.0, 3.0], [147.0, 3.0], [148.0, 2.0], [149.0, 5.0], [150.0, 4.0], [151.0, 3.0], [152.0, 3.0], [153.0, 2.0], [154.0, 2.0], [155.0, 2.0], [156.0, 5.0], [157.0, 3.0], [158.0, 2.0], [159.0, 3.0], [160.0, 2.0], [161.0, 5.0], [162.0, 2.0], [163.0, 2.0], [164.0, 3.0], [165.0, 3.0], [166.0, 3.0], [167.0, 2.0], [168.0, 5.0], [169.0, 3.0], [170.0, 2.0], [171.0, 3.0], [172.0, 2.0], [173.0, 3.0], [174.0, 5.0], [175.0, 2.0], [176.0, 3.0], [177.0, 3.0], [178.0, 3.0], [179.0, 4.0], [180.0, 4.0], [181.0, 3.0], [182.0, 2.0], [183.0, 5.0], [184.0, 3.0], [185.0, 2.0], [186.0, 3.0], [187.0, 4.0], [188.0, 3.0], [189.0, 2.0], [190.0, 3.0], [191.0, 4.0], [192.0, 4.0], [193.0, 3.0], [194.0, 4.0], [195.0, 3.0], [196.0, 2.0], [197.0, 3.0], [198.0, 3.0], [199.0, 3.0], [200.0, 2.0], [201.0, 4.0], [202.0, 2.0], [203.0, 2.0], [204.0, 4.0], [205.0, 4.0], [206.0, 2.0], [207.0, 3.0], [208.0, 3.0], [209.0, 2.0], [210.0, 2.0], [211.0, 3.0], [212.0, 2.0], [213.0, 2.0], [214.0, 4.0], [215.0, 2.0], [216.0, 2.0], [217.0, 3.0], [218.0, 4.0], [219.0, 2.0], [220.0, 5.0], [221.0, 3.0], [222.0, 2.0], [223.0, 3.0], [224.0, 4.0], [225.0, 3.0], [226.0, 5.0], [227.0, 3.0], [228.0, 4.0], [229.0, 5.0], [230.0, 3.0], [231.0, 3.0], [232.0, 5.0], [233.0, 3.0], [234.0, 3.0], [235.0, 3.0], [236.0, 2.0], [237.0, 2.0], [238.0, 2.0], [239.0, 2.0], [240.0, 2.0], [241.0, 2.0], [242.0, 4.0], [243.0, 3.0], [244.0, 3.0], [245.0, 3.0], [246.0, 4.0], [247.0, 2.0], [248.0, 3.0], [249.0, 5.0], [250.0, 4.0], [251.0, 2.0], [252.0, 4.0], [253.0, 3.0], [254.0, 3.0], [255.0, 4.0], [257.0, 2.0], [256.0, 3.0], [258.0, 3.0], [259.0, 3.0], [260.0, 3.0], [261.0, 3.0], [262.0, 3.0], [263.0, 3.0], [264.0, 3.0], [270.0, 2.0], [271.0, 4.0], [268.0, 4.0], [269.0, 4.0], [265.0, 2.0], [266.0, 4.0], [267.0, 4.0], [273.0, 2.0], [272.0, 3.0], [274.0, 6.0], [275.0, 2.0], [276.0, 6.0], [277.0, 5.0], [278.0, 7.0], [279.0, 3.0], [280.0, 6.0], [286.0, 3.0], [287.0, 4.0], [284.0, 2.0], [285.0, 3.0], [281.0, 3.0], [282.0, 3.0], [283.0, 3.0], [289.0, 6.0], [288.0, 4.0], [290.0, 2.0], [291.0, 3.0], [292.0, 2.0], [293.0, 9.0], [294.0, 5.0], [295.0, 3.0], [296.0, 6.0], [302.0, 3.0], [303.0, 3.0], [300.0, 3.0], [301.0, 3.0], [297.0, 3.0], [298.0, 6.0], [299.0, 3.0], [305.0, 3.0], [304.0, 3.0], [306.0, 3.0], [307.0, 3.0], [308.0, 3.0], [309.0, 2.0], [310.0, 6.0], [311.0, 5.0], [312.0, 3.0], [318.0, 6.0], [319.0, 2.0], [316.0, 3.0], [317.0, 4.0], [313.0, 2.0], [314.0, 4.0], [315.0, 2.0], [321.0, 5.0], [320.0, 2.0], [322.0, 7.0], [323.0, 3.0], [324.0, 6.0], [325.0, 3.0], [326.0, 2.0], [327.0, 4.0], [328.0, 4.0], [334.0, 3.0], [335.0, 3.0], [332.0, 2.0], [333.0, 3.0], [329.0, 4.0], [330.0, 6.0], [331.0, 3.0], [338.0, 4.5], [336.0, 6.0], [339.0, 3.0], [349.0, 8.5], [350.0, 5.0], [351.0, 5.0], [340.0, 3.0], [341.0, 3.0], [342.0, 3.0], [343.0, 3.0], [344.0, 3.0], [345.0, 2.0], [346.0, 2.0], [347.0, 2.0], [353.0, 2.0], [352.0, 5.0], [354.0, 5.0], [355.0, 3.0], [356.0, 6.0], [357.0, 2.0], [358.0, 7.0], [359.0, 3.0], [360.0, 3.0], [367.0, 21.0], [364.0, 3.0], [365.0, 2.0], [361.0, 4.0], [362.0, 3.0], [363.0, 3.0], [370.0, 3.0], [369.0, 6.666666666666667], [371.0, 3.0], [380.0, 3.0], [381.0, 4.0], [382.0, 3.0], [383.0, 5.0], [372.0, 5.0], [373.0, 7.0], [374.0, 6.0], [375.0, 2.0], [376.0, 4.0], [377.0, 5.0], [378.0, 2.0], [379.0, 3.0], [385.0, 3.0], [384.0, 13.0], [386.0, 3.0], [387.0, 4.0], [388.0, 8.0], [389.0, 3.0], [390.0, 10.0], [392.0, 5.0], [399.0, 3.0], [396.0, 9.0], [398.0, 3.0], [393.0, 4.0], [394.0, 7.0], [395.0, 3.0], [401.0, 2.0], [400.0, 2.0], [402.0, 4.0], [403.0, 2.0], [404.0, 2.0], [405.0, 2.0], [406.0, 3.0], [407.0, 5.0], [408.0, 10.0], [415.0, 5.0], [412.0, 5.0], [414.0, 6.0], [409.0, 5.0], [410.0, 2.0], [411.0, 3.0], [417.0, 3.0], [416.0, 2.0], [418.0, 7.0], [419.0, 3.0], [420.0, 3.0], [421.0, 5.0], [422.0, 5.0], [423.0, 3.0], [424.0, 3.0], [431.0, 2.0], [429.0, 8.5], [430.0, 4.0], [425.0, 3.0], [426.0, 4.0], [427.0, 3.0], [434.0, 6.0], [433.0, 6.5], [435.0, 3.0], [444.0, 6.0], [445.0, 8.0], [446.0, 5.0], [447.0, 3.0], [436.0, 3.0], [437.0, 3.0], [439.0, 14.0], [441.0, 18.0], [442.0, 5.333333333333333], [443.0, 9.0], [449.0, 3.0], [448.0, 3.0], [450.0, 4.0], [451.0, 2.0], [452.0, 3.0], [453.0, 6.0], [454.0, 4.0], [455.0, 4.0], [457.0, 6.0], [462.0, 3.0], [460.0, 3.0], [461.0, 2.0], [458.0, 3.5], [459.0, 2.0], [465.0, 3.0], [464.0, 10.0], [467.0, 3.0], [476.0, 3.0], [477.0, 2.0], [478.0, 2.0], [479.0, 2.0], [468.0, 2.0], [469.0, 3.0], [470.0, 5.0], [471.0, 5.0], [473.0, 12.0], [474.0, 6.0], [475.0, 4.0], [481.0, 9.0], [480.0, 5.0], [482.0, 3.0], [483.0, 3.0], [484.0, 8.0], [485.0, 8.0], [486.0, 2.0], [487.0, 3.0], [488.0, 9.0], [494.0, 7.0], [495.0, 3.0], [492.0, 5.0], [493.0, 3.0], [490.0, 9.0], [491.0, 2.0], [497.0, 6.0], [498.0, 2.0], [499.0, 5.0], [500.0, 2.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[250.69999999999987, 4.138000000000002]], "isOverall": false, "label": "Cover 5-Aggregated", "isController": false}, {"data": [[139.0, 5.0], [145.0, 5.0], [163.0, 8.0], [166.0, 4.0], [170.0, 7.0], [177.0, 5.0], [178.0, 4.0], [179.0, 5.0], [181.0, 5.5], [184.0, 4.0], [185.0, 4.0], [190.0, 5.0], [192.0, 4.0], [194.0, 5.0], [195.0, 5.0], [196.0, 4.0], [197.0, 3.0], [198.0, 4.0], [199.0, 4.5], [200.0, 4.0], [201.0, 7.0], [203.0, 4.0], [204.0, 6.0], [207.0, 6.0], [209.0, 7.0], [211.0, 4.0], [213.0, 3.0], [214.0, 6.0], [217.0, 7.5], [218.0, 5.0], [219.0, 5.5], [220.0, 8.0], [221.0, 5.0], [222.0, 4.0], [223.0, 4.666666666666667], [224.0, 3.0], [225.0, 3.25], [226.0, 6.5], [228.0, 3.5], [229.0, 4.0], [230.0, 4.0], [231.0, 3.0], [232.0, 3.6666666666666665], [233.0, 5.0], [235.0, 6.0], [236.0, 4.333333333333333], [237.0, 6.0], [238.0, 4.5], [239.0, 5.0], [240.0, 7.0], [241.0, 3.5], [243.0, 3.6666666666666665], [244.0, 3.0], [245.0, 4.0], [246.0, 4.0], [247.0, 4.75], [249.0, 3.6666666666666665], [250.0, 4.0], [251.0, 4.8], [252.0, 6.333333333333333], [254.0, 3.0], [259.0, 5.5], [257.0, 4.0], [256.0, 5.5], [258.0, 2.0], [260.0, 5.0], [262.0, 5.333333333333333], [264.0, 3.0], [270.0, 5.666666666666667], [271.0, 4.5], [268.0, 5.571428571428572], [269.0, 3.0], [265.0, 3.5], [266.0, 9.0], [267.0, 7.0], [274.0, 4.0], [273.0, 4.5], [275.0, 3.0], [284.0, 5.333333333333333], [285.0, 6.0], [286.0, 5.5], [287.0, 4.0], [276.0, 3.5], [277.0, 6.0], [278.0, 3.833333333333333], [279.0, 3.3333333333333335], [280.0, 9.75], [281.0, 3.5], [282.0, 5.0], [283.0, 4.0], [289.0, 6.333333333333333], [288.0, 3.3333333333333335], [290.0, 3.0], [300.0, 7.0], [301.0, 3.0], [302.0, 6.0], [303.0, 6.5], [292.0, 4.0], [293.0, 5.25], [294.0, 3.5], [295.0, 5.0], [296.0, 8.666666666666666], [297.0, 6.333333333333333], [298.0, 5.4], [299.0, 4.5], [307.0, 7.0], [305.0, 6.666666666666667], [304.0, 4.5], [306.0, 4.833333333333334], [309.0, 6.0], [310.0, 5.5], [312.0, 5.714285714285714], [318.0, 9.5], [319.0, 4.0], [316.0, 5.0], [317.0, 6.0], [313.0, 4.333333333333333], [314.0, 5.333333333333333], [315.0, 9.0], [321.0, 3.6666666666666665], [320.0, 6.0], [322.0, 5.0], [323.0, 5.25], [324.0, 7.75], [326.0, 8.0], [327.0, 6.5], [328.0, 5.0], [334.0, 5.0], [335.0, 3.6], [332.0, 3.6666666666666665], [333.0, 3.75], [329.0, 4.0], [330.0, 8.0], [331.0, 6.333333333333333], [338.0, 12.0], [337.0, 9.2], [339.0, 6.5], [348.0, 8.333333333333334], [349.0, 8.666666666666666], [350.0, 4.5], [351.0, 4.75], [340.0, 5.833333333333334], [341.0, 6.5], [342.0, 5.4], [343.0, 6.25], [344.0, 5.333333333333333], [345.0, 5.0], [346.0, 5.0], [353.0, 6.0], [352.0, 7.333333333333333], [355.0, 4.0], [364.0, 8.0], [365.0, 3.6666666666666665], [366.0, 9.666666666666666], [367.0, 11.2], [356.0, 8.25], [357.0, 5.333333333333333], [358.0, 4.666666666666667], [359.0, 6.5], [360.0, 4.5], [361.0, 4.0], [362.0, 5.0], [363.0, 4.5], [370.0, 4.285714285714286], [368.0, 7.75], [371.0, 5.833333333333334], [382.0, 7.666666666666667], [383.0, 3.3333333333333335], [372.0, 4.25], [373.0, 8.333333333333334], [374.0, 4.0], [375.0, 3.0], [376.0, 4.5], [377.0, 6.0], [378.0, 6.0], [379.0, 5.2], [385.0, 3.0], [384.0, 7.500000000000001], [386.0, 5.0], [387.0, 6.714285714285714], [388.0, 6.0], [389.0, 4.0], [390.0, 6.8], [391.0, 11.0], [392.0, 4.333333333333333], [398.0, 6.0], [396.0, 4.0], [397.0, 6.2], [393.0, 4.166666666666666], [394.0, 5.0], [395.0, 3.0], [401.0, 3.75], [400.0, 6.5], [402.0, 4.428571428571429], [403.0, 3.875], [404.0, 7.142857142857143], [405.0, 5.0], [406.0, 3.5], [407.0, 6.0], [408.0, 7.5], [414.0, 7.6], [415.0, 5.5], [412.0, 5.2], [413.0, 17.0], [409.0, 8.75], [410.0, 5.0], [411.0, 2.75], [417.0, 5.0], [416.0, 3.5], [418.0, 11.0], [419.0, 4.0], [420.0, 5.5], [421.0, 8.0], [422.0, 7.5], [423.0, 4.666666666666667], [424.0, 7.166666666666666], [431.0, 3.6666666666666665], [428.0, 7.333333333333333], [429.0, 5.666666666666667], [425.0, 4.0], [426.0, 4.4], [434.0, 7.4], [432.0, 12.666666666666666], [435.0, 6.333333333333334], [444.0, 4.0], [445.0, 5.0], [446.0, 3.0], [447.0, 5.0], [436.0, 3.25], [437.0, 10.0], [438.0, 10.0], [439.0, 12.75], [440.0, 14.0], [441.0, 7.0], [442.0, 4.833333333333334], [443.0, 8.5], [450.0, 6.0], [448.0, 5.75], [451.0, 3.5], [460.0, 6.0], [461.0, 3.6666666666666665], [462.0, 4.75], [463.0, 11.8], [452.0, 5.25], [453.0, 5.5], [454.0, 4.0], [455.0, 6.6], [456.0, 11.0], [457.0, 8.0], [458.0, 5.0], [459.0, 6.0], [465.0, 5.6], [464.0, 7.4], [466.0, 10.0], [467.0, 5.0], [468.0, 4.428571428571429], [469.0, 11.0], [471.0, 3.6666666666666665], [472.0, 11.0], [478.0, 4.333333333333333], [479.0, 3.6666666666666665], [476.0, 3.333333333333333], [477.0, 2.5], [473.0, 8.0], [474.0, 3.0], [481.0, 5.75], [480.0, 7.0], [482.0, 8.5], [483.0, 4.8], [484.0, 4.333333333333333], [485.0, 4.0], [486.0, 4.666666666666667], [487.0, 4.0], [488.0, 9.5], [494.0, 7.333333333333333], [495.0, 4.333333333333333], [492.0, 4.833333333333333], [493.0, 4.0], [489.0, 8.0], [490.0, 9.0], [491.0, 5.0], [496.0, 8.0], [497.0, 8.166666666666666], [498.0, 8.5], [499.0, 7.5], [500.0, 5.12554112554113]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[425.90466666666754, 5.4340000000000055]], "isOverall": false, "label": "Search-Aggregated", "isController": false}, {"data": [[11.0, 375.875], [12.0, 399.0], [13.0, 288.3333333333333], [14.0, 82.5], [15.0, 38.0], [16.0, 38.0], [17.0, 40.0], [18.0, 39.0], [19.0, 39.0], [20.0, 39.0], [21.0, 39.0], [22.0, 36.0], [23.0, 40.0], [24.0, 39.0], [25.0, 36.0], [26.0, 46.0], [27.0, 39.0], [28.0, 37.0], [29.0, 37.0], [30.0, 37.0], [31.0, 39.0], [32.0, 39.0], [33.0, 39.0], [34.0, 43.0], [35.0, 38.0], [36.0, 36.0], [37.0, 37.0], [38.0, 36.0], [39.0, 41.0], [40.0, 37.0], [41.0, 37.0], [42.0, 38.0], [43.0, 37.0], [44.0, 37.0], [45.0, 37.0], [46.0, 38.0], [47.0, 39.0], [48.0, 38.0], [49.0, 38.0], [50.0, 37.0], [51.0, 39.0], [52.0, 39.0], [53.0, 41.0], [54.0, 37.0], [55.0, 38.0], [56.0, 36.0], [57.0, 37.0], [58.0, 38.0], [59.0, 44.0], [60.0, 39.0], [61.0, 38.0], [62.0, 35.0], [63.0, 38.0], [64.0, 37.0], [65.0, 36.0], [66.0, 37.0], [67.0, 38.0], [68.0, 38.0], [69.0, 40.0], [70.0, 36.0], [71.0, 37.0], [72.0, 38.0], [73.0, 36.0], [74.0, 40.0], [75.0, 40.0], [76.0, 42.0], [77.0, 36.0], [78.0, 38.0], [79.0, 37.0], [80.0, 37.0], [81.0, 38.0], [82.0, 36.0], [83.0, 38.0], [84.0, 49.0], [85.0, 37.0], [86.0, 37.0], [87.0, 37.0], [88.0, 38.0], [89.0, 34.0], [90.0, 39.0], [91.0, 36.0], [92.0, 38.0], [93.0, 39.0], [94.0, 38.0], [95.0, 37.0], [96.0, 36.0], [97.0, 39.0], [98.0, 39.0], [99.0, 38.0], [100.0, 38.0], [101.0, 39.0], [102.0, 38.0], [103.0, 35.0], [104.0, 35.0], [105.0, 35.0], [106.0, 35.0], [107.0, 36.0], [108.0, 35.0], [109.0, 50.0], [110.0, 39.0], [111.0, 37.0], [112.0, 40.0], [113.0, 49.0], [114.0, 37.0], [115.0, 37.0], [116.0, 47.0], [117.0, 38.0], [118.0, 38.0], [119.0, 38.0], [120.0, 34.0], [121.0, 35.0], [122.0, 35.0], [123.0, 37.0], [124.0, 39.0], [125.0, 35.0], [126.0, 40.0], [127.0, 38.0], [128.0, 37.0], [129.0, 44.0], [130.0, 38.0], [131.0, 36.0], [132.0, 37.0], [133.0, 37.0], [134.0, 49.0], [135.0, 38.0], [136.0, 39.0], [137.0, 39.0], [138.0, 37.0], [139.0, 35.0], [140.0, 35.0], [141.0, 35.0], [142.0, 35.0], [143.0, 38.0], [144.0, 38.0], [145.0, 37.0], [146.0, 38.0], [147.0, 34.0], [148.0, 35.0], [149.0, 35.0], [150.0, 37.0], [151.0, 37.0], [152.0, 38.0], [153.0, 37.0], [154.0, 35.0], [155.0, 37.0], [156.0, 35.0], [157.0, 35.0], [158.0, 39.0], [159.0, 51.0], [160.0, 36.0], [161.0, 36.0], [162.0, 37.0], [163.0, 37.0], [164.0, 37.0], [165.0, 35.0], [166.0, 34.0], [167.0, 37.0], [168.0, 35.0], [169.0, 38.0], [170.0, 39.0], [171.0, 37.0], [172.0, 35.0], [173.0, 36.0], [174.0, 37.0], [175.0, 34.0], [176.0, 38.0], [177.0, 39.0], [178.0, 37.0], [179.0, 35.0], [180.0, 36.0], [181.0, 37.0], [182.0, 36.0], [183.0, 36.0], [184.0, 48.0], [185.0, 38.0], [186.0, 36.0], [187.0, 35.0], [188.0, 35.0], [189.0, 38.0], [190.0, 35.0], [191.0, 38.0], [192.0, 37.0], [193.0, 34.0], [194.0, 36.0], [195.0, 35.0], [196.0, 36.0], [197.0, 36.0], [198.0, 35.0], [199.0, 39.0], [200.0, 36.0], [201.0, 36.0], [202.0, 35.0], [203.0, 39.0], [204.0, 36.0], [205.0, 36.0], [206.0, 37.0], [207.0, 39.0], [208.0, 35.0], [209.0, 50.0], [210.0, 40.0], [211.0, 36.0], [212.0, 37.0], [213.0, 39.0], [214.0, 35.0], [215.0, 36.0], [216.0, 40.0], [217.0, 37.0], [218.0, 38.0], [219.0, 38.0], [220.0, 37.0], [221.0, 36.0], [222.0, 36.0], [223.0, 36.0], [224.0, 35.0], [225.0, 37.0], [226.0, 35.0], [227.0, 40.0], [228.0, 36.0], [229.0, 35.0], [230.0, 34.0], [231.0, 39.0], [232.0, 35.0], [233.0, 36.0], [234.0, 50.0], [235.0, 37.0], [236.0, 39.0], [237.0, 36.0], [238.0, 37.0], [239.0, 37.0], [240.0, 35.0], [241.0, 35.0], [242.0, 37.0], [243.0, 35.0], [244.0, 37.0], [245.0, 39.0], [246.0, 35.0], [247.0, 38.0], [248.0, 39.0], [249.0, 35.0], [250.0, 35.0], [251.0, 36.0], [252.0, 36.0], [253.0, 38.0], [254.0, 35.0], [255.0, 37.0], [257.0, 38.0], [256.0, 35.0], [258.0, 36.0], [259.0, 51.0], [260.0, 40.0], [261.0, 38.0], [262.0, 35.0], [263.0, 35.0], [264.0, 35.0], [270.0, 36.0], [271.0, 35.0], [268.0, 36.0], [269.0, 38.0], [265.0, 38.0], [266.0, 52.0], [267.0, 35.0], [273.0, 35.0], [272.0, 36.0], [274.0, 36.0], [275.0, 36.0], [276.0, 37.0], [277.0, 36.0], [278.0, 39.0], [279.0, 36.0], [280.0, 38.0], [286.0, 43.0], [287.0, 37.0], [284.0, 38.0], [285.0, 35.0], [281.0, 38.0], [282.0, 37.0], [283.0, 37.0], [289.0, 45.0], [288.0, 37.0], [290.0, 35.0], [291.0, 36.0], [292.0, 36.0], [293.0, 37.0], [294.0, 36.0], [295.0, 41.0], [296.0, 50.0], [302.0, 41.0], [303.0, 43.0], [300.0, 37.0], [301.0, 40.0], [297.0, 37.0], [298.0, 49.0], [299.0, 38.0], [305.0, 40.0], [304.0, 39.0], [306.0, 40.0], [307.0, 41.0], [308.0, 43.0], [309.0, 41.0], [310.0, 38.0], [311.0, 36.0], [312.0, 69.0], [318.0, 41.0], [319.0, 35.0], [316.0, 45.0], [317.0, 61.0], [313.0, 39.0], [314.0, 43.0], [315.0, 52.0], [321.0, 37.0], [320.0, 36.0], [322.0, 34.0], [323.0, 35.0], [324.0, 71.0], [325.0, 35.0], [326.0, 70.0], [327.0, 56.0], [328.0, 40.0], [334.0, 36.0], [335.0, 38.0], [332.0, 36.0], [333.0, 35.0], [329.0, 36.0], [330.0, 35.0], [331.0, 37.0], [338.0, 123.5], [336.0, 37.0], [339.0, 37.0], [349.0, 92.0], [350.0, 35.0], [351.0, 37.0], [340.0, 37.0], [341.0, 37.0], [342.0, 47.0], [343.0, 53.0], [344.0, 41.0], [345.0, 39.0], [346.0, 37.0], [347.0, 37.0], [353.0, 60.0], [352.0, 54.0], [354.0, 34.0], [355.0, 70.0], [356.0, 49.0], [357.0, 35.0], [358.0, 35.0], [359.0, 37.0], [360.0, 36.0], [366.0, 95.0], [364.0, 55.0], [365.0, 35.0], [361.0, 35.0], [362.0, 36.0], [363.0, 47.0], [369.0, 96.5], [368.0, 211.0], [370.0, 37.0], [371.0, 41.0], [372.0, 37.0], [373.0, 52.0], [374.0, 38.0], [375.0, 36.0], [376.0, 38.0], [382.0, 68.0], [383.0, 35.0], [380.0, 58.0], [381.0, 38.0], [377.0, 53.0], [378.0, 80.0], [379.0, 35.0], [385.0, 42.0], [384.0, 51.0], [386.0, 38.0], [387.0, 50.0], [388.0, 56.0], [389.0, 35.0], [390.0, 45.0], [392.0, 85.0], [398.0, 78.0], [399.0, 35.0], [396.0, 42.0], [397.0, 98.0], [393.0, 36.0], [394.0, 65.0], [395.0, 77.0], [401.0, 35.0], [400.0, 69.0], [402.0, 39.0], [403.0, 37.0], [404.0, 40.0], [405.0, 39.0], [406.0, 38.0], [407.0, 65.0], [408.0, 36.0], [415.0, 70.0], [412.0, 38.0], [414.0, 112.0], [409.0, 68.0], [410.0, 40.0], [411.0, 42.0], [417.0, 38.0], [416.0, 36.0], [418.0, 57.0], [419.0, 37.0], [420.0, 53.0], [421.0, 61.0], [422.0, 38.0], [423.0, 36.0], [424.0, 38.0], [430.0, 46.0], [431.0, 38.0], [428.0, 75.0], [429.0, 40.0], [425.0, 36.0], [426.0, 35.0], [427.0, 36.0], [433.0, 48.0], [432.0, 96.0], [434.0, 68.0], [435.0, 38.0], [436.0, 36.0], [437.0, 39.0], [438.0, 110.0], [440.0, 180.0], [446.0, 36.0], [447.0, 58.0], [444.0, 53.0], [445.0, 43.0], [441.0, 215.0], [442.0, 99.0], [443.0, 58.0], [449.0, 68.0], [448.0, 53.0], [450.0, 54.0], [451.0, 36.0], [452.0, 39.0], [453.0, 43.0], [454.0, 35.0], [455.0, 38.0], [457.0, 166.0], [462.0, 38.0], [463.0, 54.0], [460.0, 52.0], [461.0, 37.0], [458.0, 75.0], [459.0, 38.0], [465.0, 65.0], [464.0, 52.0], [466.0, 95.0], [467.0, 39.0], [468.0, 38.0], [469.0, 36.0], [470.0, 51.0], [471.0, 37.0], [473.0, 116.0], [478.0, 36.0], [479.0, 37.0], [476.0, 35.0], [477.0, 36.0], [474.0, 38.0], [475.0, 34.0], [481.0, 45.0], [480.0, 65.0], [482.0, 45.0], [483.0, 37.0], [484.0, 36.0], [485.0, 37.0], [486.0, 36.0], [487.0, 35.0], [488.0, 65.0], [494.0, 39.0], [495.0, 37.0], [492.0, 36.0], [493.0, 64.0], [489.0, 99.0], [490.0, 56.0], [491.0, 36.0], [496.0, 70.0], [497.0, 90.0], [498.0, 51.0], [499.0, 37.0], [500.0, 36.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[250.64800000000008, 51.94599999999999]], "isOverall": false, "label": "Initial song-Aggregated", "isController": false}, {"data": [[263.0, 2.2], [313.0, 2.2], [328.0, 2.6], [346.0, 2.8], [363.0, 3.0], [360.0, 3.3000000000000003], [383.0, 2.3000000000000007], [380.0, 2.6], [378.0, 3.1], [373.0, 2.6], [371.0, 2.2], [370.0, 2.3000000000000003], [369.0, 2.4], [399.0, 3.0], [397.0, 2.2000000000000006], [391.0, 2.4], [388.0, 2.4000000000000004], [415.0, 3.3], [414.0, 2.0], [412.0, 2.2], [411.0, 2.2], [410.0, 2.1], [406.0, 3.150000000000001], [405.0, 2.3499999999999996], [403.0, 2.4], [430.0, 2.2], [426.0, 5.2], [425.0, 2.0], [422.0, 2.0], [421.0, 2.0], [420.0, 2.2], [418.0, 2.0], [417.0, 3.2000000000000006], [447.0, 2.4], [446.0, 1.8], [445.0, 2.24], [444.0, 3.3], [442.0, 2.4], [437.0, 2.5], [436.0, 2.1851851851851856], [435.0, 2.3333333333333335], [462.0, 2.4000000000000004], [461.0, 2.4], [460.0, 5.4], [459.0, 2.9], [456.0, 2.4], [452.0, 2.6666666666666665], [451.0, 2.5], [449.0, 2.5], [467.0, 2.5], [479.0, 2.0], [478.0, 3.0571428571428574], [475.0, 2.766666666666667], [473.0, 3.3666666666666663], [471.0, 2.0], [470.0, 2.4199999999999995], [468.0, 2.2000000000000006], [466.0, 3.3], [465.0, 2.0], [464.0, 2.3103448275862073], [494.0, 2.9707317073170727], [495.0, 2.741176470588235], [493.0, 2.86], [492.0, 2.1599999999999997], [491.0, 2.6000000000000005], [490.0, 2.9999999999999996], [489.0, 2.4], [488.0, 3.2], [487.0, 2.0], [481.0, 2.5999999999999996], [480.0, 2.9500000000000006], [483.0, 2.2], [482.0, 3.16], [486.0, 3.0666666666666664], [485.0, 2.4333333333333336], [484.0, 3.2], [500.0, 3.0158940397350995], [498.0, 3.2571428571428576], [497.0, 2.892307692307695]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}, {"data": [[478.8172000000014, 2.8352000000000075]], "isOverall": false, "label": "Get paths to songs from each album-Aggregated", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 500.0, "title": "Time VS Threads"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: { noColumns: 2,show: true, container: '#legendTimeVsThreads' },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s: At %x.2 active threads, Average response time was %y.2 ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesTimeVsThreads"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotTimesVsThreads"), dataset, options);
            // setup overview
            $.plot($("#overviewTimesVsThreads"), dataset, prepareOverviewOptions(options));
        }
};

// Time vs threads
function refreshTimeVsThreads(){
    var infos = timeVsThreadsInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTimeVsThreads");
        return;
    }
    if(isGraph($("#flotTimesVsThreads"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTimeVsThreads");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTimesVsThreads", "#overviewTimesVsThreads");
        $('#footerTimeVsThreads .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var bytesThroughputOverTimeInfos = {
        data : {"result": {"minY": 62.31666666666667, "minX": 1.74674562E12, "maxY": 8.821140873333333E7, "series": [{"data": [[1.74674586E12, 4.438411853333333E7], [1.74674568E12, 8.821140873333333E7], [1.74674574E12, 4.820308168333333E7], [1.74674604E12, 7327439.416666667], [1.74674562E12, 6.533258793333333E7], [1.7467461E12, 2328867.7666666666], [1.74674592E12, 3.2710624483333334E7], [1.74674598E12, 2.1960399783333335E7], [1.7467458E12, 4.77206462E7]], "isOverall": false, "label": "Bytes received per second", "isController": false}, {"data": [[1.74674586E12, 4102.1], [1.74674568E12, 6066.1], [1.74674574E12, 1601.0], [1.74674604E12, 210.31666666666666], [1.74674562E12, 7602.016666666666], [1.7467461E12, 62.31666666666667], [1.74674592E12, 2327.7166666666667], [1.74674598E12, 1193.05], [1.7467458E12, 3235.233333333333]], "isOverall": false, "label": "Bytes sent per second", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.7467461E12, "title": "Bytes Throughput Over Time"}},
        getOptions : function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity) ,
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Bytes / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendBytesThroughputOverTime'
                },
                selection: {
                    mode: "xy"
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y"
                }
            };
        },
        createGraph : function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesBytesThroughputOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotBytesThroughputOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewBytesThroughputOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Bytes throughput Over Time
function refreshBytesThroughputOverTime(fixTimestamps) {
    var infos = bytesThroughputOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotBytesThroughputOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesBytesThroughputOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotBytesThroughputOverTime", "#overviewBytesThroughputOverTime");
        $('#footerBytesThroughputOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimesOverTimeInfos = {
        data: {"result": {"minY": 2.2, "minX": 1.74674562E12, "maxY": 86.4273504273504, "series": [{"data": [[1.74674586E12, 3.579487179487179], [1.74674568E12, 4.5], [1.74674574E12, 4.586206896551723], [1.74674604E12, 4.0], [1.74674592E12, 3.447916666666666], [1.74674598E12, 3.4250000000000003], [1.7467458E12, 3.6148148148148165]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[1.74674568E12, 86.4273504273504], [1.74674562E12, 76.61096605744125]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[1.74674586E12, 49.56910569105692], [1.74674568E12, 66.54194630872479], [1.74674574E12, 50.9871794871795], [1.74674562E12, 77.8550724637681], [1.74674592E12, 47.18749999999999], [1.7467458E12, 49.17320261437908]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[1.74674568E12, 6.04273504273504], [1.74674562E12, 5.493472584856394]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[1.74674586E12, 17.86666666666665], [1.74674568E12, 22.75], [1.74674574E12, 20.517241379310345], [1.74674604E12, 15.0], [1.74674592E12, 16.375], [1.74674598E12, 16.525], [1.7467458E12, 18.36296296296296]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[1.74674568E12, 5.3247863247863245], [1.74674562E12, 4.540469973890342]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[1.74674568E12, 4.564102564102564], [1.74674562E12, 4.219321148825066]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[1.74674568E12, 5.102564102564104], [1.74674562E12, 4.349869451697128]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[1.74674568E12, 4.290598290598289], [1.74674562E12, 3.4569190600522166]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[1.74674586E12, 44.192857142857136], [1.74674568E12, 34.0], [1.74674574E12, 41.47368421052632], [1.74674604E12, 39.463768115942024], [1.7467461E12, 37.45454545454545], [1.74674592E12, 41.17931034482762], [1.74674598E12, 40.516908212560374], [1.7467458E12, 43.14285714285712]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[1.74674568E12, 4.794871794871794], [1.74674562E12, 3.937336814621409]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[1.74674568E12, 5.439885496183208], [1.74674562E12, 5.420353982300888]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[1.74674568E12, 56.3076923076923], [1.74674562E12, 50.61357702349867]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[1.74674586E12, 2.857435897435899], [1.74674568E12, 3.65], [1.74674574E12, 3.1862068965517234], [1.74674604E12, 2.2], [1.74674592E12, 2.5854166666666645], [1.74674598E12, 2.6199999999999997], [1.7467458E12, 2.949629629629629]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.7467461E12, "title": "Response Time Over Time"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average response time was %y ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Times Over Time
function refreshResponseTimeOverTime(fixTimestamps) {
    var infos = responseTimesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotResponseTimesOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesOverTime", "#overviewResponseTimesOverTime");
        $('#footerResponseTimesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var latenciesOverTimeInfos = {
        data: {"result": {"minY": 1.8616187989556132, "minX": 1.74674562E12, "maxY": 22.75, "series": [{"data": [[1.74674586E12, 3.579487179487179], [1.74674568E12, 4.5], [1.74674574E12, 4.586206896551723], [1.74674604E12, 4.0], [1.74674592E12, 3.447916666666666], [1.74674598E12, 3.4250000000000003], [1.7467458E12, 3.607407407407409]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[1.74674568E12, 19.61538461538461], [1.74674562E12, 17.331592689295046]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[1.74674586E12, 3.3495934959349585], [1.74674568E12, 3.5453020134228206], [1.74674574E12, 3.3743589743589726], [1.74674562E12, 3.4202898550724643], [1.74674592E12, 3.6875], [1.7467458E12, 3.2679738562091507]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[1.74674568E12, 6.017094017094017], [1.74674562E12, 5.475195822454314]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[1.74674586E12, 17.846153846153847], [1.74674568E12, 22.75], [1.74674574E12, 20.448275862068968], [1.74674604E12, 15.0], [1.74674592E12, 16.354166666666668], [1.74674598E12, 16.525], [1.7467458E12, 18.348148148148145]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[1.74674568E12, 2.1965811965811968], [1.74674562E12, 1.9425587467362917]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[1.74674568E12, 2.410256410256411], [1.74674562E12, 2.2088772845952964]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[1.74674568E12, 2.213675213675213], [1.74674562E12, 1.9007832898172325]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[1.74674568E12, 2.273504273504274], [1.74674562E12, 1.8616187989556132]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[1.74674586E12, 3.246428571428573], [1.74674568E12, 3.0], [1.74674574E12, 3.31578947368421], [1.74674604E12, 2.9855072463768106], [1.7467461E12, 3.0], [1.74674592E12, 3.1275862068965505], [1.74674598E12, 3.0917874396135256], [1.7467458E12, 3.267857142857143]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[1.74674568E12, 2.4957264957264957], [1.74674562E12, 2.0417754569190603]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[1.74674568E12, 5.422709923664116], [1.74674562E12, 5.398230088495573]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[1.74674568E12, 2.0085470085470076], [1.74674562E12, 1.900783289817233]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[1.74674586E12, 2.8533333333333317], [1.74674568E12, 3.65], [1.74674574E12, 3.1724137931034475], [1.74674604E12, 2.2], [1.74674592E12, 2.581249999999999], [1.74674598E12, 2.6199999999999997], [1.7467458E12, 2.9481481481481495]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.7467461E12, "title": "Latencies Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response latencies in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendLatenciesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average latency was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesLatenciesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotLatenciesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewLatenciesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Latencies Over Time
function refreshLatenciesOverTime(fixTimestamps) {
    var infos = latenciesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyLatenciesOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotLatenciesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesLatenciesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotLatenciesOverTime", "#overviewLatenciesOverTime");
        $('#footerLatenciesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var connectTimeOverTimeInfos = {
        data: {"result": {"minY": 0.0, "minX": 1.74674562E12, "maxY": 1.0, "series": [{"data": [[1.74674586E12, 0.7641025641025639], [1.74674568E12, 0.75], [1.74674574E12, 0.9655172413793103], [1.74674604E12, 1.0], [1.74674592E12, 0.7395833333333333], [1.74674598E12, 0.775], [1.7467458E12, 0.7925925925925924]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[1.74674568E12, 0.7606837606837606], [1.74674562E12, 0.785900783289817]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[1.74674586E12, 0.8373983739837397], [1.74674568E12, 0.5486577181208054], [1.74674574E12, 0.8615384615384608], [1.74674562E12, 0.4202898550724638], [1.74674592E12, 0.9999999999999999], [1.7467458E12, 0.8464052287581696]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[1.74674568E12, 0.7606837606837602], [1.74674562E12, 0.785900783289817]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[1.74674586E12, 0.764102564102564], [1.74674568E12, 0.75], [1.74674574E12, 0.9655172413793104], [1.74674604E12, 1.0], [1.74674592E12, 0.7395833333333333], [1.74674598E12, 0.7749999999999998], [1.7467458E12, 0.7925925925925923]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[1.74674568E12, 0.0], [1.74674562E12, 0.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[1.74674568E12, 0.0], [1.74674562E12, 0.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[1.74674568E12, 0.0], [1.74674562E12, 0.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[1.74674568E12, 0.0], [1.74674562E12, 0.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[1.74674586E12, 0.8535714285714282], [1.74674568E12, 0.0], [1.74674574E12, 0.894736842105263], [1.74674604E12, 0.7971014492753624], [1.7467461E12, 0.9090909090909091], [1.74674592E12, 0.8034482758620685], [1.74674598E12, 0.8309178743961353], [1.7467458E12, 0.830357142857143]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[1.74674568E12, 0.0], [1.74674562E12, 0.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[1.74674568E12, 0.5715648854961825], [1.74674562E12, 0.6659292035398235]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[1.74674568E12, 0.0], [1.74674562E12, 0.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[1.74674586E12, 0.0], [1.74674568E12, 0.0], [1.74674574E12, 0.0], [1.74674604E12, 0.0], [1.74674592E12, 0.0], [1.74674598E12, 0.0], [1.7467458E12, 0.0]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.7467461E12, "title": "Connect Time Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getConnectTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average Connect Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendConnectTimeOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average connect time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesConnectTimeOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotConnectTimeOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewConnectTimeOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Connect Time Over Time
function refreshConnectTimeOverTime(fixTimestamps) {
    var infos = connectTimeOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyConnectTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotConnectTimeOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesConnectTimeOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotConnectTimeOverTime", "#overviewConnectTimeOverTime");
        $('#footerConnectTimeOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var responseTimePercentilesOverTimeInfos = {
        data: {"result": {"minY": 1.0, "minX": 1.74674562E12, "maxY": 409.0, "series": [{"data": [[1.74674586E12, 125.0], [1.74674568E12, 272.0], [1.74674574E12, 137.0], [1.74674604E12, 68.0], [1.74674562E12, 409.0], [1.7467461E12, 41.0], [1.74674592E12, 97.0], [1.74674598E12, 112.0], [1.7467458E12, 118.0]], "isOverall": false, "label": "Max", "isController": false}, {"data": [[1.74674586E12, 1.0], [1.74674568E12, 2.0], [1.74674574E12, 2.0], [1.74674604E12, 2.0], [1.74674562E12, 2.0], [1.7467461E12, 33.0], [1.74674592E12, 1.0], [1.74674598E12, 2.0], [1.7467458E12, 1.0]], "isOverall": false, "label": "Min", "isController": false}, {"data": [[1.74674586E12, 42.600000000000136], [1.74674568E12, 64.0], [1.74674574E12, 73.0], [1.74674604E12, 41.400000000000006], [1.74674562E12, 37.0], [1.7467461E12, 40.7], [1.74674592E12, 43.700000000000045], [1.74674598E12, 44.0], [1.7467458E12, 51.0]], "isOverall": false, "label": "90th percentile", "isController": false}, {"data": [[1.74674586E12, 92.0], [1.74674568E12, 168.33000000000038], [1.74674574E12, 123.15999999999997], [1.74674604E12, 68.0], [1.74674562E12, 97.9099999999994], [1.7467461E12, 41.0], [1.74674592E12, 70.0], [1.74674598E12, 72.07999999999993], [1.7467458E12, 90.0]], "isOverall": false, "label": "99th percentile", "isController": false}, {"data": [[1.74674586E12, 3.0], [1.74674568E12, 6.0], [1.74674574E12, 37.0], [1.74674604E12, 37.0], [1.74674562E12, 4.0], [1.7467461E12, 37.5], [1.74674592E12, 3.0], [1.74674598E12, 5.0], [1.7467458E12, 4.0]], "isOverall": false, "label": "Median", "isController": false}, {"data": [[1.74674586E12, 57.0], [1.74674568E12, 92.54999999999973], [1.74674574E12, 89.0], [1.74674604E12, 63.400000000000006], [1.74674562E12, 39.0], [1.7467461E12, 41.0], [1.74674592E12, 56.0], [1.74674598E12, 55.0], [1.7467458E12, 67.0]], "isOverall": false, "label": "95th percentile", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.7467461E12, "title": "Response Time Percentiles Over Time (successful requests only)"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Response Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentilesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Response time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentilesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimePercentilesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimePercentilesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Time Percentiles Over Time
function refreshResponseTimePercentilesOverTime(fixTimestamps) {
    var infos = responseTimePercentilesOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotResponseTimePercentilesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimePercentilesOverTime", "#overviewResponseTimePercentilesOverTime");
        $('#footerResponseTimePercentilesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var responseTimeVsRequestInfos = {
    data: {"result": {"minY": 2.0, "minX": 1.0, "maxY": 38.5, "series": [{"data": [[2.0, 37.0], [3.0, 38.0], [4.0, 37.0], [5.0, 37.0], [6.0, 37.0], [7.0, 37.0], [8.0, 38.5], [9.0, 35.0], [10.0, 30.5], [11.0, 18.5], [12.0, 31.5], [13.0, 30.0], [14.0, 33.0], [15.0, 5.0], [16.0, 3.0], [17.0, 5.0], [18.0, 4.0], [19.0, 3.0], [20.0, 2.0], [21.0, 3.0], [22.0, 4.0], [23.0, 4.0], [24.0, 4.0], [25.0, 4.0], [26.0, 5.0], [27.0, 2.0], [28.0, 4.0], [29.0, 3.0], [30.0, 3.0], [31.0, 2.0], [32.0, 3.0], [33.0, 3.0], [35.0, 4.0], [34.0, 5.0], [36.0, 5.0], [37.0, 3.0], [39.0, 4.5], [38.0, 4.0], [40.0, 6.0], [41.0, 2.0], [43.0, 2.0], [44.0, 3.0], [45.0, 3.0], [46.0, 2.0], [49.0, 14.0], [51.0, 3.0], [57.0, 4.0], [59.0, 3.0], [58.0, 3.0], [60.0, 3.0], [61.0, 3.0], [63.0, 3.0], [64.0, 4.0], [71.0, 4.0], [70.0, 3.0], [72.0, 4.0], [74.0, 3.0], [73.0, 4.0], [75.0, 4.0], [77.0, 4.0], [79.0, 4.0], [76.0, 4.0], [81.0, 4.0], [85.0, 4.0], [86.0, 5.0], [87.0, 6.0], [84.0, 5.0], [89.0, 5.0], [91.0, 5.0], [90.0, 4.5], [92.0, 5.0], [93.0, 5.0], [95.0, 6.0], [94.0, 6.0], [97.0, 6.0], [99.0, 6.0], [102.0, 4.0], [101.0, 6.0], [105.0, 21.0], [108.0, 6.0], [1.0, 37.0]], "isOverall": false, "label": "Successes", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 108.0, "title": "Response Time Vs Request"}},
    getOptions: function() {
        return {
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Response Time in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: {
                noColumns: 2,
                show: true,
                container: '#legendResponseTimeVsRequest'
            },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median response time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesResponseTimeVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotResponseTimeVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewResponseTimeVsRequest"), dataset, prepareOverviewOptions(options));

    }
};

// Response Time vs Request
function refreshResponseTimeVsRequest() {
    var infos = responseTimeVsRequestInfos;
    prepareSeries(infos.data);
    if (isGraph($("#flotResponseTimeVsRequest"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeVsRequest");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimeVsRequest", "#overviewResponseTimeVsRequest");
        $('#footerResponseRimeVsRequest .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var latenciesVsRequestInfos = {
    data: {"result": {"minY": 2.0, "minX": 1.0, "maxY": 6.0, "series": [{"data": [[2.0, 3.0], [3.0, 3.0], [4.0, 3.0], [5.0, 3.0], [6.0, 3.0], [7.0, 3.0], [8.0, 3.0], [9.0, 3.0], [10.0, 3.0], [11.0, 3.0], [12.0, 3.0], [13.0, 3.0], [14.0, 3.0], [15.0, 3.0], [16.0, 3.0], [17.0, 3.0], [18.0, 3.0], [19.0, 2.5], [20.0, 2.0], [21.0, 3.0], [22.0, 3.0], [23.0, 3.0], [24.0, 3.0], [25.0, 2.0], [26.0, 3.0], [27.0, 2.0], [28.0, 3.0], [29.0, 3.0], [30.0, 2.0], [31.0, 2.0], [32.0, 3.0], [33.0, 2.0], [35.0, 3.0], [34.0, 3.0], [36.0, 3.5], [37.0, 3.0], [39.0, 3.0], [38.0, 3.0], [40.0, 4.0], [41.0, 2.0], [43.0, 2.0], [44.0, 3.0], [45.0, 3.0], [46.0, 2.0], [49.0, 5.0], [51.0, 3.0], [57.0, 2.0], [59.0, 2.0], [58.0, 2.0], [60.0, 2.0], [61.0, 2.0], [63.0, 2.0], [64.0, 2.0], [71.0, 2.0], [70.0, 2.0], [72.0, 2.0], [74.0, 2.0], [73.0, 2.0], [75.0, 2.0], [77.0, 2.0], [79.0, 2.0], [76.0, 2.0], [81.0, 2.0], [85.0, 2.0], [86.0, 3.0], [87.0, 3.0], [84.0, 3.0], [89.0, 3.0], [91.0, 3.0], [90.0, 2.0], [92.0, 3.0], [93.0, 3.0], [95.0, 3.0], [94.0, 3.0], [97.0, 3.0], [99.0, 3.0], [102.0, 3.0], [101.0, 3.0], [105.0, 6.0], [108.0, 4.0], [1.0, 3.0]], "isOverall": false, "label": "Successes", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 108.0, "title": "Latencies Vs Request"}},
    getOptions: function() {
        return{
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Latency in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: { noColumns: 2,show: true, container: '#legendLatencyVsRequest' },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median Latency time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesLatencyVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotLatenciesVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewLatenciesVsRequest"), dataset, prepareOverviewOptions(options));
    }
};

// Latencies vs Request
function refreshLatenciesVsRequest() {
        var infos = latenciesVsRequestInfos;
        prepareSeries(infos.data);
        if(isGraph($("#flotLatenciesVsRequest"))){
            infos.createGraph();
        }else{
            var choiceContainer = $("#choicesLatencyVsRequest");
            createLegend(choiceContainer, infos);
            infos.createGraph();
            setGraphZoomable("#flotLatenciesVsRequest", "#overviewLatenciesVsRequest");
            $('#footerLatenciesVsRequest .legendColorBox > div').each(function(i){
                $(this).clone().prependTo(choiceContainer.find("li").eq(i));
            });
        }
};

var hitsPerSecondInfos = {
        data: {"result": {"minY": 0.36666666666666664, "minX": 1.74674562E12, "maxY": 53.36666666666667, "series": [{"data": [[1.74674586E12, 26.233333333333334], [1.74674568E12, 41.483333333333334], [1.74674574E12, 9.716666666666667], [1.74674604E12, 1.2333333333333334], [1.74674562E12, 53.36666666666667], [1.7467461E12, 0.36666666666666664], [1.74674592E12, 14.683333333333334], [1.74674598E12, 7.466666666666667], [1.7467458E12, 20.45]], "isOverall": false, "label": "hitsPerSecond", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.7467461E12, "title": "Hits Per Second"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of hits / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendHitsPerSecond"
                },
                selection: {
                    mode : 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y.2 hits/sec"
                }
            };
        },
        createGraph: function createGraph() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesHitsPerSecond"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotHitsPerSecond"), dataset, options);
            // setup overview
            $.plot($("#overviewHitsPerSecond"), dataset, prepareOverviewOptions(options));
        }
};

// Hits per second
function refreshHitsPerSecond(fixTimestamps) {
    var infos = hitsPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if (isGraph($("#flotHitsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesHitsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotHitsPerSecond", "#overviewHitsPerSecond");
        $('#footerHitsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var codesPerSecondInfos = {
        data: {"result": {"minY": 0.36666666666666664, "minX": 1.74674562E12, "maxY": 53.36666666666667, "series": [{"data": [[1.74674586E12, 26.216666666666665], [1.74674568E12, 41.46666666666667], [1.74674574E12, 9.716666666666667], [1.74674604E12, 1.25], [1.74674562E12, 53.36666666666667], [1.7467461E12, 0.36666666666666664], [1.74674592E12, 14.7], [1.74674598E12, 7.45], [1.7467458E12, 20.466666666666665]], "isOverall": false, "label": "200", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.7467461E12, "title": "Codes Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendCodesPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "Number of Response Codes %s at %x was %y.2 responses / sec"
                }
            };
        },
    createGraph: function() {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesCodesPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotCodesPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewCodesPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Codes per second
function refreshCodesPerSecond(fixTimestamps) {
    var infos = codesPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotCodesPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesCodesPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotCodesPerSecond", "#overviewCodesPerSecond");
        $('#footerCodesPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var transactionsPerSecondInfos = {
        data: {"result": {"minY": 0.016666666666666666, "minX": 1.74674562E12, "maxY": 17.466666666666665, "series": [{"data": [[1.74674568E12, 17.466666666666665], [1.74674562E12, 7.533333333333333]], "isOverall": false, "label": "Search-success", "isController": false}, {"data": [[1.74674568E12, 1.95], [1.74674562E12, 6.383333333333334]], "isOverall": false, "label": "Cover 3-success", "isController": false}, {"data": [[1.74674568E12, 1.95], [1.74674562E12, 6.383333333333334]], "isOverall": false, "label": "Cover 1-success", "isController": false}, {"data": [[1.74674586E12, 3.25], [1.74674568E12, 0.06666666666666667], [1.74674574E12, 0.48333333333333334], [1.74674604E12, 0.016666666666666666], [1.74674592E12, 1.6], [1.74674598E12, 0.6666666666666666], [1.7467458E12, 2.25]], "isOverall": false, "label": "Albums page-success", "isController": true}, {"data": [[1.74674568E12, 1.95], [1.74674562E12, 6.383333333333334]], "isOverall": false, "label": "Cover 4-success", "isController": false}, {"data": [[1.74674586E12, 16.25], [1.74674568E12, 0.3333333333333333], [1.74674574E12, 2.4166666666666665], [1.74674604E12, 0.08333333333333333], [1.74674592E12, 8.0], [1.74674598E12, 3.3333333333333335], [1.7467458E12, 11.25]], "isOverall": false, "label": "Get paths to songs from each album-success", "isController": false}, {"data": [[1.74674586E12, 2.05], [1.74674568E12, 9.933333333333334], [1.74674574E12, 6.5], [1.74674562E12, 1.15], [1.74674592E12, 0.26666666666666666], [1.7467458E12, 5.1]], "isOverall": false, "label": "Play songs from home page-success", "isController": false}, {"data": [[1.74674568E12, 1.95], [1.74674562E12, 6.383333333333334]], "isOverall": false, "label": "Initial song-success", "isController": false}, {"data": [[1.74674568E12, 1.95], [1.74674562E12, 6.383333333333334]], "isOverall": false, "label": "Cover 5-success", "isController": false}, {"data": [[1.74674568E12, 1.95], [1.74674562E12, 6.383333333333334]], "isOverall": false, "label": "Home page-success", "isController": true}, {"data": [[1.74674586E12, 4.666666666666667], [1.74674568E12, 0.016666666666666666], [1.74674574E12, 0.31666666666666665], [1.74674604E12, 1.15], [1.7467461E12, 0.36666666666666664], [1.74674592E12, 4.833333333333333], [1.74674598E12, 3.45], [1.7467458E12, 1.8666666666666667]], "isOverall": false, "label": "Play songs from albums-success", "isController": false}, {"data": [[1.74674568E12, 1.95], [1.74674562E12, 6.383333333333334]], "isOverall": false, "label": "Trending playlist-success", "isController": false}, {"data": [[1.74674568E12, 1.95], [1.74674562E12, 6.383333333333334]], "isOverall": false, "label": "Cover 2-success", "isController": false}, {"data": [[1.74674586E12, 3.25], [1.74674568E12, 0.06666666666666667], [1.74674574E12, 0.48333333333333334], [1.74674604E12, 0.016666666666666666], [1.74674592E12, 1.6], [1.74674598E12, 0.6666666666666666], [1.7467458E12, 2.25]], "isOverall": false, "label": "Get all albums-success", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.7467461E12, "title": "Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTransactionsPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                }
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTransactionsPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTransactionsPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewTransactionsPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Transactions per second
function refreshTransactionsPerSecond(fixTimestamps) {
    var infos = transactionsPerSecondInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTransactionsPerSecond");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotTransactionsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTransactionsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTransactionsPerSecond", "#overviewTransactionsPerSecond");
        $('#footerTransactionsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var totalTPSInfos = {
        data: {"result": {"minY": 0.36666666666666664, "minX": 1.74674562E12, "maxY": 59.75, "series": [{"data": [[1.74674586E12, 29.466666666666665], [1.74674568E12, 43.483333333333334], [1.74674574E12, 10.2], [1.74674604E12, 1.2666666666666666], [1.74674562E12, 59.75], [1.7467461E12, 0.36666666666666664], [1.74674592E12, 16.3], [1.74674598E12, 8.116666666666667], [1.7467458E12, 22.716666666666665]], "isOverall": false, "label": "Transaction-success", "isController": false}, {"data": [], "isOverall": false, "label": "Transaction-failure", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.7467461E12, "title": "Total Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTotalTPS"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                },
                colors: ["#9ACD32", "#FF6347"]
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTotalTPS"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTotalTPS"), dataset, options);
        // setup overview
        $.plot($("#overviewTotalTPS"), dataset, prepareOverviewOptions(options));
    }
};

// Total Transactions per second
function refreshTotalTPS(fixTimestamps) {
    var infos = totalTPSInfos;
    // We want to ignore seriesFilter
    prepareSeries(infos.data, false, true);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotTotalTPS"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTotalTPS");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTotalTPS", "#overviewTotalTPS");
        $('#footerTotalTPS .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

// Collapse the graph matching the specified DOM element depending the collapsed
// status
function collapse(elem, collapsed){
    if(collapsed){
        $(elem).parent().find(".fa-chevron-up").removeClass("fa-chevron-up").addClass("fa-chevron-down");
    } else {
        $(elem).parent().find(".fa-chevron-down").removeClass("fa-chevron-down").addClass("fa-chevron-up");
        if (elem.id == "bodyBytesThroughputOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshBytesThroughputOverTime(true);
            }
            document.location.href="#bytesThroughputOverTime";
        } else if (elem.id == "bodyLatenciesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesOverTime(true);
            }
            document.location.href="#latenciesOverTime";
        } else if (elem.id == "bodyCustomGraph") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCustomGraph(true);
            }
            document.location.href="#responseCustomGraph";
        } else if (elem.id == "bodyConnectTimeOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshConnectTimeOverTime(true);
            }
            document.location.href="#connectTimeOverTime";
        } else if (elem.id == "bodyResponseTimePercentilesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimePercentilesOverTime(true);
            }
            document.location.href="#responseTimePercentilesOverTime";
        } else if (elem.id == "bodyResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeDistribution();
            }
            document.location.href="#responseTimeDistribution" ;
        } else if (elem.id == "bodySyntheticResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshSyntheticResponseTimeDistribution();
            }
            document.location.href="#syntheticResponseTimeDistribution" ;
        } else if (elem.id == "bodyActiveThreadsOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshActiveThreadsOverTime(true);
            }
            document.location.href="#activeThreadsOverTime";
        } else if (elem.id == "bodyTimeVsThreads") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTimeVsThreads();
            }
            document.location.href="#timeVsThreads" ;
        } else if (elem.id == "bodyCodesPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCodesPerSecond(true);
            }
            document.location.href="#codesPerSecond";
        } else if (elem.id == "bodyTransactionsPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTransactionsPerSecond(true);
            }
            document.location.href="#transactionsPerSecond";
        } else if (elem.id == "bodyTotalTPS") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTotalTPS(true);
            }
            document.location.href="#totalTPS";
        } else if (elem.id == "bodyResponseTimeVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeVsRequest();
            }
            document.location.href="#responseTimeVsRequest";
        } else if (elem.id == "bodyLatenciesVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesVsRequest();
            }
            document.location.href="#latencyVsRequest";
        }
    }
}

/*
 * Activates or deactivates all series of the specified graph (represented by id parameter)
 * depending on checked argument.
 */
function toggleAll(id, checked){
    var placeholder = document.getElementById(id);

    var cases = $(placeholder).find(':checkbox');
    cases.prop('checked', checked);
    $(cases).parent().children().children().toggleClass("legend-disabled", !checked);

    var choiceContainer;
    if ( id == "choicesBytesThroughputOverTime"){
        choiceContainer = $("#choicesBytesThroughputOverTime");
        refreshBytesThroughputOverTime(false);
    } else if(id == "choicesResponseTimesOverTime"){
        choiceContainer = $("#choicesResponseTimesOverTime");
        refreshResponseTimeOverTime(false);
    }else if(id == "choicesResponseCustomGraph"){
        choiceContainer = $("#choicesResponseCustomGraph");
        refreshCustomGraph(false);
    } else if ( id == "choicesLatenciesOverTime"){
        choiceContainer = $("#choicesLatenciesOverTime");
        refreshLatenciesOverTime(false);
    } else if ( id == "choicesConnectTimeOverTime"){
        choiceContainer = $("#choicesConnectTimeOverTime");
        refreshConnectTimeOverTime(false);
    } else if ( id == "choicesResponseTimePercentilesOverTime"){
        choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        refreshResponseTimePercentilesOverTime(false);
    } else if ( id == "choicesResponseTimePercentiles"){
        choiceContainer = $("#choicesResponseTimePercentiles");
        refreshResponseTimePercentiles();
    } else if(id == "choicesActiveThreadsOverTime"){
        choiceContainer = $("#choicesActiveThreadsOverTime");
        refreshActiveThreadsOverTime(false);
    } else if ( id == "choicesTimeVsThreads"){
        choiceContainer = $("#choicesTimeVsThreads");
        refreshTimeVsThreads();
    } else if ( id == "choicesSyntheticResponseTimeDistribution"){
        choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        refreshSyntheticResponseTimeDistribution();
    } else if ( id == "choicesResponseTimeDistribution"){
        choiceContainer = $("#choicesResponseTimeDistribution");
        refreshResponseTimeDistribution();
    } else if ( id == "choicesHitsPerSecond"){
        choiceContainer = $("#choicesHitsPerSecond");
        refreshHitsPerSecond(false);
    } else if(id == "choicesCodesPerSecond"){
        choiceContainer = $("#choicesCodesPerSecond");
        refreshCodesPerSecond(false);
    } else if ( id == "choicesTransactionsPerSecond"){
        choiceContainer = $("#choicesTransactionsPerSecond");
        refreshTransactionsPerSecond(false);
    } else if ( id == "choicesTotalTPS"){
        choiceContainer = $("#choicesTotalTPS");
        refreshTotalTPS(false);
    } else if ( id == "choicesResponseTimeVsRequest"){
        choiceContainer = $("#choicesResponseTimeVsRequest");
        refreshResponseTimeVsRequest();
    } else if ( id == "choicesLatencyVsRequest"){
        choiceContainer = $("#choicesLatencyVsRequest");
        refreshLatenciesVsRequest();
    }
    var color = checked ? "black" : "#818181";
    if(choiceContainer != null) {
        choiceContainer.find("label").each(function(){
            this.style.color = color;
        });
    }
}

