{"name": "plin062-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "build": "tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.1.4", "@hookform/resolvers": "^3.7.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@tanstack/react-query": "^5.59.0", "@tanstack/react-table": "^8.20.5", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "dotenv": "^16.4.5", "howler": "^2.2.4", "lucide-react": "^0.399.0", "process": "^0.11.10", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.1", "react-router-dom": "^6.24.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/howler": "^2.2.12", "@types/node": "^20.14.9", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "postcss": "^8.4.39", "tailwindcss": "^3.4.4", "typescript": "^5.2.2", "vite": "^5.3.1"}}