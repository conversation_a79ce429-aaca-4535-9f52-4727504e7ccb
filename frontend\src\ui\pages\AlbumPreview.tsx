import {mediaPath} from "@/App.tsx";
import {useAlbum} from "@/hooks/useAlbum.ts";
import {useParams} from "react-router-dom";
import {SongsTable} from "@/ui/components/page-content/table/SongsTable.tsx";
import {Formater} from "@/business/Formater.ts";

function AlbumPreview() {
  const {albumId} = useParams();
  const {data: album, error, isFetching} = useAlbum(albumId as string);

  if (isFetching) {
    return <div>Loading...</div>;
  } else if (error) {
    return <div>Error: {error.message}</div>;
  } else if (!album) {
    return <div>Album not found</div>;
  }

  const duration = album.songs.reduce((acc, song) => acc + song.duration, 0);

  return (
    <div className="p-4 bg-bg-primary-color">
      <div className="flex gap-8">
        <img src={mediaPath + album.cover} alt="album cover" className="w-44 h-44"/>
        <div className="self-center flex flex-col gap-2">
          <h2 className="self-center text-4xl">{album.title}</h2>
          <div className="flex">
            <p>
              {Formater.formatYear(album.releaseDate)} • {album.songs.length} songs
              • {Formater.formatDuration(duration)}
            </p>
          </div>
        </div>
      </div>
      <div>
        <SongsTable data={album}/>
      </div>
    </div>
  );
}

export default AlbumPreview;