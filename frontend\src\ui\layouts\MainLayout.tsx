import Navbar from "@/ui/components/navbar/Navbar.tsx";
import {Outlet} from "react-router-dom"
import Footer from "@/ui/components/footer/Footer.tsx";
import Player from "@/ui/components/player/PlayerBar.tsx";
import {createContext, useState} from "react";
import {SongFilterType} from "@/types/types.ts";


export const FilterContext = createContext<SongFilterType>({
  name: ""
});

function MainLayout() {
  const [filter, setFilter] = useState<SongFilterType>({
    name: "",
  });

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar setFilter={setFilter}/>
      <FilterContext.Provider value={filter}>
        <div className="flex-grow">
          <Outlet/>
        </div>
      </FilterContext.Provider>
      <Footer/>
      <Player/>
    </div>
  );
}

export default MainLayout