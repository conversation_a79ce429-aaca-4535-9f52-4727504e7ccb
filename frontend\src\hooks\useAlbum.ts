import {useQuery} from "@tanstack/react-query";
import albumApi from "@/api/albumApi.ts";

export const useAlbums = () => {
  const {data, isFetching, error} = useQuery({
    queryKey: ["album"],
    queryFn: async () => await albumApi.getAll(),
    staleTime: 5 * 60 * 1000,
  });

  return {data, isFetching, error};
}

export const useAlbum = (id: string) => {
  const {data, isFetching, error} = useQuery({
    queryKey: ["album-id", id],
    queryFn: async () => await albumApi.getSingle(id),
    staleTime: 5 * 60 * 1000,
  });

  return {data, isFetching, error};
}
