import express from 'express';
import {playlistController} from "./playlist/controller";
import path from "path";
import {albumController} from "./album/controller";

export const playlistRouter = express.Router();
export const mediaRouter = express.Router();
export const albumRouter = express.Router();

playlistRouter.get('/:id', playlistController.getPlaylist);

mediaRouter.use(express.static(path.join(`${__dirname}/..`, "media")));

albumRouter.get("/:id", albumController.getAlbum);
albumRouter.get("", albumController.getAlbums);