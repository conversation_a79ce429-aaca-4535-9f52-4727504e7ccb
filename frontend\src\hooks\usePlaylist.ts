import {useQuery} from "@tanstack/react-query";
import playlistApi from "@/api/playlistApi.ts";

export const usePlaylist = (id: string, filterName: string = "") => {
  const {data, isFetching, error} = useQuery({
    queryKey: ["playlist-id", id, filterName],
    queryFn: async () => await playlistApi.getSingle(id, filterName),
    staleTime: 5 * 60 * 1000,
  });

  return {data, isFetching, error};
};
