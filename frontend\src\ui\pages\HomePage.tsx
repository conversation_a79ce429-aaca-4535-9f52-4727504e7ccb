import {SongsTable} from "@/ui/components/page-content/table/SongsTable.tsx";
import {usePlaylist} from "@/hooks/usePlaylist.ts";
import {useContext} from "react";
import {FilterContext} from "@/ui/layouts/MainLayout.tsx";

function HomePage() {
  const filterName = useContext(FilterContext);
  const {data, isFetching} = usePlaylist("0", filterName.name);

  if (isFetching) {
    return <div>Loading...</div>;
  }

  if (!data) {
    return <div>503 Service Unavailable</div>;
  }

  return (
    <div className="p-4 bg-bg-primary-color">
      <h1 className="text-center font-bold text-2xl mb-2">
        {filterName.name ? `Results for: "${filterName.name}"` : data.title}
      </h1>
      <div>
        <SongsTable data={data}/>
      </div>
    </div>
  );
}

export default HomePage