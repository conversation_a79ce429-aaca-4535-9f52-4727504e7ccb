import Image from "@/data/Image.tsx";
import profile_picture from "@/data/content/profile_picture.png";

export const name = "SoundFolio";

export const bio = "<PERSON><PERSON><PERSON> is a talented musician known for her eclectic mix of genres and captivating performances. " +
  "With a career spanning over a decade, she has released multiple albums and singles, " +
  "each showcasing his unique sound and musical evolution.";

export const collaboration = [
  "Lil Plank",
  "Dos Uno",
  "Franky Lee",
  "Mc D",
  "Luna Harmony",
  "Echo Blaze",
  "Zephyr Star",
  "Aria Phoenix",
  "River Vibe",
  "Solstice Ray",
  "Aurora Pulse",
  "Lyric Ember",
  "Nova Wave",
  "Storm Melody",
]

export const image = Image(profile_picture, "SoundFolio - profile picture")

export const socials = {
  yt: {
    name: "YouTube",
    link: "https://youtube.com",
    icon: "youtube_icon.png"
  },
  ig: {
    name: "Instagram",
    link: "https://instagram.com",
    icon: "instagram_icon.png"
  },
  fb: {
    name: "Facebook",
    link: "https://facebook.com",
    icon: "facebook_icon.png"
  },
  tt: {
    name: "TikTok",
    link: "https://tiktok.com",
    icon: "tiktok_icon.png"
  },
  tw: {
    name: "Twitter",
    link: "https://twitter.com",
    icon: "twitter_icon.png"
  },
}


export const contacts = {
  mail: {
    name: "Mail",
    text: "<EMAIL>",
    link: "mailto:<EMAIL>",
  },
  address: {
    name: "Address",
    text: "Brno, Czech Republic",
    link: "https://maps.app.goo.gl/Z7BEAayJ5irrDj7b8",
  },
  phone: {
    name: "Phone",
    text: "+420 123 456 789",
    link: "tel:+420123456789",
  },
}