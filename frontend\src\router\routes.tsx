import {createBrowserRouter, Navigate, type RouteObject} from "react-router-dom"
import HomePage from "@/ui/pages/HomePage.tsx";
import MainLayout from "@/ui/layouts/MainLayout.tsx";
import NotFoundPage from "@/ui/pages/NotFoundPage.tsx";
import AboutPage from "@/ui/pages/AboutPage.tsx";
import AlbumsPage from "@/ui/pages/AlbumsPage.tsx";
import AlbumPreview from "@/ui/pages/AlbumPreview.tsx";

const mainPages: RouteObject[] = [
  {
    index: true,
    element: <Navigate to="./home" relative="path"/>,
  },
  {
    path: 'home',
    Component: HomePage,
  },
  {
    path: '/about',
    Component: AboutPage,
  },
  {
    path: '/albums',
    Component: AlbumsPage,
  },
  {
    path: '/albums/:albumId',
    Component: AlbumPreview,
  },
]

const routes: RouteObject[] = [
  {
    path: '/',
    Component: MainLayout,
    children: mainPages
  },
  {
    path: '*',
    Component: NotFoundPage,
  },
]

const router = createBrowserRouter(routes)

export default router;
