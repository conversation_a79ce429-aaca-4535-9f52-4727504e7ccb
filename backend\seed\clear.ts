import {PrismaClient} from "@prisma/client";

const client = new PrismaClient();

async function clearDatabase() {
  await client.$transaction([
    client.genre.deleteMany(),
    client.song.deleteMany(),
    client.album.deleteMany(),
    client.playlist.deleteMany(),
  ]);
}

clearDatabase()
  .then(() => {
    console.log('All data deleted');
    process.exit(0);
  })
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });