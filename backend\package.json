{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "seed": "tsx src/seed/seed.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@asteasolutions/zod-to-openapi": "^7.3.4", "@badrap/result": "^0.2.13", "@prisma/extension-accelerate": "^2.0.2", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^17.1.0", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "swagger-ui-express": "^5.0.1", "zod": "^3.25.76", "zod-validation-error": "^3.5.3"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.15", "@types/swagger-ui-express": "^4.1.8", "nodemon": "^3.1.0", "prisma": "^6.12.0", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3"}}