{"scripts": {"seed": "tsx seed/seed.ts", "clear": "tsx seed/clear.ts", "start_dev": "ts-node src/index.ts", "dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node src/index.js"}, "devDependencies": {"@faker-js/faker": "^9.0.3", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/node": "^22.7.4", "nodemon": "^3.1.0", "prisma": "^6.1.0", "ts-node": "^10.9.2", "tsx": "^4.19.1", "typescript": "^5.6.2"}, "dependencies": {"@badrap/result": "^0.2.13", "@prisma/client": "^6.1.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.0", "zod": "^3.23.8", "zod-validation-error": "^3.4.0"}}