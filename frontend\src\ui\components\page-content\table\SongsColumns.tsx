import {ColumnDef} from "@tanstack/react-table"
import {Song} from "@/types/types.ts";
import {mediaPath} from "@/App.tsx";
import {Formater} from "@/business/Formater.ts";

export const SongsColumns: ColumnDef<Song>[] = [
  {
    accessorKey: "cover",
    header: "",

    cell: ({row}) => {
      return (
          <div className="song-cover w-16 h-16 relative  duration-300 group-hover:scale-110">
            <img src={mediaPath + row.original.cover} alt="cover"/>
          </div>
        );
    },
  },
  {
    accessorKey: "title",
    header: "Title",
    cell: ({row}) => {
      const song = row.original;

      return (
        <div className="text-lg font-bold p-4">
          {song.title}
        </div>
      );
    }
  },
  {
    accessorKey: "duration",
    header: "Time",
    cell: ({row}) => {
      const song = row.original;

      return (
        <div className="p-4">
          {Formater.formatTime(song.duration)}
        </div>
      );
    }
  },
  {
    accessorKey: "bpm",
    header: "BPM",
    cell: ({row}) => {
      const song = row.original;

      return (
        <div className="px-4">
          {song.bpm}
        </div>
      );
    }
  },
  {
    accessorKey: "genre",
    header: "Genre",
    cell: ({row}) => {
      const song = row.original;

      return (
        <div className="px-4">
          {song.genres.map((genre) => genre.name).join(", ")}
        </div>
      );
    }
  },
  {
    accessorKey: "price",
    header: "Price",
    cell: ({row}) => {
      const song = row.original;

      return (
        <div className="px-4">
          {Formater.formatPrice(song.price)}
        </div>
      );
    }
  },
]
