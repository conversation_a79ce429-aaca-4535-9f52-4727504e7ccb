// Inspirited by https://github.com/goldfire/howler.js/blob/master/examples/player/player.js

import {Song} from "@/types/types.ts";
import {Howl, Howler} from "howler";
import {mediaPath} from "@/App.tsx";

/**
 * Player class containing the state of our playlist and what song is currently playing.
 * Includes all methods for playing, skipping, updating the display, etc.
 * @param {Array} playlist Array of objects with playlist album details ({title, src, howl}).
 */

// global variables
Howler.autoSuspend = false;
Howler.html5PoolSize = 1;

export class Player {
  playlist: Song[] = [];
  songIndex: number = 0;
  isPlaying: boolean = false;
  id: string = "";
  progressCallback?: (seek: number) => void;
  songChangeCallback?: (songIndex: number) => void;

  constructor(playlist: Song[], id: string) {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this
    self.playlist = playlist;
    self.id = id;

  }

  onProgress = (callback: (seek: number) => void) => {
    this.progressCallback = callback;
  }

  onSongChange(callback: (songIndex: number) => void) {
    this.songChangeCallback = callback;
  }

  play(index: number, play: boolean = true): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this;

    const song = self.playlist[index];
    let sound: Howl;

    if (song.howl) {
      sound = song.howl;
    } else {
      sound = song.howl = new Howl({
        src: mediaPath + song.src,
        pool: 1, // size of the inactive sounds pool, sounds marked as finished are cleaned up
        // Set to true to force HTML5 Audio. This should be used for large audio files so that you don't have to wait
        // for the full file to be downloaded and decoded before playing.
        // Unfortunately this causes issues with playing - player stops working after few songs
        // html5: true

        onplay: (): void => {
          requestAnimationFrame(self.step.bind(self));
        },
        onend: function (): void {
          self.skip(true);
        },
      });
    }

    if (play) {
      sound.play();
      self.isPlaying = true;
    }
    self.songIndex = index;
    requestAnimationFrame(self.step.bind(self));
  }

  pause(): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this;

    const sound = self.playlist[self.songIndex].howl;
    if (sound) {
      sound.pause();
      self.isPlaying = false;
    }
  }

  skip(isNext: boolean): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this;

    let index = self.songIndex;
    if (isNext) {
      index = (index + 1) % self.playlist.length;
    } else {
      index = (index - 1 + self.playlist.length) % self.playlist.length;
    }
    self.skipTo(index);
  }

  skipTo(index: number): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this;

    if (self.playlist[self.songIndex].howl) {
      self.playlist[self.songIndex].howl.stop();
    }

    self.play(index, self.isPlaying)
    if (self.songChangeCallback) {
      self.songChangeCallback(index);
    }
  }

  volume(volume?: number): number {
    if (volume === undefined) {
      return Howler.volume() * 100
    }
    Howler.volume(volume / 100);
    return volume
  }

  seek(per: number): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this;

    const sound = self.playlist[self.songIndex].howl;
    const seekPosition = sound.duration() * per;

    sound.seek(seekPosition);
    requestAnimationFrame(self.step.bind(self))
  }

  step(): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this;

    const sound = self?.playlist[self?.songIndex]?.howl;
    const seek = sound?.seek() || 0;

    if (self.progressCallback) {
      self.progressCallback(seek)
    }
    requestAnimationFrame(self.step.bind(self));
  }

  duration(): number {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this;
    const sound = self.playlist[self.songIndex]?.howl;

    if (sound) {
      return Math.round(sound.duration());
    }

    return 0;
  }

  stop(): void {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this;

    const sound = self.playlist[self.songIndex].howl;
    if (sound) {
      sound.stop();
      self.isPlaying = false;
    }
  }
}

