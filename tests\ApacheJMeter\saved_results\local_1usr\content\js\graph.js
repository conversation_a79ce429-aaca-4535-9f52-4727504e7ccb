/*
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
$(document).ready(function() {

    $(".click-title").mouseenter( function(    e){
        e.preventDefault();
        this.style.cursor="pointer";
    });
    $(".click-title").mousedown( function(event){
        event.preventDefault();
    });

    // Ugly code while this script is shared among several pages
    try{
        refreshHitsPerSecond(true);
    } catch(e){}
    try{
        refreshResponseTimeOverTime(true);
    } catch(e){}
    try{
        refreshResponseTimePercentiles();
    } catch(e){}
});


var responseTimePercentilesInfos = {
        data: {"result": {"minY": 4.0, "minX": 0.0, "maxY": 197.0, "series": [{"data": [[0.0, 18.0], [0.1, 18.0], [0.2, 18.0], [0.3, 18.0], [0.4, 18.0], [0.5, 18.0], [0.6, 18.0], [0.7, 18.0], [0.8, 18.0], [0.9, 18.0], [1.0, 18.0], [1.1, 18.0], [1.2, 18.0], [1.3, 18.0], [1.4, 18.0], [1.5, 18.0], [1.6, 18.0], [1.7, 18.0], [1.8, 18.0], [1.9, 18.0], [2.0, 18.0], [2.1, 18.0], [2.2, 18.0], [2.3, 18.0], [2.4, 18.0], [2.5, 18.0], [2.6, 18.0], [2.7, 18.0], [2.8, 18.0], [2.9, 18.0], [3.0, 18.0], [3.1, 18.0], [3.2, 18.0], [3.3, 18.0], [3.4, 18.0], [3.5, 18.0], [3.6, 18.0], [3.7, 18.0], [3.8, 18.0], [3.9, 18.0], [4.0, 18.0], [4.1, 18.0], [4.2, 18.0], [4.3, 18.0], [4.4, 18.0], [4.5, 18.0], [4.6, 18.0], [4.7, 18.0], [4.8, 18.0], [4.9, 18.0], [5.0, 18.0], [5.1, 18.0], [5.2, 18.0], [5.3, 18.0], [5.4, 18.0], [5.5, 18.0], [5.6, 18.0], [5.7, 18.0], [5.8, 18.0], [5.9, 18.0], [6.0, 18.0], [6.1, 18.0], [6.2, 18.0], [6.3, 18.0], [6.4, 18.0], [6.5, 18.0], [6.6, 18.0], [6.7, 18.0], [6.8, 18.0], [6.9, 18.0], [7.0, 18.0], [7.1, 18.0], [7.2, 18.0], [7.3, 18.0], [7.4, 18.0], [7.5, 18.0], [7.6, 18.0], [7.7, 18.0], [7.8, 18.0], [7.9, 18.0], [8.0, 18.0], [8.1, 18.0], [8.2, 18.0], [8.3, 18.0], [8.4, 18.0], [8.5, 18.0], [8.6, 18.0], [8.7, 18.0], [8.8, 18.0], [8.9, 18.0], [9.0, 18.0], [9.1, 18.0], [9.2, 18.0], [9.3, 18.0], [9.4, 18.0], [9.5, 18.0], [9.6, 18.0], [9.7, 18.0], [9.8, 18.0], [9.9, 18.0], [10.0, 18.0], [10.1, 18.0], [10.2, 18.0], [10.3, 18.0], [10.4, 18.0], [10.5, 18.0], [10.6, 18.0], [10.7, 18.0], [10.8, 18.0], [10.9, 18.0], [11.0, 18.0], [11.1, 18.0], [11.2, 18.0], [11.3, 18.0], [11.4, 18.0], [11.5, 18.0], [11.6, 18.0], [11.7, 18.0], [11.8, 18.0], [11.9, 18.0], [12.0, 18.0], [12.1, 18.0], [12.2, 18.0], [12.3, 18.0], [12.4, 18.0], [12.5, 18.0], [12.6, 18.0], [12.7, 18.0], [12.8, 18.0], [12.9, 18.0], [13.0, 18.0], [13.1, 18.0], [13.2, 18.0], [13.3, 18.0], [13.4, 18.0], [13.5, 18.0], [13.6, 18.0], [13.7, 18.0], [13.8, 18.0], [13.9, 18.0], [14.0, 18.0], [14.1, 18.0], [14.2, 18.0], [14.3, 18.0], [14.4, 18.0], [14.5, 18.0], [14.6, 18.0], [14.7, 18.0], [14.8, 18.0], [14.9, 18.0], [15.0, 18.0], [15.1, 18.0], [15.2, 18.0], [15.3, 18.0], [15.4, 18.0], [15.5, 18.0], [15.6, 18.0], [15.7, 18.0], [15.8, 18.0], [15.9, 18.0], [16.0, 18.0], [16.1, 18.0], [16.2, 18.0], [16.3, 18.0], [16.4, 18.0], [16.5, 18.0], [16.6, 18.0], [16.7, 18.0], [16.8, 18.0], [16.9, 18.0], [17.0, 18.0], [17.1, 18.0], [17.2, 18.0], [17.3, 18.0], [17.4, 18.0], [17.5, 18.0], [17.6, 18.0], [17.7, 18.0], [17.8, 18.0], [17.9, 18.0], [18.0, 18.0], [18.1, 18.0], [18.2, 18.0], [18.3, 18.0], [18.4, 18.0], [18.5, 18.0], [18.6, 18.0], [18.7, 18.0], [18.8, 18.0], [18.9, 18.0], [19.0, 18.0], [19.1, 18.0], [19.2, 18.0], [19.3, 18.0], [19.4, 18.0], [19.5, 18.0], [19.6, 18.0], [19.7, 18.0], [19.8, 18.0], [19.9, 18.0], [20.0, 18.0], [20.1, 18.0], [20.2, 18.0], [20.3, 18.0], [20.4, 18.0], [20.5, 18.0], [20.6, 18.0], [20.7, 18.0], [20.8, 18.0], [20.9, 18.0], [21.0, 18.0], [21.1, 18.0], [21.2, 18.0], [21.3, 18.0], [21.4, 18.0], [21.5, 18.0], [21.6, 18.0], [21.7, 18.0], [21.8, 18.0], [21.9, 18.0], [22.0, 18.0], [22.1, 18.0], [22.2, 18.0], [22.3, 18.0], [22.4, 18.0], [22.5, 18.0], [22.6, 18.0], [22.7, 18.0], [22.8, 18.0], [22.9, 18.0], [23.0, 18.0], [23.1, 18.0], [23.2, 18.0], [23.3, 18.0], [23.4, 18.0], [23.5, 18.0], [23.6, 18.0], [23.7, 18.0], [23.8, 18.0], [23.9, 18.0], [24.0, 18.0], [24.1, 18.0], [24.2, 18.0], [24.3, 18.0], [24.4, 18.0], [24.5, 18.0], [24.6, 18.0], [24.7, 18.0], [24.8, 18.0], [24.9, 18.0], [25.0, 18.0], [25.1, 18.0], [25.2, 18.0], [25.3, 18.0], [25.4, 18.0], [25.5, 18.0], [25.6, 18.0], [25.7, 18.0], [25.8, 18.0], [25.9, 18.0], [26.0, 18.0], [26.1, 18.0], [26.2, 18.0], [26.3, 18.0], [26.4, 18.0], [26.5, 18.0], [26.6, 18.0], [26.7, 18.0], [26.8, 18.0], [26.9, 18.0], [27.0, 18.0], [27.1, 18.0], [27.2, 18.0], [27.3, 18.0], [27.4, 18.0], [27.5, 18.0], [27.6, 18.0], [27.7, 18.0], [27.8, 18.0], [27.9, 18.0], [28.0, 18.0], [28.1, 18.0], [28.2, 18.0], [28.3, 18.0], [28.4, 18.0], [28.5, 18.0], [28.6, 18.0], [28.7, 18.0], [28.8, 18.0], [28.9, 18.0], [29.0, 18.0], [29.1, 18.0], [29.2, 18.0], [29.3, 18.0], [29.4, 18.0], [29.5, 18.0], [29.6, 18.0], [29.7, 18.0], [29.8, 18.0], [29.9, 18.0], [30.0, 18.0], [30.1, 18.0], [30.2, 18.0], [30.3, 18.0], [30.4, 18.0], [30.5, 18.0], [30.6, 18.0], [30.7, 18.0], [30.8, 18.0], [30.9, 18.0], [31.0, 18.0], [31.1, 18.0], [31.2, 18.0], [31.3, 18.0], [31.4, 18.0], [31.5, 18.0], [31.6, 18.0], [31.7, 18.0], [31.8, 18.0], [31.9, 18.0], [32.0, 18.0], [32.1, 18.0], [32.2, 18.0], [32.3, 18.0], [32.4, 18.0], [32.5, 18.0], [32.6, 18.0], [32.7, 18.0], [32.8, 18.0], [32.9, 18.0], [33.0, 18.0], [33.1, 18.0], [33.2, 18.0], [33.3, 18.0], [33.4, 18.0], [33.5, 18.0], [33.6, 18.0], [33.7, 18.0], [33.8, 18.0], [33.9, 18.0], [34.0, 18.0], [34.1, 18.0], [34.2, 18.0], [34.3, 18.0], [34.4, 18.0], [34.5, 18.0], [34.6, 18.0], [34.7, 18.0], [34.8, 18.0], [34.9, 18.0], [35.0, 18.0], [35.1, 18.0], [35.2, 18.0], [35.3, 18.0], [35.4, 18.0], [35.5, 18.0], [35.6, 18.0], [35.7, 18.0], [35.8, 18.0], [35.9, 18.0], [36.0, 18.0], [36.1, 18.0], [36.2, 18.0], [36.3, 18.0], [36.4, 18.0], [36.5, 18.0], [36.6, 18.0], [36.7, 18.0], [36.8, 18.0], [36.9, 18.0], [37.0, 18.0], [37.1, 18.0], [37.2, 18.0], [37.3, 18.0], [37.4, 18.0], [37.5, 18.0], [37.6, 18.0], [37.7, 18.0], [37.8, 18.0], [37.9, 18.0], [38.0, 18.0], [38.1, 18.0], [38.2, 18.0], [38.3, 18.0], [38.4, 18.0], [38.5, 18.0], [38.6, 18.0], [38.7, 18.0], [38.8, 18.0], [38.9, 18.0], [39.0, 18.0], [39.1, 18.0], [39.2, 18.0], [39.3, 18.0], [39.4, 18.0], [39.5, 18.0], [39.6, 18.0], [39.7, 18.0], [39.8, 18.0], [39.9, 18.0], [40.0, 18.0], [40.1, 18.0], [40.2, 18.0], [40.3, 18.0], [40.4, 18.0], [40.5, 18.0], [40.6, 18.0], [40.7, 18.0], [40.8, 18.0], [40.9, 18.0], [41.0, 18.0], [41.1, 18.0], [41.2, 18.0], [41.3, 18.0], [41.4, 18.0], [41.5, 18.0], [41.6, 18.0], [41.7, 18.0], [41.8, 18.0], [41.9, 18.0], [42.0, 18.0], [42.1, 18.0], [42.2, 18.0], [42.3, 18.0], [42.4, 18.0], [42.5, 18.0], [42.6, 18.0], [42.7, 18.0], [42.8, 18.0], [42.9, 18.0], [43.0, 18.0], [43.1, 18.0], [43.2, 18.0], [43.3, 18.0], [43.4, 18.0], [43.5, 18.0], [43.6, 18.0], [43.7, 18.0], [43.8, 18.0], [43.9, 18.0], [44.0, 18.0], [44.1, 18.0], [44.2, 18.0], [44.3, 18.0], [44.4, 18.0], [44.5, 18.0], [44.6, 18.0], [44.7, 18.0], [44.8, 18.0], [44.9, 18.0], [45.0, 18.0], [45.1, 18.0], [45.2, 18.0], [45.3, 18.0], [45.4, 18.0], [45.5, 18.0], [45.6, 18.0], [45.7, 18.0], [45.8, 18.0], [45.9, 18.0], [46.0, 18.0], [46.1, 18.0], [46.2, 18.0], [46.3, 18.0], [46.4, 18.0], [46.5, 18.0], [46.6, 18.0], [46.7, 18.0], [46.8, 18.0], [46.9, 18.0], [47.0, 18.0], [47.1, 18.0], [47.2, 18.0], [47.3, 18.0], [47.4, 18.0], [47.5, 18.0], [47.6, 18.0], [47.7, 18.0], [47.8, 18.0], [47.9, 18.0], [48.0, 18.0], [48.1, 18.0], [48.2, 18.0], [48.3, 18.0], [48.4, 18.0], [48.5, 18.0], [48.6, 18.0], [48.7, 18.0], [48.8, 18.0], [48.9, 18.0], [49.0, 18.0], [49.1, 18.0], [49.2, 18.0], [49.3, 18.0], [49.4, 18.0], [49.5, 18.0], [49.6, 18.0], [49.7, 18.0], [49.8, 18.0], [49.9, 18.0], [50.0, 18.0], [50.1, 18.0], [50.2, 18.0], [50.3, 18.0], [50.4, 18.0], [50.5, 18.0], [50.6, 18.0], [50.7, 18.0], [50.8, 18.0], [50.9, 18.0], [51.0, 18.0], [51.1, 18.0], [51.2, 18.0], [51.3, 18.0], [51.4, 18.0], [51.5, 18.0], [51.6, 18.0], [51.7, 18.0], [51.8, 18.0], [51.9, 18.0], [52.0, 18.0], [52.1, 18.0], [52.2, 18.0], [52.3, 18.0], [52.4, 18.0], [52.5, 18.0], [52.6, 18.0], [52.7, 18.0], [52.8, 18.0], [52.9, 18.0], [53.0, 18.0], [53.1, 18.0], [53.2, 18.0], [53.3, 18.0], [53.4, 18.0], [53.5, 18.0], [53.6, 18.0], [53.7, 18.0], [53.8, 18.0], [53.9, 18.0], [54.0, 18.0], [54.1, 18.0], [54.2, 18.0], [54.3, 18.0], [54.4, 18.0], [54.5, 18.0], [54.6, 18.0], [54.7, 18.0], [54.8, 18.0], [54.9, 18.0], [55.0, 18.0], [55.1, 18.0], [55.2, 18.0], [55.3, 18.0], [55.4, 18.0], [55.5, 18.0], [55.6, 18.0], [55.7, 18.0], [55.8, 18.0], [55.9, 18.0], [56.0, 18.0], [56.1, 18.0], [56.2, 18.0], [56.3, 18.0], [56.4, 18.0], [56.5, 18.0], [56.6, 18.0], [56.7, 18.0], [56.8, 18.0], [56.9, 18.0], [57.0, 18.0], [57.1, 18.0], [57.2, 18.0], [57.3, 18.0], [57.4, 18.0], [57.5, 18.0], [57.6, 18.0], [57.7, 18.0], [57.8, 18.0], [57.9, 18.0], [58.0, 18.0], [58.1, 18.0], [58.2, 18.0], [58.3, 18.0], [58.4, 18.0], [58.5, 18.0], [58.6, 18.0], [58.7, 18.0], [58.8, 18.0], [58.9, 18.0], [59.0, 18.0], [59.1, 18.0], [59.2, 18.0], [59.3, 18.0], [59.4, 18.0], [59.5, 18.0], [59.6, 18.0], [59.7, 18.0], [59.8, 18.0], [59.9, 18.0], [60.0, 18.0], [60.1, 18.0], [60.2, 18.0], [60.3, 18.0], [60.4, 18.0], [60.5, 18.0], [60.6, 18.0], [60.7, 18.0], [60.8, 18.0], [60.9, 18.0], [61.0, 18.0], [61.1, 18.0], [61.2, 18.0], [61.3, 18.0], [61.4, 18.0], [61.5, 18.0], [61.6, 18.0], [61.7, 18.0], [61.8, 18.0], [61.9, 18.0], [62.0, 18.0], [62.1, 18.0], [62.2, 18.0], [62.3, 18.0], [62.4, 18.0], [62.5, 18.0], [62.6, 18.0], [62.7, 18.0], [62.8, 18.0], [62.9, 18.0], [63.0, 18.0], [63.1, 18.0], [63.2, 18.0], [63.3, 18.0], [63.4, 18.0], [63.5, 18.0], [63.6, 18.0], [63.7, 18.0], [63.8, 18.0], [63.9, 18.0], [64.0, 18.0], [64.1, 18.0], [64.2, 18.0], [64.3, 18.0], [64.4, 18.0], [64.5, 18.0], [64.6, 18.0], [64.7, 18.0], [64.8, 18.0], [64.9, 18.0], [65.0, 18.0], [65.1, 18.0], [65.2, 18.0], [65.3, 18.0], [65.4, 18.0], [65.5, 18.0], [65.6, 18.0], [65.7, 18.0], [65.8, 18.0], [65.9, 18.0], [66.0, 18.0], [66.1, 18.0], [66.2, 18.0], [66.3, 18.0], [66.4, 18.0], [66.5, 18.0], [66.6, 18.0], [66.7, 18.0], [66.8, 18.0], [66.9, 18.0], [67.0, 18.0], [67.1, 18.0], [67.2, 18.0], [67.3, 18.0], [67.4, 18.0], [67.5, 18.0], [67.6, 18.0], [67.7, 18.0], [67.8, 18.0], [67.9, 18.0], [68.0, 18.0], [68.1, 18.0], [68.2, 18.0], [68.3, 18.0], [68.4, 18.0], [68.5, 18.0], [68.6, 18.0], [68.7, 18.0], [68.8, 18.0], [68.9, 18.0], [69.0, 18.0], [69.1, 18.0], [69.2, 18.0], [69.3, 18.0], [69.4, 18.0], [69.5, 18.0], [69.6, 18.0], [69.7, 18.0], [69.8, 18.0], [69.9, 18.0], [70.0, 18.0], [70.1, 18.0], [70.2, 18.0], [70.3, 18.0], [70.4, 18.0], [70.5, 18.0], [70.6, 18.0], [70.7, 18.0], [70.8, 18.0], [70.9, 18.0], [71.0, 18.0], [71.1, 18.0], [71.2, 18.0], [71.3, 18.0], [71.4, 18.0], [71.5, 18.0], [71.6, 18.0], [71.7, 18.0], [71.8, 18.0], [71.9, 18.0], [72.0, 18.0], [72.1, 18.0], [72.2, 18.0], [72.3, 18.0], [72.4, 18.0], [72.5, 18.0], [72.6, 18.0], [72.7, 18.0], [72.8, 18.0], [72.9, 18.0], [73.0, 18.0], [73.1, 18.0], [73.2, 18.0], [73.3, 18.0], [73.4, 18.0], [73.5, 18.0], [73.6, 18.0], [73.7, 18.0], [73.8, 18.0], [73.9, 18.0], [74.0, 18.0], [74.1, 18.0], [74.2, 18.0], [74.3, 18.0], [74.4, 18.0], [74.5, 18.0], [74.6, 18.0], [74.7, 18.0], [74.8, 18.0], [74.9, 18.0], [75.0, 18.0], [75.1, 18.0], [75.2, 18.0], [75.3, 18.0], [75.4, 18.0], [75.5, 18.0], [75.6, 18.0], [75.7, 18.0], [75.8, 18.0], [75.9, 18.0], [76.0, 18.0], [76.1, 18.0], [76.2, 18.0], [76.3, 18.0], [76.4, 18.0], [76.5, 18.0], [76.6, 18.0], [76.7, 18.0], [76.8, 18.0], [76.9, 18.0], [77.0, 18.0], [77.1, 18.0], [77.2, 18.0], [77.3, 18.0], [77.4, 18.0], [77.5, 18.0], [77.6, 18.0], [77.7, 18.0], [77.8, 18.0], [77.9, 18.0], [78.0, 18.0], [78.1, 18.0], [78.2, 18.0], [78.3, 18.0], [78.4, 18.0], [78.5, 18.0], [78.6, 18.0], [78.7, 18.0], [78.8, 18.0], [78.9, 18.0], [79.0, 18.0], [79.1, 18.0], [79.2, 18.0], [79.3, 18.0], [79.4, 18.0], [79.5, 18.0], [79.6, 18.0], [79.7, 18.0], [79.8, 18.0], [79.9, 18.0], [80.0, 18.0], [80.1, 18.0], [80.2, 18.0], [80.3, 18.0], [80.4, 18.0], [80.5, 18.0], [80.6, 18.0], [80.7, 18.0], [80.8, 18.0], [80.9, 18.0], [81.0, 18.0], [81.1, 18.0], [81.2, 18.0], [81.3, 18.0], [81.4, 18.0], [81.5, 18.0], [81.6, 18.0], [81.7, 18.0], [81.8, 18.0], [81.9, 18.0], [82.0, 18.0], [82.1, 18.0], [82.2, 18.0], [82.3, 18.0], [82.4, 18.0], [82.5, 18.0], [82.6, 18.0], [82.7, 18.0], [82.8, 18.0], [82.9, 18.0], [83.0, 18.0], [83.1, 18.0], [83.2, 18.0], [83.3, 18.0], [83.4, 18.0], [83.5, 18.0], [83.6, 18.0], [83.7, 18.0], [83.8, 18.0], [83.9, 18.0], [84.0, 18.0], [84.1, 18.0], [84.2, 18.0], [84.3, 18.0], [84.4, 18.0], [84.5, 18.0], [84.6, 18.0], [84.7, 18.0], [84.8, 18.0], [84.9, 18.0], [85.0, 18.0], [85.1, 18.0], [85.2, 18.0], [85.3, 18.0], [85.4, 18.0], [85.5, 18.0], [85.6, 18.0], [85.7, 18.0], [85.8, 18.0], [85.9, 18.0], [86.0, 18.0], [86.1, 18.0], [86.2, 18.0], [86.3, 18.0], [86.4, 18.0], [86.5, 18.0], [86.6, 18.0], [86.7, 18.0], [86.8, 18.0], [86.9, 18.0], [87.0, 18.0], [87.1, 18.0], [87.2, 18.0], [87.3, 18.0], [87.4, 18.0], [87.5, 18.0], [87.6, 18.0], [87.7, 18.0], [87.8, 18.0], [87.9, 18.0], [88.0, 18.0], [88.1, 18.0], [88.2, 18.0], [88.3, 18.0], [88.4, 18.0], [88.5, 18.0], [88.6, 18.0], [88.7, 18.0], [88.8, 18.0], [88.9, 18.0], [89.0, 18.0], [89.1, 18.0], [89.2, 18.0], [89.3, 18.0], [89.4, 18.0], [89.5, 18.0], [89.6, 18.0], [89.7, 18.0], [89.8, 18.0], [89.9, 18.0], [90.0, 18.0], [90.1, 18.0], [90.2, 18.0], [90.3, 18.0], [90.4, 18.0], [90.5, 18.0], [90.6, 18.0], [90.7, 18.0], [90.8, 18.0], [90.9, 18.0], [91.0, 18.0], [91.1, 18.0], [91.2, 18.0], [91.3, 18.0], [91.4, 18.0], [91.5, 18.0], [91.6, 18.0], [91.7, 18.0], [91.8, 18.0], [91.9, 18.0], [92.0, 18.0], [92.1, 18.0], [92.2, 18.0], [92.3, 18.0], [92.4, 18.0], [92.5, 18.0], [92.6, 18.0], [92.7, 18.0], [92.8, 18.0], [92.9, 18.0], [93.0, 18.0], [93.1, 18.0], [93.2, 18.0], [93.3, 18.0], [93.4, 18.0], [93.5, 18.0], [93.6, 18.0], [93.7, 18.0], [93.8, 18.0], [93.9, 18.0], [94.0, 18.0], [94.1, 18.0], [94.2, 18.0], [94.3, 18.0], [94.4, 18.0], [94.5, 18.0], [94.6, 18.0], [94.7, 18.0], [94.8, 18.0], [94.9, 18.0], [95.0, 18.0], [95.1, 18.0], [95.2, 18.0], [95.3, 18.0], [95.4, 18.0], [95.5, 18.0], [95.6, 18.0], [95.7, 18.0], [95.8, 18.0], [95.9, 18.0], [96.0, 18.0], [96.1, 18.0], [96.2, 18.0], [96.3, 18.0], [96.4, 18.0], [96.5, 18.0], [96.6, 18.0], [96.7, 18.0], [96.8, 18.0], [96.9, 18.0], [97.0, 18.0], [97.1, 18.0], [97.2, 18.0], [97.3, 18.0], [97.4, 18.0], [97.5, 18.0], [97.6, 18.0], [97.7, 18.0], [97.8, 18.0], [97.9, 18.0], [98.0, 18.0], [98.1, 18.0], [98.2, 18.0], [98.3, 18.0], [98.4, 18.0], [98.5, 18.0], [98.6, 18.0], [98.7, 18.0], [98.8, 18.0], [98.9, 18.0], [99.0, 18.0], [99.1, 18.0], [99.2, 18.0], [99.3, 18.0], [99.4, 18.0], [99.5, 18.0], [99.6, 18.0], [99.7, 18.0], [99.8, 18.0], [99.9, 18.0]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[0.0, 197.0], [0.1, 197.0], [0.2, 197.0], [0.3, 197.0], [0.4, 197.0], [0.5, 197.0], [0.6, 197.0], [0.7, 197.0], [0.8, 197.0], [0.9, 197.0], [1.0, 197.0], [1.1, 197.0], [1.2, 197.0], [1.3, 197.0], [1.4, 197.0], [1.5, 197.0], [1.6, 197.0], [1.7, 197.0], [1.8, 197.0], [1.9, 197.0], [2.0, 197.0], [2.1, 197.0], [2.2, 197.0], [2.3, 197.0], [2.4, 197.0], [2.5, 197.0], [2.6, 197.0], [2.7, 197.0], [2.8, 197.0], [2.9, 197.0], [3.0, 197.0], [3.1, 197.0], [3.2, 197.0], [3.3, 197.0], [3.4, 197.0], [3.5, 197.0], [3.6, 197.0], [3.7, 197.0], [3.8, 197.0], [3.9, 197.0], [4.0, 197.0], [4.1, 197.0], [4.2, 197.0], [4.3, 197.0], [4.4, 197.0], [4.5, 197.0], [4.6, 197.0], [4.7, 197.0], [4.8, 197.0], [4.9, 197.0], [5.0, 197.0], [5.1, 197.0], [5.2, 197.0], [5.3, 197.0], [5.4, 197.0], [5.5, 197.0], [5.6, 197.0], [5.7, 197.0], [5.8, 197.0], [5.9, 197.0], [6.0, 197.0], [6.1, 197.0], [6.2, 197.0], [6.3, 197.0], [6.4, 197.0], [6.5, 197.0], [6.6, 197.0], [6.7, 197.0], [6.8, 197.0], [6.9, 197.0], [7.0, 197.0], [7.1, 197.0], [7.2, 197.0], [7.3, 197.0], [7.4, 197.0], [7.5, 197.0], [7.6, 197.0], [7.7, 197.0], [7.8, 197.0], [7.9, 197.0], [8.0, 197.0], [8.1, 197.0], [8.2, 197.0], [8.3, 197.0], [8.4, 197.0], [8.5, 197.0], [8.6, 197.0], [8.7, 197.0], [8.8, 197.0], [8.9, 197.0], [9.0, 197.0], [9.1, 197.0], [9.2, 197.0], [9.3, 197.0], [9.4, 197.0], [9.5, 197.0], [9.6, 197.0], [9.7, 197.0], [9.8, 197.0], [9.9, 197.0], [10.0, 197.0], [10.1, 197.0], [10.2, 197.0], [10.3, 197.0], [10.4, 197.0], [10.5, 197.0], [10.6, 197.0], [10.7, 197.0], [10.8, 197.0], [10.9, 197.0], [11.0, 197.0], [11.1, 197.0], [11.2, 197.0], [11.3, 197.0], [11.4, 197.0], [11.5, 197.0], [11.6, 197.0], [11.7, 197.0], [11.8, 197.0], [11.9, 197.0], [12.0, 197.0], [12.1, 197.0], [12.2, 197.0], [12.3, 197.0], [12.4, 197.0], [12.5, 197.0], [12.6, 197.0], [12.7, 197.0], [12.8, 197.0], [12.9, 197.0], [13.0, 197.0], [13.1, 197.0], [13.2, 197.0], [13.3, 197.0], [13.4, 197.0], [13.5, 197.0], [13.6, 197.0], [13.7, 197.0], [13.8, 197.0], [13.9, 197.0], [14.0, 197.0], [14.1, 197.0], [14.2, 197.0], [14.3, 197.0], [14.4, 197.0], [14.5, 197.0], [14.6, 197.0], [14.7, 197.0], [14.8, 197.0], [14.9, 197.0], [15.0, 197.0], [15.1, 197.0], [15.2, 197.0], [15.3, 197.0], [15.4, 197.0], [15.5, 197.0], [15.6, 197.0], [15.7, 197.0], [15.8, 197.0], [15.9, 197.0], [16.0, 197.0], [16.1, 197.0], [16.2, 197.0], [16.3, 197.0], [16.4, 197.0], [16.5, 197.0], [16.6, 197.0], [16.7, 197.0], [16.8, 197.0], [16.9, 197.0], [17.0, 197.0], [17.1, 197.0], [17.2, 197.0], [17.3, 197.0], [17.4, 197.0], [17.5, 197.0], [17.6, 197.0], [17.7, 197.0], [17.8, 197.0], [17.9, 197.0], [18.0, 197.0], [18.1, 197.0], [18.2, 197.0], [18.3, 197.0], [18.4, 197.0], [18.5, 197.0], [18.6, 197.0], [18.7, 197.0], [18.8, 197.0], [18.9, 197.0], [19.0, 197.0], [19.1, 197.0], [19.2, 197.0], [19.3, 197.0], [19.4, 197.0], [19.5, 197.0], [19.6, 197.0], [19.7, 197.0], [19.8, 197.0], [19.9, 197.0], [20.0, 197.0], [20.1, 197.0], [20.2, 197.0], [20.3, 197.0], [20.4, 197.0], [20.5, 197.0], [20.6, 197.0], [20.7, 197.0], [20.8, 197.0], [20.9, 197.0], [21.0, 197.0], [21.1, 197.0], [21.2, 197.0], [21.3, 197.0], [21.4, 197.0], [21.5, 197.0], [21.6, 197.0], [21.7, 197.0], [21.8, 197.0], [21.9, 197.0], [22.0, 197.0], [22.1, 197.0], [22.2, 197.0], [22.3, 197.0], [22.4, 197.0], [22.5, 197.0], [22.6, 197.0], [22.7, 197.0], [22.8, 197.0], [22.9, 197.0], [23.0, 197.0], [23.1, 197.0], [23.2, 197.0], [23.3, 197.0], [23.4, 197.0], [23.5, 197.0], [23.6, 197.0], [23.7, 197.0], [23.8, 197.0], [23.9, 197.0], [24.0, 197.0], [24.1, 197.0], [24.2, 197.0], [24.3, 197.0], [24.4, 197.0], [24.5, 197.0], [24.6, 197.0], [24.7, 197.0], [24.8, 197.0], [24.9, 197.0], [25.0, 197.0], [25.1, 197.0], [25.2, 197.0], [25.3, 197.0], [25.4, 197.0], [25.5, 197.0], [25.6, 197.0], [25.7, 197.0], [25.8, 197.0], [25.9, 197.0], [26.0, 197.0], [26.1, 197.0], [26.2, 197.0], [26.3, 197.0], [26.4, 197.0], [26.5, 197.0], [26.6, 197.0], [26.7, 197.0], [26.8, 197.0], [26.9, 197.0], [27.0, 197.0], [27.1, 197.0], [27.2, 197.0], [27.3, 197.0], [27.4, 197.0], [27.5, 197.0], [27.6, 197.0], [27.7, 197.0], [27.8, 197.0], [27.9, 197.0], [28.0, 197.0], [28.1, 197.0], [28.2, 197.0], [28.3, 197.0], [28.4, 197.0], [28.5, 197.0], [28.6, 197.0], [28.7, 197.0], [28.8, 197.0], [28.9, 197.0], [29.0, 197.0], [29.1, 197.0], [29.2, 197.0], [29.3, 197.0], [29.4, 197.0], [29.5, 197.0], [29.6, 197.0], [29.7, 197.0], [29.8, 197.0], [29.9, 197.0], [30.0, 197.0], [30.1, 197.0], [30.2, 197.0], [30.3, 197.0], [30.4, 197.0], [30.5, 197.0], [30.6, 197.0], [30.7, 197.0], [30.8, 197.0], [30.9, 197.0], [31.0, 197.0], [31.1, 197.0], [31.2, 197.0], [31.3, 197.0], [31.4, 197.0], [31.5, 197.0], [31.6, 197.0], [31.7, 197.0], [31.8, 197.0], [31.9, 197.0], [32.0, 197.0], [32.1, 197.0], [32.2, 197.0], [32.3, 197.0], [32.4, 197.0], [32.5, 197.0], [32.6, 197.0], [32.7, 197.0], [32.8, 197.0], [32.9, 197.0], [33.0, 197.0], [33.1, 197.0], [33.2, 197.0], [33.3, 197.0], [33.4, 197.0], [33.5, 197.0], [33.6, 197.0], [33.7, 197.0], [33.8, 197.0], [33.9, 197.0], [34.0, 197.0], [34.1, 197.0], [34.2, 197.0], [34.3, 197.0], [34.4, 197.0], [34.5, 197.0], [34.6, 197.0], [34.7, 197.0], [34.8, 197.0], [34.9, 197.0], [35.0, 197.0], [35.1, 197.0], [35.2, 197.0], [35.3, 197.0], [35.4, 197.0], [35.5, 197.0], [35.6, 197.0], [35.7, 197.0], [35.8, 197.0], [35.9, 197.0], [36.0, 197.0], [36.1, 197.0], [36.2, 197.0], [36.3, 197.0], [36.4, 197.0], [36.5, 197.0], [36.6, 197.0], [36.7, 197.0], [36.8, 197.0], [36.9, 197.0], [37.0, 197.0], [37.1, 197.0], [37.2, 197.0], [37.3, 197.0], [37.4, 197.0], [37.5, 197.0], [37.6, 197.0], [37.7, 197.0], [37.8, 197.0], [37.9, 197.0], [38.0, 197.0], [38.1, 197.0], [38.2, 197.0], [38.3, 197.0], [38.4, 197.0], [38.5, 197.0], [38.6, 197.0], [38.7, 197.0], [38.8, 197.0], [38.9, 197.0], [39.0, 197.0], [39.1, 197.0], [39.2, 197.0], [39.3, 197.0], [39.4, 197.0], [39.5, 197.0], [39.6, 197.0], [39.7, 197.0], [39.8, 197.0], [39.9, 197.0], [40.0, 197.0], [40.1, 197.0], [40.2, 197.0], [40.3, 197.0], [40.4, 197.0], [40.5, 197.0], [40.6, 197.0], [40.7, 197.0], [40.8, 197.0], [40.9, 197.0], [41.0, 197.0], [41.1, 197.0], [41.2, 197.0], [41.3, 197.0], [41.4, 197.0], [41.5, 197.0], [41.6, 197.0], [41.7, 197.0], [41.8, 197.0], [41.9, 197.0], [42.0, 197.0], [42.1, 197.0], [42.2, 197.0], [42.3, 197.0], [42.4, 197.0], [42.5, 197.0], [42.6, 197.0], [42.7, 197.0], [42.8, 197.0], [42.9, 197.0], [43.0, 197.0], [43.1, 197.0], [43.2, 197.0], [43.3, 197.0], [43.4, 197.0], [43.5, 197.0], [43.6, 197.0], [43.7, 197.0], [43.8, 197.0], [43.9, 197.0], [44.0, 197.0], [44.1, 197.0], [44.2, 197.0], [44.3, 197.0], [44.4, 197.0], [44.5, 197.0], [44.6, 197.0], [44.7, 197.0], [44.8, 197.0], [44.9, 197.0], [45.0, 197.0], [45.1, 197.0], [45.2, 197.0], [45.3, 197.0], [45.4, 197.0], [45.5, 197.0], [45.6, 197.0], [45.7, 197.0], [45.8, 197.0], [45.9, 197.0], [46.0, 197.0], [46.1, 197.0], [46.2, 197.0], [46.3, 197.0], [46.4, 197.0], [46.5, 197.0], [46.6, 197.0], [46.7, 197.0], [46.8, 197.0], [46.9, 197.0], [47.0, 197.0], [47.1, 197.0], [47.2, 197.0], [47.3, 197.0], [47.4, 197.0], [47.5, 197.0], [47.6, 197.0], [47.7, 197.0], [47.8, 197.0], [47.9, 197.0], [48.0, 197.0], [48.1, 197.0], [48.2, 197.0], [48.3, 197.0], [48.4, 197.0], [48.5, 197.0], [48.6, 197.0], [48.7, 197.0], [48.8, 197.0], [48.9, 197.0], [49.0, 197.0], [49.1, 197.0], [49.2, 197.0], [49.3, 197.0], [49.4, 197.0], [49.5, 197.0], [49.6, 197.0], [49.7, 197.0], [49.8, 197.0], [49.9, 197.0], [50.0, 197.0], [50.1, 197.0], [50.2, 197.0], [50.3, 197.0], [50.4, 197.0], [50.5, 197.0], [50.6, 197.0], [50.7, 197.0], [50.8, 197.0], [50.9, 197.0], [51.0, 197.0], [51.1, 197.0], [51.2, 197.0], [51.3, 197.0], [51.4, 197.0], [51.5, 197.0], [51.6, 197.0], [51.7, 197.0], [51.8, 197.0], [51.9, 197.0], [52.0, 197.0], [52.1, 197.0], [52.2, 197.0], [52.3, 197.0], [52.4, 197.0], [52.5, 197.0], [52.6, 197.0], [52.7, 197.0], [52.8, 197.0], [52.9, 197.0], [53.0, 197.0], [53.1, 197.0], [53.2, 197.0], [53.3, 197.0], [53.4, 197.0], [53.5, 197.0], [53.6, 197.0], [53.7, 197.0], [53.8, 197.0], [53.9, 197.0], [54.0, 197.0], [54.1, 197.0], [54.2, 197.0], [54.3, 197.0], [54.4, 197.0], [54.5, 197.0], [54.6, 197.0], [54.7, 197.0], [54.8, 197.0], [54.9, 197.0], [55.0, 197.0], [55.1, 197.0], [55.2, 197.0], [55.3, 197.0], [55.4, 197.0], [55.5, 197.0], [55.6, 197.0], [55.7, 197.0], [55.8, 197.0], [55.9, 197.0], [56.0, 197.0], [56.1, 197.0], [56.2, 197.0], [56.3, 197.0], [56.4, 197.0], [56.5, 197.0], [56.6, 197.0], [56.7, 197.0], [56.8, 197.0], [56.9, 197.0], [57.0, 197.0], [57.1, 197.0], [57.2, 197.0], [57.3, 197.0], [57.4, 197.0], [57.5, 197.0], [57.6, 197.0], [57.7, 197.0], [57.8, 197.0], [57.9, 197.0], [58.0, 197.0], [58.1, 197.0], [58.2, 197.0], [58.3, 197.0], [58.4, 197.0], [58.5, 197.0], [58.6, 197.0], [58.7, 197.0], [58.8, 197.0], [58.9, 197.0], [59.0, 197.0], [59.1, 197.0], [59.2, 197.0], [59.3, 197.0], [59.4, 197.0], [59.5, 197.0], [59.6, 197.0], [59.7, 197.0], [59.8, 197.0], [59.9, 197.0], [60.0, 197.0], [60.1, 197.0], [60.2, 197.0], [60.3, 197.0], [60.4, 197.0], [60.5, 197.0], [60.6, 197.0], [60.7, 197.0], [60.8, 197.0], [60.9, 197.0], [61.0, 197.0], [61.1, 197.0], [61.2, 197.0], [61.3, 197.0], [61.4, 197.0], [61.5, 197.0], [61.6, 197.0], [61.7, 197.0], [61.8, 197.0], [61.9, 197.0], [62.0, 197.0], [62.1, 197.0], [62.2, 197.0], [62.3, 197.0], [62.4, 197.0], [62.5, 197.0], [62.6, 197.0], [62.7, 197.0], [62.8, 197.0], [62.9, 197.0], [63.0, 197.0], [63.1, 197.0], [63.2, 197.0], [63.3, 197.0], [63.4, 197.0], [63.5, 197.0], [63.6, 197.0], [63.7, 197.0], [63.8, 197.0], [63.9, 197.0], [64.0, 197.0], [64.1, 197.0], [64.2, 197.0], [64.3, 197.0], [64.4, 197.0], [64.5, 197.0], [64.6, 197.0], [64.7, 197.0], [64.8, 197.0], [64.9, 197.0], [65.0, 197.0], [65.1, 197.0], [65.2, 197.0], [65.3, 197.0], [65.4, 197.0], [65.5, 197.0], [65.6, 197.0], [65.7, 197.0], [65.8, 197.0], [65.9, 197.0], [66.0, 197.0], [66.1, 197.0], [66.2, 197.0], [66.3, 197.0], [66.4, 197.0], [66.5, 197.0], [66.6, 197.0], [66.7, 197.0], [66.8, 197.0], [66.9, 197.0], [67.0, 197.0], [67.1, 197.0], [67.2, 197.0], [67.3, 197.0], [67.4, 197.0], [67.5, 197.0], [67.6, 197.0], [67.7, 197.0], [67.8, 197.0], [67.9, 197.0], [68.0, 197.0], [68.1, 197.0], [68.2, 197.0], [68.3, 197.0], [68.4, 197.0], [68.5, 197.0], [68.6, 197.0], [68.7, 197.0], [68.8, 197.0], [68.9, 197.0], [69.0, 197.0], [69.1, 197.0], [69.2, 197.0], [69.3, 197.0], [69.4, 197.0], [69.5, 197.0], [69.6, 197.0], [69.7, 197.0], [69.8, 197.0], [69.9, 197.0], [70.0, 197.0], [70.1, 197.0], [70.2, 197.0], [70.3, 197.0], [70.4, 197.0], [70.5, 197.0], [70.6, 197.0], [70.7, 197.0], [70.8, 197.0], [70.9, 197.0], [71.0, 197.0], [71.1, 197.0], [71.2, 197.0], [71.3, 197.0], [71.4, 197.0], [71.5, 197.0], [71.6, 197.0], [71.7, 197.0], [71.8, 197.0], [71.9, 197.0], [72.0, 197.0], [72.1, 197.0], [72.2, 197.0], [72.3, 197.0], [72.4, 197.0], [72.5, 197.0], [72.6, 197.0], [72.7, 197.0], [72.8, 197.0], [72.9, 197.0], [73.0, 197.0], [73.1, 197.0], [73.2, 197.0], [73.3, 197.0], [73.4, 197.0], [73.5, 197.0], [73.6, 197.0], [73.7, 197.0], [73.8, 197.0], [73.9, 197.0], [74.0, 197.0], [74.1, 197.0], [74.2, 197.0], [74.3, 197.0], [74.4, 197.0], [74.5, 197.0], [74.6, 197.0], [74.7, 197.0], [74.8, 197.0], [74.9, 197.0], [75.0, 197.0], [75.1, 197.0], [75.2, 197.0], [75.3, 197.0], [75.4, 197.0], [75.5, 197.0], [75.6, 197.0], [75.7, 197.0], [75.8, 197.0], [75.9, 197.0], [76.0, 197.0], [76.1, 197.0], [76.2, 197.0], [76.3, 197.0], [76.4, 197.0], [76.5, 197.0], [76.6, 197.0], [76.7, 197.0], [76.8, 197.0], [76.9, 197.0], [77.0, 197.0], [77.1, 197.0], [77.2, 197.0], [77.3, 197.0], [77.4, 197.0], [77.5, 197.0], [77.6, 197.0], [77.7, 197.0], [77.8, 197.0], [77.9, 197.0], [78.0, 197.0], [78.1, 197.0], [78.2, 197.0], [78.3, 197.0], [78.4, 197.0], [78.5, 197.0], [78.6, 197.0], [78.7, 197.0], [78.8, 197.0], [78.9, 197.0], [79.0, 197.0], [79.1, 197.0], [79.2, 197.0], [79.3, 197.0], [79.4, 197.0], [79.5, 197.0], [79.6, 197.0], [79.7, 197.0], [79.8, 197.0], [79.9, 197.0], [80.0, 197.0], [80.1, 197.0], [80.2, 197.0], [80.3, 197.0], [80.4, 197.0], [80.5, 197.0], [80.6, 197.0], [80.7, 197.0], [80.8, 197.0], [80.9, 197.0], [81.0, 197.0], [81.1, 197.0], [81.2, 197.0], [81.3, 197.0], [81.4, 197.0], [81.5, 197.0], [81.6, 197.0], [81.7, 197.0], [81.8, 197.0], [81.9, 197.0], [82.0, 197.0], [82.1, 197.0], [82.2, 197.0], [82.3, 197.0], [82.4, 197.0], [82.5, 197.0], [82.6, 197.0], [82.7, 197.0], [82.8, 197.0], [82.9, 197.0], [83.0, 197.0], [83.1, 197.0], [83.2, 197.0], [83.3, 197.0], [83.4, 197.0], [83.5, 197.0], [83.6, 197.0], [83.7, 197.0], [83.8, 197.0], [83.9, 197.0], [84.0, 197.0], [84.1, 197.0], [84.2, 197.0], [84.3, 197.0], [84.4, 197.0], [84.5, 197.0], [84.6, 197.0], [84.7, 197.0], [84.8, 197.0], [84.9, 197.0], [85.0, 197.0], [85.1, 197.0], [85.2, 197.0], [85.3, 197.0], [85.4, 197.0], [85.5, 197.0], [85.6, 197.0], [85.7, 197.0], [85.8, 197.0], [85.9, 197.0], [86.0, 197.0], [86.1, 197.0], [86.2, 197.0], [86.3, 197.0], [86.4, 197.0], [86.5, 197.0], [86.6, 197.0], [86.7, 197.0], [86.8, 197.0], [86.9, 197.0], [87.0, 197.0], [87.1, 197.0], [87.2, 197.0], [87.3, 197.0], [87.4, 197.0], [87.5, 197.0], [87.6, 197.0], [87.7, 197.0], [87.8, 197.0], [87.9, 197.0], [88.0, 197.0], [88.1, 197.0], [88.2, 197.0], [88.3, 197.0], [88.4, 197.0], [88.5, 197.0], [88.6, 197.0], [88.7, 197.0], [88.8, 197.0], [88.9, 197.0], [89.0, 197.0], [89.1, 197.0], [89.2, 197.0], [89.3, 197.0], [89.4, 197.0], [89.5, 197.0], [89.6, 197.0], [89.7, 197.0], [89.8, 197.0], [89.9, 197.0], [90.0, 197.0], [90.1, 197.0], [90.2, 197.0], [90.3, 197.0], [90.4, 197.0], [90.5, 197.0], [90.6, 197.0], [90.7, 197.0], [90.8, 197.0], [90.9, 197.0], [91.0, 197.0], [91.1, 197.0], [91.2, 197.0], [91.3, 197.0], [91.4, 197.0], [91.5, 197.0], [91.6, 197.0], [91.7, 197.0], [91.8, 197.0], [91.9, 197.0], [92.0, 197.0], [92.1, 197.0], [92.2, 197.0], [92.3, 197.0], [92.4, 197.0], [92.5, 197.0], [92.6, 197.0], [92.7, 197.0], [92.8, 197.0], [92.9, 197.0], [93.0, 197.0], [93.1, 197.0], [93.2, 197.0], [93.3, 197.0], [93.4, 197.0], [93.5, 197.0], [93.6, 197.0], [93.7, 197.0], [93.8, 197.0], [93.9, 197.0], [94.0, 197.0], [94.1, 197.0], [94.2, 197.0], [94.3, 197.0], [94.4, 197.0], [94.5, 197.0], [94.6, 197.0], [94.7, 197.0], [94.8, 197.0], [94.9, 197.0], [95.0, 197.0], [95.1, 197.0], [95.2, 197.0], [95.3, 197.0], [95.4, 197.0], [95.5, 197.0], [95.6, 197.0], [95.7, 197.0], [95.8, 197.0], [95.9, 197.0], [96.0, 197.0], [96.1, 197.0], [96.2, 197.0], [96.3, 197.0], [96.4, 197.0], [96.5, 197.0], [96.6, 197.0], [96.7, 197.0], [96.8, 197.0], [96.9, 197.0], [97.0, 197.0], [97.1, 197.0], [97.2, 197.0], [97.3, 197.0], [97.4, 197.0], [97.5, 197.0], [97.6, 197.0], [97.7, 197.0], [97.8, 197.0], [97.9, 197.0], [98.0, 197.0], [98.1, 197.0], [98.2, 197.0], [98.3, 197.0], [98.4, 197.0], [98.5, 197.0], [98.6, 197.0], [98.7, 197.0], [98.8, 197.0], [98.9, 197.0], [99.0, 197.0], [99.1, 197.0], [99.2, 197.0], [99.3, 197.0], [99.4, 197.0], [99.5, 197.0], [99.6, 197.0], [99.7, 197.0], [99.8, 197.0], [99.9, 197.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[0.0, 45.0], [0.1, 45.0], [0.2, 45.0], [0.3, 45.0], [0.4, 45.0], [0.5, 45.0], [0.6, 45.0], [0.7, 45.0], [0.8, 45.0], [0.9, 45.0], [1.0, 45.0], [1.1, 45.0], [1.2, 45.0], [1.3, 45.0], [1.4, 45.0], [1.5, 45.0], [1.6, 45.0], [1.7, 45.0], [1.8, 45.0], [1.9, 45.0], [2.0, 45.0], [2.1, 45.0], [2.2, 45.0], [2.3, 45.0], [2.4, 45.0], [2.5, 45.0], [2.6, 45.0], [2.7, 45.0], [2.8, 45.0], [2.9, 45.0], [3.0, 45.0], [3.1, 45.0], [3.2, 45.0], [3.3, 45.0], [3.4, 45.0], [3.5, 45.0], [3.6, 45.0], [3.7, 45.0], [3.8, 45.0], [3.9, 45.0], [4.0, 45.0], [4.1, 45.0], [4.2, 45.0], [4.3, 45.0], [4.4, 45.0], [4.5, 45.0], [4.6, 45.0], [4.7, 45.0], [4.8, 45.0], [4.9, 45.0], [5.0, 45.0], [5.1, 45.0], [5.2, 45.0], [5.3, 45.0], [5.4, 45.0], [5.5, 45.0], [5.6, 45.0], [5.7, 45.0], [5.8, 45.0], [5.9, 45.0], [6.0, 45.0], [6.1, 45.0], [6.2, 45.0], [6.3, 45.0], [6.4, 45.0], [6.5, 45.0], [6.6, 45.0], [6.7, 45.0], [6.8, 45.0], [6.9, 45.0], [7.0, 45.0], [7.1, 45.0], [7.2, 45.0], [7.3, 45.0], [7.4, 45.0], [7.5, 45.0], [7.6, 45.0], [7.7, 45.0], [7.8, 45.0], [7.9, 45.0], [8.0, 45.0], [8.1, 45.0], [8.2, 45.0], [8.3, 45.0], [8.4, 45.0], [8.5, 45.0], [8.6, 45.0], [8.7, 45.0], [8.8, 45.0], [8.9, 45.0], [9.0, 45.0], [9.1, 45.0], [9.2, 45.0], [9.3, 45.0], [9.4, 45.0], [9.5, 45.0], [9.6, 45.0], [9.7, 45.0], [9.8, 45.0], [9.9, 45.0], [10.0, 45.0], [10.1, 45.0], [10.2, 45.0], [10.3, 45.0], [10.4, 45.0], [10.5, 45.0], [10.6, 45.0], [10.7, 45.0], [10.8, 45.0], [10.9, 45.0], [11.0, 45.0], [11.1, 45.0], [11.2, 45.0], [11.3, 45.0], [11.4, 45.0], [11.5, 45.0], [11.6, 45.0], [11.7, 45.0], [11.8, 45.0], [11.9, 45.0], [12.0, 45.0], [12.1, 45.0], [12.2, 45.0], [12.3, 45.0], [12.4, 45.0], [12.5, 45.0], [12.6, 45.0], [12.7, 45.0], [12.8, 45.0], [12.9, 45.0], [13.0, 45.0], [13.1, 45.0], [13.2, 45.0], [13.3, 45.0], [13.4, 45.0], [13.5, 45.0], [13.6, 45.0], [13.7, 45.0], [13.8, 45.0], [13.9, 45.0], [14.0, 45.0], [14.1, 45.0], [14.2, 45.0], [14.3, 45.0], [14.4, 45.0], [14.5, 45.0], [14.6, 45.0], [14.7, 45.0], [14.8, 45.0], [14.9, 45.0], [15.0, 45.0], [15.1, 45.0], [15.2, 45.0], [15.3, 45.0], [15.4, 45.0], [15.5, 45.0], [15.6, 45.0], [15.7, 45.0], [15.8, 45.0], [15.9, 45.0], [16.0, 45.0], [16.1, 45.0], [16.2, 45.0], [16.3, 45.0], [16.4, 45.0], [16.5, 45.0], [16.6, 45.0], [16.7, 45.0], [16.8, 45.0], [16.9, 45.0], [17.0, 45.0], [17.1, 45.0], [17.2, 45.0], [17.3, 45.0], [17.4, 45.0], [17.5, 45.0], [17.6, 45.0], [17.7, 45.0], [17.8, 45.0], [17.9, 45.0], [18.0, 45.0], [18.1, 45.0], [18.2, 45.0], [18.3, 45.0], [18.4, 45.0], [18.5, 45.0], [18.6, 45.0], [18.7, 45.0], [18.8, 45.0], [18.9, 45.0], [19.0, 45.0], [19.1, 45.0], [19.2, 45.0], [19.3, 45.0], [19.4, 45.0], [19.5, 45.0], [19.6, 45.0], [19.7, 45.0], [19.8, 45.0], [19.9, 45.0], [20.0, 45.0], [20.1, 45.0], [20.2, 45.0], [20.3, 45.0], [20.4, 45.0], [20.5, 45.0], [20.6, 45.0], [20.7, 45.0], [20.8, 45.0], [20.9, 45.0], [21.0, 45.0], [21.1, 45.0], [21.2, 45.0], [21.3, 45.0], [21.4, 45.0], [21.5, 45.0], [21.6, 45.0], [21.7, 45.0], [21.8, 45.0], [21.9, 45.0], [22.0, 45.0], [22.1, 45.0], [22.2, 45.0], [22.3, 45.0], [22.4, 45.0], [22.5, 45.0], [22.6, 45.0], [22.7, 45.0], [22.8, 45.0], [22.9, 45.0], [23.0, 45.0], [23.1, 45.0], [23.2, 45.0], [23.3, 45.0], [23.4, 45.0], [23.5, 45.0], [23.6, 45.0], [23.7, 45.0], [23.8, 45.0], [23.9, 45.0], [24.0, 45.0], [24.1, 45.0], [24.2, 45.0], [24.3, 45.0], [24.4, 45.0], [24.5, 45.0], [24.6, 45.0], [24.7, 45.0], [24.8, 45.0], [24.9, 45.0], [25.0, 45.0], [25.1, 45.0], [25.2, 45.0], [25.3, 45.0], [25.4, 45.0], [25.5, 45.0], [25.6, 45.0], [25.7, 45.0], [25.8, 45.0], [25.9, 45.0], [26.0, 45.0], [26.1, 45.0], [26.2, 45.0], [26.3, 45.0], [26.4, 45.0], [26.5, 45.0], [26.6, 45.0], [26.7, 45.0], [26.8, 45.0], [26.9, 45.0], [27.0, 45.0], [27.1, 45.0], [27.2, 45.0], [27.3, 45.0], [27.4, 45.0], [27.5, 45.0], [27.6, 45.0], [27.7, 45.0], [27.8, 45.0], [27.9, 45.0], [28.0, 45.0], [28.1, 45.0], [28.2, 45.0], [28.3, 45.0], [28.4, 45.0], [28.5, 45.0], [28.6, 45.0], [28.7, 45.0], [28.8, 45.0], [28.9, 45.0], [29.0, 45.0], [29.1, 45.0], [29.2, 45.0], [29.3, 45.0], [29.4, 45.0], [29.5, 45.0], [29.6, 45.0], [29.7, 45.0], [29.8, 45.0], [29.9, 45.0], [30.0, 45.0], [30.1, 45.0], [30.2, 45.0], [30.3, 45.0], [30.4, 45.0], [30.5, 45.0], [30.6, 45.0], [30.7, 45.0], [30.8, 45.0], [30.9, 45.0], [31.0, 45.0], [31.1, 45.0], [31.2, 45.0], [31.3, 45.0], [31.4, 45.0], [31.5, 45.0], [31.6, 45.0], [31.7, 45.0], [31.8, 45.0], [31.9, 45.0], [32.0, 45.0], [32.1, 45.0], [32.2, 45.0], [32.3, 45.0], [32.4, 45.0], [32.5, 45.0], [32.6, 45.0], [32.7, 45.0], [32.8, 45.0], [32.9, 45.0], [33.0, 45.0], [33.1, 45.0], [33.2, 45.0], [33.3, 45.0], [33.4, 73.0], [33.5, 73.0], [33.6, 73.0], [33.7, 73.0], [33.8, 73.0], [33.9, 73.0], [34.0, 73.0], [34.1, 73.0], [34.2, 73.0], [34.3, 73.0], [34.4, 73.0], [34.5, 73.0], [34.6, 73.0], [34.7, 73.0], [34.8, 73.0], [34.9, 73.0], [35.0, 73.0], [35.1, 73.0], [35.2, 73.0], [35.3, 73.0], [35.4, 73.0], [35.5, 73.0], [35.6, 73.0], [35.7, 73.0], [35.8, 73.0], [35.9, 73.0], [36.0, 73.0], [36.1, 73.0], [36.2, 73.0], [36.3, 73.0], [36.4, 73.0], [36.5, 73.0], [36.6, 73.0], [36.7, 73.0], [36.8, 73.0], [36.9, 73.0], [37.0, 73.0], [37.1, 73.0], [37.2, 73.0], [37.3, 73.0], [37.4, 73.0], [37.5, 73.0], [37.6, 73.0], [37.7, 73.0], [37.8, 73.0], [37.9, 73.0], [38.0, 73.0], [38.1, 73.0], [38.2, 73.0], [38.3, 73.0], [38.4, 73.0], [38.5, 73.0], [38.6, 73.0], [38.7, 73.0], [38.8, 73.0], [38.9, 73.0], [39.0, 73.0], [39.1, 73.0], [39.2, 73.0], [39.3, 73.0], [39.4, 73.0], [39.5, 73.0], [39.6, 73.0], [39.7, 73.0], [39.8, 73.0], [39.9, 73.0], [40.0, 73.0], [40.1, 73.0], [40.2, 73.0], [40.3, 73.0], [40.4, 73.0], [40.5, 73.0], [40.6, 73.0], [40.7, 73.0], [40.8, 73.0], [40.9, 73.0], [41.0, 73.0], [41.1, 73.0], [41.2, 73.0], [41.3, 73.0], [41.4, 73.0], [41.5, 73.0], [41.6, 73.0], [41.7, 73.0], [41.8, 73.0], [41.9, 73.0], [42.0, 73.0], [42.1, 73.0], [42.2, 73.0], [42.3, 73.0], [42.4, 73.0], [42.5, 73.0], [42.6, 73.0], [42.7, 73.0], [42.8, 73.0], [42.9, 73.0], [43.0, 73.0], [43.1, 73.0], [43.2, 73.0], [43.3, 73.0], [43.4, 73.0], [43.5, 73.0], [43.6, 73.0], [43.7, 73.0], [43.8, 73.0], [43.9, 73.0], [44.0, 73.0], [44.1, 73.0], [44.2, 73.0], [44.3, 73.0], [44.4, 73.0], [44.5, 73.0], [44.6, 73.0], [44.7, 73.0], [44.8, 73.0], [44.9, 73.0], [45.0, 73.0], [45.1, 73.0], [45.2, 73.0], [45.3, 73.0], [45.4, 73.0], [45.5, 73.0], [45.6, 73.0], [45.7, 73.0], [45.8, 73.0], [45.9, 73.0], [46.0, 73.0], [46.1, 73.0], [46.2, 73.0], [46.3, 73.0], [46.4, 73.0], [46.5, 73.0], [46.6, 73.0], [46.7, 73.0], [46.8, 73.0], [46.9, 73.0], [47.0, 73.0], [47.1, 73.0], [47.2, 73.0], [47.3, 73.0], [47.4, 73.0], [47.5, 73.0], [47.6, 73.0], [47.7, 73.0], [47.8, 73.0], [47.9, 73.0], [48.0, 73.0], [48.1, 73.0], [48.2, 73.0], [48.3, 73.0], [48.4, 73.0], [48.5, 73.0], [48.6, 73.0], [48.7, 73.0], [48.8, 73.0], [48.9, 73.0], [49.0, 73.0], [49.1, 73.0], [49.2, 73.0], [49.3, 73.0], [49.4, 73.0], [49.5, 73.0], [49.6, 73.0], [49.7, 73.0], [49.8, 73.0], [49.9, 73.0], [50.0, 73.0], [50.1, 73.0], [50.2, 73.0], [50.3, 73.0], [50.4, 73.0], [50.5, 73.0], [50.6, 73.0], [50.7, 73.0], [50.8, 73.0], [50.9, 73.0], [51.0, 73.0], [51.1, 73.0], [51.2, 73.0], [51.3, 73.0], [51.4, 73.0], [51.5, 73.0], [51.6, 73.0], [51.7, 73.0], [51.8, 73.0], [51.9, 73.0], [52.0, 73.0], [52.1, 73.0], [52.2, 73.0], [52.3, 73.0], [52.4, 73.0], [52.5, 73.0], [52.6, 73.0], [52.7, 73.0], [52.8, 73.0], [52.9, 73.0], [53.0, 73.0], [53.1, 73.0], [53.2, 73.0], [53.3, 73.0], [53.4, 73.0], [53.5, 73.0], [53.6, 73.0], [53.7, 73.0], [53.8, 73.0], [53.9, 73.0], [54.0, 73.0], [54.1, 73.0], [54.2, 73.0], [54.3, 73.0], [54.4, 73.0], [54.5, 73.0], [54.6, 73.0], [54.7, 73.0], [54.8, 73.0], [54.9, 73.0], [55.0, 73.0], [55.1, 73.0], [55.2, 73.0], [55.3, 73.0], [55.4, 73.0], [55.5, 73.0], [55.6, 73.0], [55.7, 73.0], [55.8, 73.0], [55.9, 73.0], [56.0, 73.0], [56.1, 73.0], [56.2, 73.0], [56.3, 73.0], [56.4, 73.0], [56.5, 73.0], [56.6, 73.0], [56.7, 73.0], [56.8, 73.0], [56.9, 73.0], [57.0, 73.0], [57.1, 73.0], [57.2, 73.0], [57.3, 73.0], [57.4, 73.0], [57.5, 73.0], [57.6, 73.0], [57.7, 73.0], [57.8, 73.0], [57.9, 73.0], [58.0, 73.0], [58.1, 73.0], [58.2, 73.0], [58.3, 73.0], [58.4, 73.0], [58.5, 73.0], [58.6, 73.0], [58.7, 73.0], [58.8, 73.0], [58.9, 73.0], [59.0, 73.0], [59.1, 73.0], [59.2, 73.0], [59.3, 73.0], [59.4, 73.0], [59.5, 73.0], [59.6, 73.0], [59.7, 73.0], [59.8, 73.0], [59.9, 73.0], [60.0, 73.0], [60.1, 73.0], [60.2, 73.0], [60.3, 73.0], [60.4, 73.0], [60.5, 73.0], [60.6, 73.0], [60.7, 73.0], [60.8, 73.0], [60.9, 73.0], [61.0, 73.0], [61.1, 73.0], [61.2, 73.0], [61.3, 73.0], [61.4, 73.0], [61.5, 73.0], [61.6, 73.0], [61.7, 73.0], [61.8, 73.0], [61.9, 73.0], [62.0, 73.0], [62.1, 73.0], [62.2, 73.0], [62.3, 73.0], [62.4, 73.0], [62.5, 73.0], [62.6, 73.0], [62.7, 73.0], [62.8, 73.0], [62.9, 73.0], [63.0, 73.0], [63.1, 73.0], [63.2, 73.0], [63.3, 73.0], [63.4, 73.0], [63.5, 73.0], [63.6, 73.0], [63.7, 73.0], [63.8, 73.0], [63.9, 73.0], [64.0, 73.0], [64.1, 73.0], [64.2, 73.0], [64.3, 73.0], [64.4, 73.0], [64.5, 73.0], [64.6, 73.0], [64.7, 73.0], [64.8, 73.0], [64.9, 73.0], [65.0, 73.0], [65.1, 73.0], [65.2, 73.0], [65.3, 73.0], [65.4, 73.0], [65.5, 73.0], [65.6, 73.0], [65.7, 73.0], [65.8, 73.0], [65.9, 73.0], [66.0, 73.0], [66.1, 73.0], [66.2, 73.0], [66.3, 73.0], [66.4, 73.0], [66.5, 73.0], [66.6, 73.0], [66.7, 74.0], [66.8, 74.0], [66.9, 74.0], [67.0, 74.0], [67.1, 74.0], [67.2, 74.0], [67.3, 74.0], [67.4, 74.0], [67.5, 74.0], [67.6, 74.0], [67.7, 74.0], [67.8, 74.0], [67.9, 74.0], [68.0, 74.0], [68.1, 74.0], [68.2, 74.0], [68.3, 74.0], [68.4, 74.0], [68.5, 74.0], [68.6, 74.0], [68.7, 74.0], [68.8, 74.0], [68.9, 74.0], [69.0, 74.0], [69.1, 74.0], [69.2, 74.0], [69.3, 74.0], [69.4, 74.0], [69.5, 74.0], [69.6, 74.0], [69.7, 74.0], [69.8, 74.0], [69.9, 74.0], [70.0, 74.0], [70.1, 74.0], [70.2, 74.0], [70.3, 74.0], [70.4, 74.0], [70.5, 74.0], [70.6, 74.0], [70.7, 74.0], [70.8, 74.0], [70.9, 74.0], [71.0, 74.0], [71.1, 74.0], [71.2, 74.0], [71.3, 74.0], [71.4, 74.0], [71.5, 74.0], [71.6, 74.0], [71.7, 74.0], [71.8, 74.0], [71.9, 74.0], [72.0, 74.0], [72.1, 74.0], [72.2, 74.0], [72.3, 74.0], [72.4, 74.0], [72.5, 74.0], [72.6, 74.0], [72.7, 74.0], [72.8, 74.0], [72.9, 74.0], [73.0, 74.0], [73.1, 74.0], [73.2, 74.0], [73.3, 74.0], [73.4, 74.0], [73.5, 74.0], [73.6, 74.0], [73.7, 74.0], [73.8, 74.0], [73.9, 74.0], [74.0, 74.0], [74.1, 74.0], [74.2, 74.0], [74.3, 74.0], [74.4, 74.0], [74.5, 74.0], [74.6, 74.0], [74.7, 74.0], [74.8, 74.0], [74.9, 74.0], [75.0, 74.0], [75.1, 74.0], [75.2, 74.0], [75.3, 74.0], [75.4, 74.0], [75.5, 74.0], [75.6, 74.0], [75.7, 74.0], [75.8, 74.0], [75.9, 74.0], [76.0, 74.0], [76.1, 74.0], [76.2, 74.0], [76.3, 74.0], [76.4, 74.0], [76.5, 74.0], [76.6, 74.0], [76.7, 74.0], [76.8, 74.0], [76.9, 74.0], [77.0, 74.0], [77.1, 74.0], [77.2, 74.0], [77.3, 74.0], [77.4, 74.0], [77.5, 74.0], [77.6, 74.0], [77.7, 74.0], [77.8, 74.0], [77.9, 74.0], [78.0, 74.0], [78.1, 74.0], [78.2, 74.0], [78.3, 74.0], [78.4, 74.0], [78.5, 74.0], [78.6, 74.0], [78.7, 74.0], [78.8, 74.0], [78.9, 74.0], [79.0, 74.0], [79.1, 74.0], [79.2, 74.0], [79.3, 74.0], [79.4, 74.0], [79.5, 74.0], [79.6, 74.0], [79.7, 74.0], [79.8, 74.0], [79.9, 74.0], [80.0, 74.0], [80.1, 74.0], [80.2, 74.0], [80.3, 74.0], [80.4, 74.0], [80.5, 74.0], [80.6, 74.0], [80.7, 74.0], [80.8, 74.0], [80.9, 74.0], [81.0, 74.0], [81.1, 74.0], [81.2, 74.0], [81.3, 74.0], [81.4, 74.0], [81.5, 74.0], [81.6, 74.0], [81.7, 74.0], [81.8, 74.0], [81.9, 74.0], [82.0, 74.0], [82.1, 74.0], [82.2, 74.0], [82.3, 74.0], [82.4, 74.0], [82.5, 74.0], [82.6, 74.0], [82.7, 74.0], [82.8, 74.0], [82.9, 74.0], [83.0, 74.0], [83.1, 74.0], [83.2, 74.0], [83.3, 74.0], [83.4, 74.0], [83.5, 74.0], [83.6, 74.0], [83.7, 74.0], [83.8, 74.0], [83.9, 74.0], [84.0, 74.0], [84.1, 74.0], [84.2, 74.0], [84.3, 74.0], [84.4, 74.0], [84.5, 74.0], [84.6, 74.0], [84.7, 74.0], [84.8, 74.0], [84.9, 74.0], [85.0, 74.0], [85.1, 74.0], [85.2, 74.0], [85.3, 74.0], [85.4, 74.0], [85.5, 74.0], [85.6, 74.0], [85.7, 74.0], [85.8, 74.0], [85.9, 74.0], [86.0, 74.0], [86.1, 74.0], [86.2, 74.0], [86.3, 74.0], [86.4, 74.0], [86.5, 74.0], [86.6, 74.0], [86.7, 74.0], [86.8, 74.0], [86.9, 74.0], [87.0, 74.0], [87.1, 74.0], [87.2, 74.0], [87.3, 74.0], [87.4, 74.0], [87.5, 74.0], [87.6, 74.0], [87.7, 74.0], [87.8, 74.0], [87.9, 74.0], [88.0, 74.0], [88.1, 74.0], [88.2, 74.0], [88.3, 74.0], [88.4, 74.0], [88.5, 74.0], [88.6, 74.0], [88.7, 74.0], [88.8, 74.0], [88.9, 74.0], [89.0, 74.0], [89.1, 74.0], [89.2, 74.0], [89.3, 74.0], [89.4, 74.0], [89.5, 74.0], [89.6, 74.0], [89.7, 74.0], [89.8, 74.0], [89.9, 74.0], [90.0, 74.0], [90.1, 74.0], [90.2, 74.0], [90.3, 74.0], [90.4, 74.0], [90.5, 74.0], [90.6, 74.0], [90.7, 74.0], [90.8, 74.0], [90.9, 74.0], [91.0, 74.0], [91.1, 74.0], [91.2, 74.0], [91.3, 74.0], [91.4, 74.0], [91.5, 74.0], [91.6, 74.0], [91.7, 74.0], [91.8, 74.0], [91.9, 74.0], [92.0, 74.0], [92.1, 74.0], [92.2, 74.0], [92.3, 74.0], [92.4, 74.0], [92.5, 74.0], [92.6, 74.0], [92.7, 74.0], [92.8, 74.0], [92.9, 74.0], [93.0, 74.0], [93.1, 74.0], [93.2, 74.0], [93.3, 74.0], [93.4, 74.0], [93.5, 74.0], [93.6, 74.0], [93.7, 74.0], [93.8, 74.0], [93.9, 74.0], [94.0, 74.0], [94.1, 74.0], [94.2, 74.0], [94.3, 74.0], [94.4, 74.0], [94.5, 74.0], [94.6, 74.0], [94.7, 74.0], [94.8, 74.0], [94.9, 74.0], [95.0, 74.0], [95.1, 74.0], [95.2, 74.0], [95.3, 74.0], [95.4, 74.0], [95.5, 74.0], [95.6, 74.0], [95.7, 74.0], [95.8, 74.0], [95.9, 74.0], [96.0, 74.0], [96.1, 74.0], [96.2, 74.0], [96.3, 74.0], [96.4, 74.0], [96.5, 74.0], [96.6, 74.0], [96.7, 74.0], [96.8, 74.0], [96.9, 74.0], [97.0, 74.0], [97.1, 74.0], [97.2, 74.0], [97.3, 74.0], [97.4, 74.0], [97.5, 74.0], [97.6, 74.0], [97.7, 74.0], [97.8, 74.0], [97.9, 74.0], [98.0, 74.0], [98.1, 74.0], [98.2, 74.0], [98.3, 74.0], [98.4, 74.0], [98.5, 74.0], [98.6, 74.0], [98.7, 74.0], [98.8, 74.0], [98.9, 74.0], [99.0, 74.0], [99.1, 74.0], [99.2, 74.0], [99.3, 74.0], [99.4, 74.0], [99.5, 74.0], [99.6, 74.0], [99.7, 74.0], [99.8, 74.0], [99.9, 74.0]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[0.0, 122.0], [0.1, 122.0], [0.2, 122.0], [0.3, 122.0], [0.4, 122.0], [0.5, 122.0], [0.6, 122.0], [0.7, 122.0], [0.8, 122.0], [0.9, 122.0], [1.0, 122.0], [1.1, 122.0], [1.2, 122.0], [1.3, 122.0], [1.4, 122.0], [1.5, 122.0], [1.6, 122.0], [1.7, 122.0], [1.8, 122.0], [1.9, 122.0], [2.0, 122.0], [2.1, 122.0], [2.2, 122.0], [2.3, 122.0], [2.4, 122.0], [2.5, 122.0], [2.6, 122.0], [2.7, 122.0], [2.8, 122.0], [2.9, 122.0], [3.0, 122.0], [3.1, 122.0], [3.2, 122.0], [3.3, 122.0], [3.4, 122.0], [3.5, 122.0], [3.6, 122.0], [3.7, 122.0], [3.8, 122.0], [3.9, 122.0], [4.0, 122.0], [4.1, 122.0], [4.2, 122.0], [4.3, 122.0], [4.4, 122.0], [4.5, 122.0], [4.6, 122.0], [4.7, 122.0], [4.8, 122.0], [4.9, 122.0], [5.0, 122.0], [5.1, 122.0], [5.2, 122.0], [5.3, 122.0], [5.4, 122.0], [5.5, 122.0], [5.6, 122.0], [5.7, 122.0], [5.8, 122.0], [5.9, 122.0], [6.0, 122.0], [6.1, 122.0], [6.2, 122.0], [6.3, 122.0], [6.4, 122.0], [6.5, 122.0], [6.6, 122.0], [6.7, 122.0], [6.8, 122.0], [6.9, 122.0], [7.0, 122.0], [7.1, 122.0], [7.2, 122.0], [7.3, 122.0], [7.4, 122.0], [7.5, 122.0], [7.6, 122.0], [7.7, 122.0], [7.8, 122.0], [7.9, 122.0], [8.0, 122.0], [8.1, 122.0], [8.2, 122.0], [8.3, 122.0], [8.4, 122.0], [8.5, 122.0], [8.6, 122.0], [8.7, 122.0], [8.8, 122.0], [8.9, 122.0], [9.0, 122.0], [9.1, 122.0], [9.2, 122.0], [9.3, 122.0], [9.4, 122.0], [9.5, 122.0], [9.6, 122.0], [9.7, 122.0], [9.8, 122.0], [9.9, 122.0], [10.0, 122.0], [10.1, 122.0], [10.2, 122.0], [10.3, 122.0], [10.4, 122.0], [10.5, 122.0], [10.6, 122.0], [10.7, 122.0], [10.8, 122.0], [10.9, 122.0], [11.0, 122.0], [11.1, 122.0], [11.2, 122.0], [11.3, 122.0], [11.4, 122.0], [11.5, 122.0], [11.6, 122.0], [11.7, 122.0], [11.8, 122.0], [11.9, 122.0], [12.0, 122.0], [12.1, 122.0], [12.2, 122.0], [12.3, 122.0], [12.4, 122.0], [12.5, 122.0], [12.6, 122.0], [12.7, 122.0], [12.8, 122.0], [12.9, 122.0], [13.0, 122.0], [13.1, 122.0], [13.2, 122.0], [13.3, 122.0], [13.4, 122.0], [13.5, 122.0], [13.6, 122.0], [13.7, 122.0], [13.8, 122.0], [13.9, 122.0], [14.0, 122.0], [14.1, 122.0], [14.2, 122.0], [14.3, 122.0], [14.4, 122.0], [14.5, 122.0], [14.6, 122.0], [14.7, 122.0], [14.8, 122.0], [14.9, 122.0], [15.0, 122.0], [15.1, 122.0], [15.2, 122.0], [15.3, 122.0], [15.4, 122.0], [15.5, 122.0], [15.6, 122.0], [15.7, 122.0], [15.8, 122.0], [15.9, 122.0], [16.0, 122.0], [16.1, 122.0], [16.2, 122.0], [16.3, 122.0], [16.4, 122.0], [16.5, 122.0], [16.6, 122.0], [16.7, 122.0], [16.8, 122.0], [16.9, 122.0], [17.0, 122.0], [17.1, 122.0], [17.2, 122.0], [17.3, 122.0], [17.4, 122.0], [17.5, 122.0], [17.6, 122.0], [17.7, 122.0], [17.8, 122.0], [17.9, 122.0], [18.0, 122.0], [18.1, 122.0], [18.2, 122.0], [18.3, 122.0], [18.4, 122.0], [18.5, 122.0], [18.6, 122.0], [18.7, 122.0], [18.8, 122.0], [18.9, 122.0], [19.0, 122.0], [19.1, 122.0], [19.2, 122.0], [19.3, 122.0], [19.4, 122.0], [19.5, 122.0], [19.6, 122.0], [19.7, 122.0], [19.8, 122.0], [19.9, 122.0], [20.0, 122.0], [20.1, 122.0], [20.2, 122.0], [20.3, 122.0], [20.4, 122.0], [20.5, 122.0], [20.6, 122.0], [20.7, 122.0], [20.8, 122.0], [20.9, 122.0], [21.0, 122.0], [21.1, 122.0], [21.2, 122.0], [21.3, 122.0], [21.4, 122.0], [21.5, 122.0], [21.6, 122.0], [21.7, 122.0], [21.8, 122.0], [21.9, 122.0], [22.0, 122.0], [22.1, 122.0], [22.2, 122.0], [22.3, 122.0], [22.4, 122.0], [22.5, 122.0], [22.6, 122.0], [22.7, 122.0], [22.8, 122.0], [22.9, 122.0], [23.0, 122.0], [23.1, 122.0], [23.2, 122.0], [23.3, 122.0], [23.4, 122.0], [23.5, 122.0], [23.6, 122.0], [23.7, 122.0], [23.8, 122.0], [23.9, 122.0], [24.0, 122.0], [24.1, 122.0], [24.2, 122.0], [24.3, 122.0], [24.4, 122.0], [24.5, 122.0], [24.6, 122.0], [24.7, 122.0], [24.8, 122.0], [24.9, 122.0], [25.0, 122.0], [25.1, 122.0], [25.2, 122.0], [25.3, 122.0], [25.4, 122.0], [25.5, 122.0], [25.6, 122.0], [25.7, 122.0], [25.8, 122.0], [25.9, 122.0], [26.0, 122.0], [26.1, 122.0], [26.2, 122.0], [26.3, 122.0], [26.4, 122.0], [26.5, 122.0], [26.6, 122.0], [26.7, 122.0], [26.8, 122.0], [26.9, 122.0], [27.0, 122.0], [27.1, 122.0], [27.2, 122.0], [27.3, 122.0], [27.4, 122.0], [27.5, 122.0], [27.6, 122.0], [27.7, 122.0], [27.8, 122.0], [27.9, 122.0], [28.0, 122.0], [28.1, 122.0], [28.2, 122.0], [28.3, 122.0], [28.4, 122.0], [28.5, 122.0], [28.6, 122.0], [28.7, 122.0], [28.8, 122.0], [28.9, 122.0], [29.0, 122.0], [29.1, 122.0], [29.2, 122.0], [29.3, 122.0], [29.4, 122.0], [29.5, 122.0], [29.6, 122.0], [29.7, 122.0], [29.8, 122.0], [29.9, 122.0], [30.0, 122.0], [30.1, 122.0], [30.2, 122.0], [30.3, 122.0], [30.4, 122.0], [30.5, 122.0], [30.6, 122.0], [30.7, 122.0], [30.8, 122.0], [30.9, 122.0], [31.0, 122.0], [31.1, 122.0], [31.2, 122.0], [31.3, 122.0], [31.4, 122.0], [31.5, 122.0], [31.6, 122.0], [31.7, 122.0], [31.8, 122.0], [31.9, 122.0], [32.0, 122.0], [32.1, 122.0], [32.2, 122.0], [32.3, 122.0], [32.4, 122.0], [32.5, 122.0], [32.6, 122.0], [32.7, 122.0], [32.8, 122.0], [32.9, 122.0], [33.0, 122.0], [33.1, 122.0], [33.2, 122.0], [33.3, 122.0], [33.4, 122.0], [33.5, 122.0], [33.6, 122.0], [33.7, 122.0], [33.8, 122.0], [33.9, 122.0], [34.0, 122.0], [34.1, 122.0], [34.2, 122.0], [34.3, 122.0], [34.4, 122.0], [34.5, 122.0], [34.6, 122.0], [34.7, 122.0], [34.8, 122.0], [34.9, 122.0], [35.0, 122.0], [35.1, 122.0], [35.2, 122.0], [35.3, 122.0], [35.4, 122.0], [35.5, 122.0], [35.6, 122.0], [35.7, 122.0], [35.8, 122.0], [35.9, 122.0], [36.0, 122.0], [36.1, 122.0], [36.2, 122.0], [36.3, 122.0], [36.4, 122.0], [36.5, 122.0], [36.6, 122.0], [36.7, 122.0], [36.8, 122.0], [36.9, 122.0], [37.0, 122.0], [37.1, 122.0], [37.2, 122.0], [37.3, 122.0], [37.4, 122.0], [37.5, 122.0], [37.6, 122.0], [37.7, 122.0], [37.8, 122.0], [37.9, 122.0], [38.0, 122.0], [38.1, 122.0], [38.2, 122.0], [38.3, 122.0], [38.4, 122.0], [38.5, 122.0], [38.6, 122.0], [38.7, 122.0], [38.8, 122.0], [38.9, 122.0], [39.0, 122.0], [39.1, 122.0], [39.2, 122.0], [39.3, 122.0], [39.4, 122.0], [39.5, 122.0], [39.6, 122.0], [39.7, 122.0], [39.8, 122.0], [39.9, 122.0], [40.0, 122.0], [40.1, 122.0], [40.2, 122.0], [40.3, 122.0], [40.4, 122.0], [40.5, 122.0], [40.6, 122.0], [40.7, 122.0], [40.8, 122.0], [40.9, 122.0], [41.0, 122.0], [41.1, 122.0], [41.2, 122.0], [41.3, 122.0], [41.4, 122.0], [41.5, 122.0], [41.6, 122.0], [41.7, 122.0], [41.8, 122.0], [41.9, 122.0], [42.0, 122.0], [42.1, 122.0], [42.2, 122.0], [42.3, 122.0], [42.4, 122.0], [42.5, 122.0], [42.6, 122.0], [42.7, 122.0], [42.8, 122.0], [42.9, 122.0], [43.0, 122.0], [43.1, 122.0], [43.2, 122.0], [43.3, 122.0], [43.4, 122.0], [43.5, 122.0], [43.6, 122.0], [43.7, 122.0], [43.8, 122.0], [43.9, 122.0], [44.0, 122.0], [44.1, 122.0], [44.2, 122.0], [44.3, 122.0], [44.4, 122.0], [44.5, 122.0], [44.6, 122.0], [44.7, 122.0], [44.8, 122.0], [44.9, 122.0], [45.0, 122.0], [45.1, 122.0], [45.2, 122.0], [45.3, 122.0], [45.4, 122.0], [45.5, 122.0], [45.6, 122.0], [45.7, 122.0], [45.8, 122.0], [45.9, 122.0], [46.0, 122.0], [46.1, 122.0], [46.2, 122.0], [46.3, 122.0], [46.4, 122.0], [46.5, 122.0], [46.6, 122.0], [46.7, 122.0], [46.8, 122.0], [46.9, 122.0], [47.0, 122.0], [47.1, 122.0], [47.2, 122.0], [47.3, 122.0], [47.4, 122.0], [47.5, 122.0], [47.6, 122.0], [47.7, 122.0], [47.8, 122.0], [47.9, 122.0], [48.0, 122.0], [48.1, 122.0], [48.2, 122.0], [48.3, 122.0], [48.4, 122.0], [48.5, 122.0], [48.6, 122.0], [48.7, 122.0], [48.8, 122.0], [48.9, 122.0], [49.0, 122.0], [49.1, 122.0], [49.2, 122.0], [49.3, 122.0], [49.4, 122.0], [49.5, 122.0], [49.6, 122.0], [49.7, 122.0], [49.8, 122.0], [49.9, 122.0], [50.0, 122.0], [50.1, 122.0], [50.2, 122.0], [50.3, 122.0], [50.4, 122.0], [50.5, 122.0], [50.6, 122.0], [50.7, 122.0], [50.8, 122.0], [50.9, 122.0], [51.0, 122.0], [51.1, 122.0], [51.2, 122.0], [51.3, 122.0], [51.4, 122.0], [51.5, 122.0], [51.6, 122.0], [51.7, 122.0], [51.8, 122.0], [51.9, 122.0], [52.0, 122.0], [52.1, 122.0], [52.2, 122.0], [52.3, 122.0], [52.4, 122.0], [52.5, 122.0], [52.6, 122.0], [52.7, 122.0], [52.8, 122.0], [52.9, 122.0], [53.0, 122.0], [53.1, 122.0], [53.2, 122.0], [53.3, 122.0], [53.4, 122.0], [53.5, 122.0], [53.6, 122.0], [53.7, 122.0], [53.8, 122.0], [53.9, 122.0], [54.0, 122.0], [54.1, 122.0], [54.2, 122.0], [54.3, 122.0], [54.4, 122.0], [54.5, 122.0], [54.6, 122.0], [54.7, 122.0], [54.8, 122.0], [54.9, 122.0], [55.0, 122.0], [55.1, 122.0], [55.2, 122.0], [55.3, 122.0], [55.4, 122.0], [55.5, 122.0], [55.6, 122.0], [55.7, 122.0], [55.8, 122.0], [55.9, 122.0], [56.0, 122.0], [56.1, 122.0], [56.2, 122.0], [56.3, 122.0], [56.4, 122.0], [56.5, 122.0], [56.6, 122.0], [56.7, 122.0], [56.8, 122.0], [56.9, 122.0], [57.0, 122.0], [57.1, 122.0], [57.2, 122.0], [57.3, 122.0], [57.4, 122.0], [57.5, 122.0], [57.6, 122.0], [57.7, 122.0], [57.8, 122.0], [57.9, 122.0], [58.0, 122.0], [58.1, 122.0], [58.2, 122.0], [58.3, 122.0], [58.4, 122.0], [58.5, 122.0], [58.6, 122.0], [58.7, 122.0], [58.8, 122.0], [58.9, 122.0], [59.0, 122.0], [59.1, 122.0], [59.2, 122.0], [59.3, 122.0], [59.4, 122.0], [59.5, 122.0], [59.6, 122.0], [59.7, 122.0], [59.8, 122.0], [59.9, 122.0], [60.0, 122.0], [60.1, 122.0], [60.2, 122.0], [60.3, 122.0], [60.4, 122.0], [60.5, 122.0], [60.6, 122.0], [60.7, 122.0], [60.8, 122.0], [60.9, 122.0], [61.0, 122.0], [61.1, 122.0], [61.2, 122.0], [61.3, 122.0], [61.4, 122.0], [61.5, 122.0], [61.6, 122.0], [61.7, 122.0], [61.8, 122.0], [61.9, 122.0], [62.0, 122.0], [62.1, 122.0], [62.2, 122.0], [62.3, 122.0], [62.4, 122.0], [62.5, 122.0], [62.6, 122.0], [62.7, 122.0], [62.8, 122.0], [62.9, 122.0], [63.0, 122.0], [63.1, 122.0], [63.2, 122.0], [63.3, 122.0], [63.4, 122.0], [63.5, 122.0], [63.6, 122.0], [63.7, 122.0], [63.8, 122.0], [63.9, 122.0], [64.0, 122.0], [64.1, 122.0], [64.2, 122.0], [64.3, 122.0], [64.4, 122.0], [64.5, 122.0], [64.6, 122.0], [64.7, 122.0], [64.8, 122.0], [64.9, 122.0], [65.0, 122.0], [65.1, 122.0], [65.2, 122.0], [65.3, 122.0], [65.4, 122.0], [65.5, 122.0], [65.6, 122.0], [65.7, 122.0], [65.8, 122.0], [65.9, 122.0], [66.0, 122.0], [66.1, 122.0], [66.2, 122.0], [66.3, 122.0], [66.4, 122.0], [66.5, 122.0], [66.6, 122.0], [66.7, 122.0], [66.8, 122.0], [66.9, 122.0], [67.0, 122.0], [67.1, 122.0], [67.2, 122.0], [67.3, 122.0], [67.4, 122.0], [67.5, 122.0], [67.6, 122.0], [67.7, 122.0], [67.8, 122.0], [67.9, 122.0], [68.0, 122.0], [68.1, 122.0], [68.2, 122.0], [68.3, 122.0], [68.4, 122.0], [68.5, 122.0], [68.6, 122.0], [68.7, 122.0], [68.8, 122.0], [68.9, 122.0], [69.0, 122.0], [69.1, 122.0], [69.2, 122.0], [69.3, 122.0], [69.4, 122.0], [69.5, 122.0], [69.6, 122.0], [69.7, 122.0], [69.8, 122.0], [69.9, 122.0], [70.0, 122.0], [70.1, 122.0], [70.2, 122.0], [70.3, 122.0], [70.4, 122.0], [70.5, 122.0], [70.6, 122.0], [70.7, 122.0], [70.8, 122.0], [70.9, 122.0], [71.0, 122.0], [71.1, 122.0], [71.2, 122.0], [71.3, 122.0], [71.4, 122.0], [71.5, 122.0], [71.6, 122.0], [71.7, 122.0], [71.8, 122.0], [71.9, 122.0], [72.0, 122.0], [72.1, 122.0], [72.2, 122.0], [72.3, 122.0], [72.4, 122.0], [72.5, 122.0], [72.6, 122.0], [72.7, 122.0], [72.8, 122.0], [72.9, 122.0], [73.0, 122.0], [73.1, 122.0], [73.2, 122.0], [73.3, 122.0], [73.4, 122.0], [73.5, 122.0], [73.6, 122.0], [73.7, 122.0], [73.8, 122.0], [73.9, 122.0], [74.0, 122.0], [74.1, 122.0], [74.2, 122.0], [74.3, 122.0], [74.4, 122.0], [74.5, 122.0], [74.6, 122.0], [74.7, 122.0], [74.8, 122.0], [74.9, 122.0], [75.0, 122.0], [75.1, 122.0], [75.2, 122.0], [75.3, 122.0], [75.4, 122.0], [75.5, 122.0], [75.6, 122.0], [75.7, 122.0], [75.8, 122.0], [75.9, 122.0], [76.0, 122.0], [76.1, 122.0], [76.2, 122.0], [76.3, 122.0], [76.4, 122.0], [76.5, 122.0], [76.6, 122.0], [76.7, 122.0], [76.8, 122.0], [76.9, 122.0], [77.0, 122.0], [77.1, 122.0], [77.2, 122.0], [77.3, 122.0], [77.4, 122.0], [77.5, 122.0], [77.6, 122.0], [77.7, 122.0], [77.8, 122.0], [77.9, 122.0], [78.0, 122.0], [78.1, 122.0], [78.2, 122.0], [78.3, 122.0], [78.4, 122.0], [78.5, 122.0], [78.6, 122.0], [78.7, 122.0], [78.8, 122.0], [78.9, 122.0], [79.0, 122.0], [79.1, 122.0], [79.2, 122.0], [79.3, 122.0], [79.4, 122.0], [79.5, 122.0], [79.6, 122.0], [79.7, 122.0], [79.8, 122.0], [79.9, 122.0], [80.0, 122.0], [80.1, 122.0], [80.2, 122.0], [80.3, 122.0], [80.4, 122.0], [80.5, 122.0], [80.6, 122.0], [80.7, 122.0], [80.8, 122.0], [80.9, 122.0], [81.0, 122.0], [81.1, 122.0], [81.2, 122.0], [81.3, 122.0], [81.4, 122.0], [81.5, 122.0], [81.6, 122.0], [81.7, 122.0], [81.8, 122.0], [81.9, 122.0], [82.0, 122.0], [82.1, 122.0], [82.2, 122.0], [82.3, 122.0], [82.4, 122.0], [82.5, 122.0], [82.6, 122.0], [82.7, 122.0], [82.8, 122.0], [82.9, 122.0], [83.0, 122.0], [83.1, 122.0], [83.2, 122.0], [83.3, 122.0], [83.4, 122.0], [83.5, 122.0], [83.6, 122.0], [83.7, 122.0], [83.8, 122.0], [83.9, 122.0], [84.0, 122.0], [84.1, 122.0], [84.2, 122.0], [84.3, 122.0], [84.4, 122.0], [84.5, 122.0], [84.6, 122.0], [84.7, 122.0], [84.8, 122.0], [84.9, 122.0], [85.0, 122.0], [85.1, 122.0], [85.2, 122.0], [85.3, 122.0], [85.4, 122.0], [85.5, 122.0], [85.6, 122.0], [85.7, 122.0], [85.8, 122.0], [85.9, 122.0], [86.0, 122.0], [86.1, 122.0], [86.2, 122.0], [86.3, 122.0], [86.4, 122.0], [86.5, 122.0], [86.6, 122.0], [86.7, 122.0], [86.8, 122.0], [86.9, 122.0], [87.0, 122.0], [87.1, 122.0], [87.2, 122.0], [87.3, 122.0], [87.4, 122.0], [87.5, 122.0], [87.6, 122.0], [87.7, 122.0], [87.8, 122.0], [87.9, 122.0], [88.0, 122.0], [88.1, 122.0], [88.2, 122.0], [88.3, 122.0], [88.4, 122.0], [88.5, 122.0], [88.6, 122.0], [88.7, 122.0], [88.8, 122.0], [88.9, 122.0], [89.0, 122.0], [89.1, 122.0], [89.2, 122.0], [89.3, 122.0], [89.4, 122.0], [89.5, 122.0], [89.6, 122.0], [89.7, 122.0], [89.8, 122.0], [89.9, 122.0], [90.0, 122.0], [90.1, 122.0], [90.2, 122.0], [90.3, 122.0], [90.4, 122.0], [90.5, 122.0], [90.6, 122.0], [90.7, 122.0], [90.8, 122.0], [90.9, 122.0], [91.0, 122.0], [91.1, 122.0], [91.2, 122.0], [91.3, 122.0], [91.4, 122.0], [91.5, 122.0], [91.6, 122.0], [91.7, 122.0], [91.8, 122.0], [91.9, 122.0], [92.0, 122.0], [92.1, 122.0], [92.2, 122.0], [92.3, 122.0], [92.4, 122.0], [92.5, 122.0], [92.6, 122.0], [92.7, 122.0], [92.8, 122.0], [92.9, 122.0], [93.0, 122.0], [93.1, 122.0], [93.2, 122.0], [93.3, 122.0], [93.4, 122.0], [93.5, 122.0], [93.6, 122.0], [93.7, 122.0], [93.8, 122.0], [93.9, 122.0], [94.0, 122.0], [94.1, 122.0], [94.2, 122.0], [94.3, 122.0], [94.4, 122.0], [94.5, 122.0], [94.6, 122.0], [94.7, 122.0], [94.8, 122.0], [94.9, 122.0], [95.0, 122.0], [95.1, 122.0], [95.2, 122.0], [95.3, 122.0], [95.4, 122.0], [95.5, 122.0], [95.6, 122.0], [95.7, 122.0], [95.8, 122.0], [95.9, 122.0], [96.0, 122.0], [96.1, 122.0], [96.2, 122.0], [96.3, 122.0], [96.4, 122.0], [96.5, 122.0], [96.6, 122.0], [96.7, 122.0], [96.8, 122.0], [96.9, 122.0], [97.0, 122.0], [97.1, 122.0], [97.2, 122.0], [97.3, 122.0], [97.4, 122.0], [97.5, 122.0], [97.6, 122.0], [97.7, 122.0], [97.8, 122.0], [97.9, 122.0], [98.0, 122.0], [98.1, 122.0], [98.2, 122.0], [98.3, 122.0], [98.4, 122.0], [98.5, 122.0], [98.6, 122.0], [98.7, 122.0], [98.8, 122.0], [98.9, 122.0], [99.0, 122.0], [99.1, 122.0], [99.2, 122.0], [99.3, 122.0], [99.4, 122.0], [99.5, 122.0], [99.6, 122.0], [99.7, 122.0], [99.8, 122.0], [99.9, 122.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[0.0, 44.0], [0.1, 44.0], [0.2, 44.0], [0.3, 44.0], [0.4, 44.0], [0.5, 44.0], [0.6, 44.0], [0.7, 44.0], [0.8, 44.0], [0.9, 44.0], [1.0, 44.0], [1.1, 44.0], [1.2, 44.0], [1.3, 44.0], [1.4, 44.0], [1.5, 44.0], [1.6, 44.0], [1.7, 44.0], [1.8, 44.0], [1.9, 44.0], [2.0, 44.0], [2.1, 44.0], [2.2, 44.0], [2.3, 44.0], [2.4, 44.0], [2.5, 44.0], [2.6, 44.0], [2.7, 44.0], [2.8, 44.0], [2.9, 44.0], [3.0, 44.0], [3.1, 44.0], [3.2, 44.0], [3.3, 44.0], [3.4, 44.0], [3.5, 44.0], [3.6, 44.0], [3.7, 44.0], [3.8, 44.0], [3.9, 44.0], [4.0, 44.0], [4.1, 44.0], [4.2, 44.0], [4.3, 44.0], [4.4, 44.0], [4.5, 44.0], [4.6, 44.0], [4.7, 44.0], [4.8, 44.0], [4.9, 44.0], [5.0, 44.0], [5.1, 44.0], [5.2, 44.0], [5.3, 44.0], [5.4, 44.0], [5.5, 44.0], [5.6, 44.0], [5.7, 44.0], [5.8, 44.0], [5.9, 44.0], [6.0, 44.0], [6.1, 44.0], [6.2, 44.0], [6.3, 44.0], [6.4, 44.0], [6.5, 44.0], [6.6, 44.0], [6.7, 44.0], [6.8, 44.0], [6.9, 44.0], [7.0, 44.0], [7.1, 44.0], [7.2, 44.0], [7.3, 44.0], [7.4, 44.0], [7.5, 44.0], [7.6, 44.0], [7.7, 44.0], [7.8, 44.0], [7.9, 44.0], [8.0, 44.0], [8.1, 44.0], [8.2, 44.0], [8.3, 44.0], [8.4, 44.0], [8.5, 44.0], [8.6, 44.0], [8.7, 44.0], [8.8, 44.0], [8.9, 44.0], [9.0, 44.0], [9.1, 44.0], [9.2, 44.0], [9.3, 44.0], [9.4, 44.0], [9.5, 44.0], [9.6, 44.0], [9.7, 44.0], [9.8, 44.0], [9.9, 44.0], [10.0, 44.0], [10.1, 44.0], [10.2, 44.0], [10.3, 44.0], [10.4, 44.0], [10.5, 44.0], [10.6, 44.0], [10.7, 44.0], [10.8, 44.0], [10.9, 44.0], [11.0, 44.0], [11.1, 44.0], [11.2, 44.0], [11.3, 44.0], [11.4, 44.0], [11.5, 44.0], [11.6, 44.0], [11.7, 44.0], [11.8, 44.0], [11.9, 44.0], [12.0, 44.0], [12.1, 44.0], [12.2, 44.0], [12.3, 44.0], [12.4, 44.0], [12.5, 44.0], [12.6, 44.0], [12.7, 44.0], [12.8, 44.0], [12.9, 44.0], [13.0, 44.0], [13.1, 44.0], [13.2, 44.0], [13.3, 44.0], [13.4, 44.0], [13.5, 44.0], [13.6, 44.0], [13.7, 44.0], [13.8, 44.0], [13.9, 44.0], [14.0, 44.0], [14.1, 44.0], [14.2, 44.0], [14.3, 44.0], [14.4, 44.0], [14.5, 44.0], [14.6, 44.0], [14.7, 44.0], [14.8, 44.0], [14.9, 44.0], [15.0, 44.0], [15.1, 44.0], [15.2, 44.0], [15.3, 44.0], [15.4, 44.0], [15.5, 44.0], [15.6, 44.0], [15.7, 44.0], [15.8, 44.0], [15.9, 44.0], [16.0, 44.0], [16.1, 44.0], [16.2, 44.0], [16.3, 44.0], [16.4, 44.0], [16.5, 44.0], [16.6, 44.0], [16.7, 44.0], [16.8, 44.0], [16.9, 44.0], [17.0, 44.0], [17.1, 44.0], [17.2, 44.0], [17.3, 44.0], [17.4, 44.0], [17.5, 44.0], [17.6, 44.0], [17.7, 44.0], [17.8, 44.0], [17.9, 44.0], [18.0, 44.0], [18.1, 44.0], [18.2, 44.0], [18.3, 44.0], [18.4, 44.0], [18.5, 44.0], [18.6, 44.0], [18.7, 44.0], [18.8, 44.0], [18.9, 44.0], [19.0, 44.0], [19.1, 44.0], [19.2, 44.0], [19.3, 44.0], [19.4, 44.0], [19.5, 44.0], [19.6, 44.0], [19.7, 44.0], [19.8, 44.0], [19.9, 44.0], [20.0, 44.0], [20.1, 44.0], [20.2, 44.0], [20.3, 44.0], [20.4, 44.0], [20.5, 44.0], [20.6, 44.0], [20.7, 44.0], [20.8, 44.0], [20.9, 44.0], [21.0, 44.0], [21.1, 44.0], [21.2, 44.0], [21.3, 44.0], [21.4, 44.0], [21.5, 44.0], [21.6, 44.0], [21.7, 44.0], [21.8, 44.0], [21.9, 44.0], [22.0, 44.0], [22.1, 44.0], [22.2, 44.0], [22.3, 44.0], [22.4, 44.0], [22.5, 44.0], [22.6, 44.0], [22.7, 44.0], [22.8, 44.0], [22.9, 44.0], [23.0, 44.0], [23.1, 44.0], [23.2, 44.0], [23.3, 44.0], [23.4, 44.0], [23.5, 44.0], [23.6, 44.0], [23.7, 44.0], [23.8, 44.0], [23.9, 44.0], [24.0, 44.0], [24.1, 44.0], [24.2, 44.0], [24.3, 44.0], [24.4, 44.0], [24.5, 44.0], [24.6, 44.0], [24.7, 44.0], [24.8, 44.0], [24.9, 44.0], [25.0, 44.0], [25.1, 44.0], [25.2, 44.0], [25.3, 44.0], [25.4, 44.0], [25.5, 44.0], [25.6, 44.0], [25.7, 44.0], [25.8, 44.0], [25.9, 44.0], [26.0, 44.0], [26.1, 44.0], [26.2, 44.0], [26.3, 44.0], [26.4, 44.0], [26.5, 44.0], [26.6, 44.0], [26.7, 44.0], [26.8, 44.0], [26.9, 44.0], [27.0, 44.0], [27.1, 44.0], [27.2, 44.0], [27.3, 44.0], [27.4, 44.0], [27.5, 44.0], [27.6, 44.0], [27.7, 44.0], [27.8, 44.0], [27.9, 44.0], [28.0, 44.0], [28.1, 44.0], [28.2, 44.0], [28.3, 44.0], [28.4, 44.0], [28.5, 44.0], [28.6, 44.0], [28.7, 44.0], [28.8, 44.0], [28.9, 44.0], [29.0, 44.0], [29.1, 44.0], [29.2, 44.0], [29.3, 44.0], [29.4, 44.0], [29.5, 44.0], [29.6, 44.0], [29.7, 44.0], [29.8, 44.0], [29.9, 44.0], [30.0, 44.0], [30.1, 44.0], [30.2, 44.0], [30.3, 44.0], [30.4, 44.0], [30.5, 44.0], [30.6, 44.0], [30.7, 44.0], [30.8, 44.0], [30.9, 44.0], [31.0, 44.0], [31.1, 44.0], [31.2, 44.0], [31.3, 44.0], [31.4, 44.0], [31.5, 44.0], [31.6, 44.0], [31.7, 44.0], [31.8, 44.0], [31.9, 44.0], [32.0, 44.0], [32.1, 44.0], [32.2, 44.0], [32.3, 44.0], [32.4, 44.0], [32.5, 44.0], [32.6, 44.0], [32.7, 44.0], [32.8, 44.0], [32.9, 44.0], [33.0, 44.0], [33.1, 44.0], [33.2, 44.0], [33.3, 44.0], [33.4, 44.0], [33.5, 44.0], [33.6, 44.0], [33.7, 44.0], [33.8, 44.0], [33.9, 44.0], [34.0, 44.0], [34.1, 44.0], [34.2, 44.0], [34.3, 44.0], [34.4, 44.0], [34.5, 44.0], [34.6, 44.0], [34.7, 44.0], [34.8, 44.0], [34.9, 44.0], [35.0, 44.0], [35.1, 44.0], [35.2, 44.0], [35.3, 44.0], [35.4, 44.0], [35.5, 44.0], [35.6, 44.0], [35.7, 44.0], [35.8, 44.0], [35.9, 44.0], [36.0, 44.0], [36.1, 44.0], [36.2, 44.0], [36.3, 44.0], [36.4, 44.0], [36.5, 44.0], [36.6, 44.0], [36.7, 44.0], [36.8, 44.0], [36.9, 44.0], [37.0, 44.0], [37.1, 44.0], [37.2, 44.0], [37.3, 44.0], [37.4, 44.0], [37.5, 44.0], [37.6, 44.0], [37.7, 44.0], [37.8, 44.0], [37.9, 44.0], [38.0, 44.0], [38.1, 44.0], [38.2, 44.0], [38.3, 44.0], [38.4, 44.0], [38.5, 44.0], [38.6, 44.0], [38.7, 44.0], [38.8, 44.0], [38.9, 44.0], [39.0, 44.0], [39.1, 44.0], [39.2, 44.0], [39.3, 44.0], [39.4, 44.0], [39.5, 44.0], [39.6, 44.0], [39.7, 44.0], [39.8, 44.0], [39.9, 44.0], [40.0, 44.0], [40.1, 44.0], [40.2, 44.0], [40.3, 44.0], [40.4, 44.0], [40.5, 44.0], [40.6, 44.0], [40.7, 44.0], [40.8, 44.0], [40.9, 44.0], [41.0, 44.0], [41.1, 44.0], [41.2, 44.0], [41.3, 44.0], [41.4, 44.0], [41.5, 44.0], [41.6, 44.0], [41.7, 44.0], [41.8, 44.0], [41.9, 44.0], [42.0, 44.0], [42.1, 44.0], [42.2, 44.0], [42.3, 44.0], [42.4, 44.0], [42.5, 44.0], [42.6, 44.0], [42.7, 44.0], [42.8, 44.0], [42.9, 44.0], [43.0, 44.0], [43.1, 44.0], [43.2, 44.0], [43.3, 44.0], [43.4, 44.0], [43.5, 44.0], [43.6, 44.0], [43.7, 44.0], [43.8, 44.0], [43.9, 44.0], [44.0, 44.0], [44.1, 44.0], [44.2, 44.0], [44.3, 44.0], [44.4, 44.0], [44.5, 44.0], [44.6, 44.0], [44.7, 44.0], [44.8, 44.0], [44.9, 44.0], [45.0, 44.0], [45.1, 44.0], [45.2, 44.0], [45.3, 44.0], [45.4, 44.0], [45.5, 44.0], [45.6, 44.0], [45.7, 44.0], [45.8, 44.0], [45.9, 44.0], [46.0, 44.0], [46.1, 44.0], [46.2, 44.0], [46.3, 44.0], [46.4, 44.0], [46.5, 44.0], [46.6, 44.0], [46.7, 44.0], [46.8, 44.0], [46.9, 44.0], [47.0, 44.0], [47.1, 44.0], [47.2, 44.0], [47.3, 44.0], [47.4, 44.0], [47.5, 44.0], [47.6, 44.0], [47.7, 44.0], [47.8, 44.0], [47.9, 44.0], [48.0, 44.0], [48.1, 44.0], [48.2, 44.0], [48.3, 44.0], [48.4, 44.0], [48.5, 44.0], [48.6, 44.0], [48.7, 44.0], [48.8, 44.0], [48.9, 44.0], [49.0, 44.0], [49.1, 44.0], [49.2, 44.0], [49.3, 44.0], [49.4, 44.0], [49.5, 44.0], [49.6, 44.0], [49.7, 44.0], [49.8, 44.0], [49.9, 44.0], [50.0, 44.0], [50.1, 44.0], [50.2, 44.0], [50.3, 44.0], [50.4, 44.0], [50.5, 44.0], [50.6, 44.0], [50.7, 44.0], [50.8, 44.0], [50.9, 44.0], [51.0, 44.0], [51.1, 44.0], [51.2, 44.0], [51.3, 44.0], [51.4, 44.0], [51.5, 44.0], [51.6, 44.0], [51.7, 44.0], [51.8, 44.0], [51.9, 44.0], [52.0, 44.0], [52.1, 44.0], [52.2, 44.0], [52.3, 44.0], [52.4, 44.0], [52.5, 44.0], [52.6, 44.0], [52.7, 44.0], [52.8, 44.0], [52.9, 44.0], [53.0, 44.0], [53.1, 44.0], [53.2, 44.0], [53.3, 44.0], [53.4, 44.0], [53.5, 44.0], [53.6, 44.0], [53.7, 44.0], [53.8, 44.0], [53.9, 44.0], [54.0, 44.0], [54.1, 44.0], [54.2, 44.0], [54.3, 44.0], [54.4, 44.0], [54.5, 44.0], [54.6, 44.0], [54.7, 44.0], [54.8, 44.0], [54.9, 44.0], [55.0, 44.0], [55.1, 44.0], [55.2, 44.0], [55.3, 44.0], [55.4, 44.0], [55.5, 44.0], [55.6, 44.0], [55.7, 44.0], [55.8, 44.0], [55.9, 44.0], [56.0, 44.0], [56.1, 44.0], [56.2, 44.0], [56.3, 44.0], [56.4, 44.0], [56.5, 44.0], [56.6, 44.0], [56.7, 44.0], [56.8, 44.0], [56.9, 44.0], [57.0, 44.0], [57.1, 44.0], [57.2, 44.0], [57.3, 44.0], [57.4, 44.0], [57.5, 44.0], [57.6, 44.0], [57.7, 44.0], [57.8, 44.0], [57.9, 44.0], [58.0, 44.0], [58.1, 44.0], [58.2, 44.0], [58.3, 44.0], [58.4, 44.0], [58.5, 44.0], [58.6, 44.0], [58.7, 44.0], [58.8, 44.0], [58.9, 44.0], [59.0, 44.0], [59.1, 44.0], [59.2, 44.0], [59.3, 44.0], [59.4, 44.0], [59.5, 44.0], [59.6, 44.0], [59.7, 44.0], [59.8, 44.0], [59.9, 44.0], [60.0, 44.0], [60.1, 44.0], [60.2, 44.0], [60.3, 44.0], [60.4, 44.0], [60.5, 44.0], [60.6, 44.0], [60.7, 44.0], [60.8, 44.0], [60.9, 44.0], [61.0, 44.0], [61.1, 44.0], [61.2, 44.0], [61.3, 44.0], [61.4, 44.0], [61.5, 44.0], [61.6, 44.0], [61.7, 44.0], [61.8, 44.0], [61.9, 44.0], [62.0, 44.0], [62.1, 44.0], [62.2, 44.0], [62.3, 44.0], [62.4, 44.0], [62.5, 44.0], [62.6, 44.0], [62.7, 44.0], [62.8, 44.0], [62.9, 44.0], [63.0, 44.0], [63.1, 44.0], [63.2, 44.0], [63.3, 44.0], [63.4, 44.0], [63.5, 44.0], [63.6, 44.0], [63.7, 44.0], [63.8, 44.0], [63.9, 44.0], [64.0, 44.0], [64.1, 44.0], [64.2, 44.0], [64.3, 44.0], [64.4, 44.0], [64.5, 44.0], [64.6, 44.0], [64.7, 44.0], [64.8, 44.0], [64.9, 44.0], [65.0, 44.0], [65.1, 44.0], [65.2, 44.0], [65.3, 44.0], [65.4, 44.0], [65.5, 44.0], [65.6, 44.0], [65.7, 44.0], [65.8, 44.0], [65.9, 44.0], [66.0, 44.0], [66.1, 44.0], [66.2, 44.0], [66.3, 44.0], [66.4, 44.0], [66.5, 44.0], [66.6, 44.0], [66.7, 44.0], [66.8, 44.0], [66.9, 44.0], [67.0, 44.0], [67.1, 44.0], [67.2, 44.0], [67.3, 44.0], [67.4, 44.0], [67.5, 44.0], [67.6, 44.0], [67.7, 44.0], [67.8, 44.0], [67.9, 44.0], [68.0, 44.0], [68.1, 44.0], [68.2, 44.0], [68.3, 44.0], [68.4, 44.0], [68.5, 44.0], [68.6, 44.0], [68.7, 44.0], [68.8, 44.0], [68.9, 44.0], [69.0, 44.0], [69.1, 44.0], [69.2, 44.0], [69.3, 44.0], [69.4, 44.0], [69.5, 44.0], [69.6, 44.0], [69.7, 44.0], [69.8, 44.0], [69.9, 44.0], [70.0, 44.0], [70.1, 44.0], [70.2, 44.0], [70.3, 44.0], [70.4, 44.0], [70.5, 44.0], [70.6, 44.0], [70.7, 44.0], [70.8, 44.0], [70.9, 44.0], [71.0, 44.0], [71.1, 44.0], [71.2, 44.0], [71.3, 44.0], [71.4, 44.0], [71.5, 44.0], [71.6, 44.0], [71.7, 44.0], [71.8, 44.0], [71.9, 44.0], [72.0, 44.0], [72.1, 44.0], [72.2, 44.0], [72.3, 44.0], [72.4, 44.0], [72.5, 44.0], [72.6, 44.0], [72.7, 44.0], [72.8, 44.0], [72.9, 44.0], [73.0, 44.0], [73.1, 44.0], [73.2, 44.0], [73.3, 44.0], [73.4, 44.0], [73.5, 44.0], [73.6, 44.0], [73.7, 44.0], [73.8, 44.0], [73.9, 44.0], [74.0, 44.0], [74.1, 44.0], [74.2, 44.0], [74.3, 44.0], [74.4, 44.0], [74.5, 44.0], [74.6, 44.0], [74.7, 44.0], [74.8, 44.0], [74.9, 44.0], [75.0, 44.0], [75.1, 44.0], [75.2, 44.0], [75.3, 44.0], [75.4, 44.0], [75.5, 44.0], [75.6, 44.0], [75.7, 44.0], [75.8, 44.0], [75.9, 44.0], [76.0, 44.0], [76.1, 44.0], [76.2, 44.0], [76.3, 44.0], [76.4, 44.0], [76.5, 44.0], [76.6, 44.0], [76.7, 44.0], [76.8, 44.0], [76.9, 44.0], [77.0, 44.0], [77.1, 44.0], [77.2, 44.0], [77.3, 44.0], [77.4, 44.0], [77.5, 44.0], [77.6, 44.0], [77.7, 44.0], [77.8, 44.0], [77.9, 44.0], [78.0, 44.0], [78.1, 44.0], [78.2, 44.0], [78.3, 44.0], [78.4, 44.0], [78.5, 44.0], [78.6, 44.0], [78.7, 44.0], [78.8, 44.0], [78.9, 44.0], [79.0, 44.0], [79.1, 44.0], [79.2, 44.0], [79.3, 44.0], [79.4, 44.0], [79.5, 44.0], [79.6, 44.0], [79.7, 44.0], [79.8, 44.0], [79.9, 44.0], [80.0, 44.0], [80.1, 44.0], [80.2, 44.0], [80.3, 44.0], [80.4, 44.0], [80.5, 44.0], [80.6, 44.0], [80.7, 44.0], [80.8, 44.0], [80.9, 44.0], [81.0, 44.0], [81.1, 44.0], [81.2, 44.0], [81.3, 44.0], [81.4, 44.0], [81.5, 44.0], [81.6, 44.0], [81.7, 44.0], [81.8, 44.0], [81.9, 44.0], [82.0, 44.0], [82.1, 44.0], [82.2, 44.0], [82.3, 44.0], [82.4, 44.0], [82.5, 44.0], [82.6, 44.0], [82.7, 44.0], [82.8, 44.0], [82.9, 44.0], [83.0, 44.0], [83.1, 44.0], [83.2, 44.0], [83.3, 44.0], [83.4, 44.0], [83.5, 44.0], [83.6, 44.0], [83.7, 44.0], [83.8, 44.0], [83.9, 44.0], [84.0, 44.0], [84.1, 44.0], [84.2, 44.0], [84.3, 44.0], [84.4, 44.0], [84.5, 44.0], [84.6, 44.0], [84.7, 44.0], [84.8, 44.0], [84.9, 44.0], [85.0, 44.0], [85.1, 44.0], [85.2, 44.0], [85.3, 44.0], [85.4, 44.0], [85.5, 44.0], [85.6, 44.0], [85.7, 44.0], [85.8, 44.0], [85.9, 44.0], [86.0, 44.0], [86.1, 44.0], [86.2, 44.0], [86.3, 44.0], [86.4, 44.0], [86.5, 44.0], [86.6, 44.0], [86.7, 44.0], [86.8, 44.0], [86.9, 44.0], [87.0, 44.0], [87.1, 44.0], [87.2, 44.0], [87.3, 44.0], [87.4, 44.0], [87.5, 44.0], [87.6, 44.0], [87.7, 44.0], [87.8, 44.0], [87.9, 44.0], [88.0, 44.0], [88.1, 44.0], [88.2, 44.0], [88.3, 44.0], [88.4, 44.0], [88.5, 44.0], [88.6, 44.0], [88.7, 44.0], [88.8, 44.0], [88.9, 44.0], [89.0, 44.0], [89.1, 44.0], [89.2, 44.0], [89.3, 44.0], [89.4, 44.0], [89.5, 44.0], [89.6, 44.0], [89.7, 44.0], [89.8, 44.0], [89.9, 44.0], [90.0, 44.0], [90.1, 44.0], [90.2, 44.0], [90.3, 44.0], [90.4, 44.0], [90.5, 44.0], [90.6, 44.0], [90.7, 44.0], [90.8, 44.0], [90.9, 44.0], [91.0, 44.0], [91.1, 44.0], [91.2, 44.0], [91.3, 44.0], [91.4, 44.0], [91.5, 44.0], [91.6, 44.0], [91.7, 44.0], [91.8, 44.0], [91.9, 44.0], [92.0, 44.0], [92.1, 44.0], [92.2, 44.0], [92.3, 44.0], [92.4, 44.0], [92.5, 44.0], [92.6, 44.0], [92.7, 44.0], [92.8, 44.0], [92.9, 44.0], [93.0, 44.0], [93.1, 44.0], [93.2, 44.0], [93.3, 44.0], [93.4, 44.0], [93.5, 44.0], [93.6, 44.0], [93.7, 44.0], [93.8, 44.0], [93.9, 44.0], [94.0, 44.0], [94.1, 44.0], [94.2, 44.0], [94.3, 44.0], [94.4, 44.0], [94.5, 44.0], [94.6, 44.0], [94.7, 44.0], [94.8, 44.0], [94.9, 44.0], [95.0, 44.0], [95.1, 44.0], [95.2, 44.0], [95.3, 44.0], [95.4, 44.0], [95.5, 44.0], [95.6, 44.0], [95.7, 44.0], [95.8, 44.0], [95.9, 44.0], [96.0, 44.0], [96.1, 44.0], [96.2, 44.0], [96.3, 44.0], [96.4, 44.0], [96.5, 44.0], [96.6, 44.0], [96.7, 44.0], [96.8, 44.0], [96.9, 44.0], [97.0, 44.0], [97.1, 44.0], [97.2, 44.0], [97.3, 44.0], [97.4, 44.0], [97.5, 44.0], [97.6, 44.0], [97.7, 44.0], [97.8, 44.0], [97.9, 44.0], [98.0, 44.0], [98.1, 44.0], [98.2, 44.0], [98.3, 44.0], [98.4, 44.0], [98.5, 44.0], [98.6, 44.0], [98.7, 44.0], [98.8, 44.0], [98.9, 44.0], [99.0, 44.0], [99.1, 44.0], [99.2, 44.0], [99.3, 44.0], [99.4, 44.0], [99.5, 44.0], [99.6, 44.0], [99.7, 44.0], [99.8, 44.0], [99.9, 44.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[0.0, 5.0], [0.1, 5.0], [0.2, 5.0], [0.3, 5.0], [0.4, 5.0], [0.5, 5.0], [0.6, 5.0], [0.7, 5.0], [0.8, 5.0], [0.9, 5.0], [1.0, 5.0], [1.1, 5.0], [1.2, 5.0], [1.3, 5.0], [1.4, 5.0], [1.5, 5.0], [1.6, 5.0], [1.7, 5.0], [1.8, 5.0], [1.9, 5.0], [2.0, 5.0], [2.1, 5.0], [2.2, 5.0], [2.3, 5.0], [2.4, 5.0], [2.5, 5.0], [2.6, 5.0], [2.7, 5.0], [2.8, 5.0], [2.9, 5.0], [3.0, 5.0], [3.1, 5.0], [3.2, 5.0], [3.3, 5.0], [3.4, 5.0], [3.5, 5.0], [3.6, 5.0], [3.7, 5.0], [3.8, 5.0], [3.9, 5.0], [4.0, 5.0], [4.1, 5.0], [4.2, 5.0], [4.3, 5.0], [4.4, 5.0], [4.5, 5.0], [4.6, 5.0], [4.7, 5.0], [4.8, 5.0], [4.9, 5.0], [5.0, 5.0], [5.1, 5.0], [5.2, 5.0], [5.3, 5.0], [5.4, 5.0], [5.5, 5.0], [5.6, 5.0], [5.7, 5.0], [5.8, 5.0], [5.9, 5.0], [6.0, 5.0], [6.1, 5.0], [6.2, 5.0], [6.3, 5.0], [6.4, 5.0], [6.5, 5.0], [6.6, 5.0], [6.7, 5.0], [6.8, 5.0], [6.9, 5.0], [7.0, 5.0], [7.1, 5.0], [7.2, 5.0], [7.3, 5.0], [7.4, 5.0], [7.5, 5.0], [7.6, 5.0], [7.7, 5.0], [7.8, 5.0], [7.9, 5.0], [8.0, 5.0], [8.1, 5.0], [8.2, 5.0], [8.3, 5.0], [8.4, 5.0], [8.5, 5.0], [8.6, 5.0], [8.7, 5.0], [8.8, 5.0], [8.9, 5.0], [9.0, 5.0], [9.1, 5.0], [9.2, 5.0], [9.3, 5.0], [9.4, 5.0], [9.5, 5.0], [9.6, 5.0], [9.7, 5.0], [9.8, 5.0], [9.9, 5.0], [10.0, 5.0], [10.1, 5.0], [10.2, 5.0], [10.3, 5.0], [10.4, 5.0], [10.5, 5.0], [10.6, 5.0], [10.7, 5.0], [10.8, 5.0], [10.9, 5.0], [11.0, 5.0], [11.1, 5.0], [11.2, 5.0], [11.3, 5.0], [11.4, 5.0], [11.5, 5.0], [11.6, 5.0], [11.7, 5.0], [11.8, 5.0], [11.9, 5.0], [12.0, 5.0], [12.1, 5.0], [12.2, 5.0], [12.3, 5.0], [12.4, 5.0], [12.5, 5.0], [12.6, 5.0], [12.7, 5.0], [12.8, 5.0], [12.9, 5.0], [13.0, 5.0], [13.1, 5.0], [13.2, 5.0], [13.3, 5.0], [13.4, 5.0], [13.5, 5.0], [13.6, 5.0], [13.7, 5.0], [13.8, 5.0], [13.9, 5.0], [14.0, 5.0], [14.1, 5.0], [14.2, 5.0], [14.3, 5.0], [14.4, 5.0], [14.5, 5.0], [14.6, 5.0], [14.7, 5.0], [14.8, 5.0], [14.9, 5.0], [15.0, 5.0], [15.1, 5.0], [15.2, 5.0], [15.3, 5.0], [15.4, 5.0], [15.5, 5.0], [15.6, 5.0], [15.7, 5.0], [15.8, 5.0], [15.9, 5.0], [16.0, 5.0], [16.1, 5.0], [16.2, 5.0], [16.3, 5.0], [16.4, 5.0], [16.5, 5.0], [16.6, 5.0], [16.7, 5.0], [16.8, 5.0], [16.9, 5.0], [17.0, 5.0], [17.1, 5.0], [17.2, 5.0], [17.3, 5.0], [17.4, 5.0], [17.5, 5.0], [17.6, 5.0], [17.7, 5.0], [17.8, 5.0], [17.9, 5.0], [18.0, 5.0], [18.1, 5.0], [18.2, 5.0], [18.3, 5.0], [18.4, 5.0], [18.5, 5.0], [18.6, 5.0], [18.7, 5.0], [18.8, 5.0], [18.9, 5.0], [19.0, 5.0], [19.1, 5.0], [19.2, 5.0], [19.3, 5.0], [19.4, 5.0], [19.5, 5.0], [19.6, 5.0], [19.7, 5.0], [19.8, 5.0], [19.9, 5.0], [20.0, 5.0], [20.1, 5.0], [20.2, 5.0], [20.3, 5.0], [20.4, 5.0], [20.5, 5.0], [20.6, 5.0], [20.7, 5.0], [20.8, 5.0], [20.9, 5.0], [21.0, 5.0], [21.1, 5.0], [21.2, 5.0], [21.3, 5.0], [21.4, 5.0], [21.5, 5.0], [21.6, 5.0], [21.7, 5.0], [21.8, 5.0], [21.9, 5.0], [22.0, 5.0], [22.1, 5.0], [22.2, 5.0], [22.3, 5.0], [22.4, 5.0], [22.5, 5.0], [22.6, 5.0], [22.7, 5.0], [22.8, 5.0], [22.9, 5.0], [23.0, 5.0], [23.1, 5.0], [23.2, 5.0], [23.3, 5.0], [23.4, 5.0], [23.5, 5.0], [23.6, 5.0], [23.7, 5.0], [23.8, 5.0], [23.9, 5.0], [24.0, 5.0], [24.1, 5.0], [24.2, 5.0], [24.3, 5.0], [24.4, 5.0], [24.5, 5.0], [24.6, 5.0], [24.7, 5.0], [24.8, 5.0], [24.9, 5.0], [25.0, 5.0], [25.1, 5.0], [25.2, 5.0], [25.3, 5.0], [25.4, 5.0], [25.5, 5.0], [25.6, 5.0], [25.7, 5.0], [25.8, 5.0], [25.9, 5.0], [26.0, 5.0], [26.1, 5.0], [26.2, 5.0], [26.3, 5.0], [26.4, 5.0], [26.5, 5.0], [26.6, 5.0], [26.7, 5.0], [26.8, 5.0], [26.9, 5.0], [27.0, 5.0], [27.1, 5.0], [27.2, 5.0], [27.3, 5.0], [27.4, 5.0], [27.5, 5.0], [27.6, 5.0], [27.7, 5.0], [27.8, 5.0], [27.9, 5.0], [28.0, 5.0], [28.1, 5.0], [28.2, 5.0], [28.3, 5.0], [28.4, 5.0], [28.5, 5.0], [28.6, 5.0], [28.7, 5.0], [28.8, 5.0], [28.9, 5.0], [29.0, 5.0], [29.1, 5.0], [29.2, 5.0], [29.3, 5.0], [29.4, 5.0], [29.5, 5.0], [29.6, 5.0], [29.7, 5.0], [29.8, 5.0], [29.9, 5.0], [30.0, 5.0], [30.1, 5.0], [30.2, 5.0], [30.3, 5.0], [30.4, 5.0], [30.5, 5.0], [30.6, 5.0], [30.7, 5.0], [30.8, 5.0], [30.9, 5.0], [31.0, 5.0], [31.1, 5.0], [31.2, 5.0], [31.3, 5.0], [31.4, 5.0], [31.5, 5.0], [31.6, 5.0], [31.7, 5.0], [31.8, 5.0], [31.9, 5.0], [32.0, 5.0], [32.1, 5.0], [32.2, 5.0], [32.3, 5.0], [32.4, 5.0], [32.5, 5.0], [32.6, 5.0], [32.7, 5.0], [32.8, 5.0], [32.9, 5.0], [33.0, 5.0], [33.1, 5.0], [33.2, 5.0], [33.3, 5.0], [33.4, 5.0], [33.5, 5.0], [33.6, 5.0], [33.7, 5.0], [33.8, 5.0], [33.9, 5.0], [34.0, 5.0], [34.1, 5.0], [34.2, 5.0], [34.3, 5.0], [34.4, 5.0], [34.5, 5.0], [34.6, 5.0], [34.7, 5.0], [34.8, 5.0], [34.9, 5.0], [35.0, 5.0], [35.1, 5.0], [35.2, 5.0], [35.3, 5.0], [35.4, 5.0], [35.5, 5.0], [35.6, 5.0], [35.7, 5.0], [35.8, 5.0], [35.9, 5.0], [36.0, 5.0], [36.1, 5.0], [36.2, 5.0], [36.3, 5.0], [36.4, 5.0], [36.5, 5.0], [36.6, 5.0], [36.7, 5.0], [36.8, 5.0], [36.9, 5.0], [37.0, 5.0], [37.1, 5.0], [37.2, 5.0], [37.3, 5.0], [37.4, 5.0], [37.5, 5.0], [37.6, 5.0], [37.7, 5.0], [37.8, 5.0], [37.9, 5.0], [38.0, 5.0], [38.1, 5.0], [38.2, 5.0], [38.3, 5.0], [38.4, 5.0], [38.5, 5.0], [38.6, 5.0], [38.7, 5.0], [38.8, 5.0], [38.9, 5.0], [39.0, 5.0], [39.1, 5.0], [39.2, 5.0], [39.3, 5.0], [39.4, 5.0], [39.5, 5.0], [39.6, 5.0], [39.7, 5.0], [39.8, 5.0], [39.9, 5.0], [40.0, 5.0], [40.1, 5.0], [40.2, 5.0], [40.3, 5.0], [40.4, 5.0], [40.5, 5.0], [40.6, 5.0], [40.7, 5.0], [40.8, 5.0], [40.9, 5.0], [41.0, 5.0], [41.1, 5.0], [41.2, 5.0], [41.3, 5.0], [41.4, 5.0], [41.5, 5.0], [41.6, 5.0], [41.7, 5.0], [41.8, 5.0], [41.9, 5.0], [42.0, 5.0], [42.1, 5.0], [42.2, 5.0], [42.3, 5.0], [42.4, 5.0], [42.5, 5.0], [42.6, 5.0], [42.7, 5.0], [42.8, 5.0], [42.9, 5.0], [43.0, 5.0], [43.1, 5.0], [43.2, 5.0], [43.3, 5.0], [43.4, 5.0], [43.5, 5.0], [43.6, 5.0], [43.7, 5.0], [43.8, 5.0], [43.9, 5.0], [44.0, 5.0], [44.1, 5.0], [44.2, 5.0], [44.3, 5.0], [44.4, 5.0], [44.5, 5.0], [44.6, 5.0], [44.7, 5.0], [44.8, 5.0], [44.9, 5.0], [45.0, 5.0], [45.1, 5.0], [45.2, 5.0], [45.3, 5.0], [45.4, 5.0], [45.5, 5.0], [45.6, 5.0], [45.7, 5.0], [45.8, 5.0], [45.9, 5.0], [46.0, 5.0], [46.1, 5.0], [46.2, 5.0], [46.3, 5.0], [46.4, 5.0], [46.5, 5.0], [46.6, 5.0], [46.7, 5.0], [46.8, 5.0], [46.9, 5.0], [47.0, 5.0], [47.1, 5.0], [47.2, 5.0], [47.3, 5.0], [47.4, 5.0], [47.5, 5.0], [47.6, 5.0], [47.7, 5.0], [47.8, 5.0], [47.9, 5.0], [48.0, 5.0], [48.1, 5.0], [48.2, 5.0], [48.3, 5.0], [48.4, 5.0], [48.5, 5.0], [48.6, 5.0], [48.7, 5.0], [48.8, 5.0], [48.9, 5.0], [49.0, 5.0], [49.1, 5.0], [49.2, 5.0], [49.3, 5.0], [49.4, 5.0], [49.5, 5.0], [49.6, 5.0], [49.7, 5.0], [49.8, 5.0], [49.9, 5.0], [50.0, 5.0], [50.1, 5.0], [50.2, 5.0], [50.3, 5.0], [50.4, 5.0], [50.5, 5.0], [50.6, 5.0], [50.7, 5.0], [50.8, 5.0], [50.9, 5.0], [51.0, 5.0], [51.1, 5.0], [51.2, 5.0], [51.3, 5.0], [51.4, 5.0], [51.5, 5.0], [51.6, 5.0], [51.7, 5.0], [51.8, 5.0], [51.9, 5.0], [52.0, 5.0], [52.1, 5.0], [52.2, 5.0], [52.3, 5.0], [52.4, 5.0], [52.5, 5.0], [52.6, 5.0], [52.7, 5.0], [52.8, 5.0], [52.9, 5.0], [53.0, 5.0], [53.1, 5.0], [53.2, 5.0], [53.3, 5.0], [53.4, 5.0], [53.5, 5.0], [53.6, 5.0], [53.7, 5.0], [53.8, 5.0], [53.9, 5.0], [54.0, 5.0], [54.1, 5.0], [54.2, 5.0], [54.3, 5.0], [54.4, 5.0], [54.5, 5.0], [54.6, 5.0], [54.7, 5.0], [54.8, 5.0], [54.9, 5.0], [55.0, 5.0], [55.1, 5.0], [55.2, 5.0], [55.3, 5.0], [55.4, 5.0], [55.5, 5.0], [55.6, 5.0], [55.7, 5.0], [55.8, 5.0], [55.9, 5.0], [56.0, 5.0], [56.1, 5.0], [56.2, 5.0], [56.3, 5.0], [56.4, 5.0], [56.5, 5.0], [56.6, 5.0], [56.7, 5.0], [56.8, 5.0], [56.9, 5.0], [57.0, 5.0], [57.1, 5.0], [57.2, 5.0], [57.3, 5.0], [57.4, 5.0], [57.5, 5.0], [57.6, 5.0], [57.7, 5.0], [57.8, 5.0], [57.9, 5.0], [58.0, 5.0], [58.1, 5.0], [58.2, 5.0], [58.3, 5.0], [58.4, 5.0], [58.5, 5.0], [58.6, 5.0], [58.7, 5.0], [58.8, 5.0], [58.9, 5.0], [59.0, 5.0], [59.1, 5.0], [59.2, 5.0], [59.3, 5.0], [59.4, 5.0], [59.5, 5.0], [59.6, 5.0], [59.7, 5.0], [59.8, 5.0], [59.9, 5.0], [60.0, 5.0], [60.1, 5.0], [60.2, 5.0], [60.3, 5.0], [60.4, 5.0], [60.5, 5.0], [60.6, 5.0], [60.7, 5.0], [60.8, 5.0], [60.9, 5.0], [61.0, 5.0], [61.1, 5.0], [61.2, 5.0], [61.3, 5.0], [61.4, 5.0], [61.5, 5.0], [61.6, 5.0], [61.7, 5.0], [61.8, 5.0], [61.9, 5.0], [62.0, 5.0], [62.1, 5.0], [62.2, 5.0], [62.3, 5.0], [62.4, 5.0], [62.5, 5.0], [62.6, 5.0], [62.7, 5.0], [62.8, 5.0], [62.9, 5.0], [63.0, 5.0], [63.1, 5.0], [63.2, 5.0], [63.3, 5.0], [63.4, 5.0], [63.5, 5.0], [63.6, 5.0], [63.7, 5.0], [63.8, 5.0], [63.9, 5.0], [64.0, 5.0], [64.1, 5.0], [64.2, 5.0], [64.3, 5.0], [64.4, 5.0], [64.5, 5.0], [64.6, 5.0], [64.7, 5.0], [64.8, 5.0], [64.9, 5.0], [65.0, 5.0], [65.1, 5.0], [65.2, 5.0], [65.3, 5.0], [65.4, 5.0], [65.5, 5.0], [65.6, 5.0], [65.7, 5.0], [65.8, 5.0], [65.9, 5.0], [66.0, 5.0], [66.1, 5.0], [66.2, 5.0], [66.3, 5.0], [66.4, 5.0], [66.5, 5.0], [66.6, 5.0], [66.7, 5.0], [66.8, 5.0], [66.9, 5.0], [67.0, 5.0], [67.1, 5.0], [67.2, 5.0], [67.3, 5.0], [67.4, 5.0], [67.5, 5.0], [67.6, 5.0], [67.7, 5.0], [67.8, 5.0], [67.9, 5.0], [68.0, 5.0], [68.1, 5.0], [68.2, 5.0], [68.3, 5.0], [68.4, 5.0], [68.5, 5.0], [68.6, 5.0], [68.7, 5.0], [68.8, 5.0], [68.9, 5.0], [69.0, 5.0], [69.1, 5.0], [69.2, 5.0], [69.3, 5.0], [69.4, 5.0], [69.5, 5.0], [69.6, 5.0], [69.7, 5.0], [69.8, 5.0], [69.9, 5.0], [70.0, 5.0], [70.1, 5.0], [70.2, 5.0], [70.3, 5.0], [70.4, 5.0], [70.5, 5.0], [70.6, 5.0], [70.7, 5.0], [70.8, 5.0], [70.9, 5.0], [71.0, 5.0], [71.1, 5.0], [71.2, 5.0], [71.3, 5.0], [71.4, 5.0], [71.5, 5.0], [71.6, 5.0], [71.7, 5.0], [71.8, 5.0], [71.9, 5.0], [72.0, 5.0], [72.1, 5.0], [72.2, 5.0], [72.3, 5.0], [72.4, 5.0], [72.5, 5.0], [72.6, 5.0], [72.7, 5.0], [72.8, 5.0], [72.9, 5.0], [73.0, 5.0], [73.1, 5.0], [73.2, 5.0], [73.3, 5.0], [73.4, 5.0], [73.5, 5.0], [73.6, 5.0], [73.7, 5.0], [73.8, 5.0], [73.9, 5.0], [74.0, 5.0], [74.1, 5.0], [74.2, 5.0], [74.3, 5.0], [74.4, 5.0], [74.5, 5.0], [74.6, 5.0], [74.7, 5.0], [74.8, 5.0], [74.9, 5.0], [75.0, 5.0], [75.1, 5.0], [75.2, 5.0], [75.3, 5.0], [75.4, 5.0], [75.5, 5.0], [75.6, 5.0], [75.7, 5.0], [75.8, 5.0], [75.9, 5.0], [76.0, 5.0], [76.1, 5.0], [76.2, 5.0], [76.3, 5.0], [76.4, 5.0], [76.5, 5.0], [76.6, 5.0], [76.7, 5.0], [76.8, 5.0], [76.9, 5.0], [77.0, 5.0], [77.1, 5.0], [77.2, 5.0], [77.3, 5.0], [77.4, 5.0], [77.5, 5.0], [77.6, 5.0], [77.7, 5.0], [77.8, 5.0], [77.9, 5.0], [78.0, 5.0], [78.1, 5.0], [78.2, 5.0], [78.3, 5.0], [78.4, 5.0], [78.5, 5.0], [78.6, 5.0], [78.7, 5.0], [78.8, 5.0], [78.9, 5.0], [79.0, 5.0], [79.1, 5.0], [79.2, 5.0], [79.3, 5.0], [79.4, 5.0], [79.5, 5.0], [79.6, 5.0], [79.7, 5.0], [79.8, 5.0], [79.9, 5.0], [80.0, 5.0], [80.1, 5.0], [80.2, 5.0], [80.3, 5.0], [80.4, 5.0], [80.5, 5.0], [80.6, 5.0], [80.7, 5.0], [80.8, 5.0], [80.9, 5.0], [81.0, 5.0], [81.1, 5.0], [81.2, 5.0], [81.3, 5.0], [81.4, 5.0], [81.5, 5.0], [81.6, 5.0], [81.7, 5.0], [81.8, 5.0], [81.9, 5.0], [82.0, 5.0], [82.1, 5.0], [82.2, 5.0], [82.3, 5.0], [82.4, 5.0], [82.5, 5.0], [82.6, 5.0], [82.7, 5.0], [82.8, 5.0], [82.9, 5.0], [83.0, 5.0], [83.1, 5.0], [83.2, 5.0], [83.3, 5.0], [83.4, 5.0], [83.5, 5.0], [83.6, 5.0], [83.7, 5.0], [83.8, 5.0], [83.9, 5.0], [84.0, 5.0], [84.1, 5.0], [84.2, 5.0], [84.3, 5.0], [84.4, 5.0], [84.5, 5.0], [84.6, 5.0], [84.7, 5.0], [84.8, 5.0], [84.9, 5.0], [85.0, 5.0], [85.1, 5.0], [85.2, 5.0], [85.3, 5.0], [85.4, 5.0], [85.5, 5.0], [85.6, 5.0], [85.7, 5.0], [85.8, 5.0], [85.9, 5.0], [86.0, 5.0], [86.1, 5.0], [86.2, 5.0], [86.3, 5.0], [86.4, 5.0], [86.5, 5.0], [86.6, 5.0], [86.7, 5.0], [86.8, 5.0], [86.9, 5.0], [87.0, 5.0], [87.1, 5.0], [87.2, 5.0], [87.3, 5.0], [87.4, 5.0], [87.5, 5.0], [87.6, 5.0], [87.7, 5.0], [87.8, 5.0], [87.9, 5.0], [88.0, 5.0], [88.1, 5.0], [88.2, 5.0], [88.3, 5.0], [88.4, 5.0], [88.5, 5.0], [88.6, 5.0], [88.7, 5.0], [88.8, 5.0], [88.9, 5.0], [89.0, 5.0], [89.1, 5.0], [89.2, 5.0], [89.3, 5.0], [89.4, 5.0], [89.5, 5.0], [89.6, 5.0], [89.7, 5.0], [89.8, 5.0], [89.9, 5.0], [90.0, 5.0], [90.1, 5.0], [90.2, 5.0], [90.3, 5.0], [90.4, 5.0], [90.5, 5.0], [90.6, 5.0], [90.7, 5.0], [90.8, 5.0], [90.9, 5.0], [91.0, 5.0], [91.1, 5.0], [91.2, 5.0], [91.3, 5.0], [91.4, 5.0], [91.5, 5.0], [91.6, 5.0], [91.7, 5.0], [91.8, 5.0], [91.9, 5.0], [92.0, 5.0], [92.1, 5.0], [92.2, 5.0], [92.3, 5.0], [92.4, 5.0], [92.5, 5.0], [92.6, 5.0], [92.7, 5.0], [92.8, 5.0], [92.9, 5.0], [93.0, 5.0], [93.1, 5.0], [93.2, 5.0], [93.3, 5.0], [93.4, 5.0], [93.5, 5.0], [93.6, 5.0], [93.7, 5.0], [93.8, 5.0], [93.9, 5.0], [94.0, 5.0], [94.1, 5.0], [94.2, 5.0], [94.3, 5.0], [94.4, 5.0], [94.5, 5.0], [94.6, 5.0], [94.7, 5.0], [94.8, 5.0], [94.9, 5.0], [95.0, 5.0], [95.1, 5.0], [95.2, 5.0], [95.3, 5.0], [95.4, 5.0], [95.5, 5.0], [95.6, 5.0], [95.7, 5.0], [95.8, 5.0], [95.9, 5.0], [96.0, 5.0], [96.1, 5.0], [96.2, 5.0], [96.3, 5.0], [96.4, 5.0], [96.5, 5.0], [96.6, 5.0], [96.7, 5.0], [96.8, 5.0], [96.9, 5.0], [97.0, 5.0], [97.1, 5.0], [97.2, 5.0], [97.3, 5.0], [97.4, 5.0], [97.5, 5.0], [97.6, 5.0], [97.7, 5.0], [97.8, 5.0], [97.9, 5.0], [98.0, 5.0], [98.1, 5.0], [98.2, 5.0], [98.3, 5.0], [98.4, 5.0], [98.5, 5.0], [98.6, 5.0], [98.7, 5.0], [98.8, 5.0], [98.9, 5.0], [99.0, 5.0], [99.1, 5.0], [99.2, 5.0], [99.3, 5.0], [99.4, 5.0], [99.5, 5.0], [99.6, 5.0], [99.7, 5.0], [99.8, 5.0], [99.9, 5.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[0.0, 5.0], [0.1, 5.0], [0.2, 5.0], [0.3, 5.0], [0.4, 5.0], [0.5, 5.0], [0.6, 5.0], [0.7, 5.0], [0.8, 5.0], [0.9, 5.0], [1.0, 5.0], [1.1, 5.0], [1.2, 5.0], [1.3, 5.0], [1.4, 5.0], [1.5, 5.0], [1.6, 5.0], [1.7, 5.0], [1.8, 5.0], [1.9, 5.0], [2.0, 5.0], [2.1, 5.0], [2.2, 5.0], [2.3, 5.0], [2.4, 5.0], [2.5, 5.0], [2.6, 5.0], [2.7, 5.0], [2.8, 5.0], [2.9, 5.0], [3.0, 5.0], [3.1, 5.0], [3.2, 5.0], [3.3, 5.0], [3.4, 5.0], [3.5, 5.0], [3.6, 5.0], [3.7, 5.0], [3.8, 5.0], [3.9, 5.0], [4.0, 5.0], [4.1, 5.0], [4.2, 5.0], [4.3, 5.0], [4.4, 5.0], [4.5, 5.0], [4.6, 5.0], [4.7, 5.0], [4.8, 5.0], [4.9, 5.0], [5.0, 5.0], [5.1, 5.0], [5.2, 5.0], [5.3, 5.0], [5.4, 5.0], [5.5, 5.0], [5.6, 5.0], [5.7, 5.0], [5.8, 5.0], [5.9, 5.0], [6.0, 5.0], [6.1, 5.0], [6.2, 5.0], [6.3, 5.0], [6.4, 5.0], [6.5, 5.0], [6.6, 5.0], [6.7, 5.0], [6.8, 5.0], [6.9, 5.0], [7.0, 5.0], [7.1, 5.0], [7.2, 5.0], [7.3, 5.0], [7.4, 5.0], [7.5, 5.0], [7.6, 5.0], [7.7, 5.0], [7.8, 5.0], [7.9, 5.0], [8.0, 5.0], [8.1, 5.0], [8.2, 5.0], [8.3, 5.0], [8.4, 5.0], [8.5, 5.0], [8.6, 5.0], [8.7, 5.0], [8.8, 5.0], [8.9, 5.0], [9.0, 5.0], [9.1, 5.0], [9.2, 5.0], [9.3, 5.0], [9.4, 5.0], [9.5, 5.0], [9.6, 5.0], [9.7, 5.0], [9.8, 5.0], [9.9, 5.0], [10.0, 5.0], [10.1, 5.0], [10.2, 5.0], [10.3, 5.0], [10.4, 5.0], [10.5, 5.0], [10.6, 5.0], [10.7, 5.0], [10.8, 5.0], [10.9, 5.0], [11.0, 5.0], [11.1, 5.0], [11.2, 5.0], [11.3, 5.0], [11.4, 5.0], [11.5, 5.0], [11.6, 5.0], [11.7, 5.0], [11.8, 5.0], [11.9, 5.0], [12.0, 5.0], [12.1, 5.0], [12.2, 5.0], [12.3, 5.0], [12.4, 5.0], [12.5, 5.0], [12.6, 5.0], [12.7, 5.0], [12.8, 5.0], [12.9, 5.0], [13.0, 5.0], [13.1, 5.0], [13.2, 5.0], [13.3, 5.0], [13.4, 5.0], [13.5, 5.0], [13.6, 5.0], [13.7, 5.0], [13.8, 5.0], [13.9, 5.0], [14.0, 5.0], [14.1, 5.0], [14.2, 5.0], [14.3, 5.0], [14.4, 5.0], [14.5, 5.0], [14.6, 5.0], [14.7, 5.0], [14.8, 5.0], [14.9, 5.0], [15.0, 5.0], [15.1, 5.0], [15.2, 5.0], [15.3, 5.0], [15.4, 5.0], [15.5, 5.0], [15.6, 5.0], [15.7, 5.0], [15.8, 5.0], [15.9, 5.0], [16.0, 5.0], [16.1, 5.0], [16.2, 5.0], [16.3, 5.0], [16.4, 5.0], [16.5, 5.0], [16.6, 5.0], [16.7, 5.0], [16.8, 5.0], [16.9, 5.0], [17.0, 5.0], [17.1, 5.0], [17.2, 5.0], [17.3, 5.0], [17.4, 5.0], [17.5, 5.0], [17.6, 5.0], [17.7, 5.0], [17.8, 5.0], [17.9, 5.0], [18.0, 5.0], [18.1, 5.0], [18.2, 5.0], [18.3, 5.0], [18.4, 5.0], [18.5, 5.0], [18.6, 5.0], [18.7, 5.0], [18.8, 5.0], [18.9, 5.0], [19.0, 5.0], [19.1, 5.0], [19.2, 5.0], [19.3, 5.0], [19.4, 5.0], [19.5, 5.0], [19.6, 5.0], [19.7, 5.0], [19.8, 5.0], [19.9, 5.0], [20.0, 5.0], [20.1, 5.0], [20.2, 5.0], [20.3, 5.0], [20.4, 5.0], [20.5, 5.0], [20.6, 5.0], [20.7, 5.0], [20.8, 5.0], [20.9, 5.0], [21.0, 5.0], [21.1, 5.0], [21.2, 5.0], [21.3, 5.0], [21.4, 5.0], [21.5, 5.0], [21.6, 5.0], [21.7, 5.0], [21.8, 5.0], [21.9, 5.0], [22.0, 5.0], [22.1, 5.0], [22.2, 5.0], [22.3, 5.0], [22.4, 5.0], [22.5, 5.0], [22.6, 5.0], [22.7, 5.0], [22.8, 5.0], [22.9, 5.0], [23.0, 5.0], [23.1, 5.0], [23.2, 5.0], [23.3, 5.0], [23.4, 5.0], [23.5, 5.0], [23.6, 5.0], [23.7, 5.0], [23.8, 5.0], [23.9, 5.0], [24.0, 5.0], [24.1, 5.0], [24.2, 5.0], [24.3, 5.0], [24.4, 5.0], [24.5, 5.0], [24.6, 5.0], [24.7, 5.0], [24.8, 5.0], [24.9, 5.0], [25.0, 5.0], [25.1, 5.0], [25.2, 5.0], [25.3, 5.0], [25.4, 5.0], [25.5, 5.0], [25.6, 5.0], [25.7, 5.0], [25.8, 5.0], [25.9, 5.0], [26.0, 5.0], [26.1, 5.0], [26.2, 5.0], [26.3, 5.0], [26.4, 5.0], [26.5, 5.0], [26.6, 5.0], [26.7, 5.0], [26.8, 5.0], [26.9, 5.0], [27.0, 5.0], [27.1, 5.0], [27.2, 5.0], [27.3, 5.0], [27.4, 5.0], [27.5, 5.0], [27.6, 5.0], [27.7, 5.0], [27.8, 5.0], [27.9, 5.0], [28.0, 5.0], [28.1, 5.0], [28.2, 5.0], [28.3, 5.0], [28.4, 5.0], [28.5, 5.0], [28.6, 5.0], [28.7, 5.0], [28.8, 5.0], [28.9, 5.0], [29.0, 5.0], [29.1, 5.0], [29.2, 5.0], [29.3, 5.0], [29.4, 5.0], [29.5, 5.0], [29.6, 5.0], [29.7, 5.0], [29.8, 5.0], [29.9, 5.0], [30.0, 5.0], [30.1, 5.0], [30.2, 5.0], [30.3, 5.0], [30.4, 5.0], [30.5, 5.0], [30.6, 5.0], [30.7, 5.0], [30.8, 5.0], [30.9, 5.0], [31.0, 5.0], [31.1, 5.0], [31.2, 5.0], [31.3, 5.0], [31.4, 5.0], [31.5, 5.0], [31.6, 5.0], [31.7, 5.0], [31.8, 5.0], [31.9, 5.0], [32.0, 5.0], [32.1, 5.0], [32.2, 5.0], [32.3, 5.0], [32.4, 5.0], [32.5, 5.0], [32.6, 5.0], [32.7, 5.0], [32.8, 5.0], [32.9, 5.0], [33.0, 5.0], [33.1, 5.0], [33.2, 5.0], [33.3, 5.0], [33.4, 5.0], [33.5, 5.0], [33.6, 5.0], [33.7, 5.0], [33.8, 5.0], [33.9, 5.0], [34.0, 5.0], [34.1, 5.0], [34.2, 5.0], [34.3, 5.0], [34.4, 5.0], [34.5, 5.0], [34.6, 5.0], [34.7, 5.0], [34.8, 5.0], [34.9, 5.0], [35.0, 5.0], [35.1, 5.0], [35.2, 5.0], [35.3, 5.0], [35.4, 5.0], [35.5, 5.0], [35.6, 5.0], [35.7, 5.0], [35.8, 5.0], [35.9, 5.0], [36.0, 5.0], [36.1, 5.0], [36.2, 5.0], [36.3, 5.0], [36.4, 5.0], [36.5, 5.0], [36.6, 5.0], [36.7, 5.0], [36.8, 5.0], [36.9, 5.0], [37.0, 5.0], [37.1, 5.0], [37.2, 5.0], [37.3, 5.0], [37.4, 5.0], [37.5, 5.0], [37.6, 5.0], [37.7, 5.0], [37.8, 5.0], [37.9, 5.0], [38.0, 5.0], [38.1, 5.0], [38.2, 5.0], [38.3, 5.0], [38.4, 5.0], [38.5, 5.0], [38.6, 5.0], [38.7, 5.0], [38.8, 5.0], [38.9, 5.0], [39.0, 5.0], [39.1, 5.0], [39.2, 5.0], [39.3, 5.0], [39.4, 5.0], [39.5, 5.0], [39.6, 5.0], [39.7, 5.0], [39.8, 5.0], [39.9, 5.0], [40.0, 5.0], [40.1, 5.0], [40.2, 5.0], [40.3, 5.0], [40.4, 5.0], [40.5, 5.0], [40.6, 5.0], [40.7, 5.0], [40.8, 5.0], [40.9, 5.0], [41.0, 5.0], [41.1, 5.0], [41.2, 5.0], [41.3, 5.0], [41.4, 5.0], [41.5, 5.0], [41.6, 5.0], [41.7, 5.0], [41.8, 5.0], [41.9, 5.0], [42.0, 5.0], [42.1, 5.0], [42.2, 5.0], [42.3, 5.0], [42.4, 5.0], [42.5, 5.0], [42.6, 5.0], [42.7, 5.0], [42.8, 5.0], [42.9, 5.0], [43.0, 5.0], [43.1, 5.0], [43.2, 5.0], [43.3, 5.0], [43.4, 5.0], [43.5, 5.0], [43.6, 5.0], [43.7, 5.0], [43.8, 5.0], [43.9, 5.0], [44.0, 5.0], [44.1, 5.0], [44.2, 5.0], [44.3, 5.0], [44.4, 5.0], [44.5, 5.0], [44.6, 5.0], [44.7, 5.0], [44.8, 5.0], [44.9, 5.0], [45.0, 5.0], [45.1, 5.0], [45.2, 5.0], [45.3, 5.0], [45.4, 5.0], [45.5, 5.0], [45.6, 5.0], [45.7, 5.0], [45.8, 5.0], [45.9, 5.0], [46.0, 5.0], [46.1, 5.0], [46.2, 5.0], [46.3, 5.0], [46.4, 5.0], [46.5, 5.0], [46.6, 5.0], [46.7, 5.0], [46.8, 5.0], [46.9, 5.0], [47.0, 5.0], [47.1, 5.0], [47.2, 5.0], [47.3, 5.0], [47.4, 5.0], [47.5, 5.0], [47.6, 5.0], [47.7, 5.0], [47.8, 5.0], [47.9, 5.0], [48.0, 5.0], [48.1, 5.0], [48.2, 5.0], [48.3, 5.0], [48.4, 5.0], [48.5, 5.0], [48.6, 5.0], [48.7, 5.0], [48.8, 5.0], [48.9, 5.0], [49.0, 5.0], [49.1, 5.0], [49.2, 5.0], [49.3, 5.0], [49.4, 5.0], [49.5, 5.0], [49.6, 5.0], [49.7, 5.0], [49.8, 5.0], [49.9, 5.0], [50.0, 5.0], [50.1, 5.0], [50.2, 5.0], [50.3, 5.0], [50.4, 5.0], [50.5, 5.0], [50.6, 5.0], [50.7, 5.0], [50.8, 5.0], [50.9, 5.0], [51.0, 5.0], [51.1, 5.0], [51.2, 5.0], [51.3, 5.0], [51.4, 5.0], [51.5, 5.0], [51.6, 5.0], [51.7, 5.0], [51.8, 5.0], [51.9, 5.0], [52.0, 5.0], [52.1, 5.0], [52.2, 5.0], [52.3, 5.0], [52.4, 5.0], [52.5, 5.0], [52.6, 5.0], [52.7, 5.0], [52.8, 5.0], [52.9, 5.0], [53.0, 5.0], [53.1, 5.0], [53.2, 5.0], [53.3, 5.0], [53.4, 5.0], [53.5, 5.0], [53.6, 5.0], [53.7, 5.0], [53.8, 5.0], [53.9, 5.0], [54.0, 5.0], [54.1, 5.0], [54.2, 5.0], [54.3, 5.0], [54.4, 5.0], [54.5, 5.0], [54.6, 5.0], [54.7, 5.0], [54.8, 5.0], [54.9, 5.0], [55.0, 5.0], [55.1, 5.0], [55.2, 5.0], [55.3, 5.0], [55.4, 5.0], [55.5, 5.0], [55.6, 5.0], [55.7, 5.0], [55.8, 5.0], [55.9, 5.0], [56.0, 5.0], [56.1, 5.0], [56.2, 5.0], [56.3, 5.0], [56.4, 5.0], [56.5, 5.0], [56.6, 5.0], [56.7, 5.0], [56.8, 5.0], [56.9, 5.0], [57.0, 5.0], [57.1, 5.0], [57.2, 5.0], [57.3, 5.0], [57.4, 5.0], [57.5, 5.0], [57.6, 5.0], [57.7, 5.0], [57.8, 5.0], [57.9, 5.0], [58.0, 5.0], [58.1, 5.0], [58.2, 5.0], [58.3, 5.0], [58.4, 5.0], [58.5, 5.0], [58.6, 5.0], [58.7, 5.0], [58.8, 5.0], [58.9, 5.0], [59.0, 5.0], [59.1, 5.0], [59.2, 5.0], [59.3, 5.0], [59.4, 5.0], [59.5, 5.0], [59.6, 5.0], [59.7, 5.0], [59.8, 5.0], [59.9, 5.0], [60.0, 5.0], [60.1, 5.0], [60.2, 5.0], [60.3, 5.0], [60.4, 5.0], [60.5, 5.0], [60.6, 5.0], [60.7, 5.0], [60.8, 5.0], [60.9, 5.0], [61.0, 5.0], [61.1, 5.0], [61.2, 5.0], [61.3, 5.0], [61.4, 5.0], [61.5, 5.0], [61.6, 5.0], [61.7, 5.0], [61.8, 5.0], [61.9, 5.0], [62.0, 5.0], [62.1, 5.0], [62.2, 5.0], [62.3, 5.0], [62.4, 5.0], [62.5, 5.0], [62.6, 5.0], [62.7, 5.0], [62.8, 5.0], [62.9, 5.0], [63.0, 5.0], [63.1, 5.0], [63.2, 5.0], [63.3, 5.0], [63.4, 5.0], [63.5, 5.0], [63.6, 5.0], [63.7, 5.0], [63.8, 5.0], [63.9, 5.0], [64.0, 5.0], [64.1, 5.0], [64.2, 5.0], [64.3, 5.0], [64.4, 5.0], [64.5, 5.0], [64.6, 5.0], [64.7, 5.0], [64.8, 5.0], [64.9, 5.0], [65.0, 5.0], [65.1, 5.0], [65.2, 5.0], [65.3, 5.0], [65.4, 5.0], [65.5, 5.0], [65.6, 5.0], [65.7, 5.0], [65.8, 5.0], [65.9, 5.0], [66.0, 5.0], [66.1, 5.0], [66.2, 5.0], [66.3, 5.0], [66.4, 5.0], [66.5, 5.0], [66.6, 5.0], [66.7, 5.0], [66.8, 5.0], [66.9, 5.0], [67.0, 5.0], [67.1, 5.0], [67.2, 5.0], [67.3, 5.0], [67.4, 5.0], [67.5, 5.0], [67.6, 5.0], [67.7, 5.0], [67.8, 5.0], [67.9, 5.0], [68.0, 5.0], [68.1, 5.0], [68.2, 5.0], [68.3, 5.0], [68.4, 5.0], [68.5, 5.0], [68.6, 5.0], [68.7, 5.0], [68.8, 5.0], [68.9, 5.0], [69.0, 5.0], [69.1, 5.0], [69.2, 5.0], [69.3, 5.0], [69.4, 5.0], [69.5, 5.0], [69.6, 5.0], [69.7, 5.0], [69.8, 5.0], [69.9, 5.0], [70.0, 5.0], [70.1, 5.0], [70.2, 5.0], [70.3, 5.0], [70.4, 5.0], [70.5, 5.0], [70.6, 5.0], [70.7, 5.0], [70.8, 5.0], [70.9, 5.0], [71.0, 5.0], [71.1, 5.0], [71.2, 5.0], [71.3, 5.0], [71.4, 5.0], [71.5, 5.0], [71.6, 5.0], [71.7, 5.0], [71.8, 5.0], [71.9, 5.0], [72.0, 5.0], [72.1, 5.0], [72.2, 5.0], [72.3, 5.0], [72.4, 5.0], [72.5, 5.0], [72.6, 5.0], [72.7, 5.0], [72.8, 5.0], [72.9, 5.0], [73.0, 5.0], [73.1, 5.0], [73.2, 5.0], [73.3, 5.0], [73.4, 5.0], [73.5, 5.0], [73.6, 5.0], [73.7, 5.0], [73.8, 5.0], [73.9, 5.0], [74.0, 5.0], [74.1, 5.0], [74.2, 5.0], [74.3, 5.0], [74.4, 5.0], [74.5, 5.0], [74.6, 5.0], [74.7, 5.0], [74.8, 5.0], [74.9, 5.0], [75.0, 5.0], [75.1, 5.0], [75.2, 5.0], [75.3, 5.0], [75.4, 5.0], [75.5, 5.0], [75.6, 5.0], [75.7, 5.0], [75.8, 5.0], [75.9, 5.0], [76.0, 5.0], [76.1, 5.0], [76.2, 5.0], [76.3, 5.0], [76.4, 5.0], [76.5, 5.0], [76.6, 5.0], [76.7, 5.0], [76.8, 5.0], [76.9, 5.0], [77.0, 5.0], [77.1, 5.0], [77.2, 5.0], [77.3, 5.0], [77.4, 5.0], [77.5, 5.0], [77.6, 5.0], [77.7, 5.0], [77.8, 5.0], [77.9, 5.0], [78.0, 5.0], [78.1, 5.0], [78.2, 5.0], [78.3, 5.0], [78.4, 5.0], [78.5, 5.0], [78.6, 5.0], [78.7, 5.0], [78.8, 5.0], [78.9, 5.0], [79.0, 5.0], [79.1, 5.0], [79.2, 5.0], [79.3, 5.0], [79.4, 5.0], [79.5, 5.0], [79.6, 5.0], [79.7, 5.0], [79.8, 5.0], [79.9, 5.0], [80.0, 5.0], [80.1, 5.0], [80.2, 5.0], [80.3, 5.0], [80.4, 5.0], [80.5, 5.0], [80.6, 5.0], [80.7, 5.0], [80.8, 5.0], [80.9, 5.0], [81.0, 5.0], [81.1, 5.0], [81.2, 5.0], [81.3, 5.0], [81.4, 5.0], [81.5, 5.0], [81.6, 5.0], [81.7, 5.0], [81.8, 5.0], [81.9, 5.0], [82.0, 5.0], [82.1, 5.0], [82.2, 5.0], [82.3, 5.0], [82.4, 5.0], [82.5, 5.0], [82.6, 5.0], [82.7, 5.0], [82.8, 5.0], [82.9, 5.0], [83.0, 5.0], [83.1, 5.0], [83.2, 5.0], [83.3, 5.0], [83.4, 5.0], [83.5, 5.0], [83.6, 5.0], [83.7, 5.0], [83.8, 5.0], [83.9, 5.0], [84.0, 5.0], [84.1, 5.0], [84.2, 5.0], [84.3, 5.0], [84.4, 5.0], [84.5, 5.0], [84.6, 5.0], [84.7, 5.0], [84.8, 5.0], [84.9, 5.0], [85.0, 5.0], [85.1, 5.0], [85.2, 5.0], [85.3, 5.0], [85.4, 5.0], [85.5, 5.0], [85.6, 5.0], [85.7, 5.0], [85.8, 5.0], [85.9, 5.0], [86.0, 5.0], [86.1, 5.0], [86.2, 5.0], [86.3, 5.0], [86.4, 5.0], [86.5, 5.0], [86.6, 5.0], [86.7, 5.0], [86.8, 5.0], [86.9, 5.0], [87.0, 5.0], [87.1, 5.0], [87.2, 5.0], [87.3, 5.0], [87.4, 5.0], [87.5, 5.0], [87.6, 5.0], [87.7, 5.0], [87.8, 5.0], [87.9, 5.0], [88.0, 5.0], [88.1, 5.0], [88.2, 5.0], [88.3, 5.0], [88.4, 5.0], [88.5, 5.0], [88.6, 5.0], [88.7, 5.0], [88.8, 5.0], [88.9, 5.0], [89.0, 5.0], [89.1, 5.0], [89.2, 5.0], [89.3, 5.0], [89.4, 5.0], [89.5, 5.0], [89.6, 5.0], [89.7, 5.0], [89.8, 5.0], [89.9, 5.0], [90.0, 5.0], [90.1, 5.0], [90.2, 5.0], [90.3, 5.0], [90.4, 5.0], [90.5, 5.0], [90.6, 5.0], [90.7, 5.0], [90.8, 5.0], [90.9, 5.0], [91.0, 5.0], [91.1, 5.0], [91.2, 5.0], [91.3, 5.0], [91.4, 5.0], [91.5, 5.0], [91.6, 5.0], [91.7, 5.0], [91.8, 5.0], [91.9, 5.0], [92.0, 5.0], [92.1, 5.0], [92.2, 5.0], [92.3, 5.0], [92.4, 5.0], [92.5, 5.0], [92.6, 5.0], [92.7, 5.0], [92.8, 5.0], [92.9, 5.0], [93.0, 5.0], [93.1, 5.0], [93.2, 5.0], [93.3, 5.0], [93.4, 5.0], [93.5, 5.0], [93.6, 5.0], [93.7, 5.0], [93.8, 5.0], [93.9, 5.0], [94.0, 5.0], [94.1, 5.0], [94.2, 5.0], [94.3, 5.0], [94.4, 5.0], [94.5, 5.0], [94.6, 5.0], [94.7, 5.0], [94.8, 5.0], [94.9, 5.0], [95.0, 5.0], [95.1, 5.0], [95.2, 5.0], [95.3, 5.0], [95.4, 5.0], [95.5, 5.0], [95.6, 5.0], [95.7, 5.0], [95.8, 5.0], [95.9, 5.0], [96.0, 5.0], [96.1, 5.0], [96.2, 5.0], [96.3, 5.0], [96.4, 5.0], [96.5, 5.0], [96.6, 5.0], [96.7, 5.0], [96.8, 5.0], [96.9, 5.0], [97.0, 5.0], [97.1, 5.0], [97.2, 5.0], [97.3, 5.0], [97.4, 5.0], [97.5, 5.0], [97.6, 5.0], [97.7, 5.0], [97.8, 5.0], [97.9, 5.0], [98.0, 5.0], [98.1, 5.0], [98.2, 5.0], [98.3, 5.0], [98.4, 5.0], [98.5, 5.0], [98.6, 5.0], [98.7, 5.0], [98.8, 5.0], [98.9, 5.0], [99.0, 5.0], [99.1, 5.0], [99.2, 5.0], [99.3, 5.0], [99.4, 5.0], [99.5, 5.0], [99.6, 5.0], [99.7, 5.0], [99.8, 5.0], [99.9, 5.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[0.0, 5.0], [0.1, 5.0], [0.2, 5.0], [0.3, 5.0], [0.4, 5.0], [0.5, 5.0], [0.6, 5.0], [0.7, 5.0], [0.8, 5.0], [0.9, 5.0], [1.0, 5.0], [1.1, 5.0], [1.2, 5.0], [1.3, 5.0], [1.4, 5.0], [1.5, 5.0], [1.6, 5.0], [1.7, 5.0], [1.8, 5.0], [1.9, 5.0], [2.0, 5.0], [2.1, 5.0], [2.2, 5.0], [2.3, 5.0], [2.4, 5.0], [2.5, 5.0], [2.6, 5.0], [2.7, 5.0], [2.8, 5.0], [2.9, 5.0], [3.0, 5.0], [3.1, 5.0], [3.2, 5.0], [3.3, 5.0], [3.4, 5.0], [3.5, 5.0], [3.6, 5.0], [3.7, 5.0], [3.8, 5.0], [3.9, 5.0], [4.0, 5.0], [4.1, 5.0], [4.2, 5.0], [4.3, 5.0], [4.4, 5.0], [4.5, 5.0], [4.6, 5.0], [4.7, 5.0], [4.8, 5.0], [4.9, 5.0], [5.0, 5.0], [5.1, 5.0], [5.2, 5.0], [5.3, 5.0], [5.4, 5.0], [5.5, 5.0], [5.6, 5.0], [5.7, 5.0], [5.8, 5.0], [5.9, 5.0], [6.0, 5.0], [6.1, 5.0], [6.2, 5.0], [6.3, 5.0], [6.4, 5.0], [6.5, 5.0], [6.6, 5.0], [6.7, 5.0], [6.8, 5.0], [6.9, 5.0], [7.0, 5.0], [7.1, 5.0], [7.2, 5.0], [7.3, 5.0], [7.4, 5.0], [7.5, 5.0], [7.6, 5.0], [7.7, 5.0], [7.8, 5.0], [7.9, 5.0], [8.0, 5.0], [8.1, 5.0], [8.2, 5.0], [8.3, 5.0], [8.4, 5.0], [8.5, 5.0], [8.6, 5.0], [8.7, 5.0], [8.8, 5.0], [8.9, 5.0], [9.0, 5.0], [9.1, 5.0], [9.2, 5.0], [9.3, 5.0], [9.4, 5.0], [9.5, 5.0], [9.6, 5.0], [9.7, 5.0], [9.8, 5.0], [9.9, 5.0], [10.0, 5.0], [10.1, 5.0], [10.2, 5.0], [10.3, 5.0], [10.4, 5.0], [10.5, 5.0], [10.6, 5.0], [10.7, 5.0], [10.8, 5.0], [10.9, 5.0], [11.0, 5.0], [11.1, 5.0], [11.2, 5.0], [11.3, 5.0], [11.4, 5.0], [11.5, 5.0], [11.6, 5.0], [11.7, 5.0], [11.8, 5.0], [11.9, 5.0], [12.0, 5.0], [12.1, 5.0], [12.2, 5.0], [12.3, 5.0], [12.4, 5.0], [12.5, 5.0], [12.6, 5.0], [12.7, 5.0], [12.8, 5.0], [12.9, 5.0], [13.0, 5.0], [13.1, 5.0], [13.2, 5.0], [13.3, 5.0], [13.4, 5.0], [13.5, 5.0], [13.6, 5.0], [13.7, 5.0], [13.8, 5.0], [13.9, 5.0], [14.0, 5.0], [14.1, 5.0], [14.2, 5.0], [14.3, 5.0], [14.4, 5.0], [14.5, 5.0], [14.6, 5.0], [14.7, 5.0], [14.8, 5.0], [14.9, 5.0], [15.0, 5.0], [15.1, 5.0], [15.2, 5.0], [15.3, 5.0], [15.4, 5.0], [15.5, 5.0], [15.6, 5.0], [15.7, 5.0], [15.8, 5.0], [15.9, 5.0], [16.0, 5.0], [16.1, 5.0], [16.2, 5.0], [16.3, 5.0], [16.4, 5.0], [16.5, 5.0], [16.6, 5.0], [16.7, 5.0], [16.8, 5.0], [16.9, 5.0], [17.0, 5.0], [17.1, 5.0], [17.2, 5.0], [17.3, 5.0], [17.4, 5.0], [17.5, 5.0], [17.6, 5.0], [17.7, 5.0], [17.8, 5.0], [17.9, 5.0], [18.0, 5.0], [18.1, 5.0], [18.2, 5.0], [18.3, 5.0], [18.4, 5.0], [18.5, 5.0], [18.6, 5.0], [18.7, 5.0], [18.8, 5.0], [18.9, 5.0], [19.0, 5.0], [19.1, 5.0], [19.2, 5.0], [19.3, 5.0], [19.4, 5.0], [19.5, 5.0], [19.6, 5.0], [19.7, 5.0], [19.8, 5.0], [19.9, 5.0], [20.0, 5.0], [20.1, 5.0], [20.2, 5.0], [20.3, 5.0], [20.4, 5.0], [20.5, 5.0], [20.6, 5.0], [20.7, 5.0], [20.8, 5.0], [20.9, 5.0], [21.0, 5.0], [21.1, 5.0], [21.2, 5.0], [21.3, 5.0], [21.4, 5.0], [21.5, 5.0], [21.6, 5.0], [21.7, 5.0], [21.8, 5.0], [21.9, 5.0], [22.0, 5.0], [22.1, 5.0], [22.2, 5.0], [22.3, 5.0], [22.4, 5.0], [22.5, 5.0], [22.6, 5.0], [22.7, 5.0], [22.8, 5.0], [22.9, 5.0], [23.0, 5.0], [23.1, 5.0], [23.2, 5.0], [23.3, 5.0], [23.4, 5.0], [23.5, 5.0], [23.6, 5.0], [23.7, 5.0], [23.8, 5.0], [23.9, 5.0], [24.0, 5.0], [24.1, 5.0], [24.2, 5.0], [24.3, 5.0], [24.4, 5.0], [24.5, 5.0], [24.6, 5.0], [24.7, 5.0], [24.8, 5.0], [24.9, 5.0], [25.0, 5.0], [25.1, 5.0], [25.2, 5.0], [25.3, 5.0], [25.4, 5.0], [25.5, 5.0], [25.6, 5.0], [25.7, 5.0], [25.8, 5.0], [25.9, 5.0], [26.0, 5.0], [26.1, 5.0], [26.2, 5.0], [26.3, 5.0], [26.4, 5.0], [26.5, 5.0], [26.6, 5.0], [26.7, 5.0], [26.8, 5.0], [26.9, 5.0], [27.0, 5.0], [27.1, 5.0], [27.2, 5.0], [27.3, 5.0], [27.4, 5.0], [27.5, 5.0], [27.6, 5.0], [27.7, 5.0], [27.8, 5.0], [27.9, 5.0], [28.0, 5.0], [28.1, 5.0], [28.2, 5.0], [28.3, 5.0], [28.4, 5.0], [28.5, 5.0], [28.6, 5.0], [28.7, 5.0], [28.8, 5.0], [28.9, 5.0], [29.0, 5.0], [29.1, 5.0], [29.2, 5.0], [29.3, 5.0], [29.4, 5.0], [29.5, 5.0], [29.6, 5.0], [29.7, 5.0], [29.8, 5.0], [29.9, 5.0], [30.0, 5.0], [30.1, 5.0], [30.2, 5.0], [30.3, 5.0], [30.4, 5.0], [30.5, 5.0], [30.6, 5.0], [30.7, 5.0], [30.8, 5.0], [30.9, 5.0], [31.0, 5.0], [31.1, 5.0], [31.2, 5.0], [31.3, 5.0], [31.4, 5.0], [31.5, 5.0], [31.6, 5.0], [31.7, 5.0], [31.8, 5.0], [31.9, 5.0], [32.0, 5.0], [32.1, 5.0], [32.2, 5.0], [32.3, 5.0], [32.4, 5.0], [32.5, 5.0], [32.6, 5.0], [32.7, 5.0], [32.8, 5.0], [32.9, 5.0], [33.0, 5.0], [33.1, 5.0], [33.2, 5.0], [33.3, 5.0], [33.4, 5.0], [33.5, 5.0], [33.6, 5.0], [33.7, 5.0], [33.8, 5.0], [33.9, 5.0], [34.0, 5.0], [34.1, 5.0], [34.2, 5.0], [34.3, 5.0], [34.4, 5.0], [34.5, 5.0], [34.6, 5.0], [34.7, 5.0], [34.8, 5.0], [34.9, 5.0], [35.0, 5.0], [35.1, 5.0], [35.2, 5.0], [35.3, 5.0], [35.4, 5.0], [35.5, 5.0], [35.6, 5.0], [35.7, 5.0], [35.8, 5.0], [35.9, 5.0], [36.0, 5.0], [36.1, 5.0], [36.2, 5.0], [36.3, 5.0], [36.4, 5.0], [36.5, 5.0], [36.6, 5.0], [36.7, 5.0], [36.8, 5.0], [36.9, 5.0], [37.0, 5.0], [37.1, 5.0], [37.2, 5.0], [37.3, 5.0], [37.4, 5.0], [37.5, 5.0], [37.6, 5.0], [37.7, 5.0], [37.8, 5.0], [37.9, 5.0], [38.0, 5.0], [38.1, 5.0], [38.2, 5.0], [38.3, 5.0], [38.4, 5.0], [38.5, 5.0], [38.6, 5.0], [38.7, 5.0], [38.8, 5.0], [38.9, 5.0], [39.0, 5.0], [39.1, 5.0], [39.2, 5.0], [39.3, 5.0], [39.4, 5.0], [39.5, 5.0], [39.6, 5.0], [39.7, 5.0], [39.8, 5.0], [39.9, 5.0], [40.0, 5.0], [40.1, 5.0], [40.2, 5.0], [40.3, 5.0], [40.4, 5.0], [40.5, 5.0], [40.6, 5.0], [40.7, 5.0], [40.8, 5.0], [40.9, 5.0], [41.0, 5.0], [41.1, 5.0], [41.2, 5.0], [41.3, 5.0], [41.4, 5.0], [41.5, 5.0], [41.6, 5.0], [41.7, 5.0], [41.8, 5.0], [41.9, 5.0], [42.0, 5.0], [42.1, 5.0], [42.2, 5.0], [42.3, 5.0], [42.4, 5.0], [42.5, 5.0], [42.6, 5.0], [42.7, 5.0], [42.8, 5.0], [42.9, 5.0], [43.0, 5.0], [43.1, 5.0], [43.2, 5.0], [43.3, 5.0], [43.4, 5.0], [43.5, 5.0], [43.6, 5.0], [43.7, 5.0], [43.8, 5.0], [43.9, 5.0], [44.0, 5.0], [44.1, 5.0], [44.2, 5.0], [44.3, 5.0], [44.4, 5.0], [44.5, 5.0], [44.6, 5.0], [44.7, 5.0], [44.8, 5.0], [44.9, 5.0], [45.0, 5.0], [45.1, 5.0], [45.2, 5.0], [45.3, 5.0], [45.4, 5.0], [45.5, 5.0], [45.6, 5.0], [45.7, 5.0], [45.8, 5.0], [45.9, 5.0], [46.0, 5.0], [46.1, 5.0], [46.2, 5.0], [46.3, 5.0], [46.4, 5.0], [46.5, 5.0], [46.6, 5.0], [46.7, 5.0], [46.8, 5.0], [46.9, 5.0], [47.0, 5.0], [47.1, 5.0], [47.2, 5.0], [47.3, 5.0], [47.4, 5.0], [47.5, 5.0], [47.6, 5.0], [47.7, 5.0], [47.8, 5.0], [47.9, 5.0], [48.0, 5.0], [48.1, 5.0], [48.2, 5.0], [48.3, 5.0], [48.4, 5.0], [48.5, 5.0], [48.6, 5.0], [48.7, 5.0], [48.8, 5.0], [48.9, 5.0], [49.0, 5.0], [49.1, 5.0], [49.2, 5.0], [49.3, 5.0], [49.4, 5.0], [49.5, 5.0], [49.6, 5.0], [49.7, 5.0], [49.8, 5.0], [49.9, 5.0], [50.0, 5.0], [50.1, 5.0], [50.2, 5.0], [50.3, 5.0], [50.4, 5.0], [50.5, 5.0], [50.6, 5.0], [50.7, 5.0], [50.8, 5.0], [50.9, 5.0], [51.0, 5.0], [51.1, 5.0], [51.2, 5.0], [51.3, 5.0], [51.4, 5.0], [51.5, 5.0], [51.6, 5.0], [51.7, 5.0], [51.8, 5.0], [51.9, 5.0], [52.0, 5.0], [52.1, 5.0], [52.2, 5.0], [52.3, 5.0], [52.4, 5.0], [52.5, 5.0], [52.6, 5.0], [52.7, 5.0], [52.8, 5.0], [52.9, 5.0], [53.0, 5.0], [53.1, 5.0], [53.2, 5.0], [53.3, 5.0], [53.4, 5.0], [53.5, 5.0], [53.6, 5.0], [53.7, 5.0], [53.8, 5.0], [53.9, 5.0], [54.0, 5.0], [54.1, 5.0], [54.2, 5.0], [54.3, 5.0], [54.4, 5.0], [54.5, 5.0], [54.6, 5.0], [54.7, 5.0], [54.8, 5.0], [54.9, 5.0], [55.0, 5.0], [55.1, 5.0], [55.2, 5.0], [55.3, 5.0], [55.4, 5.0], [55.5, 5.0], [55.6, 5.0], [55.7, 5.0], [55.8, 5.0], [55.9, 5.0], [56.0, 5.0], [56.1, 5.0], [56.2, 5.0], [56.3, 5.0], [56.4, 5.0], [56.5, 5.0], [56.6, 5.0], [56.7, 5.0], [56.8, 5.0], [56.9, 5.0], [57.0, 5.0], [57.1, 5.0], [57.2, 5.0], [57.3, 5.0], [57.4, 5.0], [57.5, 5.0], [57.6, 5.0], [57.7, 5.0], [57.8, 5.0], [57.9, 5.0], [58.0, 5.0], [58.1, 5.0], [58.2, 5.0], [58.3, 5.0], [58.4, 5.0], [58.5, 5.0], [58.6, 5.0], [58.7, 5.0], [58.8, 5.0], [58.9, 5.0], [59.0, 5.0], [59.1, 5.0], [59.2, 5.0], [59.3, 5.0], [59.4, 5.0], [59.5, 5.0], [59.6, 5.0], [59.7, 5.0], [59.8, 5.0], [59.9, 5.0], [60.0, 5.0], [60.1, 5.0], [60.2, 5.0], [60.3, 5.0], [60.4, 5.0], [60.5, 5.0], [60.6, 5.0], [60.7, 5.0], [60.8, 5.0], [60.9, 5.0], [61.0, 5.0], [61.1, 5.0], [61.2, 5.0], [61.3, 5.0], [61.4, 5.0], [61.5, 5.0], [61.6, 5.0], [61.7, 5.0], [61.8, 5.0], [61.9, 5.0], [62.0, 5.0], [62.1, 5.0], [62.2, 5.0], [62.3, 5.0], [62.4, 5.0], [62.5, 5.0], [62.6, 5.0], [62.7, 5.0], [62.8, 5.0], [62.9, 5.0], [63.0, 5.0], [63.1, 5.0], [63.2, 5.0], [63.3, 5.0], [63.4, 5.0], [63.5, 5.0], [63.6, 5.0], [63.7, 5.0], [63.8, 5.0], [63.9, 5.0], [64.0, 5.0], [64.1, 5.0], [64.2, 5.0], [64.3, 5.0], [64.4, 5.0], [64.5, 5.0], [64.6, 5.0], [64.7, 5.0], [64.8, 5.0], [64.9, 5.0], [65.0, 5.0], [65.1, 5.0], [65.2, 5.0], [65.3, 5.0], [65.4, 5.0], [65.5, 5.0], [65.6, 5.0], [65.7, 5.0], [65.8, 5.0], [65.9, 5.0], [66.0, 5.0], [66.1, 5.0], [66.2, 5.0], [66.3, 5.0], [66.4, 5.0], [66.5, 5.0], [66.6, 5.0], [66.7, 5.0], [66.8, 5.0], [66.9, 5.0], [67.0, 5.0], [67.1, 5.0], [67.2, 5.0], [67.3, 5.0], [67.4, 5.0], [67.5, 5.0], [67.6, 5.0], [67.7, 5.0], [67.8, 5.0], [67.9, 5.0], [68.0, 5.0], [68.1, 5.0], [68.2, 5.0], [68.3, 5.0], [68.4, 5.0], [68.5, 5.0], [68.6, 5.0], [68.7, 5.0], [68.8, 5.0], [68.9, 5.0], [69.0, 5.0], [69.1, 5.0], [69.2, 5.0], [69.3, 5.0], [69.4, 5.0], [69.5, 5.0], [69.6, 5.0], [69.7, 5.0], [69.8, 5.0], [69.9, 5.0], [70.0, 5.0], [70.1, 5.0], [70.2, 5.0], [70.3, 5.0], [70.4, 5.0], [70.5, 5.0], [70.6, 5.0], [70.7, 5.0], [70.8, 5.0], [70.9, 5.0], [71.0, 5.0], [71.1, 5.0], [71.2, 5.0], [71.3, 5.0], [71.4, 5.0], [71.5, 5.0], [71.6, 5.0], [71.7, 5.0], [71.8, 5.0], [71.9, 5.0], [72.0, 5.0], [72.1, 5.0], [72.2, 5.0], [72.3, 5.0], [72.4, 5.0], [72.5, 5.0], [72.6, 5.0], [72.7, 5.0], [72.8, 5.0], [72.9, 5.0], [73.0, 5.0], [73.1, 5.0], [73.2, 5.0], [73.3, 5.0], [73.4, 5.0], [73.5, 5.0], [73.6, 5.0], [73.7, 5.0], [73.8, 5.0], [73.9, 5.0], [74.0, 5.0], [74.1, 5.0], [74.2, 5.0], [74.3, 5.0], [74.4, 5.0], [74.5, 5.0], [74.6, 5.0], [74.7, 5.0], [74.8, 5.0], [74.9, 5.0], [75.0, 5.0], [75.1, 5.0], [75.2, 5.0], [75.3, 5.0], [75.4, 5.0], [75.5, 5.0], [75.6, 5.0], [75.7, 5.0], [75.8, 5.0], [75.9, 5.0], [76.0, 5.0], [76.1, 5.0], [76.2, 5.0], [76.3, 5.0], [76.4, 5.0], [76.5, 5.0], [76.6, 5.0], [76.7, 5.0], [76.8, 5.0], [76.9, 5.0], [77.0, 5.0], [77.1, 5.0], [77.2, 5.0], [77.3, 5.0], [77.4, 5.0], [77.5, 5.0], [77.6, 5.0], [77.7, 5.0], [77.8, 5.0], [77.9, 5.0], [78.0, 5.0], [78.1, 5.0], [78.2, 5.0], [78.3, 5.0], [78.4, 5.0], [78.5, 5.0], [78.6, 5.0], [78.7, 5.0], [78.8, 5.0], [78.9, 5.0], [79.0, 5.0], [79.1, 5.0], [79.2, 5.0], [79.3, 5.0], [79.4, 5.0], [79.5, 5.0], [79.6, 5.0], [79.7, 5.0], [79.8, 5.0], [79.9, 5.0], [80.0, 5.0], [80.1, 5.0], [80.2, 5.0], [80.3, 5.0], [80.4, 5.0], [80.5, 5.0], [80.6, 5.0], [80.7, 5.0], [80.8, 5.0], [80.9, 5.0], [81.0, 5.0], [81.1, 5.0], [81.2, 5.0], [81.3, 5.0], [81.4, 5.0], [81.5, 5.0], [81.6, 5.0], [81.7, 5.0], [81.8, 5.0], [81.9, 5.0], [82.0, 5.0], [82.1, 5.0], [82.2, 5.0], [82.3, 5.0], [82.4, 5.0], [82.5, 5.0], [82.6, 5.0], [82.7, 5.0], [82.8, 5.0], [82.9, 5.0], [83.0, 5.0], [83.1, 5.0], [83.2, 5.0], [83.3, 5.0], [83.4, 5.0], [83.5, 5.0], [83.6, 5.0], [83.7, 5.0], [83.8, 5.0], [83.9, 5.0], [84.0, 5.0], [84.1, 5.0], [84.2, 5.0], [84.3, 5.0], [84.4, 5.0], [84.5, 5.0], [84.6, 5.0], [84.7, 5.0], [84.8, 5.0], [84.9, 5.0], [85.0, 5.0], [85.1, 5.0], [85.2, 5.0], [85.3, 5.0], [85.4, 5.0], [85.5, 5.0], [85.6, 5.0], [85.7, 5.0], [85.8, 5.0], [85.9, 5.0], [86.0, 5.0], [86.1, 5.0], [86.2, 5.0], [86.3, 5.0], [86.4, 5.0], [86.5, 5.0], [86.6, 5.0], [86.7, 5.0], [86.8, 5.0], [86.9, 5.0], [87.0, 5.0], [87.1, 5.0], [87.2, 5.0], [87.3, 5.0], [87.4, 5.0], [87.5, 5.0], [87.6, 5.0], [87.7, 5.0], [87.8, 5.0], [87.9, 5.0], [88.0, 5.0], [88.1, 5.0], [88.2, 5.0], [88.3, 5.0], [88.4, 5.0], [88.5, 5.0], [88.6, 5.0], [88.7, 5.0], [88.8, 5.0], [88.9, 5.0], [89.0, 5.0], [89.1, 5.0], [89.2, 5.0], [89.3, 5.0], [89.4, 5.0], [89.5, 5.0], [89.6, 5.0], [89.7, 5.0], [89.8, 5.0], [89.9, 5.0], [90.0, 5.0], [90.1, 5.0], [90.2, 5.0], [90.3, 5.0], [90.4, 5.0], [90.5, 5.0], [90.6, 5.0], [90.7, 5.0], [90.8, 5.0], [90.9, 5.0], [91.0, 5.0], [91.1, 5.0], [91.2, 5.0], [91.3, 5.0], [91.4, 5.0], [91.5, 5.0], [91.6, 5.0], [91.7, 5.0], [91.8, 5.0], [91.9, 5.0], [92.0, 5.0], [92.1, 5.0], [92.2, 5.0], [92.3, 5.0], [92.4, 5.0], [92.5, 5.0], [92.6, 5.0], [92.7, 5.0], [92.8, 5.0], [92.9, 5.0], [93.0, 5.0], [93.1, 5.0], [93.2, 5.0], [93.3, 5.0], [93.4, 5.0], [93.5, 5.0], [93.6, 5.0], [93.7, 5.0], [93.8, 5.0], [93.9, 5.0], [94.0, 5.0], [94.1, 5.0], [94.2, 5.0], [94.3, 5.0], [94.4, 5.0], [94.5, 5.0], [94.6, 5.0], [94.7, 5.0], [94.8, 5.0], [94.9, 5.0], [95.0, 5.0], [95.1, 5.0], [95.2, 5.0], [95.3, 5.0], [95.4, 5.0], [95.5, 5.0], [95.6, 5.0], [95.7, 5.0], [95.8, 5.0], [95.9, 5.0], [96.0, 5.0], [96.1, 5.0], [96.2, 5.0], [96.3, 5.0], [96.4, 5.0], [96.5, 5.0], [96.6, 5.0], [96.7, 5.0], [96.8, 5.0], [96.9, 5.0], [97.0, 5.0], [97.1, 5.0], [97.2, 5.0], [97.3, 5.0], [97.4, 5.0], [97.5, 5.0], [97.6, 5.0], [97.7, 5.0], [97.8, 5.0], [97.9, 5.0], [98.0, 5.0], [98.1, 5.0], [98.2, 5.0], [98.3, 5.0], [98.4, 5.0], [98.5, 5.0], [98.6, 5.0], [98.7, 5.0], [98.8, 5.0], [98.9, 5.0], [99.0, 5.0], [99.1, 5.0], [99.2, 5.0], [99.3, 5.0], [99.4, 5.0], [99.5, 5.0], [99.6, 5.0], [99.7, 5.0], [99.8, 5.0], [99.9, 5.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[0.0, 4.0], [0.1, 4.0], [0.2, 4.0], [0.3, 4.0], [0.4, 4.0], [0.5, 4.0], [0.6, 4.0], [0.7, 4.0], [0.8, 4.0], [0.9, 4.0], [1.0, 4.0], [1.1, 4.0], [1.2, 4.0], [1.3, 4.0], [1.4, 4.0], [1.5, 4.0], [1.6, 4.0], [1.7, 4.0], [1.8, 4.0], [1.9, 4.0], [2.0, 4.0], [2.1, 4.0], [2.2, 4.0], [2.3, 4.0], [2.4, 4.0], [2.5, 4.0], [2.6, 4.0], [2.7, 4.0], [2.8, 4.0], [2.9, 4.0], [3.0, 4.0], [3.1, 4.0], [3.2, 4.0], [3.3, 4.0], [3.4, 4.0], [3.5, 4.0], [3.6, 4.0], [3.7, 4.0], [3.8, 4.0], [3.9, 4.0], [4.0, 4.0], [4.1, 4.0], [4.2, 4.0], [4.3, 4.0], [4.4, 4.0], [4.5, 4.0], [4.6, 4.0], [4.7, 4.0], [4.8, 4.0], [4.9, 4.0], [5.0, 4.0], [5.1, 4.0], [5.2, 4.0], [5.3, 4.0], [5.4, 4.0], [5.5, 4.0], [5.6, 4.0], [5.7, 4.0], [5.8, 4.0], [5.9, 4.0], [6.0, 4.0], [6.1, 4.0], [6.2, 4.0], [6.3, 4.0], [6.4, 4.0], [6.5, 4.0], [6.6, 4.0], [6.7, 4.0], [6.8, 4.0], [6.9, 4.0], [7.0, 4.0], [7.1, 4.0], [7.2, 4.0], [7.3, 4.0], [7.4, 4.0], [7.5, 4.0], [7.6, 4.0], [7.7, 4.0], [7.8, 4.0], [7.9, 4.0], [8.0, 4.0], [8.1, 4.0], [8.2, 4.0], [8.3, 4.0], [8.4, 4.0], [8.5, 4.0], [8.6, 4.0], [8.7, 4.0], [8.8, 4.0], [8.9, 4.0], [9.0, 4.0], [9.1, 4.0], [9.2, 4.0], [9.3, 4.0], [9.4, 4.0], [9.5, 4.0], [9.6, 4.0], [9.7, 4.0], [9.8, 4.0], [9.9, 4.0], [10.0, 4.0], [10.1, 4.0], [10.2, 4.0], [10.3, 4.0], [10.4, 4.0], [10.5, 4.0], [10.6, 4.0], [10.7, 4.0], [10.8, 4.0], [10.9, 4.0], [11.0, 4.0], [11.1, 4.0], [11.2, 4.0], [11.3, 4.0], [11.4, 4.0], [11.5, 4.0], [11.6, 4.0], [11.7, 4.0], [11.8, 4.0], [11.9, 4.0], [12.0, 4.0], [12.1, 4.0], [12.2, 4.0], [12.3, 4.0], [12.4, 4.0], [12.5, 4.0], [12.6, 4.0], [12.7, 4.0], [12.8, 4.0], [12.9, 4.0], [13.0, 4.0], [13.1, 4.0], [13.2, 4.0], [13.3, 4.0], [13.4, 4.0], [13.5, 4.0], [13.6, 4.0], [13.7, 4.0], [13.8, 4.0], [13.9, 4.0], [14.0, 4.0], [14.1, 4.0], [14.2, 4.0], [14.3, 4.0], [14.4, 4.0], [14.5, 4.0], [14.6, 4.0], [14.7, 4.0], [14.8, 4.0], [14.9, 4.0], [15.0, 4.0], [15.1, 4.0], [15.2, 4.0], [15.3, 4.0], [15.4, 4.0], [15.5, 4.0], [15.6, 4.0], [15.7, 4.0], [15.8, 4.0], [15.9, 4.0], [16.0, 4.0], [16.1, 4.0], [16.2, 4.0], [16.3, 4.0], [16.4, 4.0], [16.5, 4.0], [16.6, 4.0], [16.7, 4.0], [16.8, 4.0], [16.9, 4.0], [17.0, 4.0], [17.1, 4.0], [17.2, 4.0], [17.3, 4.0], [17.4, 4.0], [17.5, 4.0], [17.6, 4.0], [17.7, 4.0], [17.8, 4.0], [17.9, 4.0], [18.0, 4.0], [18.1, 4.0], [18.2, 4.0], [18.3, 4.0], [18.4, 4.0], [18.5, 4.0], [18.6, 4.0], [18.7, 4.0], [18.8, 4.0], [18.9, 4.0], [19.0, 4.0], [19.1, 4.0], [19.2, 4.0], [19.3, 4.0], [19.4, 4.0], [19.5, 4.0], [19.6, 4.0], [19.7, 4.0], [19.8, 4.0], [19.9, 4.0], [20.0, 4.0], [20.1, 4.0], [20.2, 4.0], [20.3, 4.0], [20.4, 4.0], [20.5, 4.0], [20.6, 4.0], [20.7, 4.0], [20.8, 4.0], [20.9, 4.0], [21.0, 4.0], [21.1, 4.0], [21.2, 4.0], [21.3, 4.0], [21.4, 4.0], [21.5, 4.0], [21.6, 4.0], [21.7, 4.0], [21.8, 4.0], [21.9, 4.0], [22.0, 4.0], [22.1, 4.0], [22.2, 4.0], [22.3, 4.0], [22.4, 4.0], [22.5, 4.0], [22.6, 4.0], [22.7, 4.0], [22.8, 4.0], [22.9, 4.0], [23.0, 4.0], [23.1, 4.0], [23.2, 4.0], [23.3, 4.0], [23.4, 4.0], [23.5, 4.0], [23.6, 4.0], [23.7, 4.0], [23.8, 4.0], [23.9, 4.0], [24.0, 4.0], [24.1, 4.0], [24.2, 4.0], [24.3, 4.0], [24.4, 4.0], [24.5, 4.0], [24.6, 4.0], [24.7, 4.0], [24.8, 4.0], [24.9, 4.0], [25.0, 4.0], [25.1, 4.0], [25.2, 4.0], [25.3, 4.0], [25.4, 4.0], [25.5, 4.0], [25.6, 4.0], [25.7, 4.0], [25.8, 4.0], [25.9, 4.0], [26.0, 4.0], [26.1, 4.0], [26.2, 4.0], [26.3, 4.0], [26.4, 4.0], [26.5, 4.0], [26.6, 4.0], [26.7, 4.0], [26.8, 4.0], [26.9, 4.0], [27.0, 4.0], [27.1, 4.0], [27.2, 4.0], [27.3, 4.0], [27.4, 4.0], [27.5, 4.0], [27.6, 4.0], [27.7, 4.0], [27.8, 4.0], [27.9, 4.0], [28.0, 4.0], [28.1, 4.0], [28.2, 4.0], [28.3, 4.0], [28.4, 4.0], [28.5, 4.0], [28.6, 4.0], [28.7, 4.0], [28.8, 4.0], [28.9, 4.0], [29.0, 4.0], [29.1, 4.0], [29.2, 4.0], [29.3, 4.0], [29.4, 4.0], [29.5, 4.0], [29.6, 4.0], [29.7, 4.0], [29.8, 4.0], [29.9, 4.0], [30.0, 4.0], [30.1, 4.0], [30.2, 4.0], [30.3, 4.0], [30.4, 4.0], [30.5, 4.0], [30.6, 4.0], [30.7, 4.0], [30.8, 4.0], [30.9, 4.0], [31.0, 4.0], [31.1, 4.0], [31.2, 4.0], [31.3, 4.0], [31.4, 4.0], [31.5, 4.0], [31.6, 4.0], [31.7, 4.0], [31.8, 4.0], [31.9, 4.0], [32.0, 4.0], [32.1, 4.0], [32.2, 4.0], [32.3, 4.0], [32.4, 4.0], [32.5, 4.0], [32.6, 4.0], [32.7, 4.0], [32.8, 4.0], [32.9, 4.0], [33.0, 4.0], [33.1, 4.0], [33.2, 4.0], [33.3, 4.0], [33.4, 4.0], [33.5, 4.0], [33.6, 4.0], [33.7, 4.0], [33.8, 4.0], [33.9, 4.0], [34.0, 4.0], [34.1, 4.0], [34.2, 4.0], [34.3, 4.0], [34.4, 4.0], [34.5, 4.0], [34.6, 4.0], [34.7, 4.0], [34.8, 4.0], [34.9, 4.0], [35.0, 4.0], [35.1, 4.0], [35.2, 4.0], [35.3, 4.0], [35.4, 4.0], [35.5, 4.0], [35.6, 4.0], [35.7, 4.0], [35.8, 4.0], [35.9, 4.0], [36.0, 4.0], [36.1, 4.0], [36.2, 4.0], [36.3, 4.0], [36.4, 4.0], [36.5, 4.0], [36.6, 4.0], [36.7, 4.0], [36.8, 4.0], [36.9, 4.0], [37.0, 4.0], [37.1, 4.0], [37.2, 4.0], [37.3, 4.0], [37.4, 4.0], [37.5, 4.0], [37.6, 4.0], [37.7, 4.0], [37.8, 4.0], [37.9, 4.0], [38.0, 4.0], [38.1, 4.0], [38.2, 4.0], [38.3, 4.0], [38.4, 4.0], [38.5, 4.0], [38.6, 4.0], [38.7, 4.0], [38.8, 4.0], [38.9, 4.0], [39.0, 4.0], [39.1, 4.0], [39.2, 4.0], [39.3, 4.0], [39.4, 4.0], [39.5, 4.0], [39.6, 4.0], [39.7, 4.0], [39.8, 4.0], [39.9, 4.0], [40.0, 4.0], [40.1, 4.0], [40.2, 4.0], [40.3, 4.0], [40.4, 4.0], [40.5, 4.0], [40.6, 4.0], [40.7, 4.0], [40.8, 4.0], [40.9, 4.0], [41.0, 4.0], [41.1, 4.0], [41.2, 4.0], [41.3, 4.0], [41.4, 4.0], [41.5, 4.0], [41.6, 4.0], [41.7, 4.0], [41.8, 4.0], [41.9, 4.0], [42.0, 4.0], [42.1, 4.0], [42.2, 4.0], [42.3, 4.0], [42.4, 4.0], [42.5, 4.0], [42.6, 4.0], [42.7, 4.0], [42.8, 4.0], [42.9, 4.0], [43.0, 4.0], [43.1, 4.0], [43.2, 4.0], [43.3, 4.0], [43.4, 4.0], [43.5, 4.0], [43.6, 4.0], [43.7, 4.0], [43.8, 4.0], [43.9, 4.0], [44.0, 4.0], [44.1, 4.0], [44.2, 4.0], [44.3, 4.0], [44.4, 4.0], [44.5, 4.0], [44.6, 4.0], [44.7, 4.0], [44.8, 4.0], [44.9, 4.0], [45.0, 4.0], [45.1, 4.0], [45.2, 4.0], [45.3, 4.0], [45.4, 4.0], [45.5, 4.0], [45.6, 4.0], [45.7, 4.0], [45.8, 4.0], [45.9, 4.0], [46.0, 4.0], [46.1, 4.0], [46.2, 4.0], [46.3, 4.0], [46.4, 4.0], [46.5, 4.0], [46.6, 4.0], [46.7, 4.0], [46.8, 4.0], [46.9, 4.0], [47.0, 4.0], [47.1, 4.0], [47.2, 4.0], [47.3, 4.0], [47.4, 4.0], [47.5, 4.0], [47.6, 4.0], [47.7, 4.0], [47.8, 4.0], [47.9, 4.0], [48.0, 4.0], [48.1, 4.0], [48.2, 4.0], [48.3, 4.0], [48.4, 4.0], [48.5, 4.0], [48.6, 4.0], [48.7, 4.0], [48.8, 4.0], [48.9, 4.0], [49.0, 4.0], [49.1, 4.0], [49.2, 4.0], [49.3, 4.0], [49.4, 4.0], [49.5, 4.0], [49.6, 4.0], [49.7, 4.0], [49.8, 4.0], [49.9, 4.0], [50.0, 4.0], [50.1, 4.0], [50.2, 4.0], [50.3, 4.0], [50.4, 4.0], [50.5, 4.0], [50.6, 4.0], [50.7, 4.0], [50.8, 4.0], [50.9, 4.0], [51.0, 4.0], [51.1, 4.0], [51.2, 4.0], [51.3, 4.0], [51.4, 4.0], [51.5, 4.0], [51.6, 4.0], [51.7, 4.0], [51.8, 4.0], [51.9, 4.0], [52.0, 4.0], [52.1, 4.0], [52.2, 4.0], [52.3, 4.0], [52.4, 4.0], [52.5, 4.0], [52.6, 4.0], [52.7, 4.0], [52.8, 4.0], [52.9, 4.0], [53.0, 4.0], [53.1, 4.0], [53.2, 4.0], [53.3, 4.0], [53.4, 4.0], [53.5, 4.0], [53.6, 4.0], [53.7, 4.0], [53.8, 4.0], [53.9, 4.0], [54.0, 4.0], [54.1, 4.0], [54.2, 4.0], [54.3, 4.0], [54.4, 4.0], [54.5, 4.0], [54.6, 4.0], [54.7, 4.0], [54.8, 4.0], [54.9, 4.0], [55.0, 4.0], [55.1, 4.0], [55.2, 4.0], [55.3, 4.0], [55.4, 4.0], [55.5, 4.0], [55.6, 4.0], [55.7, 4.0], [55.8, 4.0], [55.9, 4.0], [56.0, 4.0], [56.1, 4.0], [56.2, 4.0], [56.3, 4.0], [56.4, 4.0], [56.5, 4.0], [56.6, 4.0], [56.7, 4.0], [56.8, 4.0], [56.9, 4.0], [57.0, 4.0], [57.1, 4.0], [57.2, 4.0], [57.3, 4.0], [57.4, 4.0], [57.5, 4.0], [57.6, 4.0], [57.7, 4.0], [57.8, 4.0], [57.9, 4.0], [58.0, 4.0], [58.1, 4.0], [58.2, 4.0], [58.3, 4.0], [58.4, 4.0], [58.5, 4.0], [58.6, 4.0], [58.7, 4.0], [58.8, 4.0], [58.9, 4.0], [59.0, 4.0], [59.1, 4.0], [59.2, 4.0], [59.3, 4.0], [59.4, 4.0], [59.5, 4.0], [59.6, 4.0], [59.7, 4.0], [59.8, 4.0], [59.9, 4.0], [60.0, 4.0], [60.1, 4.0], [60.2, 4.0], [60.3, 4.0], [60.4, 4.0], [60.5, 4.0], [60.6, 4.0], [60.7, 4.0], [60.8, 4.0], [60.9, 4.0], [61.0, 4.0], [61.1, 4.0], [61.2, 4.0], [61.3, 4.0], [61.4, 4.0], [61.5, 4.0], [61.6, 4.0], [61.7, 4.0], [61.8, 4.0], [61.9, 4.0], [62.0, 4.0], [62.1, 4.0], [62.2, 4.0], [62.3, 4.0], [62.4, 4.0], [62.5, 4.0], [62.6, 4.0], [62.7, 4.0], [62.8, 4.0], [62.9, 4.0], [63.0, 4.0], [63.1, 4.0], [63.2, 4.0], [63.3, 4.0], [63.4, 4.0], [63.5, 4.0], [63.6, 4.0], [63.7, 4.0], [63.8, 4.0], [63.9, 4.0], [64.0, 4.0], [64.1, 4.0], [64.2, 4.0], [64.3, 4.0], [64.4, 4.0], [64.5, 4.0], [64.6, 4.0], [64.7, 4.0], [64.8, 4.0], [64.9, 4.0], [65.0, 4.0], [65.1, 4.0], [65.2, 4.0], [65.3, 4.0], [65.4, 4.0], [65.5, 4.0], [65.6, 4.0], [65.7, 4.0], [65.8, 4.0], [65.9, 4.0], [66.0, 4.0], [66.1, 4.0], [66.2, 4.0], [66.3, 4.0], [66.4, 4.0], [66.5, 4.0], [66.6, 4.0], [66.7, 4.0], [66.8, 4.0], [66.9, 4.0], [67.0, 4.0], [67.1, 4.0], [67.2, 4.0], [67.3, 4.0], [67.4, 4.0], [67.5, 4.0], [67.6, 4.0], [67.7, 4.0], [67.8, 4.0], [67.9, 4.0], [68.0, 4.0], [68.1, 4.0], [68.2, 4.0], [68.3, 4.0], [68.4, 4.0], [68.5, 4.0], [68.6, 4.0], [68.7, 4.0], [68.8, 4.0], [68.9, 4.0], [69.0, 4.0], [69.1, 4.0], [69.2, 4.0], [69.3, 4.0], [69.4, 4.0], [69.5, 4.0], [69.6, 4.0], [69.7, 4.0], [69.8, 4.0], [69.9, 4.0], [70.0, 4.0], [70.1, 4.0], [70.2, 4.0], [70.3, 4.0], [70.4, 4.0], [70.5, 4.0], [70.6, 4.0], [70.7, 4.0], [70.8, 4.0], [70.9, 4.0], [71.0, 4.0], [71.1, 4.0], [71.2, 4.0], [71.3, 4.0], [71.4, 4.0], [71.5, 4.0], [71.6, 4.0], [71.7, 4.0], [71.8, 4.0], [71.9, 4.0], [72.0, 4.0], [72.1, 4.0], [72.2, 4.0], [72.3, 4.0], [72.4, 4.0], [72.5, 4.0], [72.6, 4.0], [72.7, 4.0], [72.8, 4.0], [72.9, 4.0], [73.0, 4.0], [73.1, 4.0], [73.2, 4.0], [73.3, 4.0], [73.4, 4.0], [73.5, 4.0], [73.6, 4.0], [73.7, 4.0], [73.8, 4.0], [73.9, 4.0], [74.0, 4.0], [74.1, 4.0], [74.2, 4.0], [74.3, 4.0], [74.4, 4.0], [74.5, 4.0], [74.6, 4.0], [74.7, 4.0], [74.8, 4.0], [74.9, 4.0], [75.0, 4.0], [75.1, 4.0], [75.2, 4.0], [75.3, 4.0], [75.4, 4.0], [75.5, 4.0], [75.6, 4.0], [75.7, 4.0], [75.8, 4.0], [75.9, 4.0], [76.0, 4.0], [76.1, 4.0], [76.2, 4.0], [76.3, 4.0], [76.4, 4.0], [76.5, 4.0], [76.6, 4.0], [76.7, 4.0], [76.8, 4.0], [76.9, 4.0], [77.0, 4.0], [77.1, 4.0], [77.2, 4.0], [77.3, 4.0], [77.4, 4.0], [77.5, 4.0], [77.6, 4.0], [77.7, 4.0], [77.8, 4.0], [77.9, 4.0], [78.0, 4.0], [78.1, 4.0], [78.2, 4.0], [78.3, 4.0], [78.4, 4.0], [78.5, 4.0], [78.6, 4.0], [78.7, 4.0], [78.8, 4.0], [78.9, 4.0], [79.0, 4.0], [79.1, 4.0], [79.2, 4.0], [79.3, 4.0], [79.4, 4.0], [79.5, 4.0], [79.6, 4.0], [79.7, 4.0], [79.8, 4.0], [79.9, 4.0], [80.0, 4.0], [80.1, 4.0], [80.2, 4.0], [80.3, 4.0], [80.4, 4.0], [80.5, 4.0], [80.6, 4.0], [80.7, 4.0], [80.8, 4.0], [80.9, 4.0], [81.0, 4.0], [81.1, 4.0], [81.2, 4.0], [81.3, 4.0], [81.4, 4.0], [81.5, 4.0], [81.6, 4.0], [81.7, 4.0], [81.8, 4.0], [81.9, 4.0], [82.0, 4.0], [82.1, 4.0], [82.2, 4.0], [82.3, 4.0], [82.4, 4.0], [82.5, 4.0], [82.6, 4.0], [82.7, 4.0], [82.8, 4.0], [82.9, 4.0], [83.0, 4.0], [83.1, 4.0], [83.2, 4.0], [83.3, 4.0], [83.4, 4.0], [83.5, 4.0], [83.6, 4.0], [83.7, 4.0], [83.8, 4.0], [83.9, 4.0], [84.0, 4.0], [84.1, 4.0], [84.2, 4.0], [84.3, 4.0], [84.4, 4.0], [84.5, 4.0], [84.6, 4.0], [84.7, 4.0], [84.8, 4.0], [84.9, 4.0], [85.0, 4.0], [85.1, 4.0], [85.2, 4.0], [85.3, 4.0], [85.4, 4.0], [85.5, 4.0], [85.6, 4.0], [85.7, 4.0], [85.8, 4.0], [85.9, 4.0], [86.0, 4.0], [86.1, 4.0], [86.2, 4.0], [86.3, 4.0], [86.4, 4.0], [86.5, 4.0], [86.6, 4.0], [86.7, 4.0], [86.8, 4.0], [86.9, 4.0], [87.0, 4.0], [87.1, 4.0], [87.2, 4.0], [87.3, 4.0], [87.4, 4.0], [87.5, 4.0], [87.6, 4.0], [87.7, 4.0], [87.8, 4.0], [87.9, 4.0], [88.0, 4.0], [88.1, 4.0], [88.2, 4.0], [88.3, 4.0], [88.4, 4.0], [88.5, 4.0], [88.6, 4.0], [88.7, 4.0], [88.8, 4.0], [88.9, 4.0], [89.0, 4.0], [89.1, 4.0], [89.2, 4.0], [89.3, 4.0], [89.4, 4.0], [89.5, 4.0], [89.6, 4.0], [89.7, 4.0], [89.8, 4.0], [89.9, 4.0], [90.0, 4.0], [90.1, 4.0], [90.2, 4.0], [90.3, 4.0], [90.4, 4.0], [90.5, 4.0], [90.6, 4.0], [90.7, 4.0], [90.8, 4.0], [90.9, 4.0], [91.0, 4.0], [91.1, 4.0], [91.2, 4.0], [91.3, 4.0], [91.4, 4.0], [91.5, 4.0], [91.6, 4.0], [91.7, 4.0], [91.8, 4.0], [91.9, 4.0], [92.0, 4.0], [92.1, 4.0], [92.2, 4.0], [92.3, 4.0], [92.4, 4.0], [92.5, 4.0], [92.6, 4.0], [92.7, 4.0], [92.8, 4.0], [92.9, 4.0], [93.0, 4.0], [93.1, 4.0], [93.2, 4.0], [93.3, 4.0], [93.4, 4.0], [93.5, 4.0], [93.6, 4.0], [93.7, 4.0], [93.8, 4.0], [93.9, 4.0], [94.0, 4.0], [94.1, 4.0], [94.2, 4.0], [94.3, 4.0], [94.4, 4.0], [94.5, 4.0], [94.6, 4.0], [94.7, 4.0], [94.8, 4.0], [94.9, 4.0], [95.0, 4.0], [95.1, 4.0], [95.2, 4.0], [95.3, 4.0], [95.4, 4.0], [95.5, 4.0], [95.6, 4.0], [95.7, 4.0], [95.8, 4.0], [95.9, 4.0], [96.0, 4.0], [96.1, 4.0], [96.2, 4.0], [96.3, 4.0], [96.4, 4.0], [96.5, 4.0], [96.6, 4.0], [96.7, 4.0], [96.8, 4.0], [96.9, 4.0], [97.0, 4.0], [97.1, 4.0], [97.2, 4.0], [97.3, 4.0], [97.4, 4.0], [97.5, 4.0], [97.6, 4.0], [97.7, 4.0], [97.8, 4.0], [97.9, 4.0], [98.0, 4.0], [98.1, 4.0], [98.2, 4.0], [98.3, 4.0], [98.4, 4.0], [98.5, 4.0], [98.6, 4.0], [98.7, 4.0], [98.8, 4.0], [98.9, 4.0], [99.0, 4.0], [99.1, 4.0], [99.2, 4.0], [99.3, 4.0], [99.4, 4.0], [99.5, 4.0], [99.6, 4.0], [99.7, 4.0], [99.8, 4.0], [99.9, 4.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[0.0, 42.0], [0.1, 42.0], [0.2, 42.0], [0.3, 42.0], [0.4, 42.0], [0.5, 42.0], [0.6, 42.0], [0.7, 42.0], [0.8, 42.0], [0.9, 42.0], [1.0, 42.0], [1.1, 42.0], [1.2, 42.0], [1.3, 42.0], [1.4, 42.0], [1.5, 42.0], [1.6, 42.0], [1.7, 42.0], [1.8, 42.0], [1.9, 42.0], [2.0, 42.0], [2.1, 42.0], [2.2, 42.0], [2.3, 42.0], [2.4, 42.0], [2.5, 42.0], [2.6, 42.0], [2.7, 42.0], [2.8, 42.0], [2.9, 42.0], [3.0, 42.0], [3.1, 42.0], [3.2, 42.0], [3.3, 42.0], [3.4, 42.0], [3.5, 42.0], [3.6, 42.0], [3.7, 42.0], [3.8, 42.0], [3.9, 42.0], [4.0, 42.0], [4.1, 42.0], [4.2, 42.0], [4.3, 42.0], [4.4, 42.0], [4.5, 42.0], [4.6, 42.0], [4.7, 42.0], [4.8, 42.0], [4.9, 42.0], [5.0, 42.0], [5.1, 42.0], [5.2, 42.0], [5.3, 42.0], [5.4, 42.0], [5.5, 42.0], [5.6, 42.0], [5.7, 42.0], [5.8, 42.0], [5.9, 42.0], [6.0, 42.0], [6.1, 42.0], [6.2, 42.0], [6.3, 42.0], [6.4, 42.0], [6.5, 42.0], [6.6, 42.0], [6.7, 42.0], [6.8, 42.0], [6.9, 42.0], [7.0, 42.0], [7.1, 42.0], [7.2, 42.0], [7.3, 42.0], [7.4, 42.0], [7.5, 42.0], [7.6, 42.0], [7.7, 42.0], [7.8, 42.0], [7.9, 42.0], [8.0, 42.0], [8.1, 42.0], [8.2, 42.0], [8.3, 42.0], [8.4, 42.0], [8.5, 42.0], [8.6, 42.0], [8.7, 42.0], [8.8, 42.0], [8.9, 42.0], [9.0, 42.0], [9.1, 42.0], [9.2, 42.0], [9.3, 42.0], [9.4, 42.0], [9.5, 42.0], [9.6, 42.0], [9.7, 42.0], [9.8, 42.0], [9.9, 42.0], [10.0, 42.0], [10.1, 42.0], [10.2, 42.0], [10.3, 42.0], [10.4, 42.0], [10.5, 42.0], [10.6, 42.0], [10.7, 42.0], [10.8, 42.0], [10.9, 42.0], [11.0, 42.0], [11.1, 42.0], [11.2, 42.0], [11.3, 42.0], [11.4, 42.0], [11.5, 42.0], [11.6, 42.0], [11.7, 42.0], [11.8, 42.0], [11.9, 42.0], [12.0, 42.0], [12.1, 42.0], [12.2, 42.0], [12.3, 42.0], [12.4, 42.0], [12.5, 42.0], [12.6, 42.0], [12.7, 42.0], [12.8, 42.0], [12.9, 42.0], [13.0, 42.0], [13.1, 42.0], [13.2, 42.0], [13.3, 42.0], [13.4, 42.0], [13.5, 42.0], [13.6, 42.0], [13.7, 42.0], [13.8, 42.0], [13.9, 42.0], [14.0, 42.0], [14.1, 42.0], [14.2, 42.0], [14.3, 42.0], [14.4, 42.0], [14.5, 42.0], [14.6, 42.0], [14.7, 42.0], [14.8, 42.0], [14.9, 42.0], [15.0, 42.0], [15.1, 42.0], [15.2, 42.0], [15.3, 42.0], [15.4, 42.0], [15.5, 42.0], [15.6, 42.0], [15.7, 42.0], [15.8, 42.0], [15.9, 42.0], [16.0, 42.0], [16.1, 42.0], [16.2, 42.0], [16.3, 42.0], [16.4, 42.0], [16.5, 42.0], [16.6, 42.0], [16.7, 42.0], [16.8, 42.0], [16.9, 42.0], [17.0, 42.0], [17.1, 42.0], [17.2, 42.0], [17.3, 42.0], [17.4, 42.0], [17.5, 42.0], [17.6, 42.0], [17.7, 42.0], [17.8, 42.0], [17.9, 42.0], [18.0, 42.0], [18.1, 42.0], [18.2, 42.0], [18.3, 42.0], [18.4, 42.0], [18.5, 42.0], [18.6, 42.0], [18.7, 42.0], [18.8, 42.0], [18.9, 42.0], [19.0, 42.0], [19.1, 42.0], [19.2, 42.0], [19.3, 42.0], [19.4, 42.0], [19.5, 42.0], [19.6, 42.0], [19.7, 42.0], [19.8, 42.0], [19.9, 42.0], [20.0, 42.0], [20.1, 42.0], [20.2, 42.0], [20.3, 42.0], [20.4, 42.0], [20.5, 42.0], [20.6, 42.0], [20.7, 42.0], [20.8, 42.0], [20.9, 42.0], [21.0, 42.0], [21.1, 42.0], [21.2, 42.0], [21.3, 42.0], [21.4, 42.0], [21.5, 42.0], [21.6, 42.0], [21.7, 42.0], [21.8, 42.0], [21.9, 42.0], [22.0, 42.0], [22.1, 42.0], [22.2, 42.0], [22.3, 42.0], [22.4, 42.0], [22.5, 42.0], [22.6, 42.0], [22.7, 42.0], [22.8, 42.0], [22.9, 42.0], [23.0, 42.0], [23.1, 42.0], [23.2, 42.0], [23.3, 42.0], [23.4, 42.0], [23.5, 42.0], [23.6, 42.0], [23.7, 42.0], [23.8, 42.0], [23.9, 42.0], [24.0, 42.0], [24.1, 42.0], [24.2, 42.0], [24.3, 42.0], [24.4, 42.0], [24.5, 42.0], [24.6, 42.0], [24.7, 42.0], [24.8, 42.0], [24.9, 42.0], [25.0, 42.0], [25.1, 42.0], [25.2, 42.0], [25.3, 42.0], [25.4, 42.0], [25.5, 42.0], [25.6, 42.0], [25.7, 42.0], [25.8, 42.0], [25.9, 42.0], [26.0, 42.0], [26.1, 42.0], [26.2, 42.0], [26.3, 42.0], [26.4, 42.0], [26.5, 42.0], [26.6, 42.0], [26.7, 42.0], [26.8, 42.0], [26.9, 42.0], [27.0, 42.0], [27.1, 42.0], [27.2, 42.0], [27.3, 42.0], [27.4, 42.0], [27.5, 42.0], [27.6, 42.0], [27.7, 42.0], [27.8, 42.0], [27.9, 42.0], [28.0, 42.0], [28.1, 42.0], [28.2, 42.0], [28.3, 42.0], [28.4, 42.0], [28.5, 42.0], [28.6, 42.0], [28.7, 42.0], [28.8, 42.0], [28.9, 42.0], [29.0, 42.0], [29.1, 42.0], [29.2, 42.0], [29.3, 42.0], [29.4, 42.0], [29.5, 42.0], [29.6, 42.0], [29.7, 42.0], [29.8, 42.0], [29.9, 42.0], [30.0, 42.0], [30.1, 42.0], [30.2, 42.0], [30.3, 42.0], [30.4, 42.0], [30.5, 42.0], [30.6, 42.0], [30.7, 42.0], [30.8, 42.0], [30.9, 42.0], [31.0, 42.0], [31.1, 42.0], [31.2, 42.0], [31.3, 42.0], [31.4, 42.0], [31.5, 42.0], [31.6, 42.0], [31.7, 42.0], [31.8, 42.0], [31.9, 42.0], [32.0, 42.0], [32.1, 42.0], [32.2, 42.0], [32.3, 42.0], [32.4, 42.0], [32.5, 42.0], [32.6, 42.0], [32.7, 42.0], [32.8, 42.0], [32.9, 42.0], [33.0, 42.0], [33.1, 42.0], [33.2, 42.0], [33.3, 42.0], [33.4, 42.0], [33.5, 42.0], [33.6, 42.0], [33.7, 42.0], [33.8, 42.0], [33.9, 42.0], [34.0, 42.0], [34.1, 42.0], [34.2, 42.0], [34.3, 42.0], [34.4, 42.0], [34.5, 42.0], [34.6, 42.0], [34.7, 42.0], [34.8, 42.0], [34.9, 42.0], [35.0, 42.0], [35.1, 42.0], [35.2, 42.0], [35.3, 42.0], [35.4, 42.0], [35.5, 42.0], [35.6, 42.0], [35.7, 42.0], [35.8, 42.0], [35.9, 42.0], [36.0, 42.0], [36.1, 42.0], [36.2, 42.0], [36.3, 42.0], [36.4, 42.0], [36.5, 42.0], [36.6, 42.0], [36.7, 42.0], [36.8, 42.0], [36.9, 42.0], [37.0, 42.0], [37.1, 42.0], [37.2, 42.0], [37.3, 42.0], [37.4, 42.0], [37.5, 42.0], [37.6, 42.0], [37.7, 42.0], [37.8, 42.0], [37.9, 42.0], [38.0, 42.0], [38.1, 42.0], [38.2, 42.0], [38.3, 42.0], [38.4, 42.0], [38.5, 42.0], [38.6, 42.0], [38.7, 42.0], [38.8, 42.0], [38.9, 42.0], [39.0, 42.0], [39.1, 42.0], [39.2, 42.0], [39.3, 42.0], [39.4, 42.0], [39.5, 42.0], [39.6, 42.0], [39.7, 42.0], [39.8, 42.0], [39.9, 42.0], [40.0, 42.0], [40.1, 42.0], [40.2, 42.0], [40.3, 42.0], [40.4, 42.0], [40.5, 42.0], [40.6, 42.0], [40.7, 42.0], [40.8, 42.0], [40.9, 42.0], [41.0, 42.0], [41.1, 42.0], [41.2, 42.0], [41.3, 42.0], [41.4, 42.0], [41.5, 42.0], [41.6, 42.0], [41.7, 42.0], [41.8, 42.0], [41.9, 42.0], [42.0, 42.0], [42.1, 42.0], [42.2, 42.0], [42.3, 42.0], [42.4, 42.0], [42.5, 42.0], [42.6, 42.0], [42.7, 42.0], [42.8, 42.0], [42.9, 42.0], [43.0, 42.0], [43.1, 42.0], [43.2, 42.0], [43.3, 42.0], [43.4, 42.0], [43.5, 42.0], [43.6, 42.0], [43.7, 42.0], [43.8, 42.0], [43.9, 42.0], [44.0, 42.0], [44.1, 42.0], [44.2, 42.0], [44.3, 42.0], [44.4, 42.0], [44.5, 42.0], [44.6, 42.0], [44.7, 42.0], [44.8, 42.0], [44.9, 42.0], [45.0, 42.0], [45.1, 42.0], [45.2, 42.0], [45.3, 42.0], [45.4, 42.0], [45.5, 42.0], [45.6, 42.0], [45.7, 42.0], [45.8, 42.0], [45.9, 42.0], [46.0, 42.0], [46.1, 42.0], [46.2, 42.0], [46.3, 42.0], [46.4, 42.0], [46.5, 42.0], [46.6, 42.0], [46.7, 42.0], [46.8, 42.0], [46.9, 42.0], [47.0, 42.0], [47.1, 42.0], [47.2, 42.0], [47.3, 42.0], [47.4, 42.0], [47.5, 42.0], [47.6, 42.0], [47.7, 42.0], [47.8, 42.0], [47.9, 42.0], [48.0, 42.0], [48.1, 42.0], [48.2, 42.0], [48.3, 42.0], [48.4, 42.0], [48.5, 42.0], [48.6, 42.0], [48.7, 42.0], [48.8, 42.0], [48.9, 42.0], [49.0, 42.0], [49.1, 42.0], [49.2, 42.0], [49.3, 42.0], [49.4, 42.0], [49.5, 42.0], [49.6, 42.0], [49.7, 42.0], [49.8, 42.0], [49.9, 42.0], [50.0, 58.0], [50.1, 58.0], [50.2, 58.0], [50.3, 58.0], [50.4, 58.0], [50.5, 58.0], [50.6, 58.0], [50.7, 58.0], [50.8, 58.0], [50.9, 58.0], [51.0, 58.0], [51.1, 58.0], [51.2, 58.0], [51.3, 58.0], [51.4, 58.0], [51.5, 58.0], [51.6, 58.0], [51.7, 58.0], [51.8, 58.0], [51.9, 58.0], [52.0, 58.0], [52.1, 58.0], [52.2, 58.0], [52.3, 58.0], [52.4, 58.0], [52.5, 58.0], [52.6, 58.0], [52.7, 58.0], [52.8, 58.0], [52.9, 58.0], [53.0, 58.0], [53.1, 58.0], [53.2, 58.0], [53.3, 58.0], [53.4, 58.0], [53.5, 58.0], [53.6, 58.0], [53.7, 58.0], [53.8, 58.0], [53.9, 58.0], [54.0, 58.0], [54.1, 58.0], [54.2, 58.0], [54.3, 58.0], [54.4, 58.0], [54.5, 58.0], [54.6, 58.0], [54.7, 58.0], [54.8, 58.0], [54.9, 58.0], [55.0, 58.0], [55.1, 58.0], [55.2, 58.0], [55.3, 58.0], [55.4, 58.0], [55.5, 58.0], [55.6, 58.0], [55.7, 58.0], [55.8, 58.0], [55.9, 58.0], [56.0, 58.0], [56.1, 58.0], [56.2, 58.0], [56.3, 58.0], [56.4, 58.0], [56.5, 58.0], [56.6, 58.0], [56.7, 58.0], [56.8, 58.0], [56.9, 58.0], [57.0, 58.0], [57.1, 58.0], [57.2, 58.0], [57.3, 58.0], [57.4, 58.0], [57.5, 58.0], [57.6, 58.0], [57.7, 58.0], [57.8, 58.0], [57.9, 58.0], [58.0, 58.0], [58.1, 58.0], [58.2, 58.0], [58.3, 58.0], [58.4, 58.0], [58.5, 58.0], [58.6, 58.0], [58.7, 58.0], [58.8, 58.0], [58.9, 58.0], [59.0, 58.0], [59.1, 58.0], [59.2, 58.0], [59.3, 58.0], [59.4, 58.0], [59.5, 58.0], [59.6, 58.0], [59.7, 58.0], [59.8, 58.0], [59.9, 58.0], [60.0, 58.0], [60.1, 58.0], [60.2, 58.0], [60.3, 58.0], [60.4, 58.0], [60.5, 58.0], [60.6, 58.0], [60.7, 58.0], [60.8, 58.0], [60.9, 58.0], [61.0, 58.0], [61.1, 58.0], [61.2, 58.0], [61.3, 58.0], [61.4, 58.0], [61.5, 58.0], [61.6, 58.0], [61.7, 58.0], [61.8, 58.0], [61.9, 58.0], [62.0, 58.0], [62.1, 58.0], [62.2, 58.0], [62.3, 58.0], [62.4, 58.0], [62.5, 58.0], [62.6, 58.0], [62.7, 58.0], [62.8, 58.0], [62.9, 58.0], [63.0, 58.0], [63.1, 58.0], [63.2, 58.0], [63.3, 58.0], [63.4, 58.0], [63.5, 58.0], [63.6, 58.0], [63.7, 58.0], [63.8, 58.0], [63.9, 58.0], [64.0, 58.0], [64.1, 58.0], [64.2, 58.0], [64.3, 58.0], [64.4, 58.0], [64.5, 58.0], [64.6, 58.0], [64.7, 58.0], [64.8, 58.0], [64.9, 58.0], [65.0, 58.0], [65.1, 58.0], [65.2, 58.0], [65.3, 58.0], [65.4, 58.0], [65.5, 58.0], [65.6, 58.0], [65.7, 58.0], [65.8, 58.0], [65.9, 58.0], [66.0, 58.0], [66.1, 58.0], [66.2, 58.0], [66.3, 58.0], [66.4, 58.0], [66.5, 58.0], [66.6, 58.0], [66.7, 58.0], [66.8, 58.0], [66.9, 58.0], [67.0, 58.0], [67.1, 58.0], [67.2, 58.0], [67.3, 58.0], [67.4, 58.0], [67.5, 58.0], [67.6, 58.0], [67.7, 58.0], [67.8, 58.0], [67.9, 58.0], [68.0, 58.0], [68.1, 58.0], [68.2, 58.0], [68.3, 58.0], [68.4, 58.0], [68.5, 58.0], [68.6, 58.0], [68.7, 58.0], [68.8, 58.0], [68.9, 58.0], [69.0, 58.0], [69.1, 58.0], [69.2, 58.0], [69.3, 58.0], [69.4, 58.0], [69.5, 58.0], [69.6, 58.0], [69.7, 58.0], [69.8, 58.0], [69.9, 58.0], [70.0, 58.0], [70.1, 58.0], [70.2, 58.0], [70.3, 58.0], [70.4, 58.0], [70.5, 58.0], [70.6, 58.0], [70.7, 58.0], [70.8, 58.0], [70.9, 58.0], [71.0, 58.0], [71.1, 58.0], [71.2, 58.0], [71.3, 58.0], [71.4, 58.0], [71.5, 58.0], [71.6, 58.0], [71.7, 58.0], [71.8, 58.0], [71.9, 58.0], [72.0, 58.0], [72.1, 58.0], [72.2, 58.0], [72.3, 58.0], [72.4, 58.0], [72.5, 58.0], [72.6, 58.0], [72.7, 58.0], [72.8, 58.0], [72.9, 58.0], [73.0, 58.0], [73.1, 58.0], [73.2, 58.0], [73.3, 58.0], [73.4, 58.0], [73.5, 58.0], [73.6, 58.0], [73.7, 58.0], [73.8, 58.0], [73.9, 58.0], [74.0, 58.0], [74.1, 58.0], [74.2, 58.0], [74.3, 58.0], [74.4, 58.0], [74.5, 58.0], [74.6, 58.0], [74.7, 58.0], [74.8, 58.0], [74.9, 58.0], [75.0, 58.0], [75.1, 58.0], [75.2, 58.0], [75.3, 58.0], [75.4, 58.0], [75.5, 58.0], [75.6, 58.0], [75.7, 58.0], [75.8, 58.0], [75.9, 58.0], [76.0, 58.0], [76.1, 58.0], [76.2, 58.0], [76.3, 58.0], [76.4, 58.0], [76.5, 58.0], [76.6, 58.0], [76.7, 58.0], [76.8, 58.0], [76.9, 58.0], [77.0, 58.0], [77.1, 58.0], [77.2, 58.0], [77.3, 58.0], [77.4, 58.0], [77.5, 58.0], [77.6, 58.0], [77.7, 58.0], [77.8, 58.0], [77.9, 58.0], [78.0, 58.0], [78.1, 58.0], [78.2, 58.0], [78.3, 58.0], [78.4, 58.0], [78.5, 58.0], [78.6, 58.0], [78.7, 58.0], [78.8, 58.0], [78.9, 58.0], [79.0, 58.0], [79.1, 58.0], [79.2, 58.0], [79.3, 58.0], [79.4, 58.0], [79.5, 58.0], [79.6, 58.0], [79.7, 58.0], [79.8, 58.0], [79.9, 58.0], [80.0, 58.0], [80.1, 58.0], [80.2, 58.0], [80.3, 58.0], [80.4, 58.0], [80.5, 58.0], [80.6, 58.0], [80.7, 58.0], [80.8, 58.0], [80.9, 58.0], [81.0, 58.0], [81.1, 58.0], [81.2, 58.0], [81.3, 58.0], [81.4, 58.0], [81.5, 58.0], [81.6, 58.0], [81.7, 58.0], [81.8, 58.0], [81.9, 58.0], [82.0, 58.0], [82.1, 58.0], [82.2, 58.0], [82.3, 58.0], [82.4, 58.0], [82.5, 58.0], [82.6, 58.0], [82.7, 58.0], [82.8, 58.0], [82.9, 58.0], [83.0, 58.0], [83.1, 58.0], [83.2, 58.0], [83.3, 58.0], [83.4, 58.0], [83.5, 58.0], [83.6, 58.0], [83.7, 58.0], [83.8, 58.0], [83.9, 58.0], [84.0, 58.0], [84.1, 58.0], [84.2, 58.0], [84.3, 58.0], [84.4, 58.0], [84.5, 58.0], [84.6, 58.0], [84.7, 58.0], [84.8, 58.0], [84.9, 58.0], [85.0, 58.0], [85.1, 58.0], [85.2, 58.0], [85.3, 58.0], [85.4, 58.0], [85.5, 58.0], [85.6, 58.0], [85.7, 58.0], [85.8, 58.0], [85.9, 58.0], [86.0, 58.0], [86.1, 58.0], [86.2, 58.0], [86.3, 58.0], [86.4, 58.0], [86.5, 58.0], [86.6, 58.0], [86.7, 58.0], [86.8, 58.0], [86.9, 58.0], [87.0, 58.0], [87.1, 58.0], [87.2, 58.0], [87.3, 58.0], [87.4, 58.0], [87.5, 58.0], [87.6, 58.0], [87.7, 58.0], [87.8, 58.0], [87.9, 58.0], [88.0, 58.0], [88.1, 58.0], [88.2, 58.0], [88.3, 58.0], [88.4, 58.0], [88.5, 58.0], [88.6, 58.0], [88.7, 58.0], [88.8, 58.0], [88.9, 58.0], [89.0, 58.0], [89.1, 58.0], [89.2, 58.0], [89.3, 58.0], [89.4, 58.0], [89.5, 58.0], [89.6, 58.0], [89.7, 58.0], [89.8, 58.0], [89.9, 58.0], [90.0, 58.0], [90.1, 58.0], [90.2, 58.0], [90.3, 58.0], [90.4, 58.0], [90.5, 58.0], [90.6, 58.0], [90.7, 58.0], [90.8, 58.0], [90.9, 58.0], [91.0, 58.0], [91.1, 58.0], [91.2, 58.0], [91.3, 58.0], [91.4, 58.0], [91.5, 58.0], [91.6, 58.0], [91.7, 58.0], [91.8, 58.0], [91.9, 58.0], [92.0, 58.0], [92.1, 58.0], [92.2, 58.0], [92.3, 58.0], [92.4, 58.0], [92.5, 58.0], [92.6, 58.0], [92.7, 58.0], [92.8, 58.0], [92.9, 58.0], [93.0, 58.0], [93.1, 58.0], [93.2, 58.0], [93.3, 58.0], [93.4, 58.0], [93.5, 58.0], [93.6, 58.0], [93.7, 58.0], [93.8, 58.0], [93.9, 58.0], [94.0, 58.0], [94.1, 58.0], [94.2, 58.0], [94.3, 58.0], [94.4, 58.0], [94.5, 58.0], [94.6, 58.0], [94.7, 58.0], [94.8, 58.0], [94.9, 58.0], [95.0, 58.0], [95.1, 58.0], [95.2, 58.0], [95.3, 58.0], [95.4, 58.0], [95.5, 58.0], [95.6, 58.0], [95.7, 58.0], [95.8, 58.0], [95.9, 58.0], [96.0, 58.0], [96.1, 58.0], [96.2, 58.0], [96.3, 58.0], [96.4, 58.0], [96.5, 58.0], [96.6, 58.0], [96.7, 58.0], [96.8, 58.0], [96.9, 58.0], [97.0, 58.0], [97.1, 58.0], [97.2, 58.0], [97.3, 58.0], [97.4, 58.0], [97.5, 58.0], [97.6, 58.0], [97.7, 58.0], [97.8, 58.0], [97.9, 58.0], [98.0, 58.0], [98.1, 58.0], [98.2, 58.0], [98.3, 58.0], [98.4, 58.0], [98.5, 58.0], [98.6, 58.0], [98.7, 58.0], [98.8, 58.0], [98.9, 58.0], [99.0, 58.0], [99.1, 58.0], [99.2, 58.0], [99.3, 58.0], [99.4, 58.0], [99.5, 58.0], [99.6, 58.0], [99.7, 58.0], [99.8, 58.0], [99.9, 58.0]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[0.0, 5.0], [0.1, 5.0], [0.2, 5.0], [0.3, 5.0], [0.4, 5.0], [0.5, 5.0], [0.6, 5.0], [0.7, 5.0], [0.8, 5.0], [0.9, 5.0], [1.0, 5.0], [1.1, 5.0], [1.2, 5.0], [1.3, 5.0], [1.4, 5.0], [1.5, 5.0], [1.6, 5.0], [1.7, 5.0], [1.8, 5.0], [1.9, 5.0], [2.0, 5.0], [2.1, 5.0], [2.2, 5.0], [2.3, 5.0], [2.4, 5.0], [2.5, 5.0], [2.6, 5.0], [2.7, 5.0], [2.8, 5.0], [2.9, 5.0], [3.0, 5.0], [3.1, 5.0], [3.2, 5.0], [3.3, 5.0], [3.4, 5.0], [3.5, 5.0], [3.6, 5.0], [3.7, 5.0], [3.8, 5.0], [3.9, 5.0], [4.0, 5.0], [4.1, 5.0], [4.2, 5.0], [4.3, 5.0], [4.4, 5.0], [4.5, 5.0], [4.6, 5.0], [4.7, 5.0], [4.8, 5.0], [4.9, 5.0], [5.0, 5.0], [5.1, 5.0], [5.2, 5.0], [5.3, 5.0], [5.4, 5.0], [5.5, 5.0], [5.6, 5.0], [5.7, 5.0], [5.8, 5.0], [5.9, 5.0], [6.0, 5.0], [6.1, 5.0], [6.2, 5.0], [6.3, 5.0], [6.4, 5.0], [6.5, 5.0], [6.6, 5.0], [6.7, 5.0], [6.8, 5.0], [6.9, 5.0], [7.0, 5.0], [7.1, 5.0], [7.2, 5.0], [7.3, 5.0], [7.4, 5.0], [7.5, 5.0], [7.6, 5.0], [7.7, 5.0], [7.8, 5.0], [7.9, 5.0], [8.0, 5.0], [8.1, 5.0], [8.2, 5.0], [8.3, 5.0], [8.4, 5.0], [8.5, 5.0], [8.6, 5.0], [8.7, 5.0], [8.8, 5.0], [8.9, 5.0], [9.0, 5.0], [9.1, 5.0], [9.2, 5.0], [9.3, 5.0], [9.4, 5.0], [9.5, 5.0], [9.6, 5.0], [9.7, 5.0], [9.8, 5.0], [9.9, 5.0], [10.0, 5.0], [10.1, 5.0], [10.2, 5.0], [10.3, 5.0], [10.4, 5.0], [10.5, 5.0], [10.6, 5.0], [10.7, 5.0], [10.8, 5.0], [10.9, 5.0], [11.0, 5.0], [11.1, 5.0], [11.2, 5.0], [11.3, 5.0], [11.4, 5.0], [11.5, 5.0], [11.6, 5.0], [11.7, 5.0], [11.8, 5.0], [11.9, 5.0], [12.0, 5.0], [12.1, 5.0], [12.2, 5.0], [12.3, 5.0], [12.4, 5.0], [12.5, 5.0], [12.6, 5.0], [12.7, 5.0], [12.8, 5.0], [12.9, 5.0], [13.0, 5.0], [13.1, 5.0], [13.2, 5.0], [13.3, 5.0], [13.4, 5.0], [13.5, 5.0], [13.6, 5.0], [13.7, 5.0], [13.8, 5.0], [13.9, 5.0], [14.0, 5.0], [14.1, 5.0], [14.2, 5.0], [14.3, 5.0], [14.4, 5.0], [14.5, 5.0], [14.6, 5.0], [14.7, 5.0], [14.8, 5.0], [14.9, 5.0], [15.0, 5.0], [15.1, 5.0], [15.2, 5.0], [15.3, 5.0], [15.4, 5.0], [15.5, 5.0], [15.6, 5.0], [15.7, 5.0], [15.8, 5.0], [15.9, 5.0], [16.0, 5.0], [16.1, 5.0], [16.2, 5.0], [16.3, 5.0], [16.4, 5.0], [16.5, 5.0], [16.6, 5.0], [16.7, 5.0], [16.8, 5.0], [16.9, 5.0], [17.0, 5.0], [17.1, 5.0], [17.2, 5.0], [17.3, 5.0], [17.4, 5.0], [17.5, 5.0], [17.6, 5.0], [17.7, 5.0], [17.8, 5.0], [17.9, 5.0], [18.0, 5.0], [18.1, 5.0], [18.2, 5.0], [18.3, 5.0], [18.4, 5.0], [18.5, 5.0], [18.6, 5.0], [18.7, 5.0], [18.8, 5.0], [18.9, 5.0], [19.0, 5.0], [19.1, 5.0], [19.2, 5.0], [19.3, 5.0], [19.4, 5.0], [19.5, 5.0], [19.6, 5.0], [19.7, 5.0], [19.8, 5.0], [19.9, 5.0], [20.0, 5.0], [20.1, 5.0], [20.2, 5.0], [20.3, 5.0], [20.4, 5.0], [20.5, 5.0], [20.6, 5.0], [20.7, 5.0], [20.8, 5.0], [20.9, 5.0], [21.0, 5.0], [21.1, 5.0], [21.2, 5.0], [21.3, 5.0], [21.4, 5.0], [21.5, 5.0], [21.6, 5.0], [21.7, 5.0], [21.8, 5.0], [21.9, 5.0], [22.0, 5.0], [22.1, 5.0], [22.2, 5.0], [22.3, 5.0], [22.4, 5.0], [22.5, 5.0], [22.6, 5.0], [22.7, 5.0], [22.8, 5.0], [22.9, 5.0], [23.0, 5.0], [23.1, 5.0], [23.2, 5.0], [23.3, 5.0], [23.4, 5.0], [23.5, 5.0], [23.6, 5.0], [23.7, 5.0], [23.8, 5.0], [23.9, 5.0], [24.0, 5.0], [24.1, 5.0], [24.2, 5.0], [24.3, 5.0], [24.4, 5.0], [24.5, 5.0], [24.6, 5.0], [24.7, 5.0], [24.8, 5.0], [24.9, 5.0], [25.0, 5.0], [25.1, 5.0], [25.2, 5.0], [25.3, 5.0], [25.4, 5.0], [25.5, 5.0], [25.6, 5.0], [25.7, 5.0], [25.8, 5.0], [25.9, 5.0], [26.0, 5.0], [26.1, 5.0], [26.2, 5.0], [26.3, 5.0], [26.4, 5.0], [26.5, 5.0], [26.6, 5.0], [26.7, 5.0], [26.8, 5.0], [26.9, 5.0], [27.0, 5.0], [27.1, 5.0], [27.2, 5.0], [27.3, 5.0], [27.4, 5.0], [27.5, 5.0], [27.6, 5.0], [27.7, 5.0], [27.8, 5.0], [27.9, 5.0], [28.0, 5.0], [28.1, 5.0], [28.2, 5.0], [28.3, 5.0], [28.4, 5.0], [28.5, 5.0], [28.6, 5.0], [28.7, 5.0], [28.8, 5.0], [28.9, 5.0], [29.0, 5.0], [29.1, 5.0], [29.2, 5.0], [29.3, 5.0], [29.4, 5.0], [29.5, 5.0], [29.6, 5.0], [29.7, 5.0], [29.8, 5.0], [29.9, 5.0], [30.0, 5.0], [30.1, 5.0], [30.2, 5.0], [30.3, 5.0], [30.4, 5.0], [30.5, 5.0], [30.6, 5.0], [30.7, 5.0], [30.8, 5.0], [30.9, 5.0], [31.0, 5.0], [31.1, 5.0], [31.2, 5.0], [31.3, 5.0], [31.4, 5.0], [31.5, 5.0], [31.6, 5.0], [31.7, 5.0], [31.8, 5.0], [31.9, 5.0], [32.0, 5.0], [32.1, 5.0], [32.2, 5.0], [32.3, 5.0], [32.4, 5.0], [32.5, 5.0], [32.6, 5.0], [32.7, 5.0], [32.8, 5.0], [32.9, 5.0], [33.0, 5.0], [33.1, 5.0], [33.2, 5.0], [33.3, 5.0], [33.4, 5.0], [33.5, 5.0], [33.6, 5.0], [33.7, 5.0], [33.8, 5.0], [33.9, 5.0], [34.0, 5.0], [34.1, 5.0], [34.2, 5.0], [34.3, 5.0], [34.4, 5.0], [34.5, 5.0], [34.6, 5.0], [34.7, 5.0], [34.8, 5.0], [34.9, 5.0], [35.0, 5.0], [35.1, 5.0], [35.2, 5.0], [35.3, 5.0], [35.4, 5.0], [35.5, 5.0], [35.6, 5.0], [35.7, 5.0], [35.8, 5.0], [35.9, 5.0], [36.0, 5.0], [36.1, 5.0], [36.2, 5.0], [36.3, 5.0], [36.4, 5.0], [36.5, 5.0], [36.6, 5.0], [36.7, 5.0], [36.8, 5.0], [36.9, 5.0], [37.0, 5.0], [37.1, 5.0], [37.2, 5.0], [37.3, 5.0], [37.4, 5.0], [37.5, 5.0], [37.6, 5.0], [37.7, 5.0], [37.8, 5.0], [37.9, 5.0], [38.0, 5.0], [38.1, 5.0], [38.2, 5.0], [38.3, 5.0], [38.4, 5.0], [38.5, 5.0], [38.6, 5.0], [38.7, 5.0], [38.8, 5.0], [38.9, 5.0], [39.0, 5.0], [39.1, 5.0], [39.2, 5.0], [39.3, 5.0], [39.4, 5.0], [39.5, 5.0], [39.6, 5.0], [39.7, 5.0], [39.8, 5.0], [39.9, 5.0], [40.0, 5.0], [40.1, 5.0], [40.2, 5.0], [40.3, 5.0], [40.4, 5.0], [40.5, 5.0], [40.6, 5.0], [40.7, 5.0], [40.8, 5.0], [40.9, 5.0], [41.0, 5.0], [41.1, 5.0], [41.2, 5.0], [41.3, 5.0], [41.4, 5.0], [41.5, 5.0], [41.6, 5.0], [41.7, 5.0], [41.8, 5.0], [41.9, 5.0], [42.0, 5.0], [42.1, 5.0], [42.2, 5.0], [42.3, 5.0], [42.4, 5.0], [42.5, 5.0], [42.6, 5.0], [42.7, 5.0], [42.8, 5.0], [42.9, 5.0], [43.0, 5.0], [43.1, 5.0], [43.2, 5.0], [43.3, 5.0], [43.4, 5.0], [43.5, 5.0], [43.6, 5.0], [43.7, 5.0], [43.8, 5.0], [43.9, 5.0], [44.0, 5.0], [44.1, 5.0], [44.2, 5.0], [44.3, 5.0], [44.4, 5.0], [44.5, 5.0], [44.6, 5.0], [44.7, 5.0], [44.8, 5.0], [44.9, 5.0], [45.0, 5.0], [45.1, 5.0], [45.2, 5.0], [45.3, 5.0], [45.4, 5.0], [45.5, 5.0], [45.6, 5.0], [45.7, 5.0], [45.8, 5.0], [45.9, 5.0], [46.0, 5.0], [46.1, 5.0], [46.2, 5.0], [46.3, 5.0], [46.4, 5.0], [46.5, 5.0], [46.6, 5.0], [46.7, 5.0], [46.8, 5.0], [46.9, 5.0], [47.0, 5.0], [47.1, 5.0], [47.2, 5.0], [47.3, 5.0], [47.4, 5.0], [47.5, 5.0], [47.6, 5.0], [47.7, 5.0], [47.8, 5.0], [47.9, 5.0], [48.0, 5.0], [48.1, 5.0], [48.2, 5.0], [48.3, 5.0], [48.4, 5.0], [48.5, 5.0], [48.6, 5.0], [48.7, 5.0], [48.8, 5.0], [48.9, 5.0], [49.0, 5.0], [49.1, 5.0], [49.2, 5.0], [49.3, 5.0], [49.4, 5.0], [49.5, 5.0], [49.6, 5.0], [49.7, 5.0], [49.8, 5.0], [49.9, 5.0], [50.0, 5.0], [50.1, 5.0], [50.2, 5.0], [50.3, 5.0], [50.4, 5.0], [50.5, 5.0], [50.6, 5.0], [50.7, 5.0], [50.8, 5.0], [50.9, 5.0], [51.0, 5.0], [51.1, 5.0], [51.2, 5.0], [51.3, 5.0], [51.4, 5.0], [51.5, 5.0], [51.6, 5.0], [51.7, 5.0], [51.8, 5.0], [51.9, 5.0], [52.0, 5.0], [52.1, 5.0], [52.2, 5.0], [52.3, 5.0], [52.4, 5.0], [52.5, 5.0], [52.6, 5.0], [52.7, 5.0], [52.8, 5.0], [52.9, 5.0], [53.0, 5.0], [53.1, 5.0], [53.2, 5.0], [53.3, 5.0], [53.4, 5.0], [53.5, 5.0], [53.6, 5.0], [53.7, 5.0], [53.8, 5.0], [53.9, 5.0], [54.0, 5.0], [54.1, 5.0], [54.2, 5.0], [54.3, 5.0], [54.4, 5.0], [54.5, 5.0], [54.6, 5.0], [54.7, 5.0], [54.8, 5.0], [54.9, 5.0], [55.0, 5.0], [55.1, 5.0], [55.2, 5.0], [55.3, 5.0], [55.4, 5.0], [55.5, 5.0], [55.6, 5.0], [55.7, 5.0], [55.8, 5.0], [55.9, 5.0], [56.0, 5.0], [56.1, 5.0], [56.2, 5.0], [56.3, 5.0], [56.4, 5.0], [56.5, 5.0], [56.6, 5.0], [56.7, 5.0], [56.8, 5.0], [56.9, 5.0], [57.0, 5.0], [57.1, 5.0], [57.2, 5.0], [57.3, 5.0], [57.4, 5.0], [57.5, 5.0], [57.6, 5.0], [57.7, 5.0], [57.8, 5.0], [57.9, 5.0], [58.0, 5.0], [58.1, 5.0], [58.2, 5.0], [58.3, 5.0], [58.4, 5.0], [58.5, 5.0], [58.6, 5.0], [58.7, 5.0], [58.8, 5.0], [58.9, 5.0], [59.0, 5.0], [59.1, 5.0], [59.2, 5.0], [59.3, 5.0], [59.4, 5.0], [59.5, 5.0], [59.6, 5.0], [59.7, 5.0], [59.8, 5.0], [59.9, 5.0], [60.0, 5.0], [60.1, 5.0], [60.2, 5.0], [60.3, 5.0], [60.4, 5.0], [60.5, 5.0], [60.6, 5.0], [60.7, 5.0], [60.8, 5.0], [60.9, 5.0], [61.0, 5.0], [61.1, 5.0], [61.2, 5.0], [61.3, 5.0], [61.4, 5.0], [61.5, 5.0], [61.6, 5.0], [61.7, 5.0], [61.8, 5.0], [61.9, 5.0], [62.0, 5.0], [62.1, 5.0], [62.2, 5.0], [62.3, 5.0], [62.4, 5.0], [62.5, 5.0], [62.6, 5.0], [62.7, 5.0], [62.8, 5.0], [62.9, 5.0], [63.0, 5.0], [63.1, 5.0], [63.2, 5.0], [63.3, 5.0], [63.4, 5.0], [63.5, 5.0], [63.6, 5.0], [63.7, 5.0], [63.8, 5.0], [63.9, 5.0], [64.0, 5.0], [64.1, 5.0], [64.2, 5.0], [64.3, 5.0], [64.4, 5.0], [64.5, 5.0], [64.6, 5.0], [64.7, 5.0], [64.8, 5.0], [64.9, 5.0], [65.0, 5.0], [65.1, 5.0], [65.2, 5.0], [65.3, 5.0], [65.4, 5.0], [65.5, 5.0], [65.6, 5.0], [65.7, 5.0], [65.8, 5.0], [65.9, 5.0], [66.0, 5.0], [66.1, 5.0], [66.2, 5.0], [66.3, 5.0], [66.4, 5.0], [66.5, 5.0], [66.6, 5.0], [66.7, 5.0], [66.8, 5.0], [66.9, 5.0], [67.0, 5.0], [67.1, 5.0], [67.2, 5.0], [67.3, 5.0], [67.4, 5.0], [67.5, 5.0], [67.6, 5.0], [67.7, 5.0], [67.8, 5.0], [67.9, 5.0], [68.0, 5.0], [68.1, 5.0], [68.2, 5.0], [68.3, 5.0], [68.4, 5.0], [68.5, 5.0], [68.6, 5.0], [68.7, 5.0], [68.8, 5.0], [68.9, 5.0], [69.0, 5.0], [69.1, 5.0], [69.2, 5.0], [69.3, 5.0], [69.4, 5.0], [69.5, 5.0], [69.6, 5.0], [69.7, 5.0], [69.8, 5.0], [69.9, 5.0], [70.0, 5.0], [70.1, 5.0], [70.2, 5.0], [70.3, 5.0], [70.4, 5.0], [70.5, 5.0], [70.6, 5.0], [70.7, 5.0], [70.8, 5.0], [70.9, 5.0], [71.0, 5.0], [71.1, 5.0], [71.2, 5.0], [71.3, 5.0], [71.4, 5.0], [71.5, 5.0], [71.6, 5.0], [71.7, 5.0], [71.8, 5.0], [71.9, 5.0], [72.0, 5.0], [72.1, 5.0], [72.2, 5.0], [72.3, 5.0], [72.4, 5.0], [72.5, 5.0], [72.6, 5.0], [72.7, 5.0], [72.8, 5.0], [72.9, 5.0], [73.0, 5.0], [73.1, 5.0], [73.2, 5.0], [73.3, 5.0], [73.4, 5.0], [73.5, 5.0], [73.6, 5.0], [73.7, 5.0], [73.8, 5.0], [73.9, 5.0], [74.0, 5.0], [74.1, 5.0], [74.2, 5.0], [74.3, 5.0], [74.4, 5.0], [74.5, 5.0], [74.6, 5.0], [74.7, 5.0], [74.8, 5.0], [74.9, 5.0], [75.0, 5.0], [75.1, 5.0], [75.2, 5.0], [75.3, 5.0], [75.4, 5.0], [75.5, 5.0], [75.6, 5.0], [75.7, 5.0], [75.8, 5.0], [75.9, 5.0], [76.0, 5.0], [76.1, 5.0], [76.2, 5.0], [76.3, 5.0], [76.4, 5.0], [76.5, 5.0], [76.6, 5.0], [76.7, 5.0], [76.8, 5.0], [76.9, 5.0], [77.0, 5.0], [77.1, 5.0], [77.2, 5.0], [77.3, 5.0], [77.4, 5.0], [77.5, 5.0], [77.6, 5.0], [77.7, 5.0], [77.8, 5.0], [77.9, 5.0], [78.0, 5.0], [78.1, 5.0], [78.2, 5.0], [78.3, 5.0], [78.4, 5.0], [78.5, 5.0], [78.6, 5.0], [78.7, 5.0], [78.8, 5.0], [78.9, 5.0], [79.0, 5.0], [79.1, 5.0], [79.2, 5.0], [79.3, 5.0], [79.4, 5.0], [79.5, 5.0], [79.6, 5.0], [79.7, 5.0], [79.8, 5.0], [79.9, 5.0], [80.0, 5.0], [80.1, 5.0], [80.2, 5.0], [80.3, 5.0], [80.4, 5.0], [80.5, 5.0], [80.6, 5.0], [80.7, 5.0], [80.8, 5.0], [80.9, 5.0], [81.0, 5.0], [81.1, 5.0], [81.2, 5.0], [81.3, 5.0], [81.4, 5.0], [81.5, 5.0], [81.6, 5.0], [81.7, 5.0], [81.8, 5.0], [81.9, 5.0], [82.0, 5.0], [82.1, 5.0], [82.2, 5.0], [82.3, 5.0], [82.4, 5.0], [82.5, 5.0], [82.6, 5.0], [82.7, 5.0], [82.8, 5.0], [82.9, 5.0], [83.0, 5.0], [83.1, 5.0], [83.2, 5.0], [83.3, 5.0], [83.4, 5.0], [83.5, 5.0], [83.6, 5.0], [83.7, 5.0], [83.8, 5.0], [83.9, 5.0], [84.0, 5.0], [84.1, 5.0], [84.2, 5.0], [84.3, 5.0], [84.4, 5.0], [84.5, 5.0], [84.6, 5.0], [84.7, 5.0], [84.8, 5.0], [84.9, 5.0], [85.0, 5.0], [85.1, 5.0], [85.2, 5.0], [85.3, 5.0], [85.4, 5.0], [85.5, 5.0], [85.6, 5.0], [85.7, 5.0], [85.8, 5.0], [85.9, 5.0], [86.0, 5.0], [86.1, 5.0], [86.2, 5.0], [86.3, 5.0], [86.4, 5.0], [86.5, 5.0], [86.6, 5.0], [86.7, 5.0], [86.8, 5.0], [86.9, 5.0], [87.0, 5.0], [87.1, 5.0], [87.2, 5.0], [87.3, 5.0], [87.4, 5.0], [87.5, 5.0], [87.6, 5.0], [87.7, 5.0], [87.8, 5.0], [87.9, 5.0], [88.0, 5.0], [88.1, 5.0], [88.2, 5.0], [88.3, 5.0], [88.4, 5.0], [88.5, 5.0], [88.6, 5.0], [88.7, 5.0], [88.8, 5.0], [88.9, 5.0], [89.0, 5.0], [89.1, 5.0], [89.2, 5.0], [89.3, 5.0], [89.4, 5.0], [89.5, 5.0], [89.6, 5.0], [89.7, 5.0], [89.8, 5.0], [89.9, 5.0], [90.0, 5.0], [90.1, 5.0], [90.2, 5.0], [90.3, 5.0], [90.4, 5.0], [90.5, 5.0], [90.6, 5.0], [90.7, 5.0], [90.8, 5.0], [90.9, 5.0], [91.0, 5.0], [91.1, 5.0], [91.2, 5.0], [91.3, 5.0], [91.4, 5.0], [91.5, 5.0], [91.6, 5.0], [91.7, 5.0], [91.8, 5.0], [91.9, 5.0], [92.0, 5.0], [92.1, 5.0], [92.2, 5.0], [92.3, 5.0], [92.4, 5.0], [92.5, 5.0], [92.6, 5.0], [92.7, 5.0], [92.8, 5.0], [92.9, 5.0], [93.0, 5.0], [93.1, 5.0], [93.2, 5.0], [93.3, 5.0], [93.4, 5.0], [93.5, 5.0], [93.6, 5.0], [93.7, 5.0], [93.8, 5.0], [93.9, 5.0], [94.0, 5.0], [94.1, 5.0], [94.2, 5.0], [94.3, 5.0], [94.4, 5.0], [94.5, 5.0], [94.6, 5.0], [94.7, 5.0], [94.8, 5.0], [94.9, 5.0], [95.0, 5.0], [95.1, 5.0], [95.2, 5.0], [95.3, 5.0], [95.4, 5.0], [95.5, 5.0], [95.6, 5.0], [95.7, 5.0], [95.8, 5.0], [95.9, 5.0], [96.0, 5.0], [96.1, 5.0], [96.2, 5.0], [96.3, 5.0], [96.4, 5.0], [96.5, 5.0], [96.6, 5.0], [96.7, 5.0], [96.8, 5.0], [96.9, 5.0], [97.0, 5.0], [97.1, 5.0], [97.2, 5.0], [97.3, 5.0], [97.4, 5.0], [97.5, 5.0], [97.6, 5.0], [97.7, 5.0], [97.8, 5.0], [97.9, 5.0], [98.0, 5.0], [98.1, 5.0], [98.2, 5.0], [98.3, 5.0], [98.4, 5.0], [98.5, 5.0], [98.6, 5.0], [98.7, 5.0], [98.8, 5.0], [98.9, 5.0], [99.0, 5.0], [99.1, 5.0], [99.2, 5.0], [99.3, 5.0], [99.4, 5.0], [99.5, 5.0], [99.6, 5.0], [99.7, 5.0], [99.8, 5.0], [99.9, 5.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[0.0, 5.0], [0.1, 5.0], [0.2, 5.0], [0.3, 5.0], [0.4, 5.0], [0.5, 5.0], [0.6, 5.0], [0.7, 5.0], [0.8, 5.0], [0.9, 5.0], [1.0, 5.0], [1.1, 5.0], [1.2, 5.0], [1.3, 5.0], [1.4, 5.0], [1.5, 5.0], [1.6, 5.0], [1.7, 5.0], [1.8, 5.0], [1.9, 5.0], [2.0, 5.0], [2.1, 5.0], [2.2, 5.0], [2.3, 5.0], [2.4, 5.0], [2.5, 5.0], [2.6, 5.0], [2.7, 5.0], [2.8, 5.0], [2.9, 5.0], [3.0, 5.0], [3.1, 5.0], [3.2, 5.0], [3.3, 5.0], [3.4, 5.0], [3.5, 5.0], [3.6, 5.0], [3.7, 5.0], [3.8, 5.0], [3.9, 5.0], [4.0, 5.0], [4.1, 5.0], [4.2, 5.0], [4.3, 5.0], [4.4, 5.0], [4.5, 5.0], [4.6, 5.0], [4.7, 5.0], [4.8, 5.0], [4.9, 5.0], [5.0, 5.0], [5.1, 5.0], [5.2, 5.0], [5.3, 5.0], [5.4, 5.0], [5.5, 5.0], [5.6, 5.0], [5.7, 5.0], [5.8, 5.0], [5.9, 5.0], [6.0, 5.0], [6.1, 5.0], [6.2, 5.0], [6.3, 5.0], [6.4, 5.0], [6.5, 5.0], [6.6, 5.0], [6.7, 5.0], [6.8, 5.0], [6.9, 5.0], [7.0, 5.0], [7.1, 5.0], [7.2, 5.0], [7.3, 5.0], [7.4, 5.0], [7.5, 5.0], [7.6, 5.0], [7.7, 5.0], [7.8, 5.0], [7.9, 5.0], [8.0, 5.0], [8.1, 5.0], [8.2, 5.0], [8.3, 5.0], [8.4, 5.0], [8.5, 5.0], [8.6, 5.0], [8.7, 5.0], [8.8, 5.0], [8.9, 5.0], [9.0, 5.0], [9.1, 5.0], [9.2, 5.0], [9.3, 5.0], [9.4, 5.0], [9.5, 5.0], [9.6, 5.0], [9.7, 5.0], [9.8, 5.0], [9.9, 5.0], [10.0, 5.0], [10.1, 5.0], [10.2, 5.0], [10.3, 5.0], [10.4, 5.0], [10.5, 5.0], [10.6, 5.0], [10.7, 5.0], [10.8, 5.0], [10.9, 5.0], [11.0, 5.0], [11.1, 5.0], [11.2, 5.0], [11.3, 5.0], [11.4, 5.0], [11.5, 5.0], [11.6, 5.0], [11.7, 5.0], [11.8, 5.0], [11.9, 5.0], [12.0, 5.0], [12.1, 5.0], [12.2, 5.0], [12.3, 5.0], [12.4, 5.0], [12.5, 5.0], [12.6, 5.0], [12.7, 5.0], [12.8, 5.0], [12.9, 5.0], [13.0, 5.0], [13.1, 5.0], [13.2, 5.0], [13.3, 5.0], [13.4, 5.0], [13.5, 5.0], [13.6, 5.0], [13.7, 5.0], [13.8, 5.0], [13.9, 5.0], [14.0, 5.0], [14.1, 5.0], [14.2, 5.0], [14.3, 5.0], [14.4, 5.0], [14.5, 5.0], [14.6, 5.0], [14.7, 5.0], [14.8, 5.0], [14.9, 5.0], [15.0, 5.0], [15.1, 5.0], [15.2, 5.0], [15.3, 5.0], [15.4, 5.0], [15.5, 5.0], [15.6, 5.0], [15.7, 5.0], [15.8, 5.0], [15.9, 5.0], [16.0, 5.0], [16.1, 5.0], [16.2, 5.0], [16.3, 5.0], [16.4, 5.0], [16.5, 5.0], [16.6, 5.0], [16.7, 5.0], [16.8, 5.0], [16.9, 5.0], [17.0, 5.0], [17.1, 5.0], [17.2, 5.0], [17.3, 5.0], [17.4, 5.0], [17.5, 5.0], [17.6, 5.0], [17.7, 5.0], [17.8, 5.0], [17.9, 5.0], [18.0, 5.0], [18.1, 5.0], [18.2, 5.0], [18.3, 5.0], [18.4, 5.0], [18.5, 5.0], [18.6, 5.0], [18.7, 5.0], [18.8, 5.0], [18.9, 5.0], [19.0, 5.0], [19.1, 5.0], [19.2, 5.0], [19.3, 5.0], [19.4, 5.0], [19.5, 5.0], [19.6, 5.0], [19.7, 5.0], [19.8, 5.0], [19.9, 5.0], [20.0, 5.0], [20.1, 5.0], [20.2, 5.0], [20.3, 5.0], [20.4, 5.0], [20.5, 5.0], [20.6, 5.0], [20.7, 5.0], [20.8, 5.0], [20.9, 5.0], [21.0, 5.0], [21.1, 5.0], [21.2, 5.0], [21.3, 5.0], [21.4, 5.0], [21.5, 5.0], [21.6, 5.0], [21.7, 5.0], [21.8, 5.0], [21.9, 5.0], [22.0, 5.0], [22.1, 5.0], [22.2, 5.0], [22.3, 5.0], [22.4, 5.0], [22.5, 5.0], [22.6, 5.0], [22.7, 5.0], [22.8, 5.0], [22.9, 5.0], [23.0, 5.0], [23.1, 5.0], [23.2, 5.0], [23.3, 5.0], [23.4, 5.0], [23.5, 5.0], [23.6, 5.0], [23.7, 5.0], [23.8, 5.0], [23.9, 5.0], [24.0, 5.0], [24.1, 5.0], [24.2, 5.0], [24.3, 5.0], [24.4, 5.0], [24.5, 5.0], [24.6, 5.0], [24.7, 5.0], [24.8, 5.0], [24.9, 5.0], [25.0, 5.0], [25.1, 5.0], [25.2, 5.0], [25.3, 5.0], [25.4, 5.0], [25.5, 5.0], [25.6, 5.0], [25.7, 5.0], [25.8, 5.0], [25.9, 5.0], [26.0, 5.0], [26.1, 5.0], [26.2, 5.0], [26.3, 5.0], [26.4, 5.0], [26.5, 5.0], [26.6, 5.0], [26.7, 5.0], [26.8, 5.0], [26.9, 5.0], [27.0, 5.0], [27.1, 5.0], [27.2, 5.0], [27.3, 5.0], [27.4, 5.0], [27.5, 5.0], [27.6, 5.0], [27.7, 5.0], [27.8, 5.0], [27.9, 5.0], [28.0, 5.0], [28.1, 5.0], [28.2, 5.0], [28.3, 5.0], [28.4, 5.0], [28.5, 5.0], [28.6, 5.0], [28.7, 5.0], [28.8, 5.0], [28.9, 5.0], [29.0, 5.0], [29.1, 5.0], [29.2, 5.0], [29.3, 5.0], [29.4, 5.0], [29.5, 5.0], [29.6, 5.0], [29.7, 5.0], [29.8, 5.0], [29.9, 5.0], [30.0, 5.0], [30.1, 5.0], [30.2, 5.0], [30.3, 5.0], [30.4, 5.0], [30.5, 5.0], [30.6, 5.0], [30.7, 5.0], [30.8, 5.0], [30.9, 5.0], [31.0, 5.0], [31.1, 5.0], [31.2, 5.0], [31.3, 5.0], [31.4, 5.0], [31.5, 5.0], [31.6, 5.0], [31.7, 5.0], [31.8, 5.0], [31.9, 5.0], [32.0, 5.0], [32.1, 5.0], [32.2, 5.0], [32.3, 5.0], [32.4, 5.0], [32.5, 5.0], [32.6, 5.0], [32.7, 5.0], [32.8, 5.0], [32.9, 5.0], [33.0, 5.0], [33.1, 5.0], [33.2, 5.0], [33.3, 5.0], [33.4, 5.0], [33.5, 5.0], [33.6, 5.0], [33.7, 5.0], [33.8, 5.0], [33.9, 5.0], [34.0, 5.0], [34.1, 5.0], [34.2, 5.0], [34.3, 5.0], [34.4, 5.0], [34.5, 5.0], [34.6, 5.0], [34.7, 5.0], [34.8, 5.0], [34.9, 5.0], [35.0, 5.0], [35.1, 5.0], [35.2, 5.0], [35.3, 5.0], [35.4, 5.0], [35.5, 5.0], [35.6, 5.0], [35.7, 5.0], [35.8, 5.0], [35.9, 5.0], [36.0, 5.0], [36.1, 5.0], [36.2, 5.0], [36.3, 5.0], [36.4, 5.0], [36.5, 5.0], [36.6, 5.0], [36.7, 5.0], [36.8, 5.0], [36.9, 5.0], [37.0, 5.0], [37.1, 5.0], [37.2, 5.0], [37.3, 5.0], [37.4, 5.0], [37.5, 5.0], [37.6, 5.0], [37.7, 5.0], [37.8, 5.0], [37.9, 5.0], [38.0, 5.0], [38.1, 5.0], [38.2, 5.0], [38.3, 5.0], [38.4, 5.0], [38.5, 5.0], [38.6, 5.0], [38.7, 5.0], [38.8, 5.0], [38.9, 5.0], [39.0, 5.0], [39.1, 5.0], [39.2, 5.0], [39.3, 5.0], [39.4, 5.0], [39.5, 5.0], [39.6, 5.0], [39.7, 5.0], [39.8, 5.0], [39.9, 5.0], [40.0, 5.0], [40.1, 5.0], [40.2, 5.0], [40.3, 5.0], [40.4, 5.0], [40.5, 5.0], [40.6, 5.0], [40.7, 5.0], [40.8, 5.0], [40.9, 5.0], [41.0, 5.0], [41.1, 5.0], [41.2, 5.0], [41.3, 5.0], [41.4, 5.0], [41.5, 5.0], [41.6, 5.0], [41.7, 5.0], [41.8, 5.0], [41.9, 5.0], [42.0, 5.0], [42.1, 5.0], [42.2, 5.0], [42.3, 5.0], [42.4, 5.0], [42.5, 5.0], [42.6, 5.0], [42.7, 5.0], [42.8, 5.0], [42.9, 5.0], [43.0, 5.0], [43.1, 5.0], [43.2, 5.0], [43.3, 5.0], [43.4, 5.0], [43.5, 5.0], [43.6, 5.0], [43.7, 5.0], [43.8, 5.0], [43.9, 5.0], [44.0, 5.0], [44.1, 5.0], [44.2, 5.0], [44.3, 5.0], [44.4, 5.0], [44.5, 5.0], [44.6, 5.0], [44.7, 5.0], [44.8, 5.0], [44.9, 5.0], [45.0, 5.0], [45.1, 5.0], [45.2, 5.0], [45.3, 5.0], [45.4, 5.0], [45.5, 5.0], [45.6, 5.0], [45.7, 5.0], [45.8, 5.0], [45.9, 5.0], [46.0, 5.0], [46.1, 5.0], [46.2, 5.0], [46.3, 5.0], [46.4, 5.0], [46.5, 5.0], [46.6, 5.0], [46.7, 5.0], [46.8, 5.0], [46.9, 5.0], [47.0, 5.0], [47.1, 5.0], [47.2, 5.0], [47.3, 5.0], [47.4, 5.0], [47.5, 5.0], [47.6, 5.0], [47.7, 5.0], [47.8, 5.0], [47.9, 5.0], [48.0, 5.0], [48.1, 5.0], [48.2, 5.0], [48.3, 5.0], [48.4, 5.0], [48.5, 5.0], [48.6, 5.0], [48.7, 5.0], [48.8, 5.0], [48.9, 5.0], [49.0, 5.0], [49.1, 5.0], [49.2, 5.0], [49.3, 5.0], [49.4, 5.0], [49.5, 5.0], [49.6, 5.0], [49.7, 5.0], [49.8, 5.0], [49.9, 5.0], [50.0, 5.0], [50.1, 5.0], [50.2, 5.0], [50.3, 5.0], [50.4, 5.0], [50.5, 5.0], [50.6, 5.0], [50.7, 5.0], [50.8, 5.0], [50.9, 5.0], [51.0, 5.0], [51.1, 5.0], [51.2, 5.0], [51.3, 5.0], [51.4, 5.0], [51.5, 5.0], [51.6, 5.0], [51.7, 5.0], [51.8, 5.0], [51.9, 5.0], [52.0, 5.0], [52.1, 5.0], [52.2, 5.0], [52.3, 5.0], [52.4, 5.0], [52.5, 5.0], [52.6, 5.0], [52.7, 5.0], [52.8, 5.0], [52.9, 5.0], [53.0, 5.0], [53.1, 5.0], [53.2, 5.0], [53.3, 5.0], [53.4, 5.0], [53.5, 5.0], [53.6, 5.0], [53.7, 5.0], [53.8, 5.0], [53.9, 5.0], [54.0, 5.0], [54.1, 5.0], [54.2, 5.0], [54.3, 5.0], [54.4, 5.0], [54.5, 5.0], [54.6, 5.0], [54.7, 5.0], [54.8, 5.0], [54.9, 5.0], [55.0, 5.0], [55.1, 5.0], [55.2, 5.0], [55.3, 5.0], [55.4, 5.0], [55.5, 5.0], [55.6, 5.0], [55.7, 5.0], [55.8, 5.0], [55.9, 5.0], [56.0, 5.0], [56.1, 5.0], [56.2, 5.0], [56.3, 5.0], [56.4, 5.0], [56.5, 5.0], [56.6, 5.0], [56.7, 5.0], [56.8, 5.0], [56.9, 5.0], [57.0, 5.0], [57.1, 5.0], [57.2, 5.0], [57.3, 5.0], [57.4, 5.0], [57.5, 5.0], [57.6, 5.0], [57.7, 5.0], [57.8, 5.0], [57.9, 5.0], [58.0, 5.0], [58.1, 5.0], [58.2, 5.0], [58.3, 5.0], [58.4, 5.0], [58.5, 5.0], [58.6, 5.0], [58.7, 5.0], [58.8, 5.0], [58.9, 5.0], [59.0, 5.0], [59.1, 5.0], [59.2, 5.0], [59.3, 5.0], [59.4, 5.0], [59.5, 5.0], [59.6, 5.0], [59.7, 5.0], [59.8, 5.0], [59.9, 5.0], [60.0, 5.0], [60.1, 5.0], [60.2, 5.0], [60.3, 5.0], [60.4, 5.0], [60.5, 5.0], [60.6, 5.0], [60.7, 5.0], [60.8, 5.0], [60.9, 5.0], [61.0, 5.0], [61.1, 5.0], [61.2, 5.0], [61.3, 5.0], [61.4, 5.0], [61.5, 5.0], [61.6, 5.0], [61.7, 5.0], [61.8, 5.0], [61.9, 5.0], [62.0, 5.0], [62.1, 5.0], [62.2, 5.0], [62.3, 5.0], [62.4, 5.0], [62.5, 5.0], [62.6, 5.0], [62.7, 5.0], [62.8, 5.0], [62.9, 5.0], [63.0, 5.0], [63.1, 5.0], [63.2, 5.0], [63.3, 5.0], [63.4, 5.0], [63.5, 5.0], [63.6, 5.0], [63.7, 5.0], [63.8, 5.0], [63.9, 5.0], [64.0, 5.0], [64.1, 5.0], [64.2, 5.0], [64.3, 5.0], [64.4, 5.0], [64.5, 5.0], [64.6, 5.0], [64.7, 5.0], [64.8, 5.0], [64.9, 5.0], [65.0, 5.0], [65.1, 5.0], [65.2, 5.0], [65.3, 5.0], [65.4, 5.0], [65.5, 5.0], [65.6, 5.0], [65.7, 5.0], [65.8, 5.0], [65.9, 5.0], [66.0, 5.0], [66.1, 5.0], [66.2, 5.0], [66.3, 5.0], [66.4, 5.0], [66.5, 5.0], [66.6, 5.0], [66.7, 8.0], [66.8, 8.0], [66.9, 8.0], [67.0, 8.0], [67.1, 8.0], [67.2, 8.0], [67.3, 8.0], [67.4, 8.0], [67.5, 8.0], [67.6, 8.0], [67.7, 8.0], [67.8, 8.0], [67.9, 8.0], [68.0, 8.0], [68.1, 8.0], [68.2, 8.0], [68.3, 8.0], [68.4, 8.0], [68.5, 8.0], [68.6, 8.0], [68.7, 8.0], [68.8, 8.0], [68.9, 8.0], [69.0, 8.0], [69.1, 8.0], [69.2, 8.0], [69.3, 8.0], [69.4, 8.0], [69.5, 8.0], [69.6, 8.0], [69.7, 8.0], [69.8, 8.0], [69.9, 8.0], [70.0, 8.0], [70.1, 8.0], [70.2, 8.0], [70.3, 8.0], [70.4, 8.0], [70.5, 8.0], [70.6, 8.0], [70.7, 8.0], [70.8, 8.0], [70.9, 8.0], [71.0, 8.0], [71.1, 8.0], [71.2, 8.0], [71.3, 8.0], [71.4, 8.0], [71.5, 8.0], [71.6, 8.0], [71.7, 8.0], [71.8, 8.0], [71.9, 8.0], [72.0, 8.0], [72.1, 8.0], [72.2, 8.0], [72.3, 8.0], [72.4, 8.0], [72.5, 8.0], [72.6, 8.0], [72.7, 8.0], [72.8, 8.0], [72.9, 8.0], [73.0, 8.0], [73.1, 8.0], [73.2, 8.0], [73.3, 8.0], [73.4, 8.0], [73.5, 8.0], [73.6, 8.0], [73.7, 8.0], [73.8, 8.0], [73.9, 8.0], [74.0, 8.0], [74.1, 8.0], [74.2, 8.0], [74.3, 8.0], [74.4, 8.0], [74.5, 8.0], [74.6, 8.0], [74.7, 8.0], [74.8, 8.0], [74.9, 8.0], [75.0, 8.0], [75.1, 8.0], [75.2, 8.0], [75.3, 8.0], [75.4, 8.0], [75.5, 8.0], [75.6, 8.0], [75.7, 8.0], [75.8, 8.0], [75.9, 8.0], [76.0, 8.0], [76.1, 8.0], [76.2, 8.0], [76.3, 8.0], [76.4, 8.0], [76.5, 8.0], [76.6, 8.0], [76.7, 8.0], [76.8, 8.0], [76.9, 8.0], [77.0, 8.0], [77.1, 8.0], [77.2, 8.0], [77.3, 8.0], [77.4, 8.0], [77.5, 8.0], [77.6, 8.0], [77.7, 8.0], [77.8, 8.0], [77.9, 8.0], [78.0, 8.0], [78.1, 8.0], [78.2, 8.0], [78.3, 8.0], [78.4, 8.0], [78.5, 8.0], [78.6, 8.0], [78.7, 8.0], [78.8, 8.0], [78.9, 8.0], [79.0, 8.0], [79.1, 8.0], [79.2, 8.0], [79.3, 8.0], [79.4, 8.0], [79.5, 8.0], [79.6, 8.0], [79.7, 8.0], [79.8, 8.0], [79.9, 8.0], [80.0, 8.0], [80.1, 8.0], [80.2, 8.0], [80.3, 8.0], [80.4, 8.0], [80.5, 8.0], [80.6, 8.0], [80.7, 8.0], [80.8, 8.0], [80.9, 8.0], [81.0, 8.0], [81.1, 8.0], [81.2, 8.0], [81.3, 8.0], [81.4, 8.0], [81.5, 8.0], [81.6, 8.0], [81.7, 8.0], [81.8, 8.0], [81.9, 8.0], [82.0, 8.0], [82.1, 8.0], [82.2, 8.0], [82.3, 8.0], [82.4, 8.0], [82.5, 8.0], [82.6, 8.0], [82.7, 8.0], [82.8, 8.0], [82.9, 8.0], [83.0, 8.0], [83.1, 8.0], [83.2, 8.0], [83.3, 8.0], [83.4, 8.0], [83.5, 8.0], [83.6, 8.0], [83.7, 8.0], [83.8, 8.0], [83.9, 8.0], [84.0, 8.0], [84.1, 8.0], [84.2, 8.0], [84.3, 8.0], [84.4, 8.0], [84.5, 8.0], [84.6, 8.0], [84.7, 8.0], [84.8, 8.0], [84.9, 8.0], [85.0, 8.0], [85.1, 8.0], [85.2, 8.0], [85.3, 8.0], [85.4, 8.0], [85.5, 8.0], [85.6, 8.0], [85.7, 8.0], [85.8, 8.0], [85.9, 8.0], [86.0, 8.0], [86.1, 8.0], [86.2, 8.0], [86.3, 8.0], [86.4, 8.0], [86.5, 8.0], [86.6, 8.0], [86.7, 8.0], [86.8, 8.0], [86.9, 8.0], [87.0, 8.0], [87.1, 8.0], [87.2, 8.0], [87.3, 8.0], [87.4, 8.0], [87.5, 8.0], [87.6, 8.0], [87.7, 8.0], [87.8, 8.0], [87.9, 8.0], [88.0, 8.0], [88.1, 8.0], [88.2, 8.0], [88.3, 8.0], [88.4, 8.0], [88.5, 8.0], [88.6, 8.0], [88.7, 8.0], [88.8, 8.0], [88.9, 8.0], [89.0, 8.0], [89.1, 8.0], [89.2, 8.0], [89.3, 8.0], [89.4, 8.0], [89.5, 8.0], [89.6, 8.0], [89.7, 8.0], [89.8, 8.0], [89.9, 8.0], [90.0, 8.0], [90.1, 8.0], [90.2, 8.0], [90.3, 8.0], [90.4, 8.0], [90.5, 8.0], [90.6, 8.0], [90.7, 8.0], [90.8, 8.0], [90.9, 8.0], [91.0, 8.0], [91.1, 8.0], [91.2, 8.0], [91.3, 8.0], [91.4, 8.0], [91.5, 8.0], [91.6, 8.0], [91.7, 8.0], [91.8, 8.0], [91.9, 8.0], [92.0, 8.0], [92.1, 8.0], [92.2, 8.0], [92.3, 8.0], [92.4, 8.0], [92.5, 8.0], [92.6, 8.0], [92.7, 8.0], [92.8, 8.0], [92.9, 8.0], [93.0, 8.0], [93.1, 8.0], [93.2, 8.0], [93.3, 8.0], [93.4, 8.0], [93.5, 8.0], [93.6, 8.0], [93.7, 8.0], [93.8, 8.0], [93.9, 8.0], [94.0, 8.0], [94.1, 8.0], [94.2, 8.0], [94.3, 8.0], [94.4, 8.0], [94.5, 8.0], [94.6, 8.0], [94.7, 8.0], [94.8, 8.0], [94.9, 8.0], [95.0, 8.0], [95.1, 8.0], [95.2, 8.0], [95.3, 8.0], [95.4, 8.0], [95.5, 8.0], [95.6, 8.0], [95.7, 8.0], [95.8, 8.0], [95.9, 8.0], [96.0, 8.0], [96.1, 8.0], [96.2, 8.0], [96.3, 8.0], [96.4, 8.0], [96.5, 8.0], [96.6, 8.0], [96.7, 8.0], [96.8, 8.0], [96.9, 8.0], [97.0, 8.0], [97.1, 8.0], [97.2, 8.0], [97.3, 8.0], [97.4, 8.0], [97.5, 8.0], [97.6, 8.0], [97.7, 8.0], [97.8, 8.0], [97.9, 8.0], [98.0, 8.0], [98.1, 8.0], [98.2, 8.0], [98.3, 8.0], [98.4, 8.0], [98.5, 8.0], [98.6, 8.0], [98.7, 8.0], [98.8, 8.0], [98.9, 8.0], [99.0, 8.0], [99.1, 8.0], [99.2, 8.0], [99.3, 8.0], [99.4, 8.0], [99.5, 8.0], [99.6, 8.0], [99.7, 8.0], [99.8, 8.0], [99.9, 8.0]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[0.0, 51.0], [0.1, 51.0], [0.2, 51.0], [0.3, 51.0], [0.4, 51.0], [0.5, 51.0], [0.6, 51.0], [0.7, 51.0], [0.8, 51.0], [0.9, 51.0], [1.0, 51.0], [1.1, 51.0], [1.2, 51.0], [1.3, 51.0], [1.4, 51.0], [1.5, 51.0], [1.6, 51.0], [1.7, 51.0], [1.8, 51.0], [1.9, 51.0], [2.0, 51.0], [2.1, 51.0], [2.2, 51.0], [2.3, 51.0], [2.4, 51.0], [2.5, 51.0], [2.6, 51.0], [2.7, 51.0], [2.8, 51.0], [2.9, 51.0], [3.0, 51.0], [3.1, 51.0], [3.2, 51.0], [3.3, 51.0], [3.4, 51.0], [3.5, 51.0], [3.6, 51.0], [3.7, 51.0], [3.8, 51.0], [3.9, 51.0], [4.0, 51.0], [4.1, 51.0], [4.2, 51.0], [4.3, 51.0], [4.4, 51.0], [4.5, 51.0], [4.6, 51.0], [4.7, 51.0], [4.8, 51.0], [4.9, 51.0], [5.0, 51.0], [5.1, 51.0], [5.2, 51.0], [5.3, 51.0], [5.4, 51.0], [5.5, 51.0], [5.6, 51.0], [5.7, 51.0], [5.8, 51.0], [5.9, 51.0], [6.0, 51.0], [6.1, 51.0], [6.2, 51.0], [6.3, 51.0], [6.4, 51.0], [6.5, 51.0], [6.6, 51.0], [6.7, 51.0], [6.8, 51.0], [6.9, 51.0], [7.0, 51.0], [7.1, 51.0], [7.2, 51.0], [7.3, 51.0], [7.4, 51.0], [7.5, 51.0], [7.6, 51.0], [7.7, 51.0], [7.8, 51.0], [7.9, 51.0], [8.0, 51.0], [8.1, 51.0], [8.2, 51.0], [8.3, 51.0], [8.4, 51.0], [8.5, 51.0], [8.6, 51.0], [8.7, 51.0], [8.8, 51.0], [8.9, 51.0], [9.0, 51.0], [9.1, 51.0], [9.2, 51.0], [9.3, 51.0], [9.4, 51.0], [9.5, 51.0], [9.6, 51.0], [9.7, 51.0], [9.8, 51.0], [9.9, 51.0], [10.0, 51.0], [10.1, 51.0], [10.2, 51.0], [10.3, 51.0], [10.4, 51.0], [10.5, 51.0], [10.6, 51.0], [10.7, 51.0], [10.8, 51.0], [10.9, 51.0], [11.0, 51.0], [11.1, 51.0], [11.2, 51.0], [11.3, 51.0], [11.4, 51.0], [11.5, 51.0], [11.6, 51.0], [11.7, 51.0], [11.8, 51.0], [11.9, 51.0], [12.0, 51.0], [12.1, 51.0], [12.2, 51.0], [12.3, 51.0], [12.4, 51.0], [12.5, 51.0], [12.6, 51.0], [12.7, 51.0], [12.8, 51.0], [12.9, 51.0], [13.0, 51.0], [13.1, 51.0], [13.2, 51.0], [13.3, 51.0], [13.4, 51.0], [13.5, 51.0], [13.6, 51.0], [13.7, 51.0], [13.8, 51.0], [13.9, 51.0], [14.0, 51.0], [14.1, 51.0], [14.2, 51.0], [14.3, 51.0], [14.4, 51.0], [14.5, 51.0], [14.6, 51.0], [14.7, 51.0], [14.8, 51.0], [14.9, 51.0], [15.0, 51.0], [15.1, 51.0], [15.2, 51.0], [15.3, 51.0], [15.4, 51.0], [15.5, 51.0], [15.6, 51.0], [15.7, 51.0], [15.8, 51.0], [15.9, 51.0], [16.0, 51.0], [16.1, 51.0], [16.2, 51.0], [16.3, 51.0], [16.4, 51.0], [16.5, 51.0], [16.6, 51.0], [16.7, 51.0], [16.8, 51.0], [16.9, 51.0], [17.0, 51.0], [17.1, 51.0], [17.2, 51.0], [17.3, 51.0], [17.4, 51.0], [17.5, 51.0], [17.6, 51.0], [17.7, 51.0], [17.8, 51.0], [17.9, 51.0], [18.0, 51.0], [18.1, 51.0], [18.2, 51.0], [18.3, 51.0], [18.4, 51.0], [18.5, 51.0], [18.6, 51.0], [18.7, 51.0], [18.8, 51.0], [18.9, 51.0], [19.0, 51.0], [19.1, 51.0], [19.2, 51.0], [19.3, 51.0], [19.4, 51.0], [19.5, 51.0], [19.6, 51.0], [19.7, 51.0], [19.8, 51.0], [19.9, 51.0], [20.0, 51.0], [20.1, 51.0], [20.2, 51.0], [20.3, 51.0], [20.4, 51.0], [20.5, 51.0], [20.6, 51.0], [20.7, 51.0], [20.8, 51.0], [20.9, 51.0], [21.0, 51.0], [21.1, 51.0], [21.2, 51.0], [21.3, 51.0], [21.4, 51.0], [21.5, 51.0], [21.6, 51.0], [21.7, 51.0], [21.8, 51.0], [21.9, 51.0], [22.0, 51.0], [22.1, 51.0], [22.2, 51.0], [22.3, 51.0], [22.4, 51.0], [22.5, 51.0], [22.6, 51.0], [22.7, 51.0], [22.8, 51.0], [22.9, 51.0], [23.0, 51.0], [23.1, 51.0], [23.2, 51.0], [23.3, 51.0], [23.4, 51.0], [23.5, 51.0], [23.6, 51.0], [23.7, 51.0], [23.8, 51.0], [23.9, 51.0], [24.0, 51.0], [24.1, 51.0], [24.2, 51.0], [24.3, 51.0], [24.4, 51.0], [24.5, 51.0], [24.6, 51.0], [24.7, 51.0], [24.8, 51.0], [24.9, 51.0], [25.0, 51.0], [25.1, 51.0], [25.2, 51.0], [25.3, 51.0], [25.4, 51.0], [25.5, 51.0], [25.6, 51.0], [25.7, 51.0], [25.8, 51.0], [25.9, 51.0], [26.0, 51.0], [26.1, 51.0], [26.2, 51.0], [26.3, 51.0], [26.4, 51.0], [26.5, 51.0], [26.6, 51.0], [26.7, 51.0], [26.8, 51.0], [26.9, 51.0], [27.0, 51.0], [27.1, 51.0], [27.2, 51.0], [27.3, 51.0], [27.4, 51.0], [27.5, 51.0], [27.6, 51.0], [27.7, 51.0], [27.8, 51.0], [27.9, 51.0], [28.0, 51.0], [28.1, 51.0], [28.2, 51.0], [28.3, 51.0], [28.4, 51.0], [28.5, 51.0], [28.6, 51.0], [28.7, 51.0], [28.8, 51.0], [28.9, 51.0], [29.0, 51.0], [29.1, 51.0], [29.2, 51.0], [29.3, 51.0], [29.4, 51.0], [29.5, 51.0], [29.6, 51.0], [29.7, 51.0], [29.8, 51.0], [29.9, 51.0], [30.0, 51.0], [30.1, 51.0], [30.2, 51.0], [30.3, 51.0], [30.4, 51.0], [30.5, 51.0], [30.6, 51.0], [30.7, 51.0], [30.8, 51.0], [30.9, 51.0], [31.0, 51.0], [31.1, 51.0], [31.2, 51.0], [31.3, 51.0], [31.4, 51.0], [31.5, 51.0], [31.6, 51.0], [31.7, 51.0], [31.8, 51.0], [31.9, 51.0], [32.0, 51.0], [32.1, 51.0], [32.2, 51.0], [32.3, 51.0], [32.4, 51.0], [32.5, 51.0], [32.6, 51.0], [32.7, 51.0], [32.8, 51.0], [32.9, 51.0], [33.0, 51.0], [33.1, 51.0], [33.2, 51.0], [33.3, 51.0], [33.4, 51.0], [33.5, 51.0], [33.6, 51.0], [33.7, 51.0], [33.8, 51.0], [33.9, 51.0], [34.0, 51.0], [34.1, 51.0], [34.2, 51.0], [34.3, 51.0], [34.4, 51.0], [34.5, 51.0], [34.6, 51.0], [34.7, 51.0], [34.8, 51.0], [34.9, 51.0], [35.0, 51.0], [35.1, 51.0], [35.2, 51.0], [35.3, 51.0], [35.4, 51.0], [35.5, 51.0], [35.6, 51.0], [35.7, 51.0], [35.8, 51.0], [35.9, 51.0], [36.0, 51.0], [36.1, 51.0], [36.2, 51.0], [36.3, 51.0], [36.4, 51.0], [36.5, 51.0], [36.6, 51.0], [36.7, 51.0], [36.8, 51.0], [36.9, 51.0], [37.0, 51.0], [37.1, 51.0], [37.2, 51.0], [37.3, 51.0], [37.4, 51.0], [37.5, 51.0], [37.6, 51.0], [37.7, 51.0], [37.8, 51.0], [37.9, 51.0], [38.0, 51.0], [38.1, 51.0], [38.2, 51.0], [38.3, 51.0], [38.4, 51.0], [38.5, 51.0], [38.6, 51.0], [38.7, 51.0], [38.8, 51.0], [38.9, 51.0], [39.0, 51.0], [39.1, 51.0], [39.2, 51.0], [39.3, 51.0], [39.4, 51.0], [39.5, 51.0], [39.6, 51.0], [39.7, 51.0], [39.8, 51.0], [39.9, 51.0], [40.0, 51.0], [40.1, 51.0], [40.2, 51.0], [40.3, 51.0], [40.4, 51.0], [40.5, 51.0], [40.6, 51.0], [40.7, 51.0], [40.8, 51.0], [40.9, 51.0], [41.0, 51.0], [41.1, 51.0], [41.2, 51.0], [41.3, 51.0], [41.4, 51.0], [41.5, 51.0], [41.6, 51.0], [41.7, 51.0], [41.8, 51.0], [41.9, 51.0], [42.0, 51.0], [42.1, 51.0], [42.2, 51.0], [42.3, 51.0], [42.4, 51.0], [42.5, 51.0], [42.6, 51.0], [42.7, 51.0], [42.8, 51.0], [42.9, 51.0], [43.0, 51.0], [43.1, 51.0], [43.2, 51.0], [43.3, 51.0], [43.4, 51.0], [43.5, 51.0], [43.6, 51.0], [43.7, 51.0], [43.8, 51.0], [43.9, 51.0], [44.0, 51.0], [44.1, 51.0], [44.2, 51.0], [44.3, 51.0], [44.4, 51.0], [44.5, 51.0], [44.6, 51.0], [44.7, 51.0], [44.8, 51.0], [44.9, 51.0], [45.0, 51.0], [45.1, 51.0], [45.2, 51.0], [45.3, 51.0], [45.4, 51.0], [45.5, 51.0], [45.6, 51.0], [45.7, 51.0], [45.8, 51.0], [45.9, 51.0], [46.0, 51.0], [46.1, 51.0], [46.2, 51.0], [46.3, 51.0], [46.4, 51.0], [46.5, 51.0], [46.6, 51.0], [46.7, 51.0], [46.8, 51.0], [46.9, 51.0], [47.0, 51.0], [47.1, 51.0], [47.2, 51.0], [47.3, 51.0], [47.4, 51.0], [47.5, 51.0], [47.6, 51.0], [47.7, 51.0], [47.8, 51.0], [47.9, 51.0], [48.0, 51.0], [48.1, 51.0], [48.2, 51.0], [48.3, 51.0], [48.4, 51.0], [48.5, 51.0], [48.6, 51.0], [48.7, 51.0], [48.8, 51.0], [48.9, 51.0], [49.0, 51.0], [49.1, 51.0], [49.2, 51.0], [49.3, 51.0], [49.4, 51.0], [49.5, 51.0], [49.6, 51.0], [49.7, 51.0], [49.8, 51.0], [49.9, 51.0], [50.0, 51.0], [50.1, 51.0], [50.2, 51.0], [50.3, 51.0], [50.4, 51.0], [50.5, 51.0], [50.6, 51.0], [50.7, 51.0], [50.8, 51.0], [50.9, 51.0], [51.0, 51.0], [51.1, 51.0], [51.2, 51.0], [51.3, 51.0], [51.4, 51.0], [51.5, 51.0], [51.6, 51.0], [51.7, 51.0], [51.8, 51.0], [51.9, 51.0], [52.0, 51.0], [52.1, 51.0], [52.2, 51.0], [52.3, 51.0], [52.4, 51.0], [52.5, 51.0], [52.6, 51.0], [52.7, 51.0], [52.8, 51.0], [52.9, 51.0], [53.0, 51.0], [53.1, 51.0], [53.2, 51.0], [53.3, 51.0], [53.4, 51.0], [53.5, 51.0], [53.6, 51.0], [53.7, 51.0], [53.8, 51.0], [53.9, 51.0], [54.0, 51.0], [54.1, 51.0], [54.2, 51.0], [54.3, 51.0], [54.4, 51.0], [54.5, 51.0], [54.6, 51.0], [54.7, 51.0], [54.8, 51.0], [54.9, 51.0], [55.0, 51.0], [55.1, 51.0], [55.2, 51.0], [55.3, 51.0], [55.4, 51.0], [55.5, 51.0], [55.6, 51.0], [55.7, 51.0], [55.8, 51.0], [55.9, 51.0], [56.0, 51.0], [56.1, 51.0], [56.2, 51.0], [56.3, 51.0], [56.4, 51.0], [56.5, 51.0], [56.6, 51.0], [56.7, 51.0], [56.8, 51.0], [56.9, 51.0], [57.0, 51.0], [57.1, 51.0], [57.2, 51.0], [57.3, 51.0], [57.4, 51.0], [57.5, 51.0], [57.6, 51.0], [57.7, 51.0], [57.8, 51.0], [57.9, 51.0], [58.0, 51.0], [58.1, 51.0], [58.2, 51.0], [58.3, 51.0], [58.4, 51.0], [58.5, 51.0], [58.6, 51.0], [58.7, 51.0], [58.8, 51.0], [58.9, 51.0], [59.0, 51.0], [59.1, 51.0], [59.2, 51.0], [59.3, 51.0], [59.4, 51.0], [59.5, 51.0], [59.6, 51.0], [59.7, 51.0], [59.8, 51.0], [59.9, 51.0], [60.0, 51.0], [60.1, 51.0], [60.2, 51.0], [60.3, 51.0], [60.4, 51.0], [60.5, 51.0], [60.6, 51.0], [60.7, 51.0], [60.8, 51.0], [60.9, 51.0], [61.0, 51.0], [61.1, 51.0], [61.2, 51.0], [61.3, 51.0], [61.4, 51.0], [61.5, 51.0], [61.6, 51.0], [61.7, 51.0], [61.8, 51.0], [61.9, 51.0], [62.0, 51.0], [62.1, 51.0], [62.2, 51.0], [62.3, 51.0], [62.4, 51.0], [62.5, 51.0], [62.6, 51.0], [62.7, 51.0], [62.8, 51.0], [62.9, 51.0], [63.0, 51.0], [63.1, 51.0], [63.2, 51.0], [63.3, 51.0], [63.4, 51.0], [63.5, 51.0], [63.6, 51.0], [63.7, 51.0], [63.8, 51.0], [63.9, 51.0], [64.0, 51.0], [64.1, 51.0], [64.2, 51.0], [64.3, 51.0], [64.4, 51.0], [64.5, 51.0], [64.6, 51.0], [64.7, 51.0], [64.8, 51.0], [64.9, 51.0], [65.0, 51.0], [65.1, 51.0], [65.2, 51.0], [65.3, 51.0], [65.4, 51.0], [65.5, 51.0], [65.6, 51.0], [65.7, 51.0], [65.8, 51.0], [65.9, 51.0], [66.0, 51.0], [66.1, 51.0], [66.2, 51.0], [66.3, 51.0], [66.4, 51.0], [66.5, 51.0], [66.6, 51.0], [66.7, 51.0], [66.8, 51.0], [66.9, 51.0], [67.0, 51.0], [67.1, 51.0], [67.2, 51.0], [67.3, 51.0], [67.4, 51.0], [67.5, 51.0], [67.6, 51.0], [67.7, 51.0], [67.8, 51.0], [67.9, 51.0], [68.0, 51.0], [68.1, 51.0], [68.2, 51.0], [68.3, 51.0], [68.4, 51.0], [68.5, 51.0], [68.6, 51.0], [68.7, 51.0], [68.8, 51.0], [68.9, 51.0], [69.0, 51.0], [69.1, 51.0], [69.2, 51.0], [69.3, 51.0], [69.4, 51.0], [69.5, 51.0], [69.6, 51.0], [69.7, 51.0], [69.8, 51.0], [69.9, 51.0], [70.0, 51.0], [70.1, 51.0], [70.2, 51.0], [70.3, 51.0], [70.4, 51.0], [70.5, 51.0], [70.6, 51.0], [70.7, 51.0], [70.8, 51.0], [70.9, 51.0], [71.0, 51.0], [71.1, 51.0], [71.2, 51.0], [71.3, 51.0], [71.4, 51.0], [71.5, 51.0], [71.6, 51.0], [71.7, 51.0], [71.8, 51.0], [71.9, 51.0], [72.0, 51.0], [72.1, 51.0], [72.2, 51.0], [72.3, 51.0], [72.4, 51.0], [72.5, 51.0], [72.6, 51.0], [72.7, 51.0], [72.8, 51.0], [72.9, 51.0], [73.0, 51.0], [73.1, 51.0], [73.2, 51.0], [73.3, 51.0], [73.4, 51.0], [73.5, 51.0], [73.6, 51.0], [73.7, 51.0], [73.8, 51.0], [73.9, 51.0], [74.0, 51.0], [74.1, 51.0], [74.2, 51.0], [74.3, 51.0], [74.4, 51.0], [74.5, 51.0], [74.6, 51.0], [74.7, 51.0], [74.8, 51.0], [74.9, 51.0], [75.0, 51.0], [75.1, 51.0], [75.2, 51.0], [75.3, 51.0], [75.4, 51.0], [75.5, 51.0], [75.6, 51.0], [75.7, 51.0], [75.8, 51.0], [75.9, 51.0], [76.0, 51.0], [76.1, 51.0], [76.2, 51.0], [76.3, 51.0], [76.4, 51.0], [76.5, 51.0], [76.6, 51.0], [76.7, 51.0], [76.8, 51.0], [76.9, 51.0], [77.0, 51.0], [77.1, 51.0], [77.2, 51.0], [77.3, 51.0], [77.4, 51.0], [77.5, 51.0], [77.6, 51.0], [77.7, 51.0], [77.8, 51.0], [77.9, 51.0], [78.0, 51.0], [78.1, 51.0], [78.2, 51.0], [78.3, 51.0], [78.4, 51.0], [78.5, 51.0], [78.6, 51.0], [78.7, 51.0], [78.8, 51.0], [78.9, 51.0], [79.0, 51.0], [79.1, 51.0], [79.2, 51.0], [79.3, 51.0], [79.4, 51.0], [79.5, 51.0], [79.6, 51.0], [79.7, 51.0], [79.8, 51.0], [79.9, 51.0], [80.0, 51.0], [80.1, 51.0], [80.2, 51.0], [80.3, 51.0], [80.4, 51.0], [80.5, 51.0], [80.6, 51.0], [80.7, 51.0], [80.8, 51.0], [80.9, 51.0], [81.0, 51.0], [81.1, 51.0], [81.2, 51.0], [81.3, 51.0], [81.4, 51.0], [81.5, 51.0], [81.6, 51.0], [81.7, 51.0], [81.8, 51.0], [81.9, 51.0], [82.0, 51.0], [82.1, 51.0], [82.2, 51.0], [82.3, 51.0], [82.4, 51.0], [82.5, 51.0], [82.6, 51.0], [82.7, 51.0], [82.8, 51.0], [82.9, 51.0], [83.0, 51.0], [83.1, 51.0], [83.2, 51.0], [83.3, 51.0], [83.4, 51.0], [83.5, 51.0], [83.6, 51.0], [83.7, 51.0], [83.8, 51.0], [83.9, 51.0], [84.0, 51.0], [84.1, 51.0], [84.2, 51.0], [84.3, 51.0], [84.4, 51.0], [84.5, 51.0], [84.6, 51.0], [84.7, 51.0], [84.8, 51.0], [84.9, 51.0], [85.0, 51.0], [85.1, 51.0], [85.2, 51.0], [85.3, 51.0], [85.4, 51.0], [85.5, 51.0], [85.6, 51.0], [85.7, 51.0], [85.8, 51.0], [85.9, 51.0], [86.0, 51.0], [86.1, 51.0], [86.2, 51.0], [86.3, 51.0], [86.4, 51.0], [86.5, 51.0], [86.6, 51.0], [86.7, 51.0], [86.8, 51.0], [86.9, 51.0], [87.0, 51.0], [87.1, 51.0], [87.2, 51.0], [87.3, 51.0], [87.4, 51.0], [87.5, 51.0], [87.6, 51.0], [87.7, 51.0], [87.8, 51.0], [87.9, 51.0], [88.0, 51.0], [88.1, 51.0], [88.2, 51.0], [88.3, 51.0], [88.4, 51.0], [88.5, 51.0], [88.6, 51.0], [88.7, 51.0], [88.8, 51.0], [88.9, 51.0], [89.0, 51.0], [89.1, 51.0], [89.2, 51.0], [89.3, 51.0], [89.4, 51.0], [89.5, 51.0], [89.6, 51.0], [89.7, 51.0], [89.8, 51.0], [89.9, 51.0], [90.0, 51.0], [90.1, 51.0], [90.2, 51.0], [90.3, 51.0], [90.4, 51.0], [90.5, 51.0], [90.6, 51.0], [90.7, 51.0], [90.8, 51.0], [90.9, 51.0], [91.0, 51.0], [91.1, 51.0], [91.2, 51.0], [91.3, 51.0], [91.4, 51.0], [91.5, 51.0], [91.6, 51.0], [91.7, 51.0], [91.8, 51.0], [91.9, 51.0], [92.0, 51.0], [92.1, 51.0], [92.2, 51.0], [92.3, 51.0], [92.4, 51.0], [92.5, 51.0], [92.6, 51.0], [92.7, 51.0], [92.8, 51.0], [92.9, 51.0], [93.0, 51.0], [93.1, 51.0], [93.2, 51.0], [93.3, 51.0], [93.4, 51.0], [93.5, 51.0], [93.6, 51.0], [93.7, 51.0], [93.8, 51.0], [93.9, 51.0], [94.0, 51.0], [94.1, 51.0], [94.2, 51.0], [94.3, 51.0], [94.4, 51.0], [94.5, 51.0], [94.6, 51.0], [94.7, 51.0], [94.8, 51.0], [94.9, 51.0], [95.0, 51.0], [95.1, 51.0], [95.2, 51.0], [95.3, 51.0], [95.4, 51.0], [95.5, 51.0], [95.6, 51.0], [95.7, 51.0], [95.8, 51.0], [95.9, 51.0], [96.0, 51.0], [96.1, 51.0], [96.2, 51.0], [96.3, 51.0], [96.4, 51.0], [96.5, 51.0], [96.6, 51.0], [96.7, 51.0], [96.8, 51.0], [96.9, 51.0], [97.0, 51.0], [97.1, 51.0], [97.2, 51.0], [97.3, 51.0], [97.4, 51.0], [97.5, 51.0], [97.6, 51.0], [97.7, 51.0], [97.8, 51.0], [97.9, 51.0], [98.0, 51.0], [98.1, 51.0], [98.2, 51.0], [98.3, 51.0], [98.4, 51.0], [98.5, 51.0], [98.6, 51.0], [98.7, 51.0], [98.8, 51.0], [98.9, 51.0], [99.0, 51.0], [99.1, 51.0], [99.2, 51.0], [99.3, 51.0], [99.4, 51.0], [99.5, 51.0], [99.6, 51.0], [99.7, 51.0], [99.8, 51.0], [99.9, 51.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[0.0, 4.0], [0.1, 4.0], [0.2, 4.0], [0.3, 4.0], [0.4, 4.0], [0.5, 4.0], [0.6, 4.0], [0.7, 4.0], [0.8, 4.0], [0.9, 4.0], [1.0, 4.0], [1.1, 4.0], [1.2, 4.0], [1.3, 4.0], [1.4, 4.0], [1.5, 4.0], [1.6, 4.0], [1.7, 4.0], [1.8, 4.0], [1.9, 4.0], [2.0, 4.0], [2.1, 4.0], [2.2, 4.0], [2.3, 4.0], [2.4, 4.0], [2.5, 4.0], [2.6, 4.0], [2.7, 4.0], [2.8, 4.0], [2.9, 4.0], [3.0, 4.0], [3.1, 4.0], [3.2, 4.0], [3.3, 4.0], [3.4, 4.0], [3.5, 4.0], [3.6, 4.0], [3.7, 4.0], [3.8, 4.0], [3.9, 4.0], [4.0, 4.0], [4.1, 4.0], [4.2, 4.0], [4.3, 4.0], [4.4, 4.0], [4.5, 4.0], [4.6, 4.0], [4.7, 4.0], [4.8, 4.0], [4.9, 4.0], [5.0, 4.0], [5.1, 4.0], [5.2, 4.0], [5.3, 4.0], [5.4, 4.0], [5.5, 4.0], [5.6, 4.0], [5.7, 4.0], [5.8, 4.0], [5.9, 4.0], [6.0, 4.0], [6.1, 4.0], [6.2, 4.0], [6.3, 4.0], [6.4, 4.0], [6.5, 4.0], [6.6, 4.0], [6.7, 4.0], [6.8, 4.0], [6.9, 4.0], [7.0, 4.0], [7.1, 4.0], [7.2, 4.0], [7.3, 4.0], [7.4, 4.0], [7.5, 4.0], [7.6, 4.0], [7.7, 4.0], [7.8, 4.0], [7.9, 4.0], [8.0, 4.0], [8.1, 4.0], [8.2, 4.0], [8.3, 4.0], [8.4, 4.0], [8.5, 4.0], [8.6, 4.0], [8.7, 4.0], [8.8, 4.0], [8.9, 4.0], [9.0, 4.0], [9.1, 4.0], [9.2, 4.0], [9.3, 4.0], [9.4, 4.0], [9.5, 4.0], [9.6, 4.0], [9.7, 4.0], [9.8, 4.0], [9.9, 4.0], [10.0, 4.0], [10.1, 4.0], [10.2, 4.0], [10.3, 4.0], [10.4, 4.0], [10.5, 4.0], [10.6, 4.0], [10.7, 4.0], [10.8, 4.0], [10.9, 4.0], [11.0, 4.0], [11.1, 4.0], [11.2, 4.0], [11.3, 4.0], [11.4, 4.0], [11.5, 4.0], [11.6, 4.0], [11.7, 4.0], [11.8, 4.0], [11.9, 4.0], [12.0, 4.0], [12.1, 4.0], [12.2, 4.0], [12.3, 4.0], [12.4, 4.0], [12.5, 4.0], [12.6, 4.0], [12.7, 4.0], [12.8, 4.0], [12.9, 4.0], [13.0, 4.0], [13.1, 4.0], [13.2, 4.0], [13.3, 4.0], [13.4, 4.0], [13.5, 4.0], [13.6, 4.0], [13.7, 4.0], [13.8, 4.0], [13.9, 4.0], [14.0, 4.0], [14.1, 4.0], [14.2, 4.0], [14.3, 4.0], [14.4, 4.0], [14.5, 4.0], [14.6, 4.0], [14.7, 4.0], [14.8, 4.0], [14.9, 4.0], [15.0, 4.0], [15.1, 4.0], [15.2, 4.0], [15.3, 4.0], [15.4, 4.0], [15.5, 4.0], [15.6, 4.0], [15.7, 4.0], [15.8, 4.0], [15.9, 4.0], [16.0, 4.0], [16.1, 4.0], [16.2, 4.0], [16.3, 4.0], [16.4, 4.0], [16.5, 4.0], [16.6, 4.0], [16.7, 4.0], [16.8, 4.0], [16.9, 4.0], [17.0, 4.0], [17.1, 4.0], [17.2, 4.0], [17.3, 4.0], [17.4, 4.0], [17.5, 4.0], [17.6, 4.0], [17.7, 4.0], [17.8, 4.0], [17.9, 4.0], [18.0, 4.0], [18.1, 4.0], [18.2, 4.0], [18.3, 4.0], [18.4, 4.0], [18.5, 4.0], [18.6, 4.0], [18.7, 4.0], [18.8, 4.0], [18.9, 4.0], [19.0, 4.0], [19.1, 4.0], [19.2, 4.0], [19.3, 4.0], [19.4, 4.0], [19.5, 4.0], [19.6, 4.0], [19.7, 4.0], [19.8, 4.0], [19.9, 4.0], [20.0, 4.0], [20.1, 4.0], [20.2, 4.0], [20.3, 4.0], [20.4, 4.0], [20.5, 4.0], [20.6, 4.0], [20.7, 4.0], [20.8, 4.0], [20.9, 4.0], [21.0, 4.0], [21.1, 4.0], [21.2, 4.0], [21.3, 4.0], [21.4, 4.0], [21.5, 4.0], [21.6, 4.0], [21.7, 4.0], [21.8, 4.0], [21.9, 4.0], [22.0, 4.0], [22.1, 4.0], [22.2, 4.0], [22.3, 4.0], [22.4, 4.0], [22.5, 4.0], [22.6, 4.0], [22.7, 4.0], [22.8, 4.0], [22.9, 4.0], [23.0, 4.0], [23.1, 4.0], [23.2, 4.0], [23.3, 4.0], [23.4, 4.0], [23.5, 4.0], [23.6, 4.0], [23.7, 4.0], [23.8, 4.0], [23.9, 4.0], [24.0, 4.0], [24.1, 4.0], [24.2, 4.0], [24.3, 4.0], [24.4, 4.0], [24.5, 4.0], [24.6, 4.0], [24.7, 4.0], [24.8, 4.0], [24.9, 4.0], [25.0, 4.0], [25.1, 4.0], [25.2, 4.0], [25.3, 4.0], [25.4, 4.0], [25.5, 4.0], [25.6, 4.0], [25.7, 4.0], [25.8, 4.0], [25.9, 4.0], [26.0, 4.0], [26.1, 4.0], [26.2, 4.0], [26.3, 4.0], [26.4, 4.0], [26.5, 4.0], [26.6, 4.0], [26.7, 4.0], [26.8, 4.0], [26.9, 4.0], [27.0, 4.0], [27.1, 4.0], [27.2, 4.0], [27.3, 4.0], [27.4, 4.0], [27.5, 4.0], [27.6, 4.0], [27.7, 4.0], [27.8, 4.0], [27.9, 4.0], [28.0, 4.0], [28.1, 4.0], [28.2, 4.0], [28.3, 4.0], [28.4, 4.0], [28.5, 4.0], [28.6, 4.0], [28.7, 4.0], [28.8, 4.0], [28.9, 4.0], [29.0, 4.0], [29.1, 4.0], [29.2, 4.0], [29.3, 4.0], [29.4, 4.0], [29.5, 4.0], [29.6, 4.0], [29.7, 4.0], [29.8, 4.0], [29.9, 4.0], [30.0, 4.0], [30.1, 4.0], [30.2, 4.0], [30.3, 4.0], [30.4, 4.0], [30.5, 4.0], [30.6, 4.0], [30.7, 4.0], [30.8, 4.0], [30.9, 4.0], [31.0, 4.0], [31.1, 4.0], [31.2, 4.0], [31.3, 4.0], [31.4, 4.0], [31.5, 4.0], [31.6, 4.0], [31.7, 4.0], [31.8, 4.0], [31.9, 4.0], [32.0, 4.0], [32.1, 4.0], [32.2, 4.0], [32.3, 4.0], [32.4, 4.0], [32.5, 4.0], [32.6, 4.0], [32.7, 4.0], [32.8, 4.0], [32.9, 4.0], [33.0, 4.0], [33.1, 4.0], [33.2, 4.0], [33.3, 4.0], [33.4, 4.0], [33.5, 4.0], [33.6, 4.0], [33.7, 4.0], [33.8, 4.0], [33.9, 4.0], [34.0, 4.0], [34.1, 4.0], [34.2, 4.0], [34.3, 4.0], [34.4, 4.0], [34.5, 4.0], [34.6, 4.0], [34.7, 4.0], [34.8, 4.0], [34.9, 4.0], [35.0, 4.0], [35.1, 4.0], [35.2, 4.0], [35.3, 4.0], [35.4, 4.0], [35.5, 4.0], [35.6, 4.0], [35.7, 4.0], [35.8, 4.0], [35.9, 4.0], [36.0, 4.0], [36.1, 4.0], [36.2, 4.0], [36.3, 4.0], [36.4, 4.0], [36.5, 4.0], [36.6, 4.0], [36.7, 4.0], [36.8, 4.0], [36.9, 4.0], [37.0, 4.0], [37.1, 4.0], [37.2, 4.0], [37.3, 4.0], [37.4, 4.0], [37.5, 4.0], [37.6, 4.0], [37.7, 4.0], [37.8, 4.0], [37.9, 4.0], [38.0, 4.0], [38.1, 4.0], [38.2, 4.0], [38.3, 4.0], [38.4, 4.0], [38.5, 4.0], [38.6, 4.0], [38.7, 4.0], [38.8, 4.0], [38.9, 4.0], [39.0, 4.0], [39.1, 4.0], [39.2, 4.0], [39.3, 4.0], [39.4, 4.0], [39.5, 4.0], [39.6, 4.0], [39.7, 4.0], [39.8, 4.0], [39.9, 4.0], [40.0, 5.0], [40.1, 5.0], [40.2, 5.0], [40.3, 5.0], [40.4, 5.0], [40.5, 5.0], [40.6, 5.0], [40.7, 5.0], [40.8, 5.0], [40.9, 5.0], [41.0, 5.0], [41.1, 5.0], [41.2, 5.0], [41.3, 5.0], [41.4, 5.0], [41.5, 5.0], [41.6, 5.0], [41.7, 5.0], [41.8, 5.0], [41.9, 5.0], [42.0, 5.0], [42.1, 5.0], [42.2, 5.0], [42.3, 5.0], [42.4, 5.0], [42.5, 5.0], [42.6, 5.0], [42.7, 5.0], [42.8, 5.0], [42.9, 5.0], [43.0, 5.0], [43.1, 5.0], [43.2, 5.0], [43.3, 5.0], [43.4, 5.0], [43.5, 5.0], [43.6, 5.0], [43.7, 5.0], [43.8, 5.0], [43.9, 5.0], [44.0, 5.0], [44.1, 5.0], [44.2, 5.0], [44.3, 5.0], [44.4, 5.0], [44.5, 5.0], [44.6, 5.0], [44.7, 5.0], [44.8, 5.0], [44.9, 5.0], [45.0, 5.0], [45.1, 5.0], [45.2, 5.0], [45.3, 5.0], [45.4, 5.0], [45.5, 5.0], [45.6, 5.0], [45.7, 5.0], [45.8, 5.0], [45.9, 5.0], [46.0, 5.0], [46.1, 5.0], [46.2, 5.0], [46.3, 5.0], [46.4, 5.0], [46.5, 5.0], [46.6, 5.0], [46.7, 5.0], [46.8, 5.0], [46.9, 5.0], [47.0, 5.0], [47.1, 5.0], [47.2, 5.0], [47.3, 5.0], [47.4, 5.0], [47.5, 5.0], [47.6, 5.0], [47.7, 5.0], [47.8, 5.0], [47.9, 5.0], [48.0, 5.0], [48.1, 5.0], [48.2, 5.0], [48.3, 5.0], [48.4, 5.0], [48.5, 5.0], [48.6, 5.0], [48.7, 5.0], [48.8, 5.0], [48.9, 5.0], [49.0, 5.0], [49.1, 5.0], [49.2, 5.0], [49.3, 5.0], [49.4, 5.0], [49.5, 5.0], [49.6, 5.0], [49.7, 5.0], [49.8, 5.0], [49.9, 5.0], [50.0, 5.0], [50.1, 5.0], [50.2, 5.0], [50.3, 5.0], [50.4, 5.0], [50.5, 5.0], [50.6, 5.0], [50.7, 5.0], [50.8, 5.0], [50.9, 5.0], [51.0, 5.0], [51.1, 5.0], [51.2, 5.0], [51.3, 5.0], [51.4, 5.0], [51.5, 5.0], [51.6, 5.0], [51.7, 5.0], [51.8, 5.0], [51.9, 5.0], [52.0, 5.0], [52.1, 5.0], [52.2, 5.0], [52.3, 5.0], [52.4, 5.0], [52.5, 5.0], [52.6, 5.0], [52.7, 5.0], [52.8, 5.0], [52.9, 5.0], [53.0, 5.0], [53.1, 5.0], [53.2, 5.0], [53.3, 5.0], [53.4, 5.0], [53.5, 5.0], [53.6, 5.0], [53.7, 5.0], [53.8, 5.0], [53.9, 5.0], [54.0, 5.0], [54.1, 5.0], [54.2, 5.0], [54.3, 5.0], [54.4, 5.0], [54.5, 5.0], [54.6, 5.0], [54.7, 5.0], [54.8, 5.0], [54.9, 5.0], [55.0, 5.0], [55.1, 5.0], [55.2, 5.0], [55.3, 5.0], [55.4, 5.0], [55.5, 5.0], [55.6, 5.0], [55.7, 5.0], [55.8, 5.0], [55.9, 5.0], [56.0, 5.0], [56.1, 5.0], [56.2, 5.0], [56.3, 5.0], [56.4, 5.0], [56.5, 5.0], [56.6, 5.0], [56.7, 5.0], [56.8, 5.0], [56.9, 5.0], [57.0, 5.0], [57.1, 5.0], [57.2, 5.0], [57.3, 5.0], [57.4, 5.0], [57.5, 5.0], [57.6, 5.0], [57.7, 5.0], [57.8, 5.0], [57.9, 5.0], [58.0, 5.0], [58.1, 5.0], [58.2, 5.0], [58.3, 5.0], [58.4, 5.0], [58.5, 5.0], [58.6, 5.0], [58.7, 5.0], [58.8, 5.0], [58.9, 5.0], [59.0, 5.0], [59.1, 5.0], [59.2, 5.0], [59.3, 5.0], [59.4, 5.0], [59.5, 5.0], [59.6, 5.0], [59.7, 5.0], [59.8, 5.0], [59.9, 5.0], [60.0, 6.0], [60.1, 6.0], [60.2, 6.0], [60.3, 6.0], [60.4, 6.0], [60.5, 6.0], [60.6, 6.0], [60.7, 6.0], [60.8, 6.0], [60.9, 6.0], [61.0, 6.0], [61.1, 6.0], [61.2, 6.0], [61.3, 6.0], [61.4, 6.0], [61.5, 6.0], [61.6, 6.0], [61.7, 6.0], [61.8, 6.0], [61.9, 6.0], [62.0, 6.0], [62.1, 6.0], [62.2, 6.0], [62.3, 6.0], [62.4, 6.0], [62.5, 6.0], [62.6, 6.0], [62.7, 6.0], [62.8, 6.0], [62.9, 6.0], [63.0, 6.0], [63.1, 6.0], [63.2, 6.0], [63.3, 6.0], [63.4, 6.0], [63.5, 6.0], [63.6, 6.0], [63.7, 6.0], [63.8, 6.0], [63.9, 6.0], [64.0, 6.0], [64.1, 6.0], [64.2, 6.0], [64.3, 6.0], [64.4, 6.0], [64.5, 6.0], [64.6, 6.0], [64.7, 6.0], [64.8, 6.0], [64.9, 6.0], [65.0, 6.0], [65.1, 6.0], [65.2, 6.0], [65.3, 6.0], [65.4, 6.0], [65.5, 6.0], [65.6, 6.0], [65.7, 6.0], [65.8, 6.0], [65.9, 6.0], [66.0, 6.0], [66.1, 6.0], [66.2, 6.0], [66.3, 6.0], [66.4, 6.0], [66.5, 6.0], [66.6, 6.0], [66.7, 6.0], [66.8, 6.0], [66.9, 6.0], [67.0, 6.0], [67.1, 6.0], [67.2, 6.0], [67.3, 6.0], [67.4, 6.0], [67.5, 6.0], [67.6, 6.0], [67.7, 6.0], [67.8, 6.0], [67.9, 6.0], [68.0, 6.0], [68.1, 6.0], [68.2, 6.0], [68.3, 6.0], [68.4, 6.0], [68.5, 6.0], [68.6, 6.0], [68.7, 6.0], [68.8, 6.0], [68.9, 6.0], [69.0, 6.0], [69.1, 6.0], [69.2, 6.0], [69.3, 6.0], [69.4, 6.0], [69.5, 6.0], [69.6, 6.0], [69.7, 6.0], [69.8, 6.0], [69.9, 6.0], [70.0, 6.0], [70.1, 6.0], [70.2, 6.0], [70.3, 6.0], [70.4, 6.0], [70.5, 6.0], [70.6, 6.0], [70.7, 6.0], [70.8, 6.0], [70.9, 6.0], [71.0, 6.0], [71.1, 6.0], [71.2, 6.0], [71.3, 6.0], [71.4, 6.0], [71.5, 6.0], [71.6, 6.0], [71.7, 6.0], [71.8, 6.0], [71.9, 6.0], [72.0, 6.0], [72.1, 6.0], [72.2, 6.0], [72.3, 6.0], [72.4, 6.0], [72.5, 6.0], [72.6, 6.0], [72.7, 6.0], [72.8, 6.0], [72.9, 6.0], [73.0, 6.0], [73.1, 6.0], [73.2, 6.0], [73.3, 6.0], [73.4, 6.0], [73.5, 6.0], [73.6, 6.0], [73.7, 6.0], [73.8, 6.0], [73.9, 6.0], [74.0, 6.0], [74.1, 6.0], [74.2, 6.0], [74.3, 6.0], [74.4, 6.0], [74.5, 6.0], [74.6, 6.0], [74.7, 6.0], [74.8, 6.0], [74.9, 6.0], [75.0, 6.0], [75.1, 6.0], [75.2, 6.0], [75.3, 6.0], [75.4, 6.0], [75.5, 6.0], [75.6, 6.0], [75.7, 6.0], [75.8, 6.0], [75.9, 6.0], [76.0, 6.0], [76.1, 6.0], [76.2, 6.0], [76.3, 6.0], [76.4, 6.0], [76.5, 6.0], [76.6, 6.0], [76.7, 6.0], [76.8, 6.0], [76.9, 6.0], [77.0, 6.0], [77.1, 6.0], [77.2, 6.0], [77.3, 6.0], [77.4, 6.0], [77.5, 6.0], [77.6, 6.0], [77.7, 6.0], [77.8, 6.0], [77.9, 6.0], [78.0, 6.0], [78.1, 6.0], [78.2, 6.0], [78.3, 6.0], [78.4, 6.0], [78.5, 6.0], [78.6, 6.0], [78.7, 6.0], [78.8, 6.0], [78.9, 6.0], [79.0, 6.0], [79.1, 6.0], [79.2, 6.0], [79.3, 6.0], [79.4, 6.0], [79.5, 6.0], [79.6, 6.0], [79.7, 6.0], [79.8, 6.0], [79.9, 6.0], [80.0, 7.0], [80.1, 7.0], [80.2, 7.0], [80.3, 7.0], [80.4, 7.0], [80.5, 7.0], [80.6, 7.0], [80.7, 7.0], [80.8, 7.0], [80.9, 7.0], [81.0, 7.0], [81.1, 7.0], [81.2, 7.0], [81.3, 7.0], [81.4, 7.0], [81.5, 7.0], [81.6, 7.0], [81.7, 7.0], [81.8, 7.0], [81.9, 7.0], [82.0, 7.0], [82.1, 7.0], [82.2, 7.0], [82.3, 7.0], [82.4, 7.0], [82.5, 7.0], [82.6, 7.0], [82.7, 7.0], [82.8, 7.0], [82.9, 7.0], [83.0, 7.0], [83.1, 7.0], [83.2, 7.0], [83.3, 7.0], [83.4, 7.0], [83.5, 7.0], [83.6, 7.0], [83.7, 7.0], [83.8, 7.0], [83.9, 7.0], [84.0, 7.0], [84.1, 7.0], [84.2, 7.0], [84.3, 7.0], [84.4, 7.0], [84.5, 7.0], [84.6, 7.0], [84.7, 7.0], [84.8, 7.0], [84.9, 7.0], [85.0, 7.0], [85.1, 7.0], [85.2, 7.0], [85.3, 7.0], [85.4, 7.0], [85.5, 7.0], [85.6, 7.0], [85.7, 7.0], [85.8, 7.0], [85.9, 7.0], [86.0, 7.0], [86.1, 7.0], [86.2, 7.0], [86.3, 7.0], [86.4, 7.0], [86.5, 7.0], [86.6, 7.0], [86.7, 7.0], [86.8, 7.0], [86.9, 7.0], [87.0, 7.0], [87.1, 7.0], [87.2, 7.0], [87.3, 7.0], [87.4, 7.0], [87.5, 7.0], [87.6, 7.0], [87.7, 7.0], [87.8, 7.0], [87.9, 7.0], [88.0, 7.0], [88.1, 7.0], [88.2, 7.0], [88.3, 7.0], [88.4, 7.0], [88.5, 7.0], [88.6, 7.0], [88.7, 7.0], [88.8, 7.0], [88.9, 7.0], [89.0, 7.0], [89.1, 7.0], [89.2, 7.0], [89.3, 7.0], [89.4, 7.0], [89.5, 7.0], [89.6, 7.0], [89.7, 7.0], [89.8, 7.0], [89.9, 7.0], [90.0, 7.0], [90.1, 7.0], [90.2, 7.0], [90.3, 7.0], [90.4, 7.0], [90.5, 7.0], [90.6, 7.0], [90.7, 7.0], [90.8, 7.0], [90.9, 7.0], [91.0, 7.0], [91.1, 7.0], [91.2, 7.0], [91.3, 7.0], [91.4, 7.0], [91.5, 7.0], [91.6, 7.0], [91.7, 7.0], [91.8, 7.0], [91.9, 7.0], [92.0, 7.0], [92.1, 7.0], [92.2, 7.0], [92.3, 7.0], [92.4, 7.0], [92.5, 7.0], [92.6, 7.0], [92.7, 7.0], [92.8, 7.0], [92.9, 7.0], [93.0, 7.0], [93.1, 7.0], [93.2, 7.0], [93.3, 7.0], [93.4, 7.0], [93.5, 7.0], [93.6, 7.0], [93.7, 7.0], [93.8, 7.0], [93.9, 7.0], [94.0, 7.0], [94.1, 7.0], [94.2, 7.0], [94.3, 7.0], [94.4, 7.0], [94.5, 7.0], [94.6, 7.0], [94.7, 7.0], [94.8, 7.0], [94.9, 7.0], [95.0, 7.0], [95.1, 7.0], [95.2, 7.0], [95.3, 7.0], [95.4, 7.0], [95.5, 7.0], [95.6, 7.0], [95.7, 7.0], [95.8, 7.0], [95.9, 7.0], [96.0, 7.0], [96.1, 7.0], [96.2, 7.0], [96.3, 7.0], [96.4, 7.0], [96.5, 7.0], [96.6, 7.0], [96.7, 7.0], [96.8, 7.0], [96.9, 7.0], [97.0, 7.0], [97.1, 7.0], [97.2, 7.0], [97.3, 7.0], [97.4, 7.0], [97.5, 7.0], [97.6, 7.0], [97.7, 7.0], [97.8, 7.0], [97.9, 7.0], [98.0, 7.0], [98.1, 7.0], [98.2, 7.0], [98.3, 7.0], [98.4, 7.0], [98.5, 7.0], [98.6, 7.0], [98.7, 7.0], [98.8, 7.0], [98.9, 7.0], [99.0, 7.0], [99.1, 7.0], [99.2, 7.0], [99.3, 7.0], [99.4, 7.0], [99.5, 7.0], [99.6, 7.0], [99.7, 7.0], [99.8, 7.0], [99.9, 7.0]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 100.0, "title": "Response Time Percentiles"}},
        getOptions: function() {
            return {
                series: {
                    points: { show: false }
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentiles'
                },
                xaxis: {
                    tickDecimals: 1,
                    axisLabel: "Percentiles",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Percentile value in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : %x.2 percentile was %y ms"
                },
                selection: { mode: "xy" },
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentiles"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesPercentiles"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesPercentiles"), dataset, prepareOverviewOptions(options));
        }
};

/**
 * @param elementId Id of element where we display message
 */
function setEmptyGraph(elementId) {
    $(function() {
        $(elementId).text("No graph series with filter="+seriesFilter);
    });
}

// Response times percentiles
function refreshResponseTimePercentiles() {
    var infos = responseTimePercentilesInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimePercentiles");
        return;
    }
    if (isGraph($("#flotResponseTimesPercentiles"))){
        infos.createGraph();
    } else {
        var choiceContainer = $("#choicesResponseTimePercentiles");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesPercentiles", "#overviewResponseTimesPercentiles");
        $('#bodyResponseTimePercentiles .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimeDistributionInfos = {
        data: {"result": {"minY": 1.0, "minX": 0.0, "maxY": 5.0, "series": [{"data": [[0.0, 1.0]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[100.0, 1.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[0.0, 3.0]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[100.0, 1.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[0.0, 1.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[0.0, 1.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[0.0, 1.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[0.0, 1.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[0.0, 1.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[0.0, 2.0]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[0.0, 1.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[0.0, 3.0]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[0.0, 1.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[0.0, 5.0]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 100, "maxX": 100.0, "title": "Response Time Distribution"}},
        getOptions: function() {
            var granularity = this.data.result.granularity;
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    barWidth: this.data.result.granularity
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " responses for " + label + " were between " + xval + " and " + (xval + granularity) + " ms";
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimeDistribution"), prepareData(data.result.series, $("#choicesResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshResponseTimeDistribution() {
    var infos = responseTimeDistributionInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeDistribution");
        return;
    }
    if (isGraph($("#flotResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var syntheticResponseTimeDistributionInfos = {
        data: {"result": {"minY": 21.0, "minX": 0.0, "ticks": [[0, "Requests having \nresponse time <= 500ms"], [1, "Requests having \nresponse time > 500ms and <= 2 000ms"], [2, "Requests having \nresponse time > 2 000ms"], [3, "Requests in error"]], "maxY": 21.0, "series": [{"data": [[0.0, 21.0]], "color": "#9ACD32", "isOverall": false, "label": "Requests having \nresponse time <= 500ms", "isController": false}, {"data": [], "color": "yellow", "isOverall": false, "label": "Requests having \nresponse time > 500ms and <= 2 000ms", "isController": false}, {"data": [], "color": "orange", "isOverall": false, "label": "Requests having \nresponse time > 2 000ms", "isController": false}, {"data": [], "color": "#FF6347", "isOverall": false, "label": "Requests in error", "isController": false}], "supportsControllersDiscrimination": false, "maxX": 4.9E-324, "title": "Synthetic Response Times Distribution"}},
        getOptions: function() {
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendSyntheticResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times ranges",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                    tickLength:0,
                    min:-0.5,
                    max:3.5
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    align: "center",
                    barWidth: 0.25,
                    fill:.75
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " " + label;
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            options.xaxis.ticks = data.result.ticks;
            $.plot($("#flotSyntheticResponseTimeDistribution"), prepareData(data.result.series, $("#choicesSyntheticResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshSyntheticResponseTimeDistribution() {
    var infos = syntheticResponseTimeDistributionInfos;
    prepareSeries(infos.data, true);
    if (isGraph($("#flotSyntheticResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerSyntheticResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var activeThreadsOverTimeInfos = {
        data: {"result": {"minY": 1.0, "minX": 1.74673968E12, "maxY": 1.0, "series": [{"data": [[1.74673974E12, 1.0], [1.74673968E12, 1.0], [1.74673986E12, 1.0], [1.7467398E12, 1.0], [1.74673992E12, 1.0]], "isOverall": false, "label": "Thread Group", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.74673992E12, "title": "Active Threads Over Time"}},
        getOptions: function() {
            return {
                series: {
                    stack: true,
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 6,
                    show: true,
                    container: '#legendActiveThreadsOverTime'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                selection: {
                    mode: 'xy'
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : At %x there were %y active threads"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesActiveThreadsOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotActiveThreadsOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewActiveThreadsOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Active Threads Over Time
function refreshActiveThreadsOverTime(fixTimestamps) {
    var infos = activeThreadsOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotActiveThreadsOverTime"))) {
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesActiveThreadsOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotActiveThreadsOverTime", "#overviewActiveThreadsOverTime");
        $('#footerActiveThreadsOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var timeVsThreadsInfos = {
        data: {"result": {"minY": 4.0, "minX": 1.0, "maxY": 197.0, "series": [{"data": [[1.0, 18.0]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[1.0, 18.0]], "isOverall": false, "label": "Get all albums-Aggregated", "isController": false}, {"data": [[1.0, 197.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[1.0, 197.0]], "isOverall": false, "label": "Home page-Aggregated", "isController": true}, {"data": [[1.0, 64.0]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[1.0, 64.0]], "isOverall": false, "label": "Play songs from home page-Aggregated", "isController": false}, {"data": [[1.0, 122.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[1.0, 122.0]], "isOverall": false, "label": "Trending playlist-Aggregated", "isController": false}, {"data": [[1.0, 44.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[1.0, 44.0]], "isOverall": false, "label": "Albums page-Aggregated", "isController": true}, {"data": [[1.0, 5.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[1.0, 5.0]], "isOverall": false, "label": "Cover 2-Aggregated", "isController": false}, {"data": [[1.0, 5.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[1.0, 5.0]], "isOverall": false, "label": "Cover 1-Aggregated", "isController": false}, {"data": [[1.0, 5.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[1.0, 5.0]], "isOverall": false, "label": "Cover 4-Aggregated", "isController": false}, {"data": [[1.0, 4.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[1.0, 4.0]], "isOverall": false, "label": "Cover 3-Aggregated", "isController": false}, {"data": [[1.0, 50.0]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[1.0, 50.0]], "isOverall": false, "label": "Play songs from albums-Aggregated", "isController": false}, {"data": [[1.0, 5.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[1.0, 5.0]], "isOverall": false, "label": "Cover 5-Aggregated", "isController": false}, {"data": [[1.0, 6.0]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[1.0, 6.0]], "isOverall": false, "label": "Search-Aggregated", "isController": false}, {"data": [[1.0, 51.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[1.0, 51.0]], "isOverall": false, "label": "Initial song-Aggregated", "isController": false}, {"data": [[1.0, 5.2]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}, {"data": [[1.0, 5.2]], "isOverall": false, "label": "Get paths to songs from each album-Aggregated", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 1.0, "title": "Time VS Threads"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: { noColumns: 2,show: true, container: '#legendTimeVsThreads' },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s: At %x.2 active threads, Average response time was %y.2 ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesTimeVsThreads"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotTimesVsThreads"), dataset, options);
            // setup overview
            $.plot($("#overviewTimesVsThreads"), dataset, prepareOverviewOptions(options));
        }
};

// Time vs threads
function refreshTimeVsThreads(){
    var infos = timeVsThreadsInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTimeVsThreads");
        return;
    }
    if(isGraph($("#flotTimesVsThreads"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTimeVsThreads");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTimesVsThreads", "#overviewTimesVsThreads");
        $('#footerTimeVsThreads .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var bytesThroughputOverTimeInfos = {
        data : {"result": {"minY": 2.8666666666666667, "minX": 1.74673968E12, "maxY": 210855.58333333334, "series": [{"data": [[1.74673974E12, 130084.61666666667], [1.74673968E12, 149299.91666666666], [1.74673986E12, 145566.88333333333], [1.7467398E12, 116077.05], [1.74673992E12, 210855.58333333334]], "isOverall": false, "label": "Bytes received per second", "isController": false}, {"data": [[1.74673974E12, 9.516666666666667], [1.74673968E12, 16.683333333333334], [1.74673986E12, 18.0], [1.7467398E12, 2.8666666666666667], [1.74673992E12, 5.616666666666666]], "isOverall": false, "label": "Bytes sent per second", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.74673992E12, "title": "Bytes Throughput Over Time"}},
        getOptions : function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity) ,
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Bytes / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendBytesThroughputOverTime'
                },
                selection: {
                    mode: "xy"
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y"
                }
            };
        },
        createGraph : function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesBytesThroughputOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotBytesThroughputOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewBytesThroughputOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Bytes throughput Over Time
function refreshBytesThroughputOverTime(fixTimestamps) {
    var infos = bytesThroughputOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotBytesThroughputOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesBytesThroughputOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotBytesThroughputOverTime", "#overviewBytesThroughputOverTime");
        $('#footerBytesThroughputOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimesOverTimeInfos = {
        data: {"result": {"minY": 4.0, "minX": 1.74673968E12, "maxY": 197.0, "series": [{"data": [[1.74673986E12, 18.0]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[1.74673968E12, 197.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[1.74673974E12, 74.0], [1.74673986E12, 73.0], [1.7467398E12, 45.0]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[1.74673968E12, 122.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[1.74673986E12, 44.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[1.74673968E12, 5.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[1.74673968E12, 5.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[1.74673968E12, 5.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[1.74673968E12, 4.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[1.74673992E12, 50.0]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[1.74673968E12, 5.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[1.74673974E12, 6.0]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[1.74673968E12, 51.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[1.74673986E12, 5.2]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.74673992E12, "title": "Response Time Over Time"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average response time was %y ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Times Over Time
function refreshResponseTimeOverTime(fixTimestamps) {
    var infos = responseTimesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotResponseTimesOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesOverTime", "#overviewResponseTimesOverTime");
        $('#footerResponseTimesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var latenciesOverTimeInfos = {
        data: {"result": {"minY": 3.0, "minX": 1.74673968E12, "maxY": 147.0, "series": [{"data": [[1.74673986E12, 18.0]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[1.74673968E12, 147.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[1.74673974E12, 17.0], [1.74673986E12, 18.0], [1.7467398E12, 4.0]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[1.74673968E12, 122.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[1.74673986E12, 43.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[1.74673968E12, 3.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[1.74673968E12, 3.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[1.74673968E12, 3.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[1.74673968E12, 3.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[1.74673992E12, 11.5]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[1.74673968E12, 4.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[1.74673974E12, 6.0]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[1.74673968E12, 9.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[1.74673986E12, 5.0]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.74673992E12, "title": "Latencies Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response latencies in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendLatenciesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average latency was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesLatenciesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotLatenciesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewLatenciesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Latencies Over Time
function refreshLatenciesOverTime(fixTimestamps) {
    var infos = latenciesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyLatenciesOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotLatenciesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesLatenciesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotLatenciesOverTime", "#overviewLatenciesOverTime");
        $('#footerLatenciesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var connectTimeOverTimeInfos = {
        data: {"result": {"minY": 0.0, "minX": 1.74673968E12, "maxY": 29.0, "series": [{"data": [[1.74673986E12, 1.0]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[1.74673968E12, 29.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[1.74673974E12, 1.0], [1.74673986E12, 2.0], [1.7467398E12, 1.0]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[1.74673968E12, 29.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[1.74673986E12, 1.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[1.74673968E12, 0.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[1.74673968E12, 0.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[1.74673968E12, 0.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[1.74673968E12, 0.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[1.74673992E12, 2.0]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[1.74673968E12, 0.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[1.74673974E12, 0.33333333333333337]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[1.74673968E12, 0.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[1.74673986E12, 0.0]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.74673992E12, "title": "Connect Time Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getConnectTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average Connect Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendConnectTimeOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average connect time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesConnectTimeOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotConnectTimeOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewConnectTimeOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Connect Time Over Time
function refreshConnectTimeOverTime(fixTimestamps) {
    var infos = connectTimeOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyConnectTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotConnectTimeOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesConnectTimeOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotConnectTimeOverTime", "#overviewConnectTimeOverTime");
        $('#footerConnectTimeOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var responseTimePercentilesOverTimeInfos = {
        data: {"result": {"minY": 4.0, "minX": 1.74673968E12, "maxY": 122.0, "series": [{"data": [[1.74673974E12, 74.0], [1.74673968E12, 122.0], [1.74673986E12, 73.0], [1.7467398E12, 45.0], [1.74673992E12, 58.0]], "isOverall": false, "label": "Max", "isController": false}, {"data": [[1.74673974E12, 5.0], [1.74673968E12, 4.0], [1.74673986E12, 4.0], [1.7467398E12, 45.0], [1.74673992E12, 42.0]], "isOverall": false, "label": "Min", "isController": false}, {"data": [[1.74673974E12, 74.0], [1.74673968E12, 122.0], [1.74673986E12, 73.0], [1.7467398E12, 45.0], [1.74673992E12, 58.0]], "isOverall": false, "label": "90th percentile", "isController": false}, {"data": [[1.74673974E12, 74.0], [1.74673968E12, 122.0], [1.74673986E12, 73.0], [1.7467398E12, 45.0], [1.74673992E12, 58.0]], "isOverall": false, "label": "99th percentile", "isController": false}, {"data": [[1.74673974E12, 6.5], [1.74673968E12, 5.0], [1.74673986E12, 6.0], [1.7467398E12, 45.0], [1.74673992E12, 50.0]], "isOverall": false, "label": "Median", "isController": false}, {"data": [[1.74673974E12, 74.0], [1.74673968E12, 122.0], [1.74673986E12, 73.0], [1.7467398E12, 45.0], [1.74673992E12, 58.0]], "isOverall": false, "label": "95th percentile", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.74673992E12, "title": "Response Time Percentiles Over Time (successful requests only)"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Response Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentilesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Response time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentilesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimePercentilesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimePercentilesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Time Percentiles Over Time
function refreshResponseTimePercentilesOverTime(fixTimestamps) {
    var infos = responseTimePercentilesOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotResponseTimePercentilesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimePercentilesOverTime", "#overviewResponseTimePercentilesOverTime");
        $('#footerResponseTimePercentilesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var responseTimeVsRequestInfos = {
    data: {"result": {"minY": 5.0, "minX": 1.0, "maxY": 45.0, "series": [{"data": [[1.0, 45.0], [6.0, 5.0]], "isOverall": false, "label": "Successes", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 6.0, "title": "Response Time Vs Request"}},
    getOptions: function() {
        return {
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Response Time in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: {
                noColumns: 2,
                show: true,
                container: '#legendResponseTimeVsRequest'
            },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median response time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesResponseTimeVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotResponseTimeVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewResponseTimeVsRequest"), dataset, prepareOverviewOptions(options));

    }
};

// Response Time vs Request
function refreshResponseTimeVsRequest() {
    var infos = responseTimeVsRequestInfos;
    prepareSeries(infos.data);
    if (isGraph($("#flotResponseTimeVsRequest"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeVsRequest");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimeVsRequest", "#overviewResponseTimeVsRequest");
        $('#footerResponseRimeVsRequest .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var latenciesVsRequestInfos = {
    data: {"result": {"minY": 4.0, "minX": 1.0, "maxY": 8.0, "series": [{"data": [[1.0, 8.0], [6.0, 4.0]], "isOverall": false, "label": "Successes", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 6.0, "title": "Latencies Vs Request"}},
    getOptions: function() {
        return{
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Latency in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: { noColumns: 2,show: true, container: '#legendLatencyVsRequest' },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median Latency time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesLatencyVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotLatenciesVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewLatenciesVsRequest"), dataset, prepareOverviewOptions(options));
    }
};

// Latencies vs Request
function refreshLatenciesVsRequest() {
        var infos = latenciesVsRequestInfos;
        prepareSeries(infos.data);
        if(isGraph($("#flotLatenciesVsRequest"))){
            infos.createGraph();
        }else{
            var choiceContainer = $("#choicesLatencyVsRequest");
            createLegend(choiceContainer, infos);
            infos.createGraph();
            setGraphZoomable("#flotLatenciesVsRequest", "#overviewLatenciesVsRequest");
            $('#footerLatenciesVsRequest .legendColorBox > div').each(function(i){
                $(this).clone().prependTo(choiceContainer.find("li").eq(i));
            });
        }
};

var hitsPerSecondInfos = {
        data: {"result": {"minY": 0.016666666666666666, "minX": 1.74673968E12, "maxY": 0.11666666666666667, "series": [{"data": [[1.74673974E12, 0.06666666666666667], [1.74673968E12, 0.11666666666666667], [1.74673986E12, 0.11666666666666667], [1.7467398E12, 0.016666666666666666], [1.74673992E12, 0.03333333333333333]], "isOverall": false, "label": "hitsPerSecond", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.74673992E12, "title": "Hits Per Second"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of hits / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendHitsPerSecond"
                },
                selection: {
                    mode : 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y.2 hits/sec"
                }
            };
        },
        createGraph: function createGraph() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesHitsPerSecond"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotHitsPerSecond"), dataset, options);
            // setup overview
            $.plot($("#overviewHitsPerSecond"), dataset, prepareOverviewOptions(options));
        }
};

// Hits per second
function refreshHitsPerSecond(fixTimestamps) {
    var infos = hitsPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if (isGraph($("#flotHitsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesHitsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotHitsPerSecond", "#overviewHitsPerSecond");
        $('#footerHitsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var codesPerSecondInfos = {
        data: {"result": {"minY": 0.016666666666666666, "minX": 1.74673968E12, "maxY": 0.11666666666666667, "series": [{"data": [[1.74673974E12, 0.06666666666666667], [1.74673968E12, 0.11666666666666667], [1.74673986E12, 0.11666666666666667], [1.7467398E12, 0.016666666666666666], [1.74673992E12, 0.03333333333333333]], "isOverall": false, "label": "200", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.74673992E12, "title": "Codes Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendCodesPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "Number of Response Codes %s at %x was %y.2 responses / sec"
                }
            };
        },
    createGraph: function() {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesCodesPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotCodesPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewCodesPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Codes per second
function refreshCodesPerSecond(fixTimestamps) {
    var infos = codesPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotCodesPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesCodesPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotCodesPerSecond", "#overviewCodesPerSecond");
        $('#footerCodesPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var transactionsPerSecondInfos = {
        data: {"result": {"minY": 0.016666666666666666, "minX": 1.74673968E12, "maxY": 0.08333333333333333, "series": [{"data": [[1.74673974E12, 0.05]], "isOverall": false, "label": "Search-success", "isController": false}, {"data": [[1.74673968E12, 0.016666666666666666]], "isOverall": false, "label": "Cover 3-success", "isController": false}, {"data": [[1.74673968E12, 0.016666666666666666]], "isOverall": false, "label": "Cover 1-success", "isController": false}, {"data": [[1.74673986E12, 0.016666666666666666]], "isOverall": false, "label": "Albums page-success", "isController": true}, {"data": [[1.74673968E12, 0.016666666666666666]], "isOverall": false, "label": "Cover 4-success", "isController": false}, {"data": [[1.74673986E12, 0.08333333333333333]], "isOverall": false, "label": "Get paths to songs from each album-success", "isController": false}, {"data": [[1.74673974E12, 0.016666666666666666], [1.74673986E12, 0.016666666666666666], [1.7467398E12, 0.016666666666666666]], "isOverall": false, "label": "Play songs from home page-success", "isController": false}, {"data": [[1.74673968E12, 0.016666666666666666]], "isOverall": false, "label": "Initial song-success", "isController": false}, {"data": [[1.74673968E12, 0.016666666666666666]], "isOverall": false, "label": "Cover 5-success", "isController": false}, {"data": [[1.74673968E12, 0.016666666666666666]], "isOverall": false, "label": "Home page-success", "isController": true}, {"data": [[1.74673992E12, 0.03333333333333333]], "isOverall": false, "label": "Play songs from albums-success", "isController": false}, {"data": [[1.74673968E12, 0.016666666666666666]], "isOverall": false, "label": "Trending playlist-success", "isController": false}, {"data": [[1.74673968E12, 0.016666666666666666]], "isOverall": false, "label": "Cover 2-success", "isController": false}, {"data": [[1.74673986E12, 0.016666666666666666]], "isOverall": false, "label": "Get all albums-success", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.74673992E12, "title": "Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTransactionsPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                }
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTransactionsPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTransactionsPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewTransactionsPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Transactions per second
function refreshTransactionsPerSecond(fixTimestamps) {
    var infos = transactionsPerSecondInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTransactionsPerSecond");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotTransactionsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTransactionsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTransactionsPerSecond", "#overviewTransactionsPerSecond");
        $('#footerTransactionsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var totalTPSInfos = {
        data: {"result": {"minY": 0.016666666666666666, "minX": 1.74673968E12, "maxY": 0.13333333333333333, "series": [{"data": [[1.74673974E12, 0.06666666666666667], [1.74673968E12, 0.13333333333333333], [1.74673986E12, 0.13333333333333333], [1.7467398E12, 0.016666666666666666], [1.74673992E12, 0.03333333333333333]], "isOverall": false, "label": "Transaction-success", "isController": false}, {"data": [], "isOverall": false, "label": "Transaction-failure", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.74673992E12, "title": "Total Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTotalTPS"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                },
                colors: ["#9ACD32", "#FF6347"]
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTotalTPS"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTotalTPS"), dataset, options);
        // setup overview
        $.plot($("#overviewTotalTPS"), dataset, prepareOverviewOptions(options));
    }
};

// Total Transactions per second
function refreshTotalTPS(fixTimestamps) {
    var infos = totalTPSInfos;
    // We want to ignore seriesFilter
    prepareSeries(infos.data, false, true);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotTotalTPS"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTotalTPS");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTotalTPS", "#overviewTotalTPS");
        $('#footerTotalTPS .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

// Collapse the graph matching the specified DOM element depending the collapsed
// status
function collapse(elem, collapsed){
    if(collapsed){
        $(elem).parent().find(".fa-chevron-up").removeClass("fa-chevron-up").addClass("fa-chevron-down");
    } else {
        $(elem).parent().find(".fa-chevron-down").removeClass("fa-chevron-down").addClass("fa-chevron-up");
        if (elem.id == "bodyBytesThroughputOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshBytesThroughputOverTime(true);
            }
            document.location.href="#bytesThroughputOverTime";
        } else if (elem.id == "bodyLatenciesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesOverTime(true);
            }
            document.location.href="#latenciesOverTime";
        } else if (elem.id == "bodyCustomGraph") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCustomGraph(true);
            }
            document.location.href="#responseCustomGraph";
        } else if (elem.id == "bodyConnectTimeOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshConnectTimeOverTime(true);
            }
            document.location.href="#connectTimeOverTime";
        } else if (elem.id == "bodyResponseTimePercentilesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimePercentilesOverTime(true);
            }
            document.location.href="#responseTimePercentilesOverTime";
        } else if (elem.id == "bodyResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeDistribution();
            }
            document.location.href="#responseTimeDistribution" ;
        } else if (elem.id == "bodySyntheticResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshSyntheticResponseTimeDistribution();
            }
            document.location.href="#syntheticResponseTimeDistribution" ;
        } else if (elem.id == "bodyActiveThreadsOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshActiveThreadsOverTime(true);
            }
            document.location.href="#activeThreadsOverTime";
        } else if (elem.id == "bodyTimeVsThreads") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTimeVsThreads();
            }
            document.location.href="#timeVsThreads" ;
        } else if (elem.id == "bodyCodesPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCodesPerSecond(true);
            }
            document.location.href="#codesPerSecond";
        } else if (elem.id == "bodyTransactionsPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTransactionsPerSecond(true);
            }
            document.location.href="#transactionsPerSecond";
        } else if (elem.id == "bodyTotalTPS") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTotalTPS(true);
            }
            document.location.href="#totalTPS";
        } else if (elem.id == "bodyResponseTimeVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeVsRequest();
            }
            document.location.href="#responseTimeVsRequest";
        } else if (elem.id == "bodyLatenciesVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesVsRequest();
            }
            document.location.href="#latencyVsRequest";
        }
    }
}

/*
 * Activates or deactivates all series of the specified graph (represented by id parameter)
 * depending on checked argument.
 */
function toggleAll(id, checked){
    var placeholder = document.getElementById(id);

    var cases = $(placeholder).find(':checkbox');
    cases.prop('checked', checked);
    $(cases).parent().children().children().toggleClass("legend-disabled", !checked);

    var choiceContainer;
    if ( id == "choicesBytesThroughputOverTime"){
        choiceContainer = $("#choicesBytesThroughputOverTime");
        refreshBytesThroughputOverTime(false);
    } else if(id == "choicesResponseTimesOverTime"){
        choiceContainer = $("#choicesResponseTimesOverTime");
        refreshResponseTimeOverTime(false);
    }else if(id == "choicesResponseCustomGraph"){
        choiceContainer = $("#choicesResponseCustomGraph");
        refreshCustomGraph(false);
    } else if ( id == "choicesLatenciesOverTime"){
        choiceContainer = $("#choicesLatenciesOverTime");
        refreshLatenciesOverTime(false);
    } else if ( id == "choicesConnectTimeOverTime"){
        choiceContainer = $("#choicesConnectTimeOverTime");
        refreshConnectTimeOverTime(false);
    } else if ( id == "choicesResponseTimePercentilesOverTime"){
        choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        refreshResponseTimePercentilesOverTime(false);
    } else if ( id == "choicesResponseTimePercentiles"){
        choiceContainer = $("#choicesResponseTimePercentiles");
        refreshResponseTimePercentiles();
    } else if(id == "choicesActiveThreadsOverTime"){
        choiceContainer = $("#choicesActiveThreadsOverTime");
        refreshActiveThreadsOverTime(false);
    } else if ( id == "choicesTimeVsThreads"){
        choiceContainer = $("#choicesTimeVsThreads");
        refreshTimeVsThreads();
    } else if ( id == "choicesSyntheticResponseTimeDistribution"){
        choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        refreshSyntheticResponseTimeDistribution();
    } else if ( id == "choicesResponseTimeDistribution"){
        choiceContainer = $("#choicesResponseTimeDistribution");
        refreshResponseTimeDistribution();
    } else if ( id == "choicesHitsPerSecond"){
        choiceContainer = $("#choicesHitsPerSecond");
        refreshHitsPerSecond(false);
    } else if(id == "choicesCodesPerSecond"){
        choiceContainer = $("#choicesCodesPerSecond");
        refreshCodesPerSecond(false);
    } else if ( id == "choicesTransactionsPerSecond"){
        choiceContainer = $("#choicesTransactionsPerSecond");
        refreshTransactionsPerSecond(false);
    } else if ( id == "choicesTotalTPS"){
        choiceContainer = $("#choicesTotalTPS");
        refreshTotalTPS(false);
    } else if ( id == "choicesResponseTimeVsRequest"){
        choiceContainer = $("#choicesResponseTimeVsRequest");
        refreshResponseTimeVsRequest();
    } else if ( id == "choicesLatencyVsRequest"){
        choiceContainer = $("#choicesLatencyVsRequest");
        refreshLatenciesVsRequest();
    }
    var color = checked ? "black" : "#818181";
    if(choiceContainer != null) {
        choiceContainer.find("label").each(function(){
            this.style.color = color;
        });
    }
}

