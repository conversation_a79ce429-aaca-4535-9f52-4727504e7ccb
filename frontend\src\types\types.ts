import {Howl} from 'howler'

export type Genre = {
  name: string
}

export type AlbumBasic = {
  id: string
  title: string
  releaseDate: Date
  cover: string
}

export type Album = AlbumBasic & {
  songs: Song[]
}

export type Playlist = {
  id: string
  title: string
  songs: Song[]
}

export type Song = {
  id: string
  title: string
  duration: number
  bpm: number
  price: number
  cover: string
  src: string
  releaseDate: Date
  key: string
  lyrics: string | null
  albumId: string | null
  playlistId: string | null
  genres: Genre[]

  howl: Howl
}

export type SongFilterType = {
  name: string,
}