import { Response, Request } from "express";
import { handleRepositoryErrors, parseRequest } from "../utils";
import { idSchema, reservationSchema } from "../validationSchemas";
import { reservationRepository } from "./repository";

const createReservation = async (req: Request, res: Response) => {
  const request = await parseRequest(reservationSchema, req, res);
  if (request == null) {
    return;
  }

  // TODO: auth against the pay gate

  // TODO: check if the capacity is available

  // TODO if both conditions above are satisfied:
  const reservation = await reservationRepository.create(request.body);

  if (reservation.isErr) {
    handleRepositoryErrors(reservation.error, res);
    return;
  }

  res.status(201).send(reservation.value);
  // TODO: send email
};

const getAll = async (req: Request, res: Response) => {
  const reservation = await reservationRepository.getAll();

  if (reservation.isErr) {
    handleRepositoryErrors(reservation.error, res);
    return;
  }

  res.status(200).send(reservation.value);
};

const getSingle = async (req: Request, res: Response) => {
  const request = await parseRequest(idSchema, req, res);
  if (request == null) {
    return;
  }

  const reservation = await reservationRepository.get(request.params.id);

  if (reservation.isErr) {
    handleRepositoryErrors(reservation.error, res);
    return;
  }

  res.status(200).send(reservation.value);
};

const updateReservation = async (req: Request, res: Response) => {
  const idRequest = await parseRequest(idSchema, req, res);
  if (idRequest == null) {
    return;
  }

  const bodyRequest = await parseRequest(reservationSchema, req, res);
  if (bodyRequest == null) {
    return;
  }

  const reservation = await reservationRepository.update(
    idRequest.params.id,
    bodyRequest.body
  );

  if (reservation.isErr) {
    handleRepositoryErrors(reservation.error, res);
    return;
  }

  res.status(200).send(reservation.value);
};

const deleteReservation = async (req: Request, res: Response) => {
  const request = await parseRequest(idSchema, req, res);
  if (request == null) {
    return;
  }

  const reservation = await reservationRepository.delete(request.params.id);

  if (reservation.isErr) {
    handleRepositoryErrors(reservation.error, res);
    return;
  }

  res.status(200).send(reservation.value);
};

export const reservationController = {
  createReservation,
  getSingle,
  getAll,
  updateReservation,
  deleteReservation,
};
