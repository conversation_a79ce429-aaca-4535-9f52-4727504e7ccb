import {Prisma} from '@prisma/client';
import {randomInt} from "node:crypto";

export const genres: Prisma.GenreCreateInput[] = [
  {
    name: "Nu Metal",
  },
  {
    name: "Rock",
  },
  {
    name: "Pop",
  },
  {
    name: "Hip-Hop",
  },
  {
    name: "Metal",
  },
  {
    name: "Jazz",
  },
  {
    name: "Blues",
  },
  {
    name: "Classical",
  },
  {
    name: "Country",
  },
  {
    name: "Electronic",
  },
  {
    name: "Folk",
  },
  {
    name: "R&B",
  },
  {
    name: "Reg<PERSON>e",
  },
  {
    name: "Soul",
  },
  {
    name: "Punk",
  },
  {
    name: "Indie",
  },
  {
    name: "Dance",
  },
  {
    name: "Techno",
  },
  {
    name: "House",
  },
  {
    name: "Trance",
  },
  {
    name: "Drum & Bass",
  },
  {
    name: "Dub step",
  },
  {
    name: "Hardcore",
  },
  {
    name: "Garage",
  },
  {
    name: "Grime",
  },
  {
    name: "Ambient",
  },
  {
    name: "Chill out",
  },
  {
    name: "Downtempo",
  },
  {
    name: "Trip Hop",
  },
]


// noinspection SpellCheckingInspection
export const albums: Prisma.AlbumCreateInput[] = [
  {
    title: "Almond Everet",
    releaseDate: new Date("2024-10-01"),
    cover: "/covers/cover1.png",
    songs: {
      create: [
        {
          title: "For The Win",
          duration: 164,
          bpm: 144,
          price: randomInt(100,500)/100,
          cover: "/covers/cover1.png",
          src: "/albums/Almond Everet/For The Win.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "R&B & soul"},
              create: {name: "R&B & soul"}
            }
          }
        },
        {
          title: "Funk AF",
          duration: 175,
          bpm: 135,
          price: randomInt(100,500)/100,
          cover: "/covers/cover1.png",
          src: "/albums/Almond Everet/Funk AF.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "D#m",
          genres: {
            connectOrCreate: {
              where: {name: "R&B & soul"},
              create: {name: "R&B & soul"}
            }
          }
        },
        {
          title: "Head of The Snake",
          duration: 174,
          bpm: 90,
          price: randomInt(100,500)/100,
          cover: "/covers/cover1.png",
          src: "/albums/Almond Everet/Head of The Snake.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "D#m",
          genres: {
            connectOrCreate: {
              where: {name: "Rock"},
              create: {name: "Rock"}
            }
          }
        },
        {
          title: "Jay Walking",
          duration: 179,
          bpm: 89,
          price: randomInt(100,500)/100,
          cover: "/covers/cover1.png",
          src: "/albums/Almond Everet/Jay Walking.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "C#m",
          genres: {
            connectOrCreate: {
              where: {name: "Rock"},
              create: {name: "Rock"}
            }
          }
        },
        {
          title: "Life on Hold",
          duration: 161,
          bpm: 105,
          price: randomInt(100,500)/100,
          cover: "/covers/cover1.png",
          src: "/albums/Almond Everet/Life on Hold.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "Pop"},
              create: {name: "Pop"}
            }
          }
        },
        {
          title: "Miss U",
          duration: 176,
          bpm: 79,
          price: randomInt(100,500)/100,
          cover: "/covers/cover1.png",
          src: "/albums/Almond Everet/Miss U.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "F#m",
          genres: {
            connectOrCreate: {
              where: {name: "Rock"},
              create: {name: "Rock"}
            }
          }
        },
        {
          title: "Rain Rain Go Away",
          duration: 210,
          bpm: 103,
          price: randomInt(100,500)/100,
          cover: "/covers/cover1.png",
          src: "/albums/Almond Everet/Rain Rain Go Away.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "A#m",
          genres: {
            connectOrCreate: {
              where: {name: "Rock"},
              create: {name: "Rock"}
            }
          }
        },
      ]
    }
  },
  {
    title: "Density & Time",
    releaseDate: new Date("2024-08-01"),
    cover: "/covers/cover2.png",
    songs: {
      create: [
        {
          title: "Denied Access",
          duration: 234,
          bpm: 144,
          price: randomInt(100,500)/100,
          cover: "/covers/cover2.png",
          src: "/albums/Density & Time/Denied Access.mp3",
          releaseDate: new Date("2024-08-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "Ambient"},
              create: {name: "Ambient"}
            }
          }
        },
        {
          title: "Gold and Crawfish",
          duration: 138,
          bpm: 135,
          price: randomInt(100,500)/100,
          cover: "/covers/cover2.png",
          src: "/albums/Density & Time/Gold and Crawfish.mp3",
          releaseDate: new Date("2024-08-01"),
          key: "D#m",
          genres: {
            connectOrCreate: {
              where: {name: "Rock"},
              create: {name: "Rock"}
            }
          }
        },
        {
          title: "Packed",
          duration: 207,
          bpm: 90,
          price: randomInt(100,500)/100,
          cover: "/covers/cover2.png",
          src: "/albums/Density & Time/Packed.mp3",
          releaseDate: new Date("2024-08-01"),
          key: "D#m",
          genres: {
            connectOrCreate: {
              where: {name: "Pop"},
              create: {name: "Pop"}
            }
          }
        },
        {
          title: "Shady",
          duration: 191,
          bpm: 89,
          price: randomInt(100,500)/100,
          cover: "/covers/cover2.png",
          src: "/albums/Density & Time/Shady.mp3",
          releaseDate: new Date("2024-08-01"),
          key: "C#m",
          genres: {
            connectOrCreate: {
              where: {name: "Hip-Hop"},
              create: {name: "Hip-Hop"}
            }
          }
        },
        {
          title: "Twin Lynches",
          duration: 254,
          bpm: 105,
          price: randomInt(100,500)/100,
          cover: "/covers/cover2.png",
          src: "/albums/Density & Time/Twin Lynches.mp3",
          releaseDate: new Date("2024-08-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "Film"},
              create: {name: "Film"}
            }
          }
        },
      ]
    }
  },
  {
    title: "Everet Almond",
    releaseDate: new Date("2024-10-01"),
    cover: "/covers/cover3.png",
    songs: {
      create: [
        {
          title: "A Stroll Alone",
          duration: 183,
          bpm: 144,
          price: randomInt(100,500)/100,
          cover: "/covers/cover3.png",
          src: "/albums/Everet Almond/A Stroll Alone.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "Film"},
              create: {name: "Film"}
            }
          }
        },
        {
          title: "Among The Stars",
          duration: 204,
          bpm: 135,
          price: randomInt(100,500)/100,
          cover: "/covers/cover3.png",
          src: "/albums/Everet Almond/Among The Stars.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "D#m",
          genres: {
            connectOrCreate: {
              where: {name: "EDM"},
              create: {name: "EDM"}
            }
          }
        },
        {
          title: "Basement Apartment",
          duration: 218,
          bpm: 90,
          price: randomInt(100,500)/100,
          cover: "/covers/cover3.png",
          src: "/albums/Everet Almond/Basement Apartment.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "D#m",
          genres: {
            connectOrCreate: {
              where: {name: "Rock"},
              create: {name: "Rock"}
            }
          }
        },
        {
          title: "Doc and Wyatt",
          duration: 195,
          bpm: 89,
          price: randomInt(100,500)/100,
          cover: "/covers/cover3.png",
          src: "/albums/Everet Almond/Doc and Wyatt.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "C#m",
          genres: {
            connectOrCreate: {
              where: {name: "Film"},
              create: {name: "Film"}
            }
          }
        },
        {
          title: "Every Night Of The Week",
          duration: 198,
          bpm: 105,
          price: randomInt(100,500)/100,
          cover: "/covers/cover3.png",
          src: "/albums/Everet Almond/Every Night Of The Week.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "Rock"},
              create: {name: "Rock"}
            }
          }
        },
        {
          title: "Everybody Get Up",
          duration: 194,
          bpm: 105,
          price: randomInt(100,500)/100,
          cover: "/covers/cover3.png",
          src: "/albums/Everet Almond/Everybody Get Up.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "Rock"},
              create: {name: "Rock"}
            }
          }
        },
        {
          title: "Feeling Fab",
          duration: 194,
          bpm: 105,
          price: randomInt(100,500)/100,
          cover: "/covers/cover3.png",
          src: "/albums/Everet Almond/Feeling Fab.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "Rock"},
              create: {name: "Rock"}
            }
          }
        },
      ]
    }
  },
  {
    title: "Everet Almond reload",
    releaseDate: new Date("2024-10-01"),
    cover: "/covers/cover4.png",
    songs: {
      create: [
        {
          title: "Read My Lips Time To Party",
          duration: 144,
          bpm: 144,
          price: randomInt(100,500)/100,
          cover: "/covers/cover4.png",
          src: "/albums/Everet Almond reload/Read My Lips Time To Party.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "EDM"},
              create: {name: "EDM"}
            }
          }
        },
        {
          title: "Red Whine",
          duration: 190,
          bpm: 135,
          price: randomInt(100,500)/100,
          cover: "/covers/cover4.png",
          src: "/albums/Everet Almond reload/Red Whine.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "D#m",
          genres: {
            connectOrCreate: {
              where: {name: "Rock"},
              create: {name: "Rock"}
            }
          }
        },
        {
          title: "Ride",
          duration: 191,
          bpm: 90,
          price: randomInt(100,500)/100,
          cover: "/covers/cover4.png",
          src: "/albums/Everet Almond reload/Ride.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "D#m",
          genres: {
            connectOrCreate: {
              where: {name: "Rock"},
              create: {name: "Rock"}
            }
          }
        },
        {
          title: "Supa Fine",
          duration: 185,
          bpm: 89,
          price: randomInt(100,500)/100,
          cover: "/covers/cover4.png",
          src: "/albums/Everet Almond reload/Supa Fine.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "C#m",
          genres: {
            connectOrCreate: {
              where: {name: "Rock"},
              create: {name: "Rock"}
            }
          }
        },
        {
          title: "Under The Sun",
          duration: 179,
          bpm: 105,
          price: randomInt(100,500)/100,
          cover: "/covers/cover4.png",
          src: "/albums/Everet Almond reload/Under The Sun.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "Folk & Country"},
              create: {name: "Folk & Country"}
            }
          }
        },
        {
          title: "Win The Battle Win The War",
          duration: 158,
          bpm: 105,
          price: randomInt(100,500)/100,
          cover: "/covers/cover4.png",
          src: "/albums/Everet Almond reload/Win The Battle Win The War.mp3",
          releaseDate: new Date("2024-10-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "Rock"},
              create: {name: "Rock"}
            }
          }
        },
      ]
    },
  },
  {
    title: "Patrick Patrikios",
    releaseDate: new Date("2024-09-01"),
    cover: "/covers/cover5.png",
    songs: {
      create: [
        {
          title: "Bazaar Ballad",
          duration: 156,
          bpm: 144,
          price: randomInt(100,500)/100,
          cover: "/covers/cover5.png",
          src: "/albums/Patrick Patrikios/Bazaar Ballad.mp3",
          releaseDate: new Date("2024-09-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "Film"},
              create: {name: "Film"}
            }
          }
        },
        {
          title: "Dune dancer",
          duration: 159,
          bpm: 135,
          price: randomInt(100,500)/100,
          cover: "/covers/cover5.png",
          src: "/albums/Patrick Patrikios/Dune dancer.mp3",
          releaseDate: new Date("2024-09-01"),
          key: "D#m",
          genres: {
            connectOrCreate: {
              where: {name: "Film"},
              create: {name: "Film"}
            }
          }
        },
        {
          title: "Echoes of emir",
          duration: 152,
          bpm: 90,
          price: randomInt(100,500)/100,
          cover: "/covers/cover5.png",
          src: "/albums/Patrick Patrikios/Echoes of emir.mp3",
          releaseDate: new Date("2024-09-01"),
          key: "D#m",
          genres: {
            connectOrCreate: {
              where: {name: "Film"},
              create: {name: "Film"}
            }
          }
        },
        {
          title: "Jasmine Whipers",
          duration: 164,
          bpm: 89,
          price: randomInt(100,500)/100,
          cover: "/covers/cover5.png",
          src: "/albums/Patrick Patrikios/Jasmine Whipers.mp3",
          releaseDate: new Date("2024-09-01"),
          key: "C#m",
          genres: {
            connectOrCreate: {
              where: {name: "Film"},
              create: {name: "Film"}
            }
          }
        },
        {
          title: "Mirage melody",
          duration: 155,
          bpm: 105,
          price: randomInt(100,500)/100,
          cover: "/covers/cover5.png",
          src: "/albums/Patrick Patrikios/Mirage melody.mp3",
          releaseDate: new Date("2024-09-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "Film"},
              create: {name: "Film"}
            }
          }
        },
        {
          title: "Sands of serenity",
          duration: 166,
          bpm: 105,
          price: randomInt(100,500)/100,
          cover: "/covers/cover5.png",
          src: "/albums/Patrick Patrikios/Sands of serenity.mp3",
          releaseDate: new Date("2024-09-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "Film"},
              create: {name: "Film"}
            }
          }
        },
        {
          title: "Sunlit Souk",
          duration: 164,
          bpm: 105,
          price: randomInt(100,500)/100,
          cover: "/covers/cover5.png",
          src: "/albums/Patrick Patrikios/Sunlit Souk.mp3",
          releaseDate: new Date("2024-09-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "Film"},
              create: {name: "Film"}
            }
          }
        },
        {
          title: "Temple of treasures",
          duration: 162,
          bpm: 105,
          price: randomInt(100,500)/100,
          cover: "/covers/cover5.png",
          src: "/albums/Patrick Patrikios/Temple of treasures.mp3",
          releaseDate: new Date("2024-09-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "Film"},
              create: {name: "Film"}
            }
          }
        },
        {
          title: "Veil of mysteries",
          duration: 156,
          bpm: 105,
          price: randomInt(100,500)/100,
          cover: "/covers/cover5.png",
          src: "/albums/Patrick Patrikios/Veil of mysteries.mp3",
          releaseDate: new Date("2024-09-01"),
          key: "G#m",
          genres: {
            connectOrCreate: {
              where: {name: "Film"},
              create: {name: "Film"}
            }
          }
        },
      ]
    }
  }
]