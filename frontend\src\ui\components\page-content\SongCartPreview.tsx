import {Song} from "@/types/types.ts";
import {Button} from "../button.tsx";
import {XMarkIcon} from "@heroicons/react/24/outline";
import {toast} from "../use-toast.ts";
import {mediaPath} from "@/App.tsx";
import {Formater} from "@/business/Formater.ts";

function SongCartPreview({song}: { song: Song }) {
  const onDelete = () => {
    toast({
      title: "Removed from cart",
      description: "Removed album " + song.title + " from cart"
    })
  }

  return (
    <>
      <div className="flex gap-2 my-2 w-full justify-between text-background">
        <img src={mediaPath + song.cover} alt="cover" className="w-10 h-10"/>
        <div>
          <p className="font-bold">{song.title}</p>
          <p>{song.bpm} BPM</p>
        </div>
        <p>{Formater.formatPrice(song.price)}</p>
        <Button variant="destructive" onClick={onDelete} className="w-max h-max p-0">
          <XMarkIcon className="h-6 w-6"/>
        </Button>

      </div>
      <hr/>
    </>
  )
}

export default SongCartPreview