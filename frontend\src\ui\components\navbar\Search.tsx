import {zod<PERSON><PERSON>olver} from "@hookform/resolvers/zod"
import {useForm} from "react-hook-form"
import {z} from "zod"

import {Button} from "@/ui/components/button.tsx"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/ui/components/form.tsx"
import {
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";
import {Input} from "../input.tsx";
import {SongFilterType} from "@/types/types.ts";
import {useNavigate} from "react-router-dom";
import { XMarkIcon } from "@heroicons/react/24/outline";

const FormSchema = z.object({
  name: z.string(),
})

type SearchProps = {
  setFilter: (filter: SongFilterType) => void,
}

function Search({setFilter}: SearchProps) {

  const navigate = useNavigate();
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: "",
    },
  })

  function onSubmit(data: z.infer<typeof FormSchema>) {
    setFilter({name: data.name})
    navigate("/home")
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="flex gap-2 w-full justify-center">
          <FormField
            control={form.control}
            name="name"
            render={({field}) => (
              <FormItem className="w-3/4 2xl:w-1/2 relative">
                <FormControl>
                  <Input placeholder="Search..." {...field} className="w-full"/>
                </FormControl>
                <Button
                  name="clear_search"
                  type="button"
                  variant="link"
                  size="icon"
                  className="absolute right-0 bottom-0"
                  onClick={() => {
                    form.setValue("name", "");
                    form.handleSubmit(onSubmit)();
                  }}
                >
                  <XMarkIcon className="h-6 w-6"/>
                </Button>
                <FormMessage/>
              </FormItem>
            )}
          />

        <Button name="search" variant="default" className="w-max h-max">
          <MagnifyingGlassIcon className="h-6 w-6 "/>
        </Button>
      </form>
    </Form>
  )
}

export default Search