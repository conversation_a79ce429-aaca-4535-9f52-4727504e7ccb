!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):"object"==typeof module&&"object"==typeof module.exports?module.exports=a(require("jquery")):a(jQuery)}(function(a){return function(a){"use strict";a.extend({tablesorter:new function(){function b(b,c,d,e){for(var f,g,h=q.parsers.length,i=!1,j="",k=!0;""===j&&k;)d++,c[d]?(i=c[d].cells[e],j=q.getElementText(b,i,e),g=a(i),b.debug&&console.log("Checking if value was empty on row "+d+", column: "+e+': "'+j+'"')):k=!1;for(;--h>=0;)if(f=q.parsers[h],f&&"text"!==f.id&&f.is&&f.is(j,b.table,i,g))return f;return q.getParserById("text")}function c(a,c){var d,e,f,g,h,i,j,k,l,m,n,o,p=a.table,r=0,s={};if(a.$tbodies=a.$table.children("tbody:not(."+a.cssInfoBlock+")"),n="undefined"==typeof c?a.$tbodies:c,o=n.length,0===o)return a.debug?console.warn("Warning: *Empty table!* Not building a parser cache"):"";for(a.debug&&(m=new Date,console[console.group?"group":"log"]("Detecting parsers for each column")),e={extractors:[],parsers:[]};o>r;){if(d=n[r].rows,d.length)for(f=a.columns,g=0;f>g;g++)h=a.$headerIndexed[g],i=q.getColumnData(p,a.headers,g),l=q.getParserById(q.getData(h,i,"extractor")),k=q.getParserById(q.getData(h,i,"sorter")),j="false"===q.getData(h,i,"parser"),a.empties[g]=(q.getData(h,i,"empty")||a.emptyTo||(a.emptyToBottom?"bottom":"top")).toLowerCase(),a.strings[g]=(q.getData(h,i,"string")||a.stringTo||"max").toLowerCase(),j&&(k=q.getParserById("no-parser")),l||(l=!1),k||(k=b(a,d,-1,g)),a.debug&&(s["("+g+") "+h.text()]={parser:k.id,extractor:l?l.id:"none",string:a.strings[g],empty:a.empties[g]}),e.parsers[g]=k,e.extractors[g]=l;r+=e.parsers.length?o:1}a.debug&&(q.isEmptyObject(s)?console.warn("  No parsers detected!"):console[console.table?"table":"log"](s),console.log("Completed detecting parsers"+q.benchmark(m)),console.groupEnd&&console.groupEnd()),a.parsers=e.parsers,a.extractors=e.extractors}function d(b,c,d){var e,f,g,h,i,j,k,l,m,n,o,p,r,s,t=b.config,u=t.parsers;if(t.$tbodies=t.$table.children("tbody:not(."+t.cssInfoBlock+")"),k="undefined"==typeof d?t.$tbodies:d,t.cache={},t.totalRows=0,!u)return t.debug?console.warn("Warning: *Empty table!* Not building a cache"):"";for(t.debug&&(n=new Date),t.showProcessing&&q.isProcessing(b,!0),j=0;j<k.length;j++){for(s=[],e=t.cache[j]={normalized:[]},o=k[j]&&k[j].rows.length||0,h=0;o>h;++h)if(p={child:[],raw:[]},l=a(k[j].rows[h]),m=[],l.hasClass(t.cssChildRow)&&0!==h)for(f=e.normalized.length-1,r=e.normalized[f][t.columns],r.$row=r.$row.add(l),l.prev().hasClass(t.cssChildRow)||l.prev().addClass(q.css.cssHasChild),g=l.children("th, td"),f=r.child.length,r.child[f]=[],i=0;i<t.columns;i++)r.child[f][i]=q.getParsedText(t,g[i],i);else{for(p.$row=l,p.order=h,i=0;i<t.columns;++i)"undefined"!=typeof u[i]?(f=q.getElementText(t,l[0].cells[i],i),p.raw.push(f),g=q.getParsedText(t,l[0].cells[i],i,f),m.push(g),"numeric"===(u[i].type||"").toLowerCase()&&(s[i]=Math.max(Math.abs(g)||0,s[i]||0))):t.debug&&console.warn("No parser found for cell:",l[0].cells[i],"does it have a header?");m[t.columns]=p,e.normalized.push(m)}e.colMax=s,t.totalRows+=e.normalized.length}t.showProcessing&&q.isProcessing(b),t.debug&&console.log("Building cache for "+o+" rows"+q.benchmark(n)),a.isFunction(c)&&c(b)}function e(a){return/^d/i.test(a)||1===a}function f(b){var c,d,f,g,i,j,k,l;for(b.headerList=[],b.headerContent=[],b.debug&&(k=new Date),b.columns=q.computeColumnIndex(b.$table.children("thead, tfoot").children("tr")),g=b.cssIcon?'<i class="'+(b.cssIcon===q.css.icon?q.css.icon:b.cssIcon+" "+q.css.icon)+'"></i>':"",b.$headers=a(a.map(b.$table.find(b.selectorHeaders),function(h,k){return d=a(h),d.parent().hasClass(b.cssIgnoreRow)?void 0:(c=q.getColumnData(b.table,b.headers,k,!0),b.headerContent[k]=d.html(),""===b.headerTemplate||d.find("."+q.css.headerIn).length||(i=b.headerTemplate.replace(q.regex.templateContent,d.html()).replace(q.regex.templateIcon,d.find("."+q.css.icon).length?"":g),b.onRenderTemplate&&(f=b.onRenderTemplate.apply(d,[k,i]),f&&"string"==typeof f&&(i=f)),d.html('<div class="'+q.css.headerIn+'">'+i+"</div>")),b.onRenderHeader&&b.onRenderHeader.apply(d,[k,b,b.$table]),h.column=parseInt(d.attr("data-column"),10),h.order=e(q.getData(d,c,"sortInitialOrder")||b.sortInitialOrder)?[1,0,2]:[0,1,2],h.count=-1,h.lockedOrder=!1,j=q.getData(d,c,"lockedOrder")||!1,"undefined"!=typeof j&&j!==!1&&(h.order=h.lockedOrder=e(j)?[1,1,1]:[0,0,0]),d.addClass(q.css.header+" "+b.cssHeader),b.headerList[k]=h,d.parent().addClass(q.css.headerRow+" "+b.cssHeaderRow).attr("role","row"),b.tabIndex&&d.attr("tabindex",0),h)})),b.$headerIndexed=[],l=0;l<b.columns;l++)d=b.$headers.filter('[data-column="'+l+'"]'),b.$headerIndexed[l]=d.not(".sorter-false").length?d.not(".sorter-false").filter(":last"):d.filter(":last");b.$table.find(b.selectorHeaders).attr({scope:"col",role:"columnheader"}),h(b.table),b.debug&&(console.log("Built headers:"+q.benchmark(k)),console.log(b.$headers))}function g(a,b,e){var f=a.config;f.$table.find(f.selectorRemove).remove(),c(f),d(a),o(f,b,e)}function h(a){var b,c,d,e,f=a.config,g=f.$headers.length;for(b=0;g>b;b++)d=f.$headers.eq(b),e=q.getColumnData(a,f.headers,b,!0),c="false"===q.getData(d,e,"sorter")||"false"===q.getData(d,e,"parser"),d[0].sortDisabled=c,d[c?"addClass":"removeClass"]("sorter-false").attr("aria-disabled",""+c),f.tabIndex&&(c?d.removeAttr("tabindex"):d.attr("tabindex","0")),a.id&&(c?d.removeAttr("aria-controls"):d.attr("aria-controls",a.id))}function i(b){var c,d,e,f,g,h,i,j,k=b.config,l=k.sortList,m=l.length,n=q.css.sortNone+" "+k.cssNone,o=[q.css.sortAsc+" "+k.cssAsc,q.css.sortDesc+" "+k.cssDesc],p=[k.cssIconAsc,k.cssIconDesc,k.cssIconNone],r=["ascending","descending"],s=a(b).find("tfoot tr").children().add(a(k.namespace+"_extra_headers")).removeClass(o.join(" "));for(k.$headers.removeClass(o.join(" ")).addClass(n).attr("aria-sort","none").find("."+q.css.icon).removeClass(p.join(" ")).addClass(p[2]),e=0;m>e;e++)if(2!==l[e][1]&&(c=k.lastClickedIndex>0?k.$headers.filter(":gt("+(k.lastClickedIndex-1)+")"):k.$headers,c=c.not(".sorter-false").filter('[data-column="'+l[e][0]+'"]'+(1===m?":last":"")),c.length)){for(f=0;f<c.length;f++)c[f].sortDisabled||c.eq(f).removeClass(n).addClass(o[l[e][1]]).attr("aria-sort",r[l[e][1]]).find("."+q.css.icon).removeClass(p[2]).addClass(p[l[e][1]]);s.length&&s.filter('[data-column="'+l[e][0]+'"]').removeClass(n).addClass(o[l[e][1]])}for(m=k.$headers.length,g=k.$headers.not(".sorter-false"),e=0;m>e;e++)h=g.eq(e),h.length&&(d=g[e],i=d.order[(d.count+1)%(k.sortReset?3:2)],j=a.trim(h.text())+": "+q.language[h.hasClass(q.css.sortAsc)?"sortAsc":h.hasClass(q.css.sortDesc)?"sortDesc":"sortNone"]+q.language[0===i?"nextAsc":1===i?"nextDesc":"nextNone"],h.attr("aria-label",j))}function j(b,c){var d,e,f,g,h,i,j,k,l=b.config,m=c||l.sortList,n=m.length;for(l.sortList=[],h=0;n>h;h++)if(k=m[h],d=parseInt(k[0],10),d<l.columns&&l.$headerIndexed[d]){switch(g=l.$headerIndexed[d][0],e=(""+k[1]).match(/^(1|d|s|o|n)/),e=e?e[0]:""){case"1":case"d":e=1;break;case"s":e=i||0;break;case"o":j=g.order[(i||0)%(l.sortReset?3:2)],e=0===j?1:1===j?0:2;break;case"n":g.count=g.count+1,e=g.order[g.count%(l.sortReset?3:2)];break;default:e=0}i=0===h?e:i,f=[d,parseInt(e,10)||0],l.sortList.push(f),e=a.inArray(f[1],g.order),g.count=e>=0?e:f[1]%(l.sortReset?3:2)}}function k(a,b){return a&&a[b]?a[b].type||"":""}function l(b,c,d){if(b.isUpdating)return setTimeout(function(){l(b,c,d)},50);var e,f,g,h,j,k,n,o=b.config,p=!d[o.sortMultiSortKey],r=o.$table,s=o.$headers.length;if(r.trigger("sortStart",b),c.count=d[o.sortResetKey]?2:(c.count+1)%(o.sortReset?3:2),o.sortRestart)for(f=c,g=0;s>g;g++)n=o.$headers.eq(g),n[0]===f||!p&&n.is("."+q.css.sortDesc+",."+q.css.sortAsc)||(n[0].count=-1);if(f=parseInt(a(c).attr("data-column"),10),p){if(o.sortList=[],null!==o.sortForce)for(e=o.sortForce,h=0;h<e.length;h++)e[h][0]!==f&&o.sortList.push(e[h]);if(j=c.order[c.count],2>j&&(o.sortList.push([f,j]),c.colSpan>1))for(h=1;h<c.colSpan;h++)o.sortList.push([f+h,j])}else{if(o.sortAppend&&o.sortList.length>1)for(h=0;h<o.sortAppend.length;h++)k=q.isValueInArray(o.sortAppend[h][0],o.sortList),k>=0&&o.sortList.splice(k,1);if(q.isValueInArray(f,o.sortList)>=0)for(h=0;h<o.sortList.length;h++)k=o.sortList[h],j=o.$headerIndexed[k[0]][0],k[0]===f&&(k[1]=j.order[c.count],2===k[1]&&(o.sortList.splice(h,1),j.count=-1));else if(j=c.order[c.count],2>j&&(o.sortList.push([f,j]),c.colSpan>1))for(h=1;h<c.colSpan;h++)o.sortList.push([f+h,j])}if(null!==o.sortAppend)for(e=o.sortAppend,h=0;h<e.length;h++)e[h][0]!==f&&o.sortList.push(e[h]);r.trigger("sortBegin",b),setTimeout(function(){i(b),m(b),q.appendCache(o),r.trigger("sortEnd",b)},1)}function m(a){var b,c,d,e,f,g,h,i,j,l,m,n=0,o=a.config,p=o.textSorter||"",r=o.sortList,s=r.length,t=o.$tbodies.length;if(!o.serverSideSorting&&!q.isEmptyObject(o.cache)){for(o.debug&&(f=new Date),c=0;t>c;c++)g=o.cache[c].colMax,h=o.cache[c].normalized,h.sort(function(c,f){for(b=0;s>b;b++){if(e=r[b][0],i=r[b][1],n=0===i,o.sortStable&&c[e]===f[e]&&1===s)return c[o.columns].order-f[o.columns].order;if(d=/n/i.test(k(o.parsers,e)),d&&o.strings[e]?(d="boolean"==typeof o.string[o.strings[e]]?(n?1:-1)*(o.string[o.strings[e]]?-1:1):o.strings[e]?o.string[o.strings[e]]||0:0,j=o.numberSorter?o.numberSorter(c[e],f[e],n,g[e],a):q["sortNumeric"+(n?"Asc":"Desc")](c[e],f[e],d,g[e],e,a)):(l=n?c:f,m=n?f:c,j="function"==typeof p?p(l[e],m[e],n,e,a):"object"==typeof p&&p.hasOwnProperty(e)?p[e](l[e],m[e],n,e,a):q["sortNatural"+(n?"Asc":"Desc")](c[e],f[e],e,a,o)),j)return j}return c[o.columns].order-f[o.columns].order});o.debug&&console.log("Sorting on "+r.toString()+" and dir "+i+" time"+q.benchmark(f))}}function n(b,c){b.table.isUpdating&&b.$table.trigger("updateComplete",b.table),a.isFunction(c)&&c(b.table)}function o(b,c,d){var e=a.isArray(c)?c:b.sortList,f="undefined"==typeof c?b.resort:c;f===!1||b.serverSideSorting||b.table.isProcessing?(n(b,d),q.applyWidget(b.table,!1)):e.length?b.$table.trigger("sorton",[e,function(){n(b,d)},!0]):b.$table.trigger("sortReset",[function(){n(b,d),q.applyWidget(b.table,!1)}])}function p(b){var c=b.config,d=c.$table,e="sortReset update updateRows updateAll updateHeaders addRows updateCell updateComplete sorton appendCache updateCache applyWidgetId applyWidgets refreshWidgets destroy mouseup mouseleave ".split(" ").join(c.namespace+" ");d.unbind(e.replace(q.regex.spaces," ")).bind("sortReset"+c.namespace,function(a,b){a.stopPropagation(),q.sortReset(this.config,b)}).bind("updateAll"+c.namespace,function(a,b,c){a.stopPropagation(),q.updateAll(this.config,b,c)}).bind("update"+c.namespace+" updateRows"+c.namespace,function(a,b,c){a.stopPropagation(),q.update(this.config,b,c)}).bind("updateHeaders"+c.namespace,function(a,b){a.stopPropagation(),q.updateHeaders(this.config,b)}).bind("updateCell"+c.namespace,function(a,b,c,d){a.stopPropagation(),q.updateCell(this.config,b,c,d)}).bind("addRows"+c.namespace,function(a,b,c,d){a.stopPropagation(),q.addRows(this.config,b,c,d)}).bind("updateComplete"+c.namespace,function(){b.isUpdating=!1}).bind("sorton"+c.namespace,function(a,b,c,d){a.stopPropagation(),q.sortOn(this.config,b,c,d)}).bind("appendCache"+c.namespace,function(c,d,e){c.stopPropagation(),q.appendCache(this.config,e),a.isFunction(d)&&d(b)}).bind("updateCache"+c.namespace,function(a,b,c){a.stopPropagation(),q.updateCache(this.config,b,c)}).bind("applyWidgetId"+c.namespace,function(a,c){a.stopPropagation(),q.getWidgetById(c).format(b,this.config,this.config.widgetOptions)}).bind("applyWidgets"+c.namespace,function(a,c){a.stopPropagation(),q.applyWidget(b,c)}).bind("refreshWidgets"+c.namespace,function(a,c,d){a.stopPropagation(),q.refreshWidgets(b,c,d)}).bind("destroy"+c.namespace,function(a,c,d){a.stopPropagation(),q.destroy(b,c,d)}).bind("resetToLoadState"+c.namespace,function(d){d.stopPropagation(),q.removeWidget(b,!0,!1),c=a.extend(!0,q.defaults,c.originalSettings),b.hasInitialized=!1,q.setup(b,c)})}var q=this;q.version="2.23.5",q.parsers=[],q.widgets=[],q.defaults={theme:"default",widthFixed:!1,showProcessing:!1,headerTemplate:"{content}",onRenderTemplate:null,onRenderHeader:null,cancelSelection:!0,tabIndex:!0,dateFormat:"mmddyyyy",sortMultiSortKey:"shiftKey",sortResetKey:"ctrlKey",usNumberFormat:!0,delayInit:!1,serverSideSorting:!1,resort:!0,headers:{},ignoreCase:!0,sortForce:null,sortList:[],sortAppend:null,sortStable:!1,sortInitialOrder:"asc",sortLocaleCompare:!1,sortReset:!1,sortRestart:!1,emptyTo:"bottom",stringTo:"max",textExtraction:"basic",textAttribute:"data-text",textSorter:null,numberSorter:null,widgets:[],widgetOptions:{zebra:["even","odd"]},initWidgets:!0,widgetClass:"widget-{name}",initialized:null,tableClass:"",cssAsc:"",cssDesc:"",cssNone:"",cssHeader:"",cssHeaderRow:"",cssProcessing:"",cssChildRow:"tablesorter-childRow",cssIcon:"tablesorter-icon",cssIconNone:"",cssIconAsc:"",cssIconDesc:"",cssInfoBlock:"tablesorter-infoOnly",cssNoSort:"tablesorter-noSort",cssIgnoreRow:"tablesorter-ignoreRow",pointerClick:"click",pointerDown:"mousedown",pointerUp:"mouseup",selectorHeaders:"> thead th, > thead td",selectorSort:"th, td",selectorRemove:".remove-me",debug:!1,headerList:[],empties:{},strings:{},parsers:[]},q.css={table:"tablesorter",cssHasChild:"tablesorter-hasChildRow",childRow:"tablesorter-childRow",colgroup:"tablesorter-colgroup",header:"tablesorter-header",headerRow:"tablesorter-headerRow",headerIn:"tablesorter-header-inner",icon:"tablesorter-icon",processing:"tablesorter-processing",sortAsc:"tablesorter-headerAsc",sortDesc:"tablesorter-headerDesc",sortNone:"tablesorter-headerUnSorted"},q.language={sortAsc:"Ascending sort applied, ",sortDesc:"Descending sort applied, ",sortNone:"No sort applied, ",nextAsc:"activate to apply an ascending sort",nextDesc:"activate to apply a descending sort",nextNone:"activate to remove the sort"},q.regex={templateContent:/\{content\}/g,templateIcon:/\{icon\}/g,templateName:/\{name\}/i,spaces:/\s+/g,nonWord:/\W/g,formElements:/(input|select|button|textarea)/i},q.instanceMethods={},q.isEmptyObject=function(a){for(var b in a)return!1;return!0},q.getElementText=function(b,c,d){if(!c)return"";var e,f=b.textExtraction||"",g=c.jquery?c:a(c);return"string"==typeof f?"basic"===f&&"undefined"!=typeof(e=g.attr(b.textAttribute))?a.trim(e):a.trim(c.textContent||g.text()):"function"==typeof f?a.trim(f(g[0],b.table,d)):"function"==typeof(e=q.getColumnData(b.table,f,d))?a.trim(e(g[0],b.table,d)):a.trim(g[0].textContent||g.text())},q.getParsedText=function(a,b,c,d){"undefined"==typeof d&&(d=q.getElementText(a,b,c));var e=""+d,f=a.parsers[c],g=a.extractors[c];return f&&(g&&"function"==typeof g.format&&(d=g.format(d,a.table,b,c)),e="no-parser"===f.id?"":f.format(""+d,a.table,b,c),a.ignoreCase&&"string"==typeof e&&(e=e.toLowerCase())),e},q.construct=function(b){return this.each(function(){var c=this,d=a.extend(!0,{},q.defaults,b,q.instanceMethods);d.originalSettings=b,!c.hasInitialized&&q.buildTable&&"TABLE"!==this.nodeName?q.buildTable(c,d):q.setup(c,d)})},q.setup=function(b,e){if(!b||!b.tHead||0===b.tBodies.length||b.hasInitialized===!0)return void(e.debug&&(b.hasInitialized?console.warn("Stopping initialization. Tablesorter has already been initialized"):console.error("Stopping initialization! No table, thead or tbody")));var g="",h=a(b),j=a.metadata;b.hasInitialized=!1,b.isProcessing=!0,b.config=e,a.data(b,"tablesorter",e),e.debug&&(console[console.group?"group":"log"]("Initializing tablesorter"),a.data(b,"startoveralltimer",new Date)),e.supportsDataObject=function(a){return a[0]=parseInt(a[0],10),a[0]>1||1===a[0]&&parseInt(a[1],10)>=4}(a.fn.jquery.split(".")),e.string={max:1,min:-1,emptymin:1,emptymax:-1,zero:0,none:0,"null":0,top:!0,bottom:!1},e.emptyTo=e.emptyTo.toLowerCase(),e.stringTo=e.stringTo.toLowerCase(),/tablesorter\-/.test(h.attr("class"))||(g=""!==e.theme?" tablesorter-"+e.theme:""),e.table=b,e.$table=h.addClass(q.css.table+" "+e.tableClass+g).attr("role","grid"),e.$headers=h.find(e.selectorHeaders),e.namespace?e.namespace="."+e.namespace.replace(q.regex.nonWord,""):e.namespace=".tablesorter"+Math.random().toString(16).slice(2),e.$table.children().children("tr").attr("role","row"),e.$tbodies=h.children("tbody:not(."+e.cssInfoBlock+")").attr({"aria-live":"polite","aria-relevant":"all"}),e.$table.children("caption").length&&(g=e.$table.children("caption")[0],g.id||(g.id=e.namespace.slice(1)+"caption"),e.$table.attr("aria-labelledby",g.id)),e.widgetInit={},e.textExtraction=e.$table.attr("data-text-extraction")||e.textExtraction||"basic",f(e),q.fixColumnWidth(b),q.applyWidgetOptions(b,e),c(e),e.totalRows=0,e.delayInit||d(b),q.bindEvents(b,e.$headers,!0),p(b),e.supportsDataObject&&"undefined"!=typeof h.data().sortlist?e.sortList=h.data().sortlist:j&&h.metadata()&&h.metadata().sortlist&&(e.sortList=h.metadata().sortlist),q.applyWidget(b,!0),e.sortList.length>0?h.trigger("sorton",[e.sortList,{},!e.initWidgets,!0]):(i(b),e.initWidgets&&q.applyWidget(b,!1)),e.showProcessing&&h.unbind("sortBegin"+e.namespace+" sortEnd"+e.namespace).bind("sortBegin"+e.namespace+" sortEnd"+e.namespace,function(a){clearTimeout(e.processTimer),q.isProcessing(b),"sortBegin"===a.type&&(e.processTimer=setTimeout(function(){q.isProcessing(b,!0)},500))}),b.hasInitialized=!0,b.isProcessing=!1,e.debug&&(console.log("Overall initialization time: "+q.benchmark(a.data(b,"startoveralltimer"))),e.debug&&console.groupEnd&&console.groupEnd()),h.trigger("tablesorter-initialized",b),"function"==typeof e.initialized&&e.initialized(b)},q.fixColumnWidth=function(b){b=a(b)[0];var c,d,e,f,g,h=b.config,i=h.$table.children("colgroup");if(i.length&&i.hasClass(q.css.colgroup)&&i.remove(),h.widthFixed&&0===h.$table.children("colgroup").length){for(i=a('<colgroup class="'+q.css.colgroup+'">'),c=h.$table.width(),e=h.$tbodies.find("tr:first").children(":visible"),f=e.length,g=0;f>g;g++)d=parseInt(e.eq(g).width()/c*1e3,10)/10+"%",i.append(a("<col>").css("width",d));h.$table.prepend(i)}},q.getColumnData=function(b,c,d,e,f){if("undefined"!=typeof c&&null!==c){b=a(b)[0];var g,h,i=b.config,j=f||i.$headers,k=i.$headerIndexed&&i.$headerIndexed[d]||j.filter('[data-column="'+d+'"]:last');if(c[d])return e?c[d]:c[j.index(k)];for(h in c)if("string"==typeof h&&(g=k.filter(h).add(k.find(h)),g.length))return c[h]}},q.computeColumnIndex=function(b){var c,d,e,f,g,h,i,j,k,l,m,n,o=[],p=[],q={};for(c=0;c<b.length;c++)for(i=b[c].cells,d=0;d<i.length;d++){for(h=i[d],g=a(h),j=h.parentNode.rowIndex,k=j+"-"+g.index(),l=h.rowSpan||1,m=h.colSpan||1,"undefined"==typeof o[j]&&(o[j]=[]),e=0;e<o[j].length+1;e++)if("undefined"==typeof o[j][e]){n=e;break}for(q[k]=n,g.attr({"data-column":n}),e=j;j+l>e;e++)for("undefined"==typeof o[e]&&(o[e]=[]),p=o[e],f=n;n+m>f;f++)p[f]="x"}return p.length},q.isProcessing=function(b,c,d){b=a(b);var e=b[0].config,f=d||b.find("."+q.css.header);c?("undefined"!=typeof d&&e.sortList.length>0&&(f=f.filter(function(){return this.sortDisabled?!1:q.isValueInArray(parseFloat(a(this).attr("data-column")),e.sortList)>=0})),b.add(f).addClass(q.css.processing+" "+e.cssProcessing)):b.add(f).removeClass(q.css.processing+" "+e.cssProcessing)},q.processTbody=function(b,c,d){b=a(b)[0];var e;return d?(b.isProcessing=!0,c.before('<colgroup class="tablesorter-savemyplace"/>'),e=a.fn.detach?c.detach():c.remove()):(e=a(b).find("colgroup.tablesorter-savemyplace"),c.insertAfter(e),e.remove(),void(b.isProcessing=!1))},q.clearTableBody=function(b){a(b)[0].config.$tbodies.children().detach()},q.bindEvents=function(b,c,e){b=a(b)[0];var f,g=null,h=b.config;e!==!0&&(c.addClass(h.namespace.slice(1)+"_extra_headers"),f=a.fn.closest?c.closest("table")[0]:c.parents("table")[0],f&&"TABLE"===f.nodeName&&f!==b&&a(f).addClass(h.namespace.slice(1)+"_extra_table")),f=(h.pointerDown+" "+h.pointerUp+" "+h.pointerClick+" sort keyup ").replace(q.regex.spaces," ").split(" ").join(h.namespace+" "),c.find(h.selectorSort).add(c.filter(h.selectorSort)).unbind(f).bind(f,function(e,f){var i,j,k,m=a(e.target),n=" "+e.type+" ";if(!(1!==(e.which||e.button)&&!n.match(" "+h.pointerClick+" | sort | keyup ")||" keyup "===n&&13!==e.which||n.match(" "+h.pointerClick+" ")&&"undefined"!=typeof e.which||n.match(" "+h.pointerUp+" ")&&g!==e.target&&f!==!0)){if(n.match(" "+h.pointerDown+" "))return g=e.target,k=m.jquery.split("."),void("1"===k[0]&&k[1]<4&&e.preventDefault());if(g=null,q.regex.formElements.test(e.target.nodeName)||m.hasClass(h.cssNoSort)||m.parents("."+h.cssNoSort).length>0||m.parents("button").length>0)return!h.cancelSelection;h.delayInit&&q.isEmptyObject(h.cache)&&d(b),i=a.fn.closest?a(this).closest("th, td"):/TH|TD/.test(this.nodeName)?a(this):a(this).parents("th, td"),k=c.index(i),h.lastClickedIndex=0>k?i.attr("data-column"):k,j=h.$headers[h.lastClickedIndex],j&&!j.sortDisabled&&l(b,j,e)}}),h.cancelSelection&&c.attr("unselectable","on").bind("selectstart",!1).css({"user-select":"none",MozUserSelect:"none"})},q.sortReset=function(b,c){var d=b.table;b.sortList=[],i(d),m(d),q.appendCache(b),a.isFunction(c)&&c(d)},q.updateAll=function(a,b,c){var d=a.table;d.isUpdating=!0,q.refreshWidgets(d,!0,!0),f(a),q.bindEvents(d,a.$headers,!0),p(d),g(d,b,c)},q.update=function(a,b,c){var d=a.table;d.isUpdating=!0,h(d),g(d,b,c)},q.updateHeaders=function(a,b){a.table.isUpdating=!0,f(a),q.bindEvents(a.table,a.$headers,!0),n(a,b)},q.updateCell=function(b,c,d,e){b.table.isUpdating=!0,b.$table.find(b.selectorRemove).remove();var f,g,h,i,j=(b.table,b.$tbodies),k=a(c),l=j.index(a.fn.closest?k.closest("tbody"):k.parents("tbody").filter(":first")),m=b.cache[l],p=a.fn.closest?k.closest("tr"):k.parents("tr").filter(":first");c=k[0],j.length&&l>=0&&(g=j.eq(l).find("tr").index(p),i=m.normalized[g],h=k.index(),f=q.getParsedText(b,c,h),i[h]=f,i[b.columns].$row=p,"numeric"===(b.parsers[h].type||"").toLowerCase()&&(m.colMax[h]=Math.max(Math.abs(f)||0,m.colMax[h]||0)),f="undefined"!==d?d:b.resort,f!==!1?o(b,f,e):n(b,e))},q.addRows=function(b,d,e,f){var i,j,k,l,m,n,p,r="string"==typeof d&&1===b.$tbodies.length&&/<tr/.test(d||""),s=b.table;if(r)d=a(d),b.$tbodies.append(d);else if(!(d&&d instanceof jQuery&&(a.fn.closest?d.closest("table")[0]:d.parents("table")[0])===b.table))return b.debug&&console.error("addRows method requires (1) a jQuery selector reference to rows that have already been added to the table, or (2) row HTML string to be added to a table with only one tbody"),!1;if(s.isUpdating=!0,q.isEmptyObject(b.cache))h(s),g(s,e,f);else{for(n=d.filter("tr").attr("role","row").length,p=b.$tbodies.index(d.parents("tbody").filter(":first")),b.parsers&&b.parsers.length||c(b),i=0;n>i;i++){for(k=d[i].cells.length,m=[],l={child:[],$row:d.eq(i),order:b.cache[p].normalized.length},j=0;k>j;j++)m[j]=q.getParsedText(b,d[i].cells[j],j),"numeric"===(b.parsers[j].type||"").toLowerCase()&&(b.cache[p].colMax[j]=Math.max(Math.abs(m[j])||0,b.cache[p].colMax[j]||0));m.push(l),b.cache[p].normalized.push(m)}o(b,e,f)}},q.updateCache=function(a,b,e){a.parsers&&a.parsers.length||c(a,e),d(a.table,b,e)},q.appendCache=function(a,b){var c,d,e,f,g,h,i,j=a.table,k=a.widgetOptions,l=a.$tbodies,m=[],n=a.cache;if(q.isEmptyObject(n))return a.appender?a.appender(j,m):j.isUpdating?a.$table.trigger("updateComplete",j):"";for(a.debug&&(i=new Date),h=0;h<l.length;h++)if(e=l.eq(h),e.length){for(f=q.processTbody(j,e,!0),c=n[h].normalized,d=c.length,g=0;d>g;g++)m.push(c[g][a.columns].$row),a.appender&&(!a.pager||a.pager.removeRows&&k.pager_removeRows||a.pager.ajax)||f.append(c[g][a.columns].$row);q.processTbody(j,f,!1)}a.appender&&a.appender(j,m),a.debug&&console.log("Rebuilt table"+q.benchmark(i)),b||a.appender||q.applyWidget(j),j.isUpdating&&a.$table.trigger("updateComplete",j)},q.sortOn=function(b,c,e,f){var g=b.table;b.$table.trigger("sortStart",g),j(g,c),i(g),b.delayInit&&q.isEmptyObject(b.cache)&&d(g),b.$table.trigger("sortBegin",g),m(g),q.appendCache(b,f),b.$table.trigger("sortEnd",g),q.applyWidget(g),a.isFunction(e)&&e(g)},q.restoreHeaders=function(b){var c,d,e=a(b)[0].config,f=e.$table.find(e.selectorHeaders),g=f.length;for(c=0;g>c;c++)d=f.eq(c),d.find("."+q.css.headerIn).length&&d.html(e.headerContent[c])},q.destroy=function(b,c,d){if(b=a(b)[0],b.hasInitialized){q.removeWidget(b,!0,!1);var e,f=a(b),g=b.config,h=g.debug,i=f.find("thead:first"),j=i.find("tr."+q.css.headerRow).removeClass(q.css.headerRow+" "+g.cssHeaderRow),k=f.find("tfoot:first > tr").children("th, td");c===!1&&a.inArray("uitheme",g.widgets)>=0&&(f.trigger("applyWidgetId",["uitheme"]),f.trigger("applyWidgetId",["zebra"])),i.find("tr").not(j).remove(),e="sortReset update updateRows updateAll updateHeaders updateCell addRows updateComplete sorton appendCache updateCache applyWidgetId applyWidgets refreshWidgets destroy mouseup mouseleave keypress "+"sortBegin sortEnd resetToLoadState ".split(" ").join(g.namespace+" "),f.removeData("tablesorter").unbind(e.replace(q.regex.spaces," ")),g.$headers.add(k).removeClass([q.css.header,g.cssHeader,g.cssAsc,g.cssDesc,q.css.sortAsc,q.css.sortDesc,q.css.sortNone].join(" ")).removeAttr("data-column").removeAttr("aria-label").attr("aria-disabled","true"),j.find(g.selectorSort).unbind("mousedown mouseup keypress ".split(" ").join(g.namespace+" ").replace(q.regex.spaces," ")),q.restoreHeaders(b),f.toggleClass(q.css.table+" "+g.tableClass+" tablesorter-"+g.theme,c===!1),b.hasInitialized=!1,delete b.config.cache,"function"==typeof d&&d(b),h&&console.log("tablesorter has been removed")}},q.regex.chunk=/(^([+\-]?(?:\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?)?$|^0x[0-9a-f]+$|\d+)/gi,q.regex.chunks=/(^\\0|\\0$)/,q.regex.hex=/^0x[0-9a-f]+$/i,q.sortNatural=function(a,b){if(a===b)return 0;var c,d,e,f,g,h,i,j,k=q.regex;if(k.hex.test(b)){if(d=parseInt(a.match(k.hex),16),f=parseInt(b.match(k.hex),16),f>d)return-1;if(d>f)return 1}for(c=a.replace(k.chunk,"\\0$1\\0").replace(k.chunks,"").split("\\0"),e=b.replace(k.chunk,"\\0$1\\0").replace(k.chunks,"").split("\\0"),j=Math.max(c.length,e.length),i=0;j>i;i++){if(g=isNaN(c[i])?c[i]||0:parseFloat(c[i])||0,h=isNaN(e[i])?e[i]||0:parseFloat(e[i])||0,isNaN(g)!==isNaN(h))return isNaN(g)?1:-1;if(typeof g!=typeof h&&(g+="",h+=""),h>g)return-1;if(g>h)return 1}return 0},q.sortNaturalAsc=function(a,b,c,d,e){if(a===b)return 0;var f=e.string[e.empties[c]||e.emptyTo];return""===a&&0!==f?"boolean"==typeof f?f?-1:1:-f||-1:""===b&&0!==f?"boolean"==typeof f?f?1:-1:f||1:q.sortNatural(a,b)},q.sortNaturalDesc=function(a,b,c,d,e){if(a===b)return 0;var f=e.string[e.empties[c]||e.emptyTo];return""===a&&0!==f?"boolean"==typeof f?f?-1:1:f||1:""===b&&0!==f?"boolean"==typeof f?f?1:-1:-f||-1:q.sortNatural(b,a)},q.sortText=function(a,b){return a>b?1:b>a?-1:0},q.getTextValue=function(a,b,c){if(c){var d,e=a?a.length:0,f=c+b;for(d=0;e>d;d++)f+=a.charCodeAt(d);return b*f}return 0},q.sortNumericAsc=function(a,b,c,d,e,f){if(a===b)return 0;var g=f.config,h=g.string[g.empties[e]||g.emptyTo];return""===a&&0!==h?"boolean"==typeof h?h?-1:1:-h||-1:""===b&&0!==h?"boolean"==typeof h?h?1:-1:h||1:(isNaN(a)&&(a=q.getTextValue(a,c,d)),isNaN(b)&&(b=q.getTextValue(b,c,d)),a-b)},q.sortNumericDesc=function(a,b,c,d,e,f){if(a===b)return 0;var g=f.config,h=g.string[g.empties[e]||g.emptyTo];return""===a&&0!==h?"boolean"==typeof h?h?-1:1:h||1:""===b&&0!==h?"boolean"==typeof h?h?1:-1:-h||-1:(isNaN(a)&&(a=q.getTextValue(a,c,d)),isNaN(b)&&(b=q.getTextValue(b,c,d)),b-a)},q.sortNumeric=function(a,b){return a-b},q.characterEquivalents={a:"áàâãäąå",A:"ÁÀÂÃÄĄÅ",c:"çćč",C:"ÇĆČ",e:"éèêëěę",E:"ÉÈÊËĚĘ",i:"íìİîïı",I:"ÍÌİÎÏ",o:"óòôõöō",O:"ÓÒÔÕÖŌ",ss:"ß",SS:"ẞ",u:"úùûüů",U:"ÚÙÛÜŮ"},q.replaceAccents=function(a){var b,c="[",d=q.characterEquivalents;if(!q.characterRegex){q.characterRegexArray={};for(b in d)"string"==typeof b&&(c+=d[b],q.characterRegexArray[b]=new RegExp("["+d[b]+"]","g"));q.characterRegex=new RegExp(c+"]")}if(q.characterRegex.test(a))for(b in d)"string"==typeof b&&(a=a.replace(q.characterRegexArray[b],b));return a},q.isValueInArray=function(a,b){var c,d=b&&b.length||0;for(c=0;d>c;c++)if(b[c][0]===a)return c;return-1},q.addParser=function(a){var b,c=q.parsers.length,d=!0;for(b=0;c>b;b++)q.parsers[b].id.toLowerCase()===a.id.toLowerCase()&&(d=!1);d&&q.parsers.push(a)},q.addInstanceMethods=function(b){a.extend(q.instanceMethods,b)},q.getParserById=function(a){if("false"==a)return!1;var b,c=q.parsers.length;for(b=0;c>b;b++)if(q.parsers[b].id.toLowerCase()===a.toString().toLowerCase())return q.parsers[b];return!1},q.addWidget=function(a){q.widgets.push(a)},q.hasWidget=function(b,c){return b=a(b),b.length&&b[0].config&&b[0].config.widgetInit[c]||!1},q.getWidgetById=function(a){var b,c,d=q.widgets.length;for(b=0;d>b;b++)if(c=q.widgets[b],c&&c.hasOwnProperty("id")&&c.id.toLowerCase()===a.toLowerCase())return c},q.applyWidgetOptions=function(b,c){var d,e,f=c.widgets.length;if(f)for(d=0;f>d;d++)e=q.getWidgetById(c.widgets[d]),e&&"options"in e&&(c.widgetOptions=a.extend(!0,{},e.options,c.widgetOptions))},q.applyWidget=function(b,c,d){b=a(b)[0];var e,f,g,h,i,j,k,l,m,n,o=b.config,p=" "+o.table.className+" ",r=[];if(c===!1||!b.hasInitialized||!b.isApplyingWidgets&&!b.isUpdating){if(o.debug&&(k=new Date),n=new RegExp("\\s"+o.widgetClass.replace(q.regex.templateName,"([\\w-]+)")+"\\s","g"),p.match(n)&&(m=p.match(n)))for(f=m.length,e=0;f>e;e++)o.widgets.push(m[e].replace(n,"$1"));if(o.widgets.length){for(b.isApplyingWidgets=!0,o.widgets=a.grep(o.widgets,function(b,c){return a.inArray(b,o.widgets)===c}),g=o.widgets||[],f=g.length,e=0;f>e;e++)n=q.getWidgetById(g[e]),n&&n.id&&(n.priority||(n.priority=10),r[e]=n);for(r.sort(function(a,b){return a.priority<b.priority?-1:a.priority===b.priority?0:1}),f=r.length,o.debug&&console[console.group?"group":"log"]("Start "+(c?"initializing":"applying")+" widgets"),e=0;f>e;e++)h=r[e],h&&(i=h.id,j=!1,o.debug&&(l=new Date),(c||!o.widgetInit[i])&&(o.widgetInit[i]=!0,b.hasInitialized&&q.applyWidgetOptions(b,b.config),"init"in h&&(j=!0,o.debug&&console[console.group?"group":"log"]("Initializing "+i+" widget"),h.init(b,h,b.config,b.config.widgetOptions))),!c&&"format"in h&&(j=!0,o.debug&&console[console.group?"group":"log"]("Updating "+i+" widget"),h.format(b,b.config,b.config.widgetOptions,!1)),o.debug&&j&&(console.log("Completed "+(c?"initializing ":"applying ")+i+" widget"+q.benchmark(l)),console.groupEnd&&console.groupEnd()));o.debug&&console.groupEnd&&console.groupEnd(),c||"function"!=typeof d||d(b)}setTimeout(function(){b.isApplyingWidgets=!1,a.data(b,"lastWidgetApplication",new Date)},0),o.debug&&(m=o.widgets.length,console.log("Completed "+(c===!0?"initializing ":"applying ")+m+" widget"+(1!==m?"s":"")+q.benchmark(k)))}},q.removeWidget=function(b,c,d){b=a(b)[0];var e,f,g,h,i=b.config;if(c===!0)for(c=[],h=q.widgets.length,g=0;h>g;g++)f=q.widgets[g],f&&f.id&&c.push(f.id);else c=(a.isArray(c)?c.join(","):c||"").toLowerCase().split(/[\s,]+/);for(h=c.length,e=0;h>e;e++)f=q.getWidgetById(c[e]),g=a.inArray(c[e],i.widgets),f&&"remove"in f&&(i.debug&&g>=0&&console.log('Removing "'+c[e]+'" widget'),i.debug&&console.log((d?"Refreshing":"Removing")+' "'+c[e]+'" widget'),f.remove(b,i,i.widgetOptions,d),i.widgetInit[c[e]]=!1),g>=0&&d!==!0&&i.widgets.splice(g,1)},q.refreshWidgets=function(b,c,d){b=a(b)[0];var e,f=b.config,g=f.widgets,h=q.widgets,i=h.length,j=[],k=function(b){a(b).trigger("refreshComplete")};for(e=0;i>e;e++)h[e]&&h[e].id&&(c||a.inArray(h[e].id,g)<0)&&j.push(h[e].id);q.removeWidget(b,j.join(","),!0),d!==!0?(q.applyWidget(b,c||!1,k),c&&q.applyWidget(b,!1,k)):k(b)},q.getColumnText=function(b,c,d){b=a(b)[0];var e,f,g,h,i,j,k,l,m,n,o="function"==typeof d,p="all"===c,r={raw:[],parsed:[],$cell:[]},s=b.config;if(!q.isEmptyObject(s)){for(i=s.$tbodies.length,e=0;i>e;e++)for(g=s.cache[e].normalized,j=g.length,f=0;j>f;f++)n=!0,h=g[f],l=p?h.slice(0,s.columns):h[c],h=h[s.columns],k=p?h.raw:h.raw[c],m=p?h.$row.children():h.$row.children().eq(c),o&&(n=d({tbodyIndex:e,rowIndex:f,parsed:l,raw:k,$row:h.$row,$cell:m})),n!==!1&&(r.parsed.push(l),r.raw.push(k),r.$cell.push(m));return r}s.debug&&console.warn("No cache found - aborting getColumnText function!")},q.getData=function(b,c,d){var e,f,g="",h=a(b);return h.length?(e=a.metadata?h.metadata():!1,f=" "+(h.attr("class")||""),
"undefined"!=typeof h.data(d)||"undefined"!=typeof h.data(d.toLowerCase())?g+=h.data(d)||h.data(d.toLowerCase()):e&&"undefined"!=typeof e[d]?g+=e[d]:c&&"undefined"!=typeof c[d]?g+=c[d]:" "!==f&&f.match(" "+d+"-")&&(g=f.match(new RegExp("\\s"+d+"-([\\w-]+)"))[1]||""),a.trim(g)):""},q.regex.comma=/,/g,q.regex.digitNonUS=/[\s|\.]/g,q.regex.digitNegativeTest=/^\s*\([.\d]+\)/,q.regex.digitNegativeReplace=/^\s*\(([.\d]+)\)/,q.formatFloat=function(b,c){if("string"!=typeof b||""===b)return b;var d,e=c&&c.config?c.config.usNumberFormat!==!1:"undefined"!=typeof c?c:!0;return b=e?b.replace(q.regex.comma,""):b.replace(q.regex.digitNonUS,"").replace(q.regex.comma,"."),q.regex.digitNegativeTest.test(b)&&(b=b.replace(q.regex.digitNegativeReplace,"-$1")),d=parseFloat(b),isNaN(d)?a.trim(b):d},q.regex.digitTest=/^[\-+(]?\d+[)]?$/,q.regex.digitReplace=/[,.'"\s]/g,q.isDigit=function(a){return isNaN(a)?q.regex.digitTest.test(a.toString().replace(q.regex.digitReplace,"")):""!==a}}});var b=a.tablesorter;a.fn.extend({tablesorter:b.construct}),window.console&&window.console.log||(b.logs=[],console={},console.log=console.warn=console.error=console.table=function(){b.logs.push([Date.now(),arguments])}),b.log=function(){console.log(arguments)},b.benchmark=function(a){return" ("+((new Date).getTime()-a.getTime())+"ms)"},b.addParser({id:"no-parser",is:function(){return!1},format:function(){return""},type:"text"}),b.addParser({id:"text",is:function(){return!0},format:function(c,d){var e=d.config;return c&&(c=a.trim(e.ignoreCase?c.toLocaleLowerCase():c),c=e.sortLocaleCompare?b.replaceAccents(c):c),c},type:"text"}),b.regex.nondigit=/[^\w,. \-()]/g,b.addParser({id:"digit",is:function(a){return b.isDigit(a)},format:function(c,d){var e=b.formatFloat((c||"").replace(b.regex.nondigit,""),d);return c&&"number"==typeof e?e:c?a.trim(c&&d.config.ignoreCase?c.toLocaleLowerCase():c):c},type:"numeric"}),b.regex.currencyReplace=/[+\-,. ]/g,b.regex.currencyTest=/^\(?\d+[\u00a3$\u20ac\u00a4\u00a5\u00a2?.]|[\u00a3$\u20ac\u00a4\u00a5\u00a2?.]\d+\)?$/,b.addParser({id:"currency",is:function(a){return a=(a||"").replace(b.regex.currencyReplace,""),b.regex.currencyTest.test(a)},format:function(c,d){var e=b.formatFloat((c||"").replace(b.regex.nondigit,""),d);return c&&"number"==typeof e?e:c?a.trim(c&&d.config.ignoreCase?c.toLocaleLowerCase():c):c},type:"numeric"}),b.regex.urlProtocolTest=/^(https?|ftp|file):\/\//,b.regex.urlProtocolReplace=/(https?|ftp|file):\/\//,b.addParser({id:"url",is:function(a){return b.regex.urlProtocolTest.test(a)},format:function(c){return c?a.trim(c.replace(b.regex.urlProtocolReplace,"")):c},parsed:!0,type:"text"}),b.regex.dash=/-/g,b.regex.isoDate=/^\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2}/,b.addParser({id:"isoDate",is:function(a){return b.regex.isoDate.test(a)},format:function(a,c){var d=a?new Date(a.replace(b.regex.dash,"/")):a;return d instanceof Date&&isFinite(d)?d.getTime():a},type:"numeric"}),b.regex.percent=/%/g,b.regex.percentTest=/(\d\s*?%|%\s*?\d)/,b.addParser({id:"percent",is:function(a){return b.regex.percentTest.test(a)&&a.length<15},format:function(a,c){return a?b.formatFloat(a.replace(b.regex.percent,""),c):a},type:"numeric"}),b.addParser({id:"image",is:function(a,b,c,d){return d.find("img").length>0},format:function(b,c,d){return a(d).find("img").attr(c.config.imgAttr||"alt")||b},parsed:!0,type:"text"}),b.regex.dateReplace=/(\S)([AP]M)$/i,b.regex.usLongDateTest1=/^[A-Z]{3,10}\.?\s+\d{1,2},?\s+(\d{4})(\s+\d{1,2}:\d{2}(:\d{2})?(\s+[AP]M)?)?$/i,b.regex.usLongDateTest2=/^\d{1,2}\s+[A-Z]{3,10}\s+\d{4}/i,b.addParser({id:"usLongDate",is:function(a){return b.regex.usLongDateTest1.test(a)||b.regex.usLongDateTest2.test(a)},format:function(a,c){var d=a?new Date(a.replace(b.regex.dateReplace,"$1 $2")):a;return d instanceof Date&&isFinite(d)?d.getTime():a},type:"numeric"}),b.regex.shortDateTest=/(^\d{1,2}[\/\s]\d{1,2}[\/\s]\d{4})|(^\d{4}[\/\s]\d{1,2}[\/\s]\d{1,2})/,b.regex.shortDateReplace=/[\-.,]/g,b.regex.shortDateXXY=/(\d{1,2})[\/\s](\d{1,2})[\/\s](\d{4})/,b.regex.shortDateYMD=/(\d{4})[\/\s](\d{1,2})[\/\s](\d{1,2})/,b.addParser({id:"shortDate",is:function(a){return a=(a||"").replace(b.regex.spaces," ").replace(b.regex.shortDateReplace,"/"),b.regex.shortDateTest.test(a)},format:function(a,c,d,e){if(a){var f,g,h=c.config,i=h.$headerIndexed[e],j=i.length&&i[0].dateFormat||b.getData(i,b.getColumnData(c,h.headers,e),"dateFormat")||h.dateFormat;return g=a.replace(b.regex.spaces," ").replace(b.regex.shortDateReplace,"/"),"mmddyyyy"===j?g=g.replace(b.regex.shortDateXXY,"$3/$1/$2"):"ddmmyyyy"===j?g=g.replace(b.regex.shortDateXXY,"$3/$2/$1"):"yyyymmdd"===j&&(g=g.replace(b.regex.shortDateYMD,"$1/$2/$3")),f=new Date(g),f instanceof Date&&isFinite(f)?f.getTime():a}return a},type:"numeric"}),b.regex.timeTest=/^(([0-2]?\d:[0-5]\d)|([0-1]?\d:[0-5]\d\s?([AP]M)))$/i,b.addParser({id:"time",is:function(a){return b.regex.timeTest.test(a)},format:function(a,c){var d=a?new Date("2000/01/01 "+a.replace(b.regex.dateReplace,"$1 $2")):a;return d instanceof Date&&isFinite(d)?d.getTime():a},type:"numeric"}),b.addParser({id:"metadata",is:function(){return!1},format:function(b,c,d){var e=c.config,f=e.parserMetadataName?e.parserMetadataName:"sortValue";return a(d).metadata()[f]},type:"numeric"}),b.addWidget({id:"zebra",priority:90,format:function(b,c,d){var e,f,g,h,i,j,k,l,m=new RegExp(c.cssChildRow,"i"),n=c.$tbodies.add(a(c.namespace+"_extra_table").children("tbody:not(."+c.cssInfoBlock+")"));for(c.debug&&(i=new Date),j=0;j<n.length;j++)for(g=0,e=n.eq(j).children("tr:visible").not(c.selectorRemove),l=e.length,k=0;l>k;k++)f=e.eq(k),m.test(f[0].className)||g++,h=g%2===0,f.removeClass(d.zebra[h?1:0]).addClass(d.zebra[h?0:1])},remove:function(a,c,d,e){if(!e){var f,g,h=c.$tbodies,i=(d.zebra||["even","odd"]).join(" ");for(f=0;f<h.length;f++)g=b.processTbody(a,h.eq(f),!0),g.children().removeClass(i),b.processTbody(a,g,!1)}}})}(jQuery),a.tablesorter});