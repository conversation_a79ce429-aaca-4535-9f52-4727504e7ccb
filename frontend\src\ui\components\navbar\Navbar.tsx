import NavbarNavigationMenu from "./NavbarNavigationMenu.tsx";
import Search from "./Search.tsx";
import Logo from "./Logo.tsx";
import {SongFilterType} from "@/types/types.ts";

type NavbarProps = {
  setFilter: (filter: SongFilterType) => void,
}

function Navbar({setFilter}: NavbarProps) {
  return (
    <div className="p-4 bg-bg-secondary-color">
      <div className="flex  ">
        <Logo/>
          <Search setFilter={setFilter}/>
      </div>

      <div className="flex justify-center pt-2">
        <NavbarNavigationMenu/>
      </div>
    </div>
  )
}


export default Navbar