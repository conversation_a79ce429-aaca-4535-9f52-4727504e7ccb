/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: "",
  variants: {
    extend: {
      display: ["group-hover"],
    },
  },
  theme: {
    colors: {
      transparent: 'transparent',
      current: 'currentColor',

      'primary-text-color': "#A4A4A4",
      'secondary-text-color': "#FFFEEE",


      // 'primary-component-color': "#005B41",
      // 'secondary-component-color': "#008170",

      // 'bg-primary-color': "#D8EFD3", OLD
      // 'bg-secondary-color': "#95D2B3", OLD

      'bg-primary-color':"#dde0df",
      'bg-secondary-color':"#cecece",

      'black': "#000000",
      'grey': "#808080",

    },
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
        "lg": "1024px",
        "md": "768px",
        "sm": "640px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        // background: "#D8EFD3",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        // foreground: "#000000",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}