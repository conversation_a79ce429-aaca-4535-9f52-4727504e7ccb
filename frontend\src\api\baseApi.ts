import axios, {AxiosRequestConfig} from "axios";

type ApiRespMulti<T> = {
  items: T[];
  message?: string;
};


const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_BACKEND_URL,
});

async function getAll<T>(path: string, config?: AxiosRequestConfig) {
  const resp = await axiosInstance.get<ApiRespMulti<T>>(path, config);
  return resp.data;
}

async function getSingle<T>(path: string, filter?: string) {
  const resp = await axiosInstance.get<T>(path, {params: {filter}});
  return resp.data;
}

async function postSingle<T>(path: string, payload: unknown) {
  const resp = await axiosInstance.post<T>(path, payload);
  return resp.data;
}

async function putSingle<T>(path: string, payload: unknown) {
  const resp = await axiosInstance.put<T>(path, payload);
  return resp.data;
}

async function deleteSingle<T>(path: string, payload: unknown = undefined) {
  const resp = await axiosInstance.delete<T>(path, {data: payload});
  return resp.data;
}

const BaseApi = {
  get: axiosInstance.get,
  getAll,
  getSingle,
  post: axiosInstance.post,
  postSingle,
  put: axiosInstance.put,
  putSingle,
  delete: axiosInstance.delete,
  deleteSingle,
};

export default BaseApi;
