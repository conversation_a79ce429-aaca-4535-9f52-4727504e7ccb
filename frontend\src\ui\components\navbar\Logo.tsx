import {SparklesIcon} from "@heroicons/react/24/outline";
import {Link} from "react-router-dom";
import {name} from "@/data/content/staticContent.ts";

function Logo() {
  return (
    <Link to="/home" className="font-bold text-2xl space-x-1 hover:underline hover:text-background flex">
      <SparklesIcon className="h-6 w-6"/>
      <h1 className="md:inline hidden">
        {name}
      </h1>
    </Link>
  );
}

export default Logo;