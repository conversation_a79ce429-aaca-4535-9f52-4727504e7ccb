import {flexRender, type Table as TableType} from "@tanstack/react-table";
import {Table, TableBody, TableHead, TableHeader, TableRow} from "@/ui/components/table.tsx";
import {PlayerContext} from "@/App.tsx";
import {useContext} from "react";
import {playerSwitch} from "@/business/playerSwitch.ts";
import {Album, Playlist} from "@/types/types.ts";

interface ItemTableProps<T> {
  table: TableType<T>;
  playlist: Album|Playlist
}

export function ItemTable<T>({table, playlist}: ItemTableProps<T>, ) {
  const {player, setPlayer} = useContext(PlayerContext);

  const onPlay = (index: string) => {
    const id = parseInt(index);
    playerSwitch({data: playlist, songIndex: id, player, setPlayer})
  }

  return (
    <div className="rounded-md border border-light-color text-primary-color h-full">
      <div className="">
        <Table className="text-primary-color">
          <TableHeader className="text-primary-color">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return <TableHead key={header.id} colSpan={header.colSpan} className="text-primary-color">
                    {header.isPlaceholder ? null : (
                      <div>
                        {flexRender(header.column.columnDef.header, header.getContext())}
                      </div>
                    )}
                  </TableHead>
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.map((row) => {
              return <TableRow key={row.id} className="group" onClick={() => onPlay(row.id)}>
                {row.getVisibleCells().map((cell) => {
                  return (
                    <td key={cell.id} className="p-1 ">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  );
                })}
              </TableRow>
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}