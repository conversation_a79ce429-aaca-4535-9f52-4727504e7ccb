import {albumRepository} from "./repository";
import {handleRepositoryErrors, parseRequest} from "../utils";
import {uuidQueryRequestSchema} from "../validationSchemas";
import {Request, Response} from "express";

const getAlbum = async (req: Request, res: Response) => {
  const request = await parseRequest(uuidQueryRequestSchema, req, res);
  if (!request) {
    return;
  }
  const filter = request.params.id;
  const album = await albumRepository.get(filter);

  if (album.isErr) {
    handleRepositoryErrors(album.error, res);
    return;
  }

  res.status(200).send(album.value);
}

const getAlbums = async (req: Request, res: Response) => {

  const albums = await albumRepository.getAll();

  if (albums.isErr) {
    handleRepositoryErrors(albums.error, res);
    return;
  }

  res.status(200).send({ items: albums.value, message: 'All categories fetched' });
}

export const albumController = {
  getAlbum,
  getAlbums
}