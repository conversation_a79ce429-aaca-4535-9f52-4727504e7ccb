import {Link} from "react-router-dom";
import {name, bio, collaboration, image} from "@/data/content/staticContent.ts";

function AboutPage() {
  return (
    <>
      <h2 className="text-center my-10 font-bold text-2xl">About Me</h2>
      <div className="m-auto mb-20 border-2 rounded-3xl border-t-0 border-grey p-4 flex flex-col justify-center w-11/12
      md:grid md:grid-cols-3 lg:w-2/3 2xl:w-1/2">
        <div className="col-start-1 col-end-1 w-52 self-center">
          <div>
            {image}
            <p className="text-center border-2 border-[#dfd7ca] bg-[#dfd7ca] rounded-b-2xl border-t-0 font-bold">
              {name}
            </p>
          </div>
          <div className="grid grid-cols-2 justify-items-end px-2">
            <span className="justify-self-start">Tracks:</span>84
            <span className="justify-self-start">Followers:</span>1.2M
            <span className="justify-self-start">Since:</span>2010
          </div>
        </div>

        <div className="col-start-2 col-span-2">
          <p className="border-b border-grey mb-4">
            {bio}
          </p>

          <h2>Collaborations:</h2>
          <ul className="flex justify-start flex-wrap">
            {collaboration.map((collaborator, index) => (
              <li key={index}>
                <Link to="" className="m-2 hover:underline hover:font-bold">
                  {collaborator}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </>
  );
}

export default AboutPage


