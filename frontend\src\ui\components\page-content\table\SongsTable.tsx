import {getCoreRowModel, useReactTable,} from "@tanstack/react-table";
import {SongsColumns} from "@/ui/components/page-content/table/SongsColumns.tsx";
import {Album, Playlist, Song} from "@/types/types.ts";
import {ItemTable} from "@/ui/components/page-content/table/ItemTable.tsx";


export function SongsTable({data}: { data: Album|Playlist }) {
  const songs = data.songs;

  const table = useReactTable({
    data: songs ?? [] as Song[],
    columns: SongsColumns,
    rowCount: songs?.length,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    debugTable: true,
  });

  return (
    <>
      <ItemTable table={table} playlist={data}/>
    </>
  )
}


