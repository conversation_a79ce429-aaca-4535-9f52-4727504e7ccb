import AlbumCard from "@/ui/components/page-content/AlbumCard.tsx";
import {AlbumBasic} from "@/types/types.ts";
import {useAlbums} from "@/hooks/useAlbum.ts";

function AlbumsPage() {
  const {data: albums, isFetching} = useAlbums();

  if (isFetching) {
    return <div>Loading...</div>;
  }

  return (
    <div className="flex flex-wrap justify-evenly">
      {albums?.items.map((album: AlbumBasic) => (
        <AlbumCard key={album.title} album={album}/>
      ))}
    </div>
  )
}

export default AlbumsPage
