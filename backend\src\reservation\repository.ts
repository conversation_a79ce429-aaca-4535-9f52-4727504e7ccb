import client from "../client";
import { Result } from "@badrap/result";
import { NotFoundError } from "../utils";
import { DbResult } from "../types";
import { PriceInput, ReservationInput } from "../validationSchemas";
import { priceRepository } from "../price/repository";
import { Reservation } from "../../generated/prisma";

function calculateTotalPrice(
  reservation: ReservationInput["body"],
  price: PriceInput["body"]
) {
  return (
    price.car * reservation.car +
    price.moto * reservation.moto +
    price.caravan * reservation.caravan +
    price.people * reservation.people +
    price.children * reservation.children +
    price.dog * reservation.dog +
    price.tent * reservation.tent +
    (reservation.electricity ? price.electricity : 0)
  );
}

export const reservationRepository = {
  create: async function (
    reservation: ReservationInput["body"]
  ): DbResult<Reservation> {
    try {
      const price = await priceRepository.get();
      if (price.isErr) {
        return Result.err(new Error("Price not found"));
      }
      const totalPrice = calculateTotalPrice(reservation, price.value);
      const reservationWithPrice = {
        ...reservation,
        totalPrice,
      };


      const result = await client.reservation.create({
        data: reservationWithPrice,
      });

      if (!result) {
        return Result.err(new Error("Reservation not created"));
      }
      return Result.ok(result);
    } catch (e) {
      return Result.err(new NotFoundError());
    }
  },

  get: async function (id: number): DbResult<Reservation> {
    try {
      const result = await client.reservation.findUnique({
        where: {
          id: id,
        },
      });

      if (!result) {
        return Result.err(new Error("Reservation not found"));
      }
      return Result.ok(result);
    } catch (e) {
      return Result.err(new NotFoundError());
    }
  },

  getAll: async function (): DbResult<Reservation[]> {
    try {
      const result = await client.reservation.findMany();

      if (!result) {
        return Result.err(new Error("Price not found"));
      }
      return Result.ok(result);
    } catch (e) {
      return Result.err(new NotFoundError());
    }
  },

  update: async function (
    id: number,
    reservation: ReservationInput["body"]
  ): DbResult<Reservation> {
    try {
      const result = await client.reservation.update({
        where: {
          id: id,
        },
        data: reservation,
      });

      if (!result) {
        return Result.err(new Error("Reservation not updated"));
      }
      return Result.ok(result);
    } catch (e) {
      return Result.err(new NotFoundError());
    }
  },

  delete: async function (id: number): DbResult<Reservation> {
    try {
      const result = await client.reservation.delete({
        where: {
          id: id,
        },
      });

      if (!result) {
        return Result.err(new Error("Reservation not deleted"));
      }
      return Result.ok(result);
    } catch (e) {
      return Result.err(new NotFoundError());
    }
  },
};
