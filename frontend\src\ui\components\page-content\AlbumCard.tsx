import {AlbumBasic} from "@/types/types.ts";
import {But<PERSON>} from "../button.tsx";
import {mediaPath, PlayerContext} from "@/App.tsx";
import {Link} from "react-router-dom";
import {PlayIcon} from "@heroicons/react/24/outline";
import {useContext} from "react";
import {useAlbum} from "@/hooks/useAlbum.ts";
import {Formater} from "@/business/Formater.ts";
import {playerSwitch} from "@/business/playerSwitch.ts";

function AlbumCard({album}: { album: AlbumBasic }) {
  const {player, setPlayer} = useContext(PlayerContext);
  const {data} = useAlbum(album.id)

  const onClick = async () => {
    playerSwitch({data, songIndex: 0, player, setPlayer})
  }

  return (
    <div className="group border-2 w-56 h-66 relative transition duration-300 hover:scale-110
    hover:bg-secondary-text-color my-4 mx-2">

      <Button variant="secondary" onClick={onClick}
              className="w-max h-max absolute left-2 bottom-1/4 2xl:hidden group-hover:inline rounded-2xl">
        <PlayIcon className="h-6 w-6 text-gray-500"/>
      </Button>

      <Link to={"/albums/" + album.id}>
        <img src={mediaPath + album.cover} alt="album cover" className="w-full h-3/4"/>
        <div className="flex flex-col justify-center items-center p-2 ">
          <strong>{album.title}</strong>
          <p className="justify-self-center">{Formater.formatYear(album.releaseDate)}</p>
        </div>
      </Link>
    </div>
  )
}

export default AlbumCard
