/*
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
$(document).ready(function() {

    $(".click-title").mouseenter( function(    e){
        e.preventDefault();
        this.style.cursor="pointer";
    });
    $(".click-title").mousedown( function(event){
        event.preventDefault();
    });

    // Ugly code while this script is shared among several pages
    try{
        refreshHitsPerSecond(true);
    } catch(e){}
    try{
        refreshResponseTimeOverTime(true);
    } catch(e){}
    try{
        refreshResponseTimePercentiles();
    } catch(e){}
});


var responseTimePercentilesInfos = {
        data: {"result": {"minY": 7.0, "minX": 0.0, "maxY": 8648.0, "series": [{"data": [[0.0, 8.0], [0.1, 8.0], [0.2, 8.0], [0.3, 8.0], [0.4, 8.0], [0.5, 8.0], [0.6, 8.0], [0.7, 8.0], [0.8, 8.0], [0.9, 8.0], [1.0, 8.0], [1.1, 8.0], [1.2, 8.0], [1.3, 8.0], [1.4, 8.0], [1.5, 8.0], [1.6, 8.0], [1.7, 8.0], [1.8, 8.0], [1.9, 8.0], [2.0, 8.0], [2.1, 8.0], [2.2, 8.0], [2.3, 8.0], [2.4, 8.0], [2.5, 8.0], [2.6, 8.0], [2.7, 8.0], [2.8, 8.0], [2.9, 8.0], [3.0, 8.0], [3.1, 8.0], [3.2, 8.0], [3.3, 8.0], [3.4, 8.0], [3.5, 8.0], [3.6, 8.0], [3.7, 8.0], [3.8, 8.0], [3.9, 8.0], [4.0, 8.0], [4.1, 8.0], [4.2, 8.0], [4.3, 8.0], [4.4, 8.0], [4.5, 8.0], [4.6, 8.0], [4.7, 8.0], [4.8, 8.0], [4.9, 8.0], [5.0, 8.0], [5.1, 8.0], [5.2, 8.0], [5.3, 8.0], [5.4, 8.0], [5.5, 8.0], [5.6, 8.0], [5.7, 8.0], [5.8, 8.0], [5.9, 8.0], [6.0, 8.0], [6.1, 8.0], [6.2, 8.0], [6.3, 8.0], [6.4, 8.0], [6.5, 8.0], [6.6, 8.0], [6.7, 8.0], [6.8, 8.0], [6.9, 8.0], [7.0, 8.0], [7.1, 8.0], [7.2, 8.0], [7.3, 8.0], [7.4, 8.0], [7.5, 8.0], [7.6, 8.0], [7.7, 8.0], [7.8, 8.0], [7.9, 8.0], [8.0, 8.0], [8.1, 8.0], [8.2, 8.0], [8.3, 8.0], [8.4, 8.0], [8.5, 8.0], [8.6, 8.0], [8.7, 8.0], [8.8, 8.0], [8.9, 8.0], [9.0, 8.0], [9.1, 8.0], [9.2, 8.0], [9.3, 8.0], [9.4, 8.0], [9.5, 8.0], [9.6, 8.0], [9.7, 8.0], [9.8, 8.0], [9.9, 8.0], [10.0, 9.0], [10.1, 9.0], [10.2, 9.0], [10.3, 9.0], [10.4, 9.0], [10.5, 9.0], [10.6, 9.0], [10.7, 9.0], [10.8, 9.0], [10.9, 9.0], [11.0, 9.0], [11.1, 9.0], [11.2, 9.0], [11.3, 9.0], [11.4, 9.0], [11.5, 9.0], [11.6, 9.0], [11.7, 9.0], [11.8, 9.0], [11.9, 9.0], [12.0, 9.0], [12.1, 9.0], [12.2, 9.0], [12.3, 9.0], [12.4, 9.0], [12.5, 9.0], [12.6, 9.0], [12.7, 9.0], [12.8, 9.0], [12.9, 9.0], [13.0, 9.0], [13.1, 9.0], [13.2, 9.0], [13.3, 9.0], [13.4, 9.0], [13.5, 9.0], [13.6, 9.0], [13.7, 9.0], [13.8, 9.0], [13.9, 9.0], [14.0, 9.0], [14.1, 9.0], [14.2, 9.0], [14.3, 9.0], [14.4, 9.0], [14.5, 9.0], [14.6, 9.0], [14.7, 9.0], [14.8, 9.0], [14.9, 9.0], [15.0, 9.0], [15.1, 9.0], [15.2, 9.0], [15.3, 9.0], [15.4, 9.0], [15.5, 9.0], [15.6, 9.0], [15.7, 9.0], [15.8, 9.0], [15.9, 9.0], [16.0, 9.0], [16.1, 9.0], [16.2, 9.0], [16.3, 9.0], [16.4, 9.0], [16.5, 9.0], [16.6, 9.0], [16.7, 9.0], [16.8, 9.0], [16.9, 9.0], [17.0, 9.0], [17.1, 9.0], [17.2, 9.0], [17.3, 9.0], [17.4, 9.0], [17.5, 9.0], [17.6, 9.0], [17.7, 9.0], [17.8, 9.0], [17.9, 9.0], [18.0, 9.0], [18.1, 9.0], [18.2, 9.0], [18.3, 9.0], [18.4, 9.0], [18.5, 9.0], [18.6, 9.0], [18.7, 9.0], [18.8, 9.0], [18.9, 9.0], [19.0, 9.0], [19.1, 9.0], [19.2, 9.0], [19.3, 9.0], [19.4, 9.0], [19.5, 9.0], [19.6, 9.0], [19.7, 9.0], [19.8, 9.0], [19.9, 9.0], [20.0, 9.0], [20.1, 9.0], [20.2, 9.0], [20.3, 9.0], [20.4, 9.0], [20.5, 9.0], [20.6, 9.0], [20.7, 9.0], [20.8, 9.0], [20.9, 9.0], [21.0, 9.0], [21.1, 9.0], [21.2, 9.0], [21.3, 9.0], [21.4, 9.0], [21.5, 9.0], [21.6, 9.0], [21.7, 9.0], [21.8, 9.0], [21.9, 9.0], [22.0, 9.0], [22.1, 9.0], [22.2, 9.0], [22.3, 9.0], [22.4, 9.0], [22.5, 9.0], [22.6, 9.0], [22.7, 9.0], [22.8, 9.0], [22.9, 9.0], [23.0, 9.0], [23.1, 9.0], [23.2, 9.0], [23.3, 9.0], [23.4, 9.0], [23.5, 9.0], [23.6, 9.0], [23.7, 9.0], [23.8, 9.0], [23.9, 9.0], [24.0, 9.0], [24.1, 9.0], [24.2, 9.0], [24.3, 9.0], [24.4, 9.0], [24.5, 9.0], [24.6, 9.0], [24.7, 9.0], [24.8, 9.0], [24.9, 9.0], [25.0, 9.0], [25.1, 9.0], [25.2, 9.0], [25.3, 9.0], [25.4, 9.0], [25.5, 9.0], [25.6, 9.0], [25.7, 9.0], [25.8, 9.0], [25.9, 9.0], [26.0, 9.0], [26.1, 9.0], [26.2, 9.0], [26.3, 9.0], [26.4, 9.0], [26.5, 9.0], [26.6, 9.0], [26.7, 9.0], [26.8, 9.0], [26.9, 9.0], [27.0, 9.0], [27.1, 9.0], [27.2, 9.0], [27.3, 9.0], [27.4, 9.0], [27.5, 9.0], [27.6, 9.0], [27.7, 9.0], [27.8, 9.0], [27.9, 9.0], [28.0, 9.0], [28.1, 9.0], [28.2, 9.0], [28.3, 9.0], [28.4, 9.0], [28.5, 9.0], [28.6, 9.0], [28.7, 9.0], [28.8, 9.0], [28.9, 9.0], [29.0, 9.0], [29.1, 9.0], [29.2, 9.0], [29.3, 9.0], [29.4, 9.0], [29.5, 9.0], [29.6, 9.0], [29.7, 9.0], [29.8, 9.0], [29.9, 9.0], [30.0, 12.0], [30.1, 12.0], [30.2, 12.0], [30.3, 12.0], [30.4, 12.0], [30.5, 12.0], [30.6, 12.0], [30.7, 12.0], [30.8, 12.0], [30.9, 12.0], [31.0, 12.0], [31.1, 12.0], [31.2, 12.0], [31.3, 12.0], [31.4, 12.0], [31.5, 12.0], [31.6, 12.0], [31.7, 12.0], [31.8, 12.0], [31.9, 12.0], [32.0, 12.0], [32.1, 12.0], [32.2, 12.0], [32.3, 12.0], [32.4, 12.0], [32.5, 12.0], [32.6, 12.0], [32.7, 12.0], [32.8, 12.0], [32.9, 12.0], [33.0, 12.0], [33.1, 12.0], [33.2, 12.0], [33.3, 12.0], [33.4, 12.0], [33.5, 12.0], [33.6, 12.0], [33.7, 12.0], [33.8, 12.0], [33.9, 12.0], [34.0, 12.0], [34.1, 12.0], [34.2, 12.0], [34.3, 12.0], [34.4, 12.0], [34.5, 12.0], [34.6, 12.0], [34.7, 12.0], [34.8, 12.0], [34.9, 12.0], [35.0, 12.0], [35.1, 12.0], [35.2, 12.0], [35.3, 12.0], [35.4, 12.0], [35.5, 12.0], [35.6, 12.0], [35.7, 12.0], [35.8, 12.0], [35.9, 12.0], [36.0, 12.0], [36.1, 12.0], [36.2, 12.0], [36.3, 12.0], [36.4, 12.0], [36.5, 12.0], [36.6, 12.0], [36.7, 12.0], [36.8, 12.0], [36.9, 12.0], [37.0, 12.0], [37.1, 12.0], [37.2, 12.0], [37.3, 12.0], [37.4, 12.0], [37.5, 12.0], [37.6, 12.0], [37.7, 12.0], [37.8, 12.0], [37.9, 12.0], [38.0, 12.0], [38.1, 12.0], [38.2, 12.0], [38.3, 12.0], [38.4, 12.0], [38.5, 12.0], [38.6, 12.0], [38.7, 12.0], [38.8, 12.0], [38.9, 12.0], [39.0, 12.0], [39.1, 12.0], [39.2, 12.0], [39.3, 12.0], [39.4, 12.0], [39.5, 12.0], [39.6, 12.0], [39.7, 12.0], [39.8, 12.0], [39.9, 12.0], [40.0, 13.0], [40.1, 13.0], [40.2, 13.0], [40.3, 13.0], [40.4, 13.0], [40.5, 13.0], [40.6, 13.0], [40.7, 13.0], [40.8, 13.0], [40.9, 13.0], [41.0, 13.0], [41.1, 13.0], [41.2, 13.0], [41.3, 13.0], [41.4, 13.0], [41.5, 13.0], [41.6, 13.0], [41.7, 13.0], [41.8, 13.0], [41.9, 13.0], [42.0, 13.0], [42.1, 13.0], [42.2, 13.0], [42.3, 13.0], [42.4, 13.0], [42.5, 13.0], [42.6, 13.0], [42.7, 13.0], [42.8, 13.0], [42.9, 13.0], [43.0, 13.0], [43.1, 13.0], [43.2, 13.0], [43.3, 13.0], [43.4, 13.0], [43.5, 13.0], [43.6, 13.0], [43.7, 13.0], [43.8, 13.0], [43.9, 13.0], [44.0, 13.0], [44.1, 13.0], [44.2, 13.0], [44.3, 13.0], [44.4, 13.0], [44.5, 13.0], [44.6, 13.0], [44.7, 13.0], [44.8, 13.0], [44.9, 13.0], [45.0, 13.0], [45.1, 13.0], [45.2, 13.0], [45.3, 13.0], [45.4, 13.0], [45.5, 13.0], [45.6, 13.0], [45.7, 13.0], [45.8, 13.0], [45.9, 13.0], [46.0, 13.0], [46.1, 13.0], [46.2, 13.0], [46.3, 13.0], [46.4, 13.0], [46.5, 13.0], [46.6, 13.0], [46.7, 13.0], [46.8, 13.0], [46.9, 13.0], [47.0, 13.0], [47.1, 13.0], [47.2, 13.0], [47.3, 13.0], [47.4, 13.0], [47.5, 13.0], [47.6, 13.0], [47.7, 13.0], [47.8, 13.0], [47.9, 13.0], [48.0, 13.0], [48.1, 13.0], [48.2, 13.0], [48.3, 13.0], [48.4, 13.0], [48.5, 13.0], [48.6, 13.0], [48.7, 13.0], [48.8, 13.0], [48.9, 13.0], [49.0, 13.0], [49.1, 13.0], [49.2, 13.0], [49.3, 13.0], [49.4, 13.0], [49.5, 13.0], [49.6, 13.0], [49.7, 13.0], [49.8, 13.0], [49.9, 13.0], [50.0, 13.0], [50.1, 13.0], [50.2, 13.0], [50.3, 13.0], [50.4, 13.0], [50.5, 13.0], [50.6, 13.0], [50.7, 13.0], [50.8, 13.0], [50.9, 13.0], [51.0, 13.0], [51.1, 13.0], [51.2, 13.0], [51.3, 13.0], [51.4, 13.0], [51.5, 13.0], [51.6, 13.0], [51.7, 13.0], [51.8, 13.0], [51.9, 13.0], [52.0, 13.0], [52.1, 13.0], [52.2, 13.0], [52.3, 13.0], [52.4, 13.0], [52.5, 13.0], [52.6, 13.0], [52.7, 13.0], [52.8, 13.0], [52.9, 13.0], [53.0, 13.0], [53.1, 13.0], [53.2, 13.0], [53.3, 13.0], [53.4, 13.0], [53.5, 13.0], [53.6, 13.0], [53.7, 13.0], [53.8, 13.0], [53.9, 13.0], [54.0, 13.0], [54.1, 13.0], [54.2, 13.0], [54.3, 13.0], [54.4, 13.0], [54.5, 13.0], [54.6, 13.0], [54.7, 13.0], [54.8, 13.0], [54.9, 13.0], [55.0, 13.0], [55.1, 13.0], [55.2, 13.0], [55.3, 13.0], [55.4, 13.0], [55.5, 13.0], [55.6, 13.0], [55.7, 13.0], [55.8, 13.0], [55.9, 13.0], [56.0, 13.0], [56.1, 13.0], [56.2, 13.0], [56.3, 13.0], [56.4, 13.0], [56.5, 13.0], [56.6, 13.0], [56.7, 13.0], [56.8, 13.0], [56.9, 13.0], [57.0, 13.0], [57.1, 13.0], [57.2, 13.0], [57.3, 13.0], [57.4, 13.0], [57.5, 13.0], [57.6, 13.0], [57.7, 13.0], [57.8, 13.0], [57.9, 13.0], [58.0, 13.0], [58.1, 13.0], [58.2, 13.0], [58.3, 13.0], [58.4, 13.0], [58.5, 13.0], [58.6, 13.0], [58.7, 13.0], [58.8, 13.0], [58.9, 13.0], [59.0, 13.0], [59.1, 13.0], [59.2, 13.0], [59.3, 13.0], [59.4, 13.0], [59.5, 13.0], [59.6, 13.0], [59.7, 13.0], [59.8, 13.0], [59.9, 13.0], [60.0, 13.0], [60.1, 13.0], [60.2, 13.0], [60.3, 13.0], [60.4, 13.0], [60.5, 13.0], [60.6, 13.0], [60.7, 13.0], [60.8, 13.0], [60.9, 13.0], [61.0, 13.0], [61.1, 13.0], [61.2, 13.0], [61.3, 13.0], [61.4, 13.0], [61.5, 13.0], [61.6, 13.0], [61.7, 13.0], [61.8, 13.0], [61.9, 13.0], [62.0, 13.0], [62.1, 13.0], [62.2, 13.0], [62.3, 13.0], [62.4, 13.0], [62.5, 13.0], [62.6, 13.0], [62.7, 13.0], [62.8, 13.0], [62.9, 13.0], [63.0, 13.0], [63.1, 13.0], [63.2, 13.0], [63.3, 13.0], [63.4, 13.0], [63.5, 13.0], [63.6, 13.0], [63.7, 13.0], [63.8, 13.0], [63.9, 13.0], [64.0, 13.0], [64.1, 13.0], [64.2, 13.0], [64.3, 13.0], [64.4, 13.0], [64.5, 13.0], [64.6, 13.0], [64.7, 13.0], [64.8, 13.0], [64.9, 13.0], [65.0, 13.0], [65.1, 13.0], [65.2, 13.0], [65.3, 13.0], [65.4, 13.0], [65.5, 13.0], [65.6, 13.0], [65.7, 13.0], [65.8, 13.0], [65.9, 13.0], [66.0, 13.0], [66.1, 13.0], [66.2, 13.0], [66.3, 13.0], [66.4, 13.0], [66.5, 13.0], [66.6, 13.0], [66.7, 13.0], [66.8, 13.0], [66.9, 13.0], [67.0, 13.0], [67.1, 13.0], [67.2, 13.0], [67.3, 13.0], [67.4, 13.0], [67.5, 13.0], [67.6, 13.0], [67.7, 13.0], [67.8, 13.0], [67.9, 13.0], [68.0, 13.0], [68.1, 13.0], [68.2, 13.0], [68.3, 13.0], [68.4, 13.0], [68.5, 13.0], [68.6, 13.0], [68.7, 13.0], [68.8, 13.0], [68.9, 13.0], [69.0, 13.0], [69.1, 13.0], [69.2, 13.0], [69.3, 13.0], [69.4, 13.0], [69.5, 13.0], [69.6, 13.0], [69.7, 13.0], [69.8, 13.0], [69.9, 13.0], [70.0, 14.0], [70.1, 14.0], [70.2, 14.0], [70.3, 14.0], [70.4, 14.0], [70.5, 14.0], [70.6, 14.0], [70.7, 14.0], [70.8, 14.0], [70.9, 14.0], [71.0, 14.0], [71.1, 14.0], [71.2, 14.0], [71.3, 14.0], [71.4, 14.0], [71.5, 14.0], [71.6, 14.0], [71.7, 14.0], [71.8, 14.0], [71.9, 14.0], [72.0, 14.0], [72.1, 14.0], [72.2, 14.0], [72.3, 14.0], [72.4, 14.0], [72.5, 14.0], [72.6, 14.0], [72.7, 14.0], [72.8, 14.0], [72.9, 14.0], [73.0, 14.0], [73.1, 14.0], [73.2, 14.0], [73.3, 14.0], [73.4, 14.0], [73.5, 14.0], [73.6, 14.0], [73.7, 14.0], [73.8, 14.0], [73.9, 14.0], [74.0, 14.0], [74.1, 14.0], [74.2, 14.0], [74.3, 14.0], [74.4, 14.0], [74.5, 14.0], [74.6, 14.0], [74.7, 14.0], [74.8, 14.0], [74.9, 14.0], [75.0, 14.0], [75.1, 14.0], [75.2, 14.0], [75.3, 14.0], [75.4, 14.0], [75.5, 14.0], [75.6, 14.0], [75.7, 14.0], [75.8, 14.0], [75.9, 14.0], [76.0, 14.0], [76.1, 14.0], [76.2, 14.0], [76.3, 14.0], [76.4, 14.0], [76.5, 14.0], [76.6, 14.0], [76.7, 14.0], [76.8, 14.0], [76.9, 14.0], [77.0, 14.0], [77.1, 14.0], [77.2, 14.0], [77.3, 14.0], [77.4, 14.0], [77.5, 14.0], [77.6, 14.0], [77.7, 14.0], [77.8, 14.0], [77.9, 14.0], [78.0, 14.0], [78.1, 14.0], [78.2, 14.0], [78.3, 14.0], [78.4, 14.0], [78.5, 14.0], [78.6, 14.0], [78.7, 14.0], [78.8, 14.0], [78.9, 14.0], [79.0, 14.0], [79.1, 14.0], [79.2, 14.0], [79.3, 14.0], [79.4, 14.0], [79.5, 14.0], [79.6, 14.0], [79.7, 14.0], [79.8, 14.0], [79.9, 14.0], [80.0, 14.0], [80.1, 14.0], [80.2, 14.0], [80.3, 14.0], [80.4, 14.0], [80.5, 14.0], [80.6, 14.0], [80.7, 14.0], [80.8, 14.0], [80.9, 14.0], [81.0, 14.0], [81.1, 14.0], [81.2, 14.0], [81.3, 14.0], [81.4, 14.0], [81.5, 14.0], [81.6, 14.0], [81.7, 14.0], [81.8, 14.0], [81.9, 14.0], [82.0, 14.0], [82.1, 14.0], [82.2, 14.0], [82.3, 14.0], [82.4, 14.0], [82.5, 14.0], [82.6, 14.0], [82.7, 14.0], [82.8, 14.0], [82.9, 14.0], [83.0, 14.0], [83.1, 14.0], [83.2, 14.0], [83.3, 14.0], [83.4, 14.0], [83.5, 14.0], [83.6, 14.0], [83.7, 14.0], [83.8, 14.0], [83.9, 14.0], [84.0, 14.0], [84.1, 14.0], [84.2, 14.0], [84.3, 14.0], [84.4, 14.0], [84.5, 14.0], [84.6, 14.0], [84.7, 14.0], [84.8, 14.0], [84.9, 14.0], [85.0, 14.0], [85.1, 14.0], [85.2, 14.0], [85.3, 14.0], [85.4, 14.0], [85.5, 14.0], [85.6, 14.0], [85.7, 14.0], [85.8, 14.0], [85.9, 14.0], [86.0, 14.0], [86.1, 14.0], [86.2, 14.0], [86.3, 14.0], [86.4, 14.0], [86.5, 14.0], [86.6, 14.0], [86.7, 14.0], [86.8, 14.0], [86.9, 14.0], [87.0, 14.0], [87.1, 14.0], [87.2, 14.0], [87.3, 14.0], [87.4, 14.0], [87.5, 14.0], [87.6, 14.0], [87.7, 14.0], [87.8, 14.0], [87.9, 14.0], [88.0, 14.0], [88.1, 14.0], [88.2, 14.0], [88.3, 14.0], [88.4, 14.0], [88.5, 14.0], [88.6, 14.0], [88.7, 14.0], [88.8, 14.0], [88.9, 14.0], [89.0, 14.0], [89.1, 14.0], [89.2, 14.0], [89.3, 14.0], [89.4, 14.0], [89.5, 14.0], [89.6, 14.0], [89.7, 14.0], [89.8, 14.0], [89.9, 14.0], [90.0, 16.0], [90.1, 16.0], [90.2, 16.0], [90.3, 16.0], [90.4, 16.0], [90.5, 16.0], [90.6, 16.0], [90.7, 16.0], [90.8, 16.0], [90.9, 16.0], [91.0, 16.0], [91.1, 16.0], [91.2, 16.0], [91.3, 16.0], [91.4, 16.0], [91.5, 16.0], [91.6, 16.0], [91.7, 16.0], [91.8, 16.0], [91.9, 16.0], [92.0, 16.0], [92.1, 16.0], [92.2, 16.0], [92.3, 16.0], [92.4, 16.0], [92.5, 16.0], [92.6, 16.0], [92.7, 16.0], [92.8, 16.0], [92.9, 16.0], [93.0, 16.0], [93.1, 16.0], [93.2, 16.0], [93.3, 16.0], [93.4, 16.0], [93.5, 16.0], [93.6, 16.0], [93.7, 16.0], [93.8, 16.0], [93.9, 16.0], [94.0, 16.0], [94.1, 16.0], [94.2, 16.0], [94.3, 16.0], [94.4, 16.0], [94.5, 16.0], [94.6, 16.0], [94.7, 16.0], [94.8, 16.0], [94.9, 16.0], [95.0, 16.0], [95.1, 16.0], [95.2, 16.0], [95.3, 16.0], [95.4, 16.0], [95.5, 16.0], [95.6, 16.0], [95.7, 16.0], [95.8, 16.0], [95.9, 16.0], [96.0, 16.0], [96.1, 16.0], [96.2, 16.0], [96.3, 16.0], [96.4, 16.0], [96.5, 16.0], [96.6, 16.0], [96.7, 16.0], [96.8, 16.0], [96.9, 16.0], [97.0, 16.0], [97.1, 16.0], [97.2, 16.0], [97.3, 16.0], [97.4, 16.0], [97.5, 16.0], [97.6, 16.0], [97.7, 16.0], [97.8, 16.0], [97.9, 16.0], [98.0, 16.0], [98.1, 16.0], [98.2, 16.0], [98.3, 16.0], [98.4, 16.0], [98.5, 16.0], [98.6, 16.0], [98.7, 16.0], [98.8, 16.0], [98.9, 16.0], [99.0, 16.0], [99.1, 16.0], [99.2, 16.0], [99.3, 16.0], [99.4, 16.0], [99.5, 16.0], [99.6, 16.0], [99.7, 16.0], [99.8, 16.0], [99.9, 16.0]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[0.0, 8103.0], [0.1, 8103.0], [0.2, 8103.0], [0.3, 8103.0], [0.4, 8103.0], [0.5, 8103.0], [0.6, 8103.0], [0.7, 8103.0], [0.8, 8103.0], [0.9, 8103.0], [1.0, 8103.0], [1.1, 8103.0], [1.2, 8103.0], [1.3, 8103.0], [1.4, 8103.0], [1.5, 8103.0], [1.6, 8103.0], [1.7, 8103.0], [1.8, 8103.0], [1.9, 8103.0], [2.0, 8103.0], [2.1, 8103.0], [2.2, 8103.0], [2.3, 8103.0], [2.4, 8103.0], [2.5, 8103.0], [2.6, 8103.0], [2.7, 8103.0], [2.8, 8103.0], [2.9, 8103.0], [3.0, 8103.0], [3.1, 8103.0], [3.2, 8103.0], [3.3, 8103.0], [3.4, 8103.0], [3.5, 8103.0], [3.6, 8103.0], [3.7, 8103.0], [3.8, 8103.0], [3.9, 8103.0], [4.0, 8103.0], [4.1, 8103.0], [4.2, 8103.0], [4.3, 8103.0], [4.4, 8103.0], [4.5, 8103.0], [4.6, 8103.0], [4.7, 8103.0], [4.8, 8103.0], [4.9, 8103.0], [5.0, 8103.0], [5.1, 8103.0], [5.2, 8103.0], [5.3, 8103.0], [5.4, 8103.0], [5.5, 8103.0], [5.6, 8103.0], [5.7, 8103.0], [5.8, 8103.0], [5.9, 8103.0], [6.0, 8103.0], [6.1, 8103.0], [6.2, 8103.0], [6.3, 8103.0], [6.4, 8103.0], [6.5, 8103.0], [6.6, 8103.0], [6.7, 8103.0], [6.8, 8103.0], [6.9, 8103.0], [7.0, 8103.0], [7.1, 8103.0], [7.2, 8103.0], [7.3, 8103.0], [7.4, 8103.0], [7.5, 8103.0], [7.6, 8103.0], [7.7, 8103.0], [7.8, 8103.0], [7.9, 8103.0], [8.0, 8103.0], [8.1, 8103.0], [8.2, 8103.0], [8.3, 8103.0], [8.4, 8103.0], [8.5, 8103.0], [8.6, 8103.0], [8.7, 8103.0], [8.8, 8103.0], [8.9, 8103.0], [9.0, 8103.0], [9.1, 8103.0], [9.2, 8103.0], [9.3, 8103.0], [9.4, 8103.0], [9.5, 8103.0], [9.6, 8103.0], [9.7, 8103.0], [9.8, 8103.0], [9.9, 8103.0], [10.0, 8144.0], [10.1, 8144.0], [10.2, 8144.0], [10.3, 8144.0], [10.4, 8144.0], [10.5, 8144.0], [10.6, 8144.0], [10.7, 8144.0], [10.8, 8144.0], [10.9, 8144.0], [11.0, 8144.0], [11.1, 8144.0], [11.2, 8144.0], [11.3, 8144.0], [11.4, 8144.0], [11.5, 8144.0], [11.6, 8144.0], [11.7, 8144.0], [11.8, 8144.0], [11.9, 8144.0], [12.0, 8144.0], [12.1, 8144.0], [12.2, 8144.0], [12.3, 8144.0], [12.4, 8144.0], [12.5, 8144.0], [12.6, 8144.0], [12.7, 8144.0], [12.8, 8144.0], [12.9, 8144.0], [13.0, 8144.0], [13.1, 8144.0], [13.2, 8144.0], [13.3, 8144.0], [13.4, 8144.0], [13.5, 8144.0], [13.6, 8144.0], [13.7, 8144.0], [13.8, 8144.0], [13.9, 8144.0], [14.0, 8144.0], [14.1, 8144.0], [14.2, 8144.0], [14.3, 8144.0], [14.4, 8144.0], [14.5, 8144.0], [14.6, 8144.0], [14.7, 8144.0], [14.8, 8144.0], [14.9, 8144.0], [15.0, 8144.0], [15.1, 8144.0], [15.2, 8144.0], [15.3, 8144.0], [15.4, 8144.0], [15.5, 8144.0], [15.6, 8144.0], [15.7, 8144.0], [15.8, 8144.0], [15.9, 8144.0], [16.0, 8144.0], [16.1, 8144.0], [16.2, 8144.0], [16.3, 8144.0], [16.4, 8144.0], [16.5, 8144.0], [16.6, 8144.0], [16.7, 8144.0], [16.8, 8144.0], [16.9, 8144.0], [17.0, 8144.0], [17.1, 8144.0], [17.2, 8144.0], [17.3, 8144.0], [17.4, 8144.0], [17.5, 8144.0], [17.6, 8144.0], [17.7, 8144.0], [17.8, 8144.0], [17.9, 8144.0], [18.0, 8144.0], [18.1, 8144.0], [18.2, 8144.0], [18.3, 8144.0], [18.4, 8144.0], [18.5, 8144.0], [18.6, 8144.0], [18.7, 8144.0], [18.8, 8144.0], [18.9, 8144.0], [19.0, 8144.0], [19.1, 8144.0], [19.2, 8144.0], [19.3, 8144.0], [19.4, 8144.0], [19.5, 8144.0], [19.6, 8144.0], [19.7, 8144.0], [19.8, 8144.0], [19.9, 8144.0], [20.0, 8151.0], [20.1, 8151.0], [20.2, 8151.0], [20.3, 8151.0], [20.4, 8151.0], [20.5, 8151.0], [20.6, 8151.0], [20.7, 8151.0], [20.8, 8151.0], [20.9, 8151.0], [21.0, 8151.0], [21.1, 8151.0], [21.2, 8151.0], [21.3, 8151.0], [21.4, 8151.0], [21.5, 8151.0], [21.6, 8151.0], [21.7, 8151.0], [21.8, 8151.0], [21.9, 8151.0], [22.0, 8151.0], [22.1, 8151.0], [22.2, 8151.0], [22.3, 8151.0], [22.4, 8151.0], [22.5, 8151.0], [22.6, 8151.0], [22.7, 8151.0], [22.8, 8151.0], [22.9, 8151.0], [23.0, 8151.0], [23.1, 8151.0], [23.2, 8151.0], [23.3, 8151.0], [23.4, 8151.0], [23.5, 8151.0], [23.6, 8151.0], [23.7, 8151.0], [23.8, 8151.0], [23.9, 8151.0], [24.0, 8151.0], [24.1, 8151.0], [24.2, 8151.0], [24.3, 8151.0], [24.4, 8151.0], [24.5, 8151.0], [24.6, 8151.0], [24.7, 8151.0], [24.8, 8151.0], [24.9, 8151.0], [25.0, 8151.0], [25.1, 8151.0], [25.2, 8151.0], [25.3, 8151.0], [25.4, 8151.0], [25.5, 8151.0], [25.6, 8151.0], [25.7, 8151.0], [25.8, 8151.0], [25.9, 8151.0], [26.0, 8151.0], [26.1, 8151.0], [26.2, 8151.0], [26.3, 8151.0], [26.4, 8151.0], [26.5, 8151.0], [26.6, 8151.0], [26.7, 8151.0], [26.8, 8151.0], [26.9, 8151.0], [27.0, 8151.0], [27.1, 8151.0], [27.2, 8151.0], [27.3, 8151.0], [27.4, 8151.0], [27.5, 8151.0], [27.6, 8151.0], [27.7, 8151.0], [27.8, 8151.0], [27.9, 8151.0], [28.0, 8151.0], [28.1, 8151.0], [28.2, 8151.0], [28.3, 8151.0], [28.4, 8151.0], [28.5, 8151.0], [28.6, 8151.0], [28.7, 8151.0], [28.8, 8151.0], [28.9, 8151.0], [29.0, 8151.0], [29.1, 8151.0], [29.2, 8151.0], [29.3, 8151.0], [29.4, 8151.0], [29.5, 8151.0], [29.6, 8151.0], [29.7, 8151.0], [29.8, 8151.0], [29.9, 8151.0], [30.0, 8169.0], [30.1, 8169.0], [30.2, 8169.0], [30.3, 8169.0], [30.4, 8169.0], [30.5, 8169.0], [30.6, 8169.0], [30.7, 8169.0], [30.8, 8169.0], [30.9, 8169.0], [31.0, 8169.0], [31.1, 8169.0], [31.2, 8169.0], [31.3, 8169.0], [31.4, 8169.0], [31.5, 8169.0], [31.6, 8169.0], [31.7, 8169.0], [31.8, 8169.0], [31.9, 8169.0], [32.0, 8169.0], [32.1, 8169.0], [32.2, 8169.0], [32.3, 8169.0], [32.4, 8169.0], [32.5, 8169.0], [32.6, 8169.0], [32.7, 8169.0], [32.8, 8169.0], [32.9, 8169.0], [33.0, 8169.0], [33.1, 8169.0], [33.2, 8169.0], [33.3, 8169.0], [33.4, 8169.0], [33.5, 8169.0], [33.6, 8169.0], [33.7, 8169.0], [33.8, 8169.0], [33.9, 8169.0], [34.0, 8169.0], [34.1, 8169.0], [34.2, 8169.0], [34.3, 8169.0], [34.4, 8169.0], [34.5, 8169.0], [34.6, 8169.0], [34.7, 8169.0], [34.8, 8169.0], [34.9, 8169.0], [35.0, 8169.0], [35.1, 8169.0], [35.2, 8169.0], [35.3, 8169.0], [35.4, 8169.0], [35.5, 8169.0], [35.6, 8169.0], [35.7, 8169.0], [35.8, 8169.0], [35.9, 8169.0], [36.0, 8169.0], [36.1, 8169.0], [36.2, 8169.0], [36.3, 8169.0], [36.4, 8169.0], [36.5, 8169.0], [36.6, 8169.0], [36.7, 8169.0], [36.8, 8169.0], [36.9, 8169.0], [37.0, 8169.0], [37.1, 8169.0], [37.2, 8169.0], [37.3, 8169.0], [37.4, 8169.0], [37.5, 8169.0], [37.6, 8169.0], [37.7, 8169.0], [37.8, 8169.0], [37.9, 8169.0], [38.0, 8169.0], [38.1, 8169.0], [38.2, 8169.0], [38.3, 8169.0], [38.4, 8169.0], [38.5, 8169.0], [38.6, 8169.0], [38.7, 8169.0], [38.8, 8169.0], [38.9, 8169.0], [39.0, 8169.0], [39.1, 8169.0], [39.2, 8169.0], [39.3, 8169.0], [39.4, 8169.0], [39.5, 8169.0], [39.6, 8169.0], [39.7, 8169.0], [39.8, 8169.0], [39.9, 8169.0], [40.0, 8192.0], [40.1, 8192.0], [40.2, 8192.0], [40.3, 8192.0], [40.4, 8192.0], [40.5, 8192.0], [40.6, 8192.0], [40.7, 8192.0], [40.8, 8192.0], [40.9, 8192.0], [41.0, 8192.0], [41.1, 8192.0], [41.2, 8192.0], [41.3, 8192.0], [41.4, 8192.0], [41.5, 8192.0], [41.6, 8192.0], [41.7, 8192.0], [41.8, 8192.0], [41.9, 8192.0], [42.0, 8192.0], [42.1, 8192.0], [42.2, 8192.0], [42.3, 8192.0], [42.4, 8192.0], [42.5, 8192.0], [42.6, 8192.0], [42.7, 8192.0], [42.8, 8192.0], [42.9, 8192.0], [43.0, 8192.0], [43.1, 8192.0], [43.2, 8192.0], [43.3, 8192.0], [43.4, 8192.0], [43.5, 8192.0], [43.6, 8192.0], [43.7, 8192.0], [43.8, 8192.0], [43.9, 8192.0], [44.0, 8192.0], [44.1, 8192.0], [44.2, 8192.0], [44.3, 8192.0], [44.4, 8192.0], [44.5, 8192.0], [44.6, 8192.0], [44.7, 8192.0], [44.8, 8192.0], [44.9, 8192.0], [45.0, 8192.0], [45.1, 8192.0], [45.2, 8192.0], [45.3, 8192.0], [45.4, 8192.0], [45.5, 8192.0], [45.6, 8192.0], [45.7, 8192.0], [45.8, 8192.0], [45.9, 8192.0], [46.0, 8192.0], [46.1, 8192.0], [46.2, 8192.0], [46.3, 8192.0], [46.4, 8192.0], [46.5, 8192.0], [46.6, 8192.0], [46.7, 8192.0], [46.8, 8192.0], [46.9, 8192.0], [47.0, 8192.0], [47.1, 8192.0], [47.2, 8192.0], [47.3, 8192.0], [47.4, 8192.0], [47.5, 8192.0], [47.6, 8192.0], [47.7, 8192.0], [47.8, 8192.0], [47.9, 8192.0], [48.0, 8192.0], [48.1, 8192.0], [48.2, 8192.0], [48.3, 8192.0], [48.4, 8192.0], [48.5, 8192.0], [48.6, 8192.0], [48.7, 8192.0], [48.8, 8192.0], [48.9, 8192.0], [49.0, 8192.0], [49.1, 8192.0], [49.2, 8192.0], [49.3, 8192.0], [49.4, 8192.0], [49.5, 8192.0], [49.6, 8192.0], [49.7, 8192.0], [49.8, 8192.0], [49.9, 8192.0], [50.0, 8212.0], [50.1, 8212.0], [50.2, 8212.0], [50.3, 8212.0], [50.4, 8212.0], [50.5, 8212.0], [50.6, 8212.0], [50.7, 8212.0], [50.8, 8212.0], [50.9, 8212.0], [51.0, 8212.0], [51.1, 8212.0], [51.2, 8212.0], [51.3, 8212.0], [51.4, 8212.0], [51.5, 8212.0], [51.6, 8212.0], [51.7, 8212.0], [51.8, 8212.0], [51.9, 8212.0], [52.0, 8212.0], [52.1, 8212.0], [52.2, 8212.0], [52.3, 8212.0], [52.4, 8212.0], [52.5, 8212.0], [52.6, 8212.0], [52.7, 8212.0], [52.8, 8212.0], [52.9, 8212.0], [53.0, 8212.0], [53.1, 8212.0], [53.2, 8212.0], [53.3, 8212.0], [53.4, 8212.0], [53.5, 8212.0], [53.6, 8212.0], [53.7, 8212.0], [53.8, 8212.0], [53.9, 8212.0], [54.0, 8212.0], [54.1, 8212.0], [54.2, 8212.0], [54.3, 8212.0], [54.4, 8212.0], [54.5, 8212.0], [54.6, 8212.0], [54.7, 8212.0], [54.8, 8212.0], [54.9, 8212.0], [55.0, 8212.0], [55.1, 8212.0], [55.2, 8212.0], [55.3, 8212.0], [55.4, 8212.0], [55.5, 8212.0], [55.6, 8212.0], [55.7, 8212.0], [55.8, 8212.0], [55.9, 8212.0], [56.0, 8212.0], [56.1, 8212.0], [56.2, 8212.0], [56.3, 8212.0], [56.4, 8212.0], [56.5, 8212.0], [56.6, 8212.0], [56.7, 8212.0], [56.8, 8212.0], [56.9, 8212.0], [57.0, 8212.0], [57.1, 8212.0], [57.2, 8212.0], [57.3, 8212.0], [57.4, 8212.0], [57.5, 8212.0], [57.6, 8212.0], [57.7, 8212.0], [57.8, 8212.0], [57.9, 8212.0], [58.0, 8212.0], [58.1, 8212.0], [58.2, 8212.0], [58.3, 8212.0], [58.4, 8212.0], [58.5, 8212.0], [58.6, 8212.0], [58.7, 8212.0], [58.8, 8212.0], [58.9, 8212.0], [59.0, 8212.0], [59.1, 8212.0], [59.2, 8212.0], [59.3, 8212.0], [59.4, 8212.0], [59.5, 8212.0], [59.6, 8212.0], [59.7, 8212.0], [59.8, 8212.0], [59.9, 8212.0], [60.0, 8269.0], [60.1, 8269.0], [60.2, 8269.0], [60.3, 8269.0], [60.4, 8269.0], [60.5, 8269.0], [60.6, 8269.0], [60.7, 8269.0], [60.8, 8269.0], [60.9, 8269.0], [61.0, 8269.0], [61.1, 8269.0], [61.2, 8269.0], [61.3, 8269.0], [61.4, 8269.0], [61.5, 8269.0], [61.6, 8269.0], [61.7, 8269.0], [61.8, 8269.0], [61.9, 8269.0], [62.0, 8269.0], [62.1, 8269.0], [62.2, 8269.0], [62.3, 8269.0], [62.4, 8269.0], [62.5, 8269.0], [62.6, 8269.0], [62.7, 8269.0], [62.8, 8269.0], [62.9, 8269.0], [63.0, 8269.0], [63.1, 8269.0], [63.2, 8269.0], [63.3, 8269.0], [63.4, 8269.0], [63.5, 8269.0], [63.6, 8269.0], [63.7, 8269.0], [63.8, 8269.0], [63.9, 8269.0], [64.0, 8269.0], [64.1, 8269.0], [64.2, 8269.0], [64.3, 8269.0], [64.4, 8269.0], [64.5, 8269.0], [64.6, 8269.0], [64.7, 8269.0], [64.8, 8269.0], [64.9, 8269.0], [65.0, 8269.0], [65.1, 8269.0], [65.2, 8269.0], [65.3, 8269.0], [65.4, 8269.0], [65.5, 8269.0], [65.6, 8269.0], [65.7, 8269.0], [65.8, 8269.0], [65.9, 8269.0], [66.0, 8269.0], [66.1, 8269.0], [66.2, 8269.0], [66.3, 8269.0], [66.4, 8269.0], [66.5, 8269.0], [66.6, 8269.0], [66.7, 8269.0], [66.8, 8269.0], [66.9, 8269.0], [67.0, 8269.0], [67.1, 8269.0], [67.2, 8269.0], [67.3, 8269.0], [67.4, 8269.0], [67.5, 8269.0], [67.6, 8269.0], [67.7, 8269.0], [67.8, 8269.0], [67.9, 8269.0], [68.0, 8269.0], [68.1, 8269.0], [68.2, 8269.0], [68.3, 8269.0], [68.4, 8269.0], [68.5, 8269.0], [68.6, 8269.0], [68.7, 8269.0], [68.8, 8269.0], [68.9, 8269.0], [69.0, 8269.0], [69.1, 8269.0], [69.2, 8269.0], [69.3, 8269.0], [69.4, 8269.0], [69.5, 8269.0], [69.6, 8269.0], [69.7, 8269.0], [69.8, 8269.0], [69.9, 8269.0], [70.0, 8347.0], [70.1, 8347.0], [70.2, 8347.0], [70.3, 8347.0], [70.4, 8347.0], [70.5, 8347.0], [70.6, 8347.0], [70.7, 8347.0], [70.8, 8347.0], [70.9, 8347.0], [71.0, 8347.0], [71.1, 8347.0], [71.2, 8347.0], [71.3, 8347.0], [71.4, 8347.0], [71.5, 8347.0], [71.6, 8347.0], [71.7, 8347.0], [71.8, 8347.0], [71.9, 8347.0], [72.0, 8347.0], [72.1, 8347.0], [72.2, 8347.0], [72.3, 8347.0], [72.4, 8347.0], [72.5, 8347.0], [72.6, 8347.0], [72.7, 8347.0], [72.8, 8347.0], [72.9, 8347.0], [73.0, 8347.0], [73.1, 8347.0], [73.2, 8347.0], [73.3, 8347.0], [73.4, 8347.0], [73.5, 8347.0], [73.6, 8347.0], [73.7, 8347.0], [73.8, 8347.0], [73.9, 8347.0], [74.0, 8347.0], [74.1, 8347.0], [74.2, 8347.0], [74.3, 8347.0], [74.4, 8347.0], [74.5, 8347.0], [74.6, 8347.0], [74.7, 8347.0], [74.8, 8347.0], [74.9, 8347.0], [75.0, 8347.0], [75.1, 8347.0], [75.2, 8347.0], [75.3, 8347.0], [75.4, 8347.0], [75.5, 8347.0], [75.6, 8347.0], [75.7, 8347.0], [75.8, 8347.0], [75.9, 8347.0], [76.0, 8347.0], [76.1, 8347.0], [76.2, 8347.0], [76.3, 8347.0], [76.4, 8347.0], [76.5, 8347.0], [76.6, 8347.0], [76.7, 8347.0], [76.8, 8347.0], [76.9, 8347.0], [77.0, 8347.0], [77.1, 8347.0], [77.2, 8347.0], [77.3, 8347.0], [77.4, 8347.0], [77.5, 8347.0], [77.6, 8347.0], [77.7, 8347.0], [77.8, 8347.0], [77.9, 8347.0], [78.0, 8347.0], [78.1, 8347.0], [78.2, 8347.0], [78.3, 8347.0], [78.4, 8347.0], [78.5, 8347.0], [78.6, 8347.0], [78.7, 8347.0], [78.8, 8347.0], [78.9, 8347.0], [79.0, 8347.0], [79.1, 8347.0], [79.2, 8347.0], [79.3, 8347.0], [79.4, 8347.0], [79.5, 8347.0], [79.6, 8347.0], [79.7, 8347.0], [79.8, 8347.0], [79.9, 8347.0], [80.0, 8387.0], [80.1, 8387.0], [80.2, 8387.0], [80.3, 8387.0], [80.4, 8387.0], [80.5, 8387.0], [80.6, 8387.0], [80.7, 8387.0], [80.8, 8387.0], [80.9, 8387.0], [81.0, 8387.0], [81.1, 8387.0], [81.2, 8387.0], [81.3, 8387.0], [81.4, 8387.0], [81.5, 8387.0], [81.6, 8387.0], [81.7, 8387.0], [81.8, 8387.0], [81.9, 8387.0], [82.0, 8387.0], [82.1, 8387.0], [82.2, 8387.0], [82.3, 8387.0], [82.4, 8387.0], [82.5, 8387.0], [82.6, 8387.0], [82.7, 8387.0], [82.8, 8387.0], [82.9, 8387.0], [83.0, 8387.0], [83.1, 8387.0], [83.2, 8387.0], [83.3, 8387.0], [83.4, 8387.0], [83.5, 8387.0], [83.6, 8387.0], [83.7, 8387.0], [83.8, 8387.0], [83.9, 8387.0], [84.0, 8387.0], [84.1, 8387.0], [84.2, 8387.0], [84.3, 8387.0], [84.4, 8387.0], [84.5, 8387.0], [84.6, 8387.0], [84.7, 8387.0], [84.8, 8387.0], [84.9, 8387.0], [85.0, 8387.0], [85.1, 8387.0], [85.2, 8387.0], [85.3, 8387.0], [85.4, 8387.0], [85.5, 8387.0], [85.6, 8387.0], [85.7, 8387.0], [85.8, 8387.0], [85.9, 8387.0], [86.0, 8387.0], [86.1, 8387.0], [86.2, 8387.0], [86.3, 8387.0], [86.4, 8387.0], [86.5, 8387.0], [86.6, 8387.0], [86.7, 8387.0], [86.8, 8387.0], [86.9, 8387.0], [87.0, 8387.0], [87.1, 8387.0], [87.2, 8387.0], [87.3, 8387.0], [87.4, 8387.0], [87.5, 8387.0], [87.6, 8387.0], [87.7, 8387.0], [87.8, 8387.0], [87.9, 8387.0], [88.0, 8387.0], [88.1, 8387.0], [88.2, 8387.0], [88.3, 8387.0], [88.4, 8387.0], [88.5, 8387.0], [88.6, 8387.0], [88.7, 8387.0], [88.8, 8387.0], [88.9, 8387.0], [89.0, 8387.0], [89.1, 8387.0], [89.2, 8387.0], [89.3, 8387.0], [89.4, 8387.0], [89.5, 8387.0], [89.6, 8387.0], [89.7, 8387.0], [89.8, 8387.0], [89.9, 8387.0], [90.0, 8648.0], [90.1, 8648.0], [90.2, 8648.0], [90.3, 8648.0], [90.4, 8648.0], [90.5, 8648.0], [90.6, 8648.0], [90.7, 8648.0], [90.8, 8648.0], [90.9, 8648.0], [91.0, 8648.0], [91.1, 8648.0], [91.2, 8648.0], [91.3, 8648.0], [91.4, 8648.0], [91.5, 8648.0], [91.6, 8648.0], [91.7, 8648.0], [91.8, 8648.0], [91.9, 8648.0], [92.0, 8648.0], [92.1, 8648.0], [92.2, 8648.0], [92.3, 8648.0], [92.4, 8648.0], [92.5, 8648.0], [92.6, 8648.0], [92.7, 8648.0], [92.8, 8648.0], [92.9, 8648.0], [93.0, 8648.0], [93.1, 8648.0], [93.2, 8648.0], [93.3, 8648.0], [93.4, 8648.0], [93.5, 8648.0], [93.6, 8648.0], [93.7, 8648.0], [93.8, 8648.0], [93.9, 8648.0], [94.0, 8648.0], [94.1, 8648.0], [94.2, 8648.0], [94.3, 8648.0], [94.4, 8648.0], [94.5, 8648.0], [94.6, 8648.0], [94.7, 8648.0], [94.8, 8648.0], [94.9, 8648.0], [95.0, 8648.0], [95.1, 8648.0], [95.2, 8648.0], [95.3, 8648.0], [95.4, 8648.0], [95.5, 8648.0], [95.6, 8648.0], [95.7, 8648.0], [95.8, 8648.0], [95.9, 8648.0], [96.0, 8648.0], [96.1, 8648.0], [96.2, 8648.0], [96.3, 8648.0], [96.4, 8648.0], [96.5, 8648.0], [96.6, 8648.0], [96.7, 8648.0], [96.8, 8648.0], [96.9, 8648.0], [97.0, 8648.0], [97.1, 8648.0], [97.2, 8648.0], [97.3, 8648.0], [97.4, 8648.0], [97.5, 8648.0], [97.6, 8648.0], [97.7, 8648.0], [97.8, 8648.0], [97.9, 8648.0], [98.0, 8648.0], [98.1, 8648.0], [98.2, 8648.0], [98.3, 8648.0], [98.4, 8648.0], [98.5, 8648.0], [98.6, 8648.0], [98.7, 8648.0], [98.8, 8648.0], [98.9, 8648.0], [99.0, 8648.0], [99.1, 8648.0], [99.2, 8648.0], [99.3, 8648.0], [99.4, 8648.0], [99.5, 8648.0], [99.6, 8648.0], [99.7, 8648.0], [99.8, 8648.0], [99.9, 8648.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[0.0, 5191.0], [0.1, 5191.0], [0.2, 5191.0], [0.3, 5191.0], [0.4, 5191.0], [0.5, 5191.0], [0.6, 5191.0], [0.7, 5191.0], [0.8, 5191.0], [0.9, 5191.0], [1.0, 5191.0], [1.1, 5191.0], [1.2, 5191.0], [1.3, 5191.0], [1.4, 5191.0], [1.5, 5191.0], [1.6, 5191.0], [1.7, 5191.0], [1.8, 5191.0], [1.9, 5191.0], [2.0, 5191.0], [2.1, 5191.0], [2.2, 5191.0], [2.3, 5191.0], [2.4, 5191.0], [2.5, 5191.0], [2.6, 5191.0], [2.7, 5191.0], [2.8, 5191.0], [2.9, 5191.0], [3.0, 5191.0], [3.1, 5191.0], [3.2, 5191.0], [3.3, 5191.0], [3.4, 5255.0], [3.5, 5255.0], [3.6, 5255.0], [3.7, 5255.0], [3.8, 5255.0], [3.9, 5255.0], [4.0, 5255.0], [4.1, 5255.0], [4.2, 5255.0], [4.3, 5255.0], [4.4, 5255.0], [4.5, 5255.0], [4.6, 5255.0], [4.7, 5255.0], [4.8, 5255.0], [4.9, 5255.0], [5.0, 5255.0], [5.1, 5255.0], [5.2, 5255.0], [5.3, 5255.0], [5.4, 5255.0], [5.5, 5255.0], [5.6, 5255.0], [5.7, 5255.0], [5.8, 5255.0], [5.9, 5255.0], [6.0, 5255.0], [6.1, 5255.0], [6.2, 5255.0], [6.3, 5255.0], [6.4, 5255.0], [6.5, 5255.0], [6.6, 5255.0], [6.7, 5526.0], [6.8, 5526.0], [6.9, 5526.0], [7.0, 5526.0], [7.1, 5526.0], [7.2, 5526.0], [7.3, 5526.0], [7.4, 5526.0], [7.5, 5526.0], [7.6, 5526.0], [7.7, 5526.0], [7.8, 5526.0], [7.9, 5526.0], [8.0, 5526.0], [8.1, 5526.0], [8.2, 5526.0], [8.3, 5526.0], [8.4, 5526.0], [8.5, 5526.0], [8.6, 5526.0], [8.7, 5526.0], [8.8, 5526.0], [8.9, 5526.0], [9.0, 5526.0], [9.1, 5526.0], [9.2, 5526.0], [9.3, 5526.0], [9.4, 5526.0], [9.5, 5526.0], [9.6, 5526.0], [9.7, 5526.0], [9.8, 5526.0], [9.9, 5526.0], [10.0, 5530.0], [10.1, 5530.0], [10.2, 5530.0], [10.3, 5530.0], [10.4, 5530.0], [10.5, 5530.0], [10.6, 5530.0], [10.7, 5530.0], [10.8, 5530.0], [10.9, 5530.0], [11.0, 5530.0], [11.1, 5530.0], [11.2, 5530.0], [11.3, 5530.0], [11.4, 5530.0], [11.5, 5530.0], [11.6, 5530.0], [11.7, 5530.0], [11.8, 5530.0], [11.9, 5530.0], [12.0, 5530.0], [12.1, 5530.0], [12.2, 5530.0], [12.3, 5530.0], [12.4, 5530.0], [12.5, 5530.0], [12.6, 5530.0], [12.7, 5530.0], [12.8, 5530.0], [12.9, 5530.0], [13.0, 5530.0], [13.1, 5530.0], [13.2, 5530.0], [13.3, 5530.0], [13.4, 5866.0], [13.5, 5866.0], [13.6, 5866.0], [13.7, 5866.0], [13.8, 5866.0], [13.9, 5866.0], [14.0, 5866.0], [14.1, 5866.0], [14.2, 5866.0], [14.3, 5866.0], [14.4, 5866.0], [14.5, 5866.0], [14.6, 5866.0], [14.7, 5866.0], [14.8, 5866.0], [14.9, 5866.0], [15.0, 5866.0], [15.1, 5866.0], [15.2, 5866.0], [15.3, 5866.0], [15.4, 5866.0], [15.5, 5866.0], [15.6, 5866.0], [15.7, 5866.0], [15.8, 5866.0], [15.9, 5866.0], [16.0, 5866.0], [16.1, 5866.0], [16.2, 5866.0], [16.3, 5866.0], [16.4, 5866.0], [16.5, 5866.0], [16.6, 5866.0], [16.7, 5899.0], [16.8, 5899.0], [16.9, 5899.0], [17.0, 5899.0], [17.1, 5899.0], [17.2, 5899.0], [17.3, 5899.0], [17.4, 5899.0], [17.5, 5899.0], [17.6, 5899.0], [17.7, 5899.0], [17.8, 5899.0], [17.9, 5899.0], [18.0, 5899.0], [18.1, 5899.0], [18.2, 5899.0], [18.3, 5899.0], [18.4, 5899.0], [18.5, 5899.0], [18.6, 5899.0], [18.7, 5899.0], [18.8, 5899.0], [18.9, 5899.0], [19.0, 5899.0], [19.1, 5899.0], [19.2, 5899.0], [19.3, 5899.0], [19.4, 5899.0], [19.5, 5899.0], [19.6, 5899.0], [19.7, 5899.0], [19.8, 5899.0], [19.9, 5899.0], [20.0, 6092.0], [20.1, 6092.0], [20.2, 6092.0], [20.3, 6092.0], [20.4, 6092.0], [20.5, 6092.0], [20.6, 6092.0], [20.7, 6092.0], [20.8, 6092.0], [20.9, 6092.0], [21.0, 6092.0], [21.1, 6092.0], [21.2, 6092.0], [21.3, 6092.0], [21.4, 6092.0], [21.5, 6092.0], [21.6, 6092.0], [21.7, 6092.0], [21.8, 6092.0], [21.9, 6092.0], [22.0, 6092.0], [22.1, 6092.0], [22.2, 6092.0], [22.3, 6092.0], [22.4, 6092.0], [22.5, 6092.0], [22.6, 6092.0], [22.7, 6092.0], [22.8, 6092.0], [22.9, 6092.0], [23.0, 6092.0], [23.1, 6092.0], [23.2, 6092.0], [23.3, 6092.0], [23.4, 6118.0], [23.5, 6118.0], [23.6, 6118.0], [23.7, 6118.0], [23.8, 6118.0], [23.9, 6118.0], [24.0, 6118.0], [24.1, 6118.0], [24.2, 6118.0], [24.3, 6118.0], [24.4, 6118.0], [24.5, 6118.0], [24.6, 6118.0], [24.7, 6118.0], [24.8, 6118.0], [24.9, 6118.0], [25.0, 6118.0], [25.1, 6118.0], [25.2, 6118.0], [25.3, 6118.0], [25.4, 6118.0], [25.5, 6118.0], [25.6, 6118.0], [25.7, 6118.0], [25.8, 6118.0], [25.9, 6118.0], [26.0, 6118.0], [26.1, 6118.0], [26.2, 6118.0], [26.3, 6118.0], [26.4, 6118.0], [26.5, 6118.0], [26.6, 6118.0], [26.7, 6120.0], [26.8, 6120.0], [26.9, 6120.0], [27.0, 6120.0], [27.1, 6120.0], [27.2, 6120.0], [27.3, 6120.0], [27.4, 6120.0], [27.5, 6120.0], [27.6, 6120.0], [27.7, 6120.0], [27.8, 6120.0], [27.9, 6120.0], [28.0, 6120.0], [28.1, 6120.0], [28.2, 6120.0], [28.3, 6120.0], [28.4, 6120.0], [28.5, 6120.0], [28.6, 6120.0], [28.7, 6120.0], [28.8, 6120.0], [28.9, 6120.0], [29.0, 6120.0], [29.1, 6120.0], [29.2, 6120.0], [29.3, 6120.0], [29.4, 6120.0], [29.5, 6120.0], [29.6, 6120.0], [29.7, 6120.0], [29.8, 6120.0], [29.9, 6120.0], [30.0, 6126.0], [30.1, 6126.0], [30.2, 6126.0], [30.3, 6126.0], [30.4, 6126.0], [30.5, 6126.0], [30.6, 6126.0], [30.7, 6126.0], [30.8, 6126.0], [30.9, 6126.0], [31.0, 6126.0], [31.1, 6126.0], [31.2, 6126.0], [31.3, 6126.0], [31.4, 6126.0], [31.5, 6126.0], [31.6, 6126.0], [31.7, 6126.0], [31.8, 6126.0], [31.9, 6126.0], [32.0, 6126.0], [32.1, 6126.0], [32.2, 6126.0], [32.3, 6126.0], [32.4, 6126.0], [32.5, 6126.0], [32.6, 6126.0], [32.7, 6126.0], [32.8, 6126.0], [32.9, 6126.0], [33.0, 6126.0], [33.1, 6126.0], [33.2, 6126.0], [33.3, 6126.0], [33.4, 6203.0], [33.5, 6203.0], [33.6, 6203.0], [33.7, 6203.0], [33.8, 6203.0], [33.9, 6203.0], [34.0, 6203.0], [34.1, 6203.0], [34.2, 6203.0], [34.3, 6203.0], [34.4, 6203.0], [34.5, 6203.0], [34.6, 6203.0], [34.7, 6203.0], [34.8, 6203.0], [34.9, 6203.0], [35.0, 6203.0], [35.1, 6203.0], [35.2, 6203.0], [35.3, 6203.0], [35.4, 6203.0], [35.5, 6203.0], [35.6, 6203.0], [35.7, 6203.0], [35.8, 6203.0], [35.9, 6203.0], [36.0, 6203.0], [36.1, 6203.0], [36.2, 6203.0], [36.3, 6203.0], [36.4, 6203.0], [36.5, 6203.0], [36.6, 6203.0], [36.7, 6274.0], [36.8, 6274.0], [36.9, 6274.0], [37.0, 6274.0], [37.1, 6274.0], [37.2, 6274.0], [37.3, 6274.0], [37.4, 6274.0], [37.5, 6274.0], [37.6, 6274.0], [37.7, 6274.0], [37.8, 6274.0], [37.9, 6274.0], [38.0, 6274.0], [38.1, 6274.0], [38.2, 6274.0], [38.3, 6274.0], [38.4, 6274.0], [38.5, 6274.0], [38.6, 6274.0], [38.7, 6274.0], [38.8, 6274.0], [38.9, 6274.0], [39.0, 6274.0], [39.1, 6274.0], [39.2, 6274.0], [39.3, 6274.0], [39.4, 6274.0], [39.5, 6274.0], [39.6, 6274.0], [39.7, 6274.0], [39.8, 6274.0], [39.9, 6274.0], [40.0, 6327.0], [40.1, 6327.0], [40.2, 6327.0], [40.3, 6327.0], [40.4, 6327.0], [40.5, 6327.0], [40.6, 6327.0], [40.7, 6327.0], [40.8, 6327.0], [40.9, 6327.0], [41.0, 6327.0], [41.1, 6327.0], [41.2, 6327.0], [41.3, 6327.0], [41.4, 6327.0], [41.5, 6327.0], [41.6, 6327.0], [41.7, 6327.0], [41.8, 6327.0], [41.9, 6327.0], [42.0, 6327.0], [42.1, 6327.0], [42.2, 6327.0], [42.3, 6327.0], [42.4, 6327.0], [42.5, 6327.0], [42.6, 6327.0], [42.7, 6327.0], [42.8, 6327.0], [42.9, 6327.0], [43.0, 6327.0], [43.1, 6327.0], [43.2, 6327.0], [43.3, 6327.0], [43.4, 6334.0], [43.5, 6334.0], [43.6, 6334.0], [43.7, 6334.0], [43.8, 6334.0], [43.9, 6334.0], [44.0, 6334.0], [44.1, 6334.0], [44.2, 6334.0], [44.3, 6334.0], [44.4, 6334.0], [44.5, 6334.0], [44.6, 6334.0], [44.7, 6334.0], [44.8, 6334.0], [44.9, 6334.0], [45.0, 6334.0], [45.1, 6334.0], [45.2, 6334.0], [45.3, 6334.0], [45.4, 6334.0], [45.5, 6334.0], [45.6, 6334.0], [45.7, 6334.0], [45.8, 6334.0], [45.9, 6334.0], [46.0, 6334.0], [46.1, 6334.0], [46.2, 6334.0], [46.3, 6334.0], [46.4, 6334.0], [46.5, 6334.0], [46.6, 6334.0], [46.7, 6351.0], [46.8, 6351.0], [46.9, 6351.0], [47.0, 6351.0], [47.1, 6351.0], [47.2, 6351.0], [47.3, 6351.0], [47.4, 6351.0], [47.5, 6351.0], [47.6, 6351.0], [47.7, 6351.0], [47.8, 6351.0], [47.9, 6351.0], [48.0, 6351.0], [48.1, 6351.0], [48.2, 6351.0], [48.3, 6351.0], [48.4, 6351.0], [48.5, 6351.0], [48.6, 6351.0], [48.7, 6351.0], [48.8, 6351.0], [48.9, 6351.0], [49.0, 6351.0], [49.1, 6351.0], [49.2, 6351.0], [49.3, 6351.0], [49.4, 6351.0], [49.5, 6351.0], [49.6, 6351.0], [49.7, 6351.0], [49.8, 6351.0], [49.9, 6351.0], [50.0, 6351.0], [50.1, 6508.0], [50.2, 6508.0], [50.3, 6508.0], [50.4, 6508.0], [50.5, 6508.0], [50.6, 6508.0], [50.7, 6508.0], [50.8, 6508.0], [50.9, 6508.0], [51.0, 6508.0], [51.1, 6508.0], [51.2, 6508.0], [51.3, 6508.0], [51.4, 6508.0], [51.5, 6508.0], [51.6, 6508.0], [51.7, 6508.0], [51.8, 6508.0], [51.9, 6508.0], [52.0, 6508.0], [52.1, 6508.0], [52.2, 6508.0], [52.3, 6508.0], [52.4, 6508.0], [52.5, 6508.0], [52.6, 6508.0], [52.7, 6508.0], [52.8, 6508.0], [52.9, 6508.0], [53.0, 6508.0], [53.1, 6508.0], [53.2, 6508.0], [53.3, 6508.0], [53.4, 6764.0], [53.5, 6764.0], [53.6, 6764.0], [53.7, 6764.0], [53.8, 6764.0], [53.9, 6764.0], [54.0, 6764.0], [54.1, 6764.0], [54.2, 6764.0], [54.3, 6764.0], [54.4, 6764.0], [54.5, 6764.0], [54.6, 6764.0], [54.7, 6764.0], [54.8, 6764.0], [54.9, 6764.0], [55.0, 6764.0], [55.1, 6764.0], [55.2, 6764.0], [55.3, 6764.0], [55.4, 6764.0], [55.5, 6764.0], [55.6, 6764.0], [55.7, 6764.0], [55.8, 6764.0], [55.9, 6764.0], [56.0, 6764.0], [56.1, 6764.0], [56.2, 6764.0], [56.3, 6764.0], [56.4, 6764.0], [56.5, 6764.0], [56.6, 6764.0], [56.7, 6867.0], [56.8, 6867.0], [56.9, 6867.0], [57.0, 6867.0], [57.1, 6867.0], [57.2, 6867.0], [57.3, 6867.0], [57.4, 6867.0], [57.5, 6867.0], [57.6, 6867.0], [57.7, 6867.0], [57.8, 6867.0], [57.9, 6867.0], [58.0, 6867.0], [58.1, 6867.0], [58.2, 6867.0], [58.3, 6867.0], [58.4, 6867.0], [58.5, 6867.0], [58.6, 6867.0], [58.7, 6867.0], [58.8, 6867.0], [58.9, 6867.0], [59.0, 6867.0], [59.1, 6867.0], [59.2, 6867.0], [59.3, 6867.0], [59.4, 6867.0], [59.5, 6867.0], [59.6, 6867.0], [59.7, 6867.0], [59.8, 6867.0], [59.9, 6867.0], [60.0, 6867.0], [60.1, 7416.0], [60.2, 7416.0], [60.3, 7416.0], [60.4, 7416.0], [60.5, 7416.0], [60.6, 7416.0], [60.7, 7416.0], [60.8, 7416.0], [60.9, 7416.0], [61.0, 7416.0], [61.1, 7416.0], [61.2, 7416.0], [61.3, 7416.0], [61.4, 7416.0], [61.5, 7416.0], [61.6, 7416.0], [61.7, 7416.0], [61.8, 7416.0], [61.9, 7416.0], [62.0, 7416.0], [62.1, 7416.0], [62.2, 7416.0], [62.3, 7416.0], [62.4, 7416.0], [62.5, 7416.0], [62.6, 7416.0], [62.7, 7416.0], [62.8, 7416.0], [62.9, 7416.0], [63.0, 7416.0], [63.1, 7416.0], [63.2, 7416.0], [63.3, 7416.0], [63.4, 7464.0], [63.5, 7464.0], [63.6, 7464.0], [63.7, 7464.0], [63.8, 7464.0], [63.9, 7464.0], [64.0, 7464.0], [64.1, 7464.0], [64.2, 7464.0], [64.3, 7464.0], [64.4, 7464.0], [64.5, 7464.0], [64.6, 7464.0], [64.7, 7464.0], [64.8, 7464.0], [64.9, 7464.0], [65.0, 7464.0], [65.1, 7464.0], [65.2, 7464.0], [65.3, 7464.0], [65.4, 7464.0], [65.5, 7464.0], [65.6, 7464.0], [65.7, 7464.0], [65.8, 7464.0], [65.9, 7464.0], [66.0, 7464.0], [66.1, 7464.0], [66.2, 7464.0], [66.3, 7464.0], [66.4, 7464.0], [66.5, 7464.0], [66.6, 7464.0], [66.7, 7480.0], [66.8, 7480.0], [66.9, 7480.0], [67.0, 7480.0], [67.1, 7480.0], [67.2, 7480.0], [67.3, 7480.0], [67.4, 7480.0], [67.5, 7480.0], [67.6, 7480.0], [67.7, 7480.0], [67.8, 7480.0], [67.9, 7480.0], [68.0, 7480.0], [68.1, 7480.0], [68.2, 7480.0], [68.3, 7480.0], [68.4, 7480.0], [68.5, 7480.0], [68.6, 7480.0], [68.7, 7480.0], [68.8, 7480.0], [68.9, 7480.0], [69.0, 7480.0], [69.1, 7480.0], [69.2, 7480.0], [69.3, 7480.0], [69.4, 7480.0], [69.5, 7480.0], [69.6, 7480.0], [69.7, 7480.0], [69.8, 7480.0], [69.9, 7480.0], [70.0, 7480.0], [70.1, 7721.0], [70.2, 7721.0], [70.3, 7721.0], [70.4, 7721.0], [70.5, 7721.0], [70.6, 7721.0], [70.7, 7721.0], [70.8, 7721.0], [70.9, 7721.0], [71.0, 7721.0], [71.1, 7721.0], [71.2, 7721.0], [71.3, 7721.0], [71.4, 7721.0], [71.5, 7721.0], [71.6, 7721.0], [71.7, 7721.0], [71.8, 7721.0], [71.9, 7721.0], [72.0, 7721.0], [72.1, 7721.0], [72.2, 7721.0], [72.3, 7721.0], [72.4, 7721.0], [72.5, 7721.0], [72.6, 7721.0], [72.7, 7721.0], [72.8, 7721.0], [72.9, 7721.0], [73.0, 7721.0], [73.1, 7721.0], [73.2, 7721.0], [73.3, 7721.0], [73.4, 7869.0], [73.5, 7869.0], [73.6, 7869.0], [73.7, 7869.0], [73.8, 7869.0], [73.9, 7869.0], [74.0, 7869.0], [74.1, 7869.0], [74.2, 7869.0], [74.3, 7869.0], [74.4, 7869.0], [74.5, 7869.0], [74.6, 7869.0], [74.7, 7869.0], [74.8, 7869.0], [74.9, 7869.0], [75.0, 7869.0], [75.1, 7869.0], [75.2, 7869.0], [75.3, 7869.0], [75.4, 7869.0], [75.5, 7869.0], [75.6, 7869.0], [75.7, 7869.0], [75.8, 7869.0], [75.9, 7869.0], [76.0, 7869.0], [76.1, 7869.0], [76.2, 7869.0], [76.3, 7869.0], [76.4, 7869.0], [76.5, 7869.0], [76.6, 7869.0], [76.7, 7890.0], [76.8, 7890.0], [76.9, 7890.0], [77.0, 7890.0], [77.1, 7890.0], [77.2, 7890.0], [77.3, 7890.0], [77.4, 7890.0], [77.5, 7890.0], [77.6, 7890.0], [77.7, 7890.0], [77.8, 7890.0], [77.9, 7890.0], [78.0, 7890.0], [78.1, 7890.0], [78.2, 7890.0], [78.3, 7890.0], [78.4, 7890.0], [78.5, 7890.0], [78.6, 7890.0], [78.7, 7890.0], [78.8, 7890.0], [78.9, 7890.0], [79.0, 7890.0], [79.1, 7890.0], [79.2, 7890.0], [79.3, 7890.0], [79.4, 7890.0], [79.5, 7890.0], [79.6, 7890.0], [79.7, 7890.0], [79.8, 7890.0], [79.9, 7890.0], [80.0, 7902.0], [80.1, 7902.0], [80.2, 7902.0], [80.3, 7902.0], [80.4, 7902.0], [80.5, 7902.0], [80.6, 7902.0], [80.7, 7902.0], [80.8, 7902.0], [80.9, 7902.0], [81.0, 7902.0], [81.1, 7902.0], [81.2, 7902.0], [81.3, 7902.0], [81.4, 7902.0], [81.5, 7902.0], [81.6, 7902.0], [81.7, 7902.0], [81.8, 7902.0], [81.9, 7902.0], [82.0, 7902.0], [82.1, 7902.0], [82.2, 7902.0], [82.3, 7902.0], [82.4, 7902.0], [82.5, 7902.0], [82.6, 7902.0], [82.7, 7902.0], [82.8, 7902.0], [82.9, 7902.0], [83.0, 7902.0], [83.1, 7902.0], [83.2, 7902.0], [83.3, 7902.0], [83.4, 7902.0], [83.5, 7902.0], [83.6, 7902.0], [83.7, 7902.0], [83.8, 7902.0], [83.9, 7902.0], [84.0, 7902.0], [84.1, 7902.0], [84.2, 7902.0], [84.3, 7902.0], [84.4, 7902.0], [84.5, 7902.0], [84.6, 7902.0], [84.7, 7902.0], [84.8, 7902.0], [84.9, 7902.0], [85.0, 7902.0], [85.1, 7902.0], [85.2, 7902.0], [85.3, 7902.0], [85.4, 7902.0], [85.5, 7902.0], [85.6, 7902.0], [85.7, 7902.0], [85.8, 7902.0], [85.9, 7902.0], [86.0, 7902.0], [86.1, 7902.0], [86.2, 7902.0], [86.3, 7902.0], [86.4, 7902.0], [86.5, 7902.0], [86.6, 7902.0], [86.7, 7943.0], [86.8, 7943.0], [86.9, 7943.0], [87.0, 7943.0], [87.1, 7943.0], [87.2, 7943.0], [87.3, 7943.0], [87.4, 7943.0], [87.5, 7943.0], [87.6, 7943.0], [87.7, 7943.0], [87.8, 7943.0], [87.9, 7943.0], [88.0, 7943.0], [88.1, 7943.0], [88.2, 7943.0], [88.3, 7943.0], [88.4, 7943.0], [88.5, 7943.0], [88.6, 7943.0], [88.7, 7943.0], [88.8, 7943.0], [88.9, 7943.0], [89.0, 7943.0], [89.1, 7943.0], [89.2, 7943.0], [89.3, 7943.0], [89.4, 7943.0], [89.5, 7943.0], [89.6, 7943.0], [89.7, 7943.0], [89.8, 7943.0], [89.9, 7943.0], [90.0, 8141.0], [90.1, 8141.0], [90.2, 8141.0], [90.3, 8141.0], [90.4, 8141.0], [90.5, 8141.0], [90.6, 8141.0], [90.7, 8141.0], [90.8, 8141.0], [90.9, 8141.0], [91.0, 8141.0], [91.1, 8141.0], [91.2, 8141.0], [91.3, 8141.0], [91.4, 8141.0], [91.5, 8141.0], [91.6, 8141.0], [91.7, 8141.0], [91.8, 8141.0], [91.9, 8141.0], [92.0, 8141.0], [92.1, 8141.0], [92.2, 8141.0], [92.3, 8141.0], [92.4, 8141.0], [92.5, 8141.0], [92.6, 8141.0], [92.7, 8141.0], [92.8, 8141.0], [92.9, 8141.0], [93.0, 8141.0], [93.1, 8141.0], [93.2, 8141.0], [93.3, 8141.0], [93.4, 8332.0], [93.5, 8332.0], [93.6, 8332.0], [93.7, 8332.0], [93.8, 8332.0], [93.9, 8332.0], [94.0, 8332.0], [94.1, 8332.0], [94.2, 8332.0], [94.3, 8332.0], [94.4, 8332.0], [94.5, 8332.0], [94.6, 8332.0], [94.7, 8332.0], [94.8, 8332.0], [94.9, 8332.0], [95.0, 8332.0], [95.1, 8332.0], [95.2, 8332.0], [95.3, 8332.0], [95.4, 8332.0], [95.5, 8332.0], [95.6, 8332.0], [95.7, 8332.0], [95.8, 8332.0], [95.9, 8332.0], [96.0, 8332.0], [96.1, 8332.0], [96.2, 8332.0], [96.3, 8332.0], [96.4, 8332.0], [96.5, 8332.0], [96.6, 8332.0], [96.7, 8337.0], [96.8, 8337.0], [96.9, 8337.0], [97.0, 8337.0], [97.1, 8337.0], [97.2, 8337.0], [97.3, 8337.0], [97.4, 8337.0], [97.5, 8337.0], [97.6, 8337.0], [97.7, 8337.0], [97.8, 8337.0], [97.9, 8337.0], [98.0, 8337.0], [98.1, 8337.0], [98.2, 8337.0], [98.3, 8337.0], [98.4, 8337.0], [98.5, 8337.0], [98.6, 8337.0], [98.7, 8337.0], [98.8, 8337.0], [98.9, 8337.0], [99.0, 8337.0], [99.1, 8337.0], [99.2, 8337.0], [99.3, 8337.0], [99.4, 8337.0], [99.5, 8337.0], [99.6, 8337.0], [99.7, 8337.0], [99.8, 8337.0], [99.9, 8337.0]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[0.0, 9.0], [0.1, 9.0], [0.2, 9.0], [0.3, 9.0], [0.4, 9.0], [0.5, 9.0], [0.6, 9.0], [0.7, 9.0], [0.8, 9.0], [0.9, 9.0], [1.0, 9.0], [1.1, 9.0], [1.2, 9.0], [1.3, 9.0], [1.4, 9.0], [1.5, 9.0], [1.6, 9.0], [1.7, 9.0], [1.8, 9.0], [1.9, 9.0], [2.0, 9.0], [2.1, 9.0], [2.2, 9.0], [2.3, 9.0], [2.4, 9.0], [2.5, 9.0], [2.6, 9.0], [2.7, 9.0], [2.8, 9.0], [2.9, 9.0], [3.0, 9.0], [3.1, 9.0], [3.2, 9.0], [3.3, 9.0], [3.4, 9.0], [3.5, 9.0], [3.6, 9.0], [3.7, 9.0], [3.8, 9.0], [3.9, 9.0], [4.0, 9.0], [4.1, 9.0], [4.2, 9.0], [4.3, 9.0], [4.4, 9.0], [4.5, 9.0], [4.6, 9.0], [4.7, 9.0], [4.8, 9.0], [4.9, 9.0], [5.0, 9.0], [5.1, 9.0], [5.2, 9.0], [5.3, 9.0], [5.4, 9.0], [5.5, 9.0], [5.6, 9.0], [5.7, 9.0], [5.8, 9.0], [5.9, 9.0], [6.0, 9.0], [6.1, 9.0], [6.2, 9.0], [6.3, 9.0], [6.4, 9.0], [6.5, 9.0], [6.6, 9.0], [6.7, 9.0], [6.8, 9.0], [6.9, 9.0], [7.0, 9.0], [7.1, 9.0], [7.2, 9.0], [7.3, 9.0], [7.4, 9.0], [7.5, 9.0], [7.6, 9.0], [7.7, 9.0], [7.8, 9.0], [7.9, 9.0], [8.0, 9.0], [8.1, 9.0], [8.2, 9.0], [8.3, 9.0], [8.4, 9.0], [8.5, 9.0], [8.6, 9.0], [8.7, 9.0], [8.8, 9.0], [8.9, 9.0], [9.0, 9.0], [9.1, 9.0], [9.2, 9.0], [9.3, 9.0], [9.4, 9.0], [9.5, 9.0], [9.6, 9.0], [9.7, 9.0], [9.8, 9.0], [9.9, 9.0], [10.0, 9.0], [10.1, 9.0], [10.2, 9.0], [10.3, 9.0], [10.4, 9.0], [10.5, 9.0], [10.6, 9.0], [10.7, 9.0], [10.8, 9.0], [10.9, 9.0], [11.0, 9.0], [11.1, 9.0], [11.2, 9.0], [11.3, 9.0], [11.4, 9.0], [11.5, 9.0], [11.6, 9.0], [11.7, 9.0], [11.8, 9.0], [11.9, 9.0], [12.0, 9.0], [12.1, 9.0], [12.2, 9.0], [12.3, 9.0], [12.4, 9.0], [12.5, 9.0], [12.6, 9.0], [12.7, 9.0], [12.8, 9.0], [12.9, 9.0], [13.0, 9.0], [13.1, 9.0], [13.2, 9.0], [13.3, 9.0], [13.4, 9.0], [13.5, 9.0], [13.6, 9.0], [13.7, 9.0], [13.8, 9.0], [13.9, 9.0], [14.0, 9.0], [14.1, 9.0], [14.2, 9.0], [14.3, 9.0], [14.4, 9.0], [14.5, 9.0], [14.6, 9.0], [14.7, 9.0], [14.8, 9.0], [14.9, 9.0], [15.0, 9.0], [15.1, 9.0], [15.2, 9.0], [15.3, 9.0], [15.4, 9.0], [15.5, 9.0], [15.6, 9.0], [15.7, 9.0], [15.8, 9.0], [15.9, 9.0], [16.0, 9.0], [16.1, 9.0], [16.2, 9.0], [16.3, 9.0], [16.4, 9.0], [16.5, 9.0], [16.6, 9.0], [16.7, 9.0], [16.8, 9.0], [16.9, 9.0], [17.0, 9.0], [17.1, 9.0], [17.2, 9.0], [17.3, 9.0], [17.4, 9.0], [17.5, 9.0], [17.6, 9.0], [17.7, 9.0], [17.8, 9.0], [17.9, 9.0], [18.0, 9.0], [18.1, 9.0], [18.2, 9.0], [18.3, 9.0], [18.4, 9.0], [18.5, 9.0], [18.6, 9.0], [18.7, 9.0], [18.8, 9.0], [18.9, 9.0], [19.0, 9.0], [19.1, 9.0], [19.2, 9.0], [19.3, 9.0], [19.4, 9.0], [19.5, 9.0], [19.6, 9.0], [19.7, 9.0], [19.8, 9.0], [19.9, 9.0], [20.0, 9.0], [20.1, 9.0], [20.2, 9.0], [20.3, 9.0], [20.4, 9.0], [20.5, 9.0], [20.6, 9.0], [20.7, 9.0], [20.8, 9.0], [20.9, 9.0], [21.0, 9.0], [21.1, 9.0], [21.2, 9.0], [21.3, 9.0], [21.4, 9.0], [21.5, 9.0], [21.6, 9.0], [21.7, 9.0], [21.8, 9.0], [21.9, 9.0], [22.0, 9.0], [22.1, 9.0], [22.2, 9.0], [22.3, 9.0], [22.4, 9.0], [22.5, 9.0], [22.6, 9.0], [22.7, 9.0], [22.8, 9.0], [22.9, 9.0], [23.0, 9.0], [23.1, 9.0], [23.2, 9.0], [23.3, 9.0], [23.4, 9.0], [23.5, 9.0], [23.6, 9.0], [23.7, 9.0], [23.8, 9.0], [23.9, 9.0], [24.0, 9.0], [24.1, 9.0], [24.2, 9.0], [24.3, 9.0], [24.4, 9.0], [24.5, 9.0], [24.6, 9.0], [24.7, 9.0], [24.8, 9.0], [24.9, 9.0], [25.0, 9.0], [25.1, 9.0], [25.2, 9.0], [25.3, 9.0], [25.4, 9.0], [25.5, 9.0], [25.6, 9.0], [25.7, 9.0], [25.8, 9.0], [25.9, 9.0], [26.0, 9.0], [26.1, 9.0], [26.2, 9.0], [26.3, 9.0], [26.4, 9.0], [26.5, 9.0], [26.6, 9.0], [26.7, 9.0], [26.8, 9.0], [26.9, 9.0], [27.0, 9.0], [27.1, 9.0], [27.2, 9.0], [27.3, 9.0], [27.4, 9.0], [27.5, 9.0], [27.6, 9.0], [27.7, 9.0], [27.8, 9.0], [27.9, 9.0], [28.0, 9.0], [28.1, 9.0], [28.2, 9.0], [28.3, 9.0], [28.4, 9.0], [28.5, 9.0], [28.6, 9.0], [28.7, 9.0], [28.8, 9.0], [28.9, 9.0], [29.0, 9.0], [29.1, 9.0], [29.2, 9.0], [29.3, 9.0], [29.4, 9.0], [29.5, 9.0], [29.6, 9.0], [29.7, 9.0], [29.8, 9.0], [29.9, 9.0], [30.0, 10.0], [30.1, 10.0], [30.2, 10.0], [30.3, 10.0], [30.4, 10.0], [30.5, 10.0], [30.6, 10.0], [30.7, 10.0], [30.8, 10.0], [30.9, 10.0], [31.0, 10.0], [31.1, 10.0], [31.2, 10.0], [31.3, 10.0], [31.4, 10.0], [31.5, 10.0], [31.6, 10.0], [31.7, 10.0], [31.8, 10.0], [31.9, 10.0], [32.0, 10.0], [32.1, 10.0], [32.2, 10.0], [32.3, 10.0], [32.4, 10.0], [32.5, 10.0], [32.6, 10.0], [32.7, 10.0], [32.8, 10.0], [32.9, 10.0], [33.0, 10.0], [33.1, 10.0], [33.2, 10.0], [33.3, 10.0], [33.4, 10.0], [33.5, 10.0], [33.6, 10.0], [33.7, 10.0], [33.8, 10.0], [33.9, 10.0], [34.0, 10.0], [34.1, 10.0], [34.2, 10.0], [34.3, 10.0], [34.4, 10.0], [34.5, 10.0], [34.6, 10.0], [34.7, 10.0], [34.8, 10.0], [34.9, 10.0], [35.0, 10.0], [35.1, 10.0], [35.2, 10.0], [35.3, 10.0], [35.4, 10.0], [35.5, 10.0], [35.6, 10.0], [35.7, 10.0], [35.8, 10.0], [35.9, 10.0], [36.0, 10.0], [36.1, 10.0], [36.2, 10.0], [36.3, 10.0], [36.4, 10.0], [36.5, 10.0], [36.6, 10.0], [36.7, 10.0], [36.8, 10.0], [36.9, 10.0], [37.0, 10.0], [37.1, 10.0], [37.2, 10.0], [37.3, 10.0], [37.4, 10.0], [37.5, 10.0], [37.6, 10.0], [37.7, 10.0], [37.8, 10.0], [37.9, 10.0], [38.0, 10.0], [38.1, 10.0], [38.2, 10.0], [38.3, 10.0], [38.4, 10.0], [38.5, 10.0], [38.6, 10.0], [38.7, 10.0], [38.8, 10.0], [38.9, 10.0], [39.0, 10.0], [39.1, 10.0], [39.2, 10.0], [39.3, 10.0], [39.4, 10.0], [39.5, 10.0], [39.6, 10.0], [39.7, 10.0], [39.8, 10.0], [39.9, 10.0], [40.0, 11.0], [40.1, 11.0], [40.2, 11.0], [40.3, 11.0], [40.4, 11.0], [40.5, 11.0], [40.6, 11.0], [40.7, 11.0], [40.8, 11.0], [40.9, 11.0], [41.0, 11.0], [41.1, 11.0], [41.2, 11.0], [41.3, 11.0], [41.4, 11.0], [41.5, 11.0], [41.6, 11.0], [41.7, 11.0], [41.8, 11.0], [41.9, 11.0], [42.0, 11.0], [42.1, 11.0], [42.2, 11.0], [42.3, 11.0], [42.4, 11.0], [42.5, 11.0], [42.6, 11.0], [42.7, 11.0], [42.8, 11.0], [42.9, 11.0], [43.0, 11.0], [43.1, 11.0], [43.2, 11.0], [43.3, 11.0], [43.4, 11.0], [43.5, 11.0], [43.6, 11.0], [43.7, 11.0], [43.8, 11.0], [43.9, 11.0], [44.0, 11.0], [44.1, 11.0], [44.2, 11.0], [44.3, 11.0], [44.4, 11.0], [44.5, 11.0], [44.6, 11.0], [44.7, 11.0], [44.8, 11.0], [44.9, 11.0], [45.0, 11.0], [45.1, 11.0], [45.2, 11.0], [45.3, 11.0], [45.4, 11.0], [45.5, 11.0], [45.6, 11.0], [45.7, 11.0], [45.8, 11.0], [45.9, 11.0], [46.0, 11.0], [46.1, 11.0], [46.2, 11.0], [46.3, 11.0], [46.4, 11.0], [46.5, 11.0], [46.6, 11.0], [46.7, 11.0], [46.8, 11.0], [46.9, 11.0], [47.0, 11.0], [47.1, 11.0], [47.2, 11.0], [47.3, 11.0], [47.4, 11.0], [47.5, 11.0], [47.6, 11.0], [47.7, 11.0], [47.8, 11.0], [47.9, 11.0], [48.0, 11.0], [48.1, 11.0], [48.2, 11.0], [48.3, 11.0], [48.4, 11.0], [48.5, 11.0], [48.6, 11.0], [48.7, 11.0], [48.8, 11.0], [48.9, 11.0], [49.0, 11.0], [49.1, 11.0], [49.2, 11.0], [49.3, 11.0], [49.4, 11.0], [49.5, 11.0], [49.6, 11.0], [49.7, 11.0], [49.8, 11.0], [49.9, 11.0], [50.0, 11.0], [50.1, 11.0], [50.2, 11.0], [50.3, 11.0], [50.4, 11.0], [50.5, 11.0], [50.6, 11.0], [50.7, 11.0], [50.8, 11.0], [50.9, 11.0], [51.0, 11.0], [51.1, 11.0], [51.2, 11.0], [51.3, 11.0], [51.4, 11.0], [51.5, 11.0], [51.6, 11.0], [51.7, 11.0], [51.8, 11.0], [51.9, 11.0], [52.0, 11.0], [52.1, 11.0], [52.2, 11.0], [52.3, 11.0], [52.4, 11.0], [52.5, 11.0], [52.6, 11.0], [52.7, 11.0], [52.8, 11.0], [52.9, 11.0], [53.0, 11.0], [53.1, 11.0], [53.2, 11.0], [53.3, 11.0], [53.4, 11.0], [53.5, 11.0], [53.6, 11.0], [53.7, 11.0], [53.8, 11.0], [53.9, 11.0], [54.0, 11.0], [54.1, 11.0], [54.2, 11.0], [54.3, 11.0], [54.4, 11.0], [54.5, 11.0], [54.6, 11.0], [54.7, 11.0], [54.8, 11.0], [54.9, 11.0], [55.0, 11.0], [55.1, 11.0], [55.2, 11.0], [55.3, 11.0], [55.4, 11.0], [55.5, 11.0], [55.6, 11.0], [55.7, 11.0], [55.8, 11.0], [55.9, 11.0], [56.0, 11.0], [56.1, 11.0], [56.2, 11.0], [56.3, 11.0], [56.4, 11.0], [56.5, 11.0], [56.6, 11.0], [56.7, 11.0], [56.8, 11.0], [56.9, 11.0], [57.0, 11.0], [57.1, 11.0], [57.2, 11.0], [57.3, 11.0], [57.4, 11.0], [57.5, 11.0], [57.6, 11.0], [57.7, 11.0], [57.8, 11.0], [57.9, 11.0], [58.0, 11.0], [58.1, 11.0], [58.2, 11.0], [58.3, 11.0], [58.4, 11.0], [58.5, 11.0], [58.6, 11.0], [58.7, 11.0], [58.8, 11.0], [58.9, 11.0], [59.0, 11.0], [59.1, 11.0], [59.2, 11.0], [59.3, 11.0], [59.4, 11.0], [59.5, 11.0], [59.6, 11.0], [59.7, 11.0], [59.8, 11.0], [59.9, 11.0], [60.0, 15.0], [60.1, 15.0], [60.2, 15.0], [60.3, 15.0], [60.4, 15.0], [60.5, 15.0], [60.6, 15.0], [60.7, 15.0], [60.8, 15.0], [60.9, 15.0], [61.0, 15.0], [61.1, 15.0], [61.2, 15.0], [61.3, 15.0], [61.4, 15.0], [61.5, 15.0], [61.6, 15.0], [61.7, 15.0], [61.8, 15.0], [61.9, 15.0], [62.0, 15.0], [62.1, 15.0], [62.2, 15.0], [62.3, 15.0], [62.4, 15.0], [62.5, 15.0], [62.6, 15.0], [62.7, 15.0], [62.8, 15.0], [62.9, 15.0], [63.0, 15.0], [63.1, 15.0], [63.2, 15.0], [63.3, 15.0], [63.4, 15.0], [63.5, 15.0], [63.6, 15.0], [63.7, 15.0], [63.8, 15.0], [63.9, 15.0], [64.0, 15.0], [64.1, 15.0], [64.2, 15.0], [64.3, 15.0], [64.4, 15.0], [64.5, 15.0], [64.6, 15.0], [64.7, 15.0], [64.8, 15.0], [64.9, 15.0], [65.0, 15.0], [65.1, 15.0], [65.2, 15.0], [65.3, 15.0], [65.4, 15.0], [65.5, 15.0], [65.6, 15.0], [65.7, 15.0], [65.8, 15.0], [65.9, 15.0], [66.0, 15.0], [66.1, 15.0], [66.2, 15.0], [66.3, 15.0], [66.4, 15.0], [66.5, 15.0], [66.6, 15.0], [66.7, 15.0], [66.8, 15.0], [66.9, 15.0], [67.0, 15.0], [67.1, 15.0], [67.2, 15.0], [67.3, 15.0], [67.4, 15.0], [67.5, 15.0], [67.6, 15.0], [67.7, 15.0], [67.8, 15.0], [67.9, 15.0], [68.0, 15.0], [68.1, 15.0], [68.2, 15.0], [68.3, 15.0], [68.4, 15.0], [68.5, 15.0], [68.6, 15.0], [68.7, 15.0], [68.8, 15.0], [68.9, 15.0], [69.0, 15.0], [69.1, 15.0], [69.2, 15.0], [69.3, 15.0], [69.4, 15.0], [69.5, 15.0], [69.6, 15.0], [69.7, 15.0], [69.8, 15.0], [69.9, 15.0], [70.0, 18.0], [70.1, 18.0], [70.2, 18.0], [70.3, 18.0], [70.4, 18.0], [70.5, 18.0], [70.6, 18.0], [70.7, 18.0], [70.8, 18.0], [70.9, 18.0], [71.0, 18.0], [71.1, 18.0], [71.2, 18.0], [71.3, 18.0], [71.4, 18.0], [71.5, 18.0], [71.6, 18.0], [71.7, 18.0], [71.8, 18.0], [71.9, 18.0], [72.0, 18.0], [72.1, 18.0], [72.2, 18.0], [72.3, 18.0], [72.4, 18.0], [72.5, 18.0], [72.6, 18.0], [72.7, 18.0], [72.8, 18.0], [72.9, 18.0], [73.0, 18.0], [73.1, 18.0], [73.2, 18.0], [73.3, 18.0], [73.4, 18.0], [73.5, 18.0], [73.6, 18.0], [73.7, 18.0], [73.8, 18.0], [73.9, 18.0], [74.0, 18.0], [74.1, 18.0], [74.2, 18.0], [74.3, 18.0], [74.4, 18.0], [74.5, 18.0], [74.6, 18.0], [74.7, 18.0], [74.8, 18.0], [74.9, 18.0], [75.0, 18.0], [75.1, 18.0], [75.2, 18.0], [75.3, 18.0], [75.4, 18.0], [75.5, 18.0], [75.6, 18.0], [75.7, 18.0], [75.8, 18.0], [75.9, 18.0], [76.0, 18.0], [76.1, 18.0], [76.2, 18.0], [76.3, 18.0], [76.4, 18.0], [76.5, 18.0], [76.6, 18.0], [76.7, 18.0], [76.8, 18.0], [76.9, 18.0], [77.0, 18.0], [77.1, 18.0], [77.2, 18.0], [77.3, 18.0], [77.4, 18.0], [77.5, 18.0], [77.6, 18.0], [77.7, 18.0], [77.8, 18.0], [77.9, 18.0], [78.0, 18.0], [78.1, 18.0], [78.2, 18.0], [78.3, 18.0], [78.4, 18.0], [78.5, 18.0], [78.6, 18.0], [78.7, 18.0], [78.8, 18.0], [78.9, 18.0], [79.0, 18.0], [79.1, 18.0], [79.2, 18.0], [79.3, 18.0], [79.4, 18.0], [79.5, 18.0], [79.6, 18.0], [79.7, 18.0], [79.8, 18.0], [79.9, 18.0], [80.0, 26.0], [80.1, 26.0], [80.2, 26.0], [80.3, 26.0], [80.4, 26.0], [80.5, 26.0], [80.6, 26.0], [80.7, 26.0], [80.8, 26.0], [80.9, 26.0], [81.0, 26.0], [81.1, 26.0], [81.2, 26.0], [81.3, 26.0], [81.4, 26.0], [81.5, 26.0], [81.6, 26.0], [81.7, 26.0], [81.8, 26.0], [81.9, 26.0], [82.0, 26.0], [82.1, 26.0], [82.2, 26.0], [82.3, 26.0], [82.4, 26.0], [82.5, 26.0], [82.6, 26.0], [82.7, 26.0], [82.8, 26.0], [82.9, 26.0], [83.0, 26.0], [83.1, 26.0], [83.2, 26.0], [83.3, 26.0], [83.4, 26.0], [83.5, 26.0], [83.6, 26.0], [83.7, 26.0], [83.8, 26.0], [83.9, 26.0], [84.0, 26.0], [84.1, 26.0], [84.2, 26.0], [84.3, 26.0], [84.4, 26.0], [84.5, 26.0], [84.6, 26.0], [84.7, 26.0], [84.8, 26.0], [84.9, 26.0], [85.0, 26.0], [85.1, 26.0], [85.2, 26.0], [85.3, 26.0], [85.4, 26.0], [85.5, 26.0], [85.6, 26.0], [85.7, 26.0], [85.8, 26.0], [85.9, 26.0], [86.0, 26.0], [86.1, 26.0], [86.2, 26.0], [86.3, 26.0], [86.4, 26.0], [86.5, 26.0], [86.6, 26.0], [86.7, 26.0], [86.8, 26.0], [86.9, 26.0], [87.0, 26.0], [87.1, 26.0], [87.2, 26.0], [87.3, 26.0], [87.4, 26.0], [87.5, 26.0], [87.6, 26.0], [87.7, 26.0], [87.8, 26.0], [87.9, 26.0], [88.0, 26.0], [88.1, 26.0], [88.2, 26.0], [88.3, 26.0], [88.4, 26.0], [88.5, 26.0], [88.6, 26.0], [88.7, 26.0], [88.8, 26.0], [88.9, 26.0], [89.0, 26.0], [89.1, 26.0], [89.2, 26.0], [89.3, 26.0], [89.4, 26.0], [89.5, 26.0], [89.6, 26.0], [89.7, 26.0], [89.8, 26.0], [89.9, 26.0], [90.0, 37.0], [90.1, 37.0], [90.2, 37.0], [90.3, 37.0], [90.4, 37.0], [90.5, 37.0], [90.6, 37.0], [90.7, 37.0], [90.8, 37.0], [90.9, 37.0], [91.0, 37.0], [91.1, 37.0], [91.2, 37.0], [91.3, 37.0], [91.4, 37.0], [91.5, 37.0], [91.6, 37.0], [91.7, 37.0], [91.8, 37.0], [91.9, 37.0], [92.0, 37.0], [92.1, 37.0], [92.2, 37.0], [92.3, 37.0], [92.4, 37.0], [92.5, 37.0], [92.6, 37.0], [92.7, 37.0], [92.8, 37.0], [92.9, 37.0], [93.0, 37.0], [93.1, 37.0], [93.2, 37.0], [93.3, 37.0], [93.4, 37.0], [93.5, 37.0], [93.6, 37.0], [93.7, 37.0], [93.8, 37.0], [93.9, 37.0], [94.0, 37.0], [94.1, 37.0], [94.2, 37.0], [94.3, 37.0], [94.4, 37.0], [94.5, 37.0], [94.6, 37.0], [94.7, 37.0], [94.8, 37.0], [94.9, 37.0], [95.0, 37.0], [95.1, 37.0], [95.2, 37.0], [95.3, 37.0], [95.4, 37.0], [95.5, 37.0], [95.6, 37.0], [95.7, 37.0], [95.8, 37.0], [95.9, 37.0], [96.0, 37.0], [96.1, 37.0], [96.2, 37.0], [96.3, 37.0], [96.4, 37.0], [96.5, 37.0], [96.6, 37.0], [96.7, 37.0], [96.8, 37.0], [96.9, 37.0], [97.0, 37.0], [97.1, 37.0], [97.2, 37.0], [97.3, 37.0], [97.4, 37.0], [97.5, 37.0], [97.6, 37.0], [97.7, 37.0], [97.8, 37.0], [97.9, 37.0], [98.0, 37.0], [98.1, 37.0], [98.2, 37.0], [98.3, 37.0], [98.4, 37.0], [98.5, 37.0], [98.6, 37.0], [98.7, 37.0], [98.8, 37.0], [98.9, 37.0], [99.0, 37.0], [99.1, 37.0], [99.2, 37.0], [99.3, 37.0], [99.4, 37.0], [99.5, 37.0], [99.6, 37.0], [99.7, 37.0], [99.8, 37.0], [99.9, 37.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[0.0, 48.0], [0.1, 48.0], [0.2, 48.0], [0.3, 48.0], [0.4, 48.0], [0.5, 48.0], [0.6, 48.0], [0.7, 48.0], [0.8, 48.0], [0.9, 48.0], [1.0, 48.0], [1.1, 48.0], [1.2, 48.0], [1.3, 48.0], [1.4, 48.0], [1.5, 48.0], [1.6, 48.0], [1.7, 48.0], [1.8, 48.0], [1.9, 48.0], [2.0, 48.0], [2.1, 48.0], [2.2, 48.0], [2.3, 48.0], [2.4, 48.0], [2.5, 48.0], [2.6, 48.0], [2.7, 48.0], [2.8, 48.0], [2.9, 48.0], [3.0, 48.0], [3.1, 48.0], [3.2, 48.0], [3.3, 48.0], [3.4, 48.0], [3.5, 48.0], [3.6, 48.0], [3.7, 48.0], [3.8, 48.0], [3.9, 48.0], [4.0, 48.0], [4.1, 48.0], [4.2, 48.0], [4.3, 48.0], [4.4, 48.0], [4.5, 48.0], [4.6, 48.0], [4.7, 48.0], [4.8, 48.0], [4.9, 48.0], [5.0, 48.0], [5.1, 48.0], [5.2, 48.0], [5.3, 48.0], [5.4, 48.0], [5.5, 48.0], [5.6, 48.0], [5.7, 48.0], [5.8, 48.0], [5.9, 48.0], [6.0, 48.0], [6.1, 48.0], [6.2, 48.0], [6.3, 48.0], [6.4, 48.0], [6.5, 48.0], [6.6, 48.0], [6.7, 48.0], [6.8, 48.0], [6.9, 48.0], [7.0, 48.0], [7.1, 48.0], [7.2, 48.0], [7.3, 48.0], [7.4, 48.0], [7.5, 48.0], [7.6, 48.0], [7.7, 48.0], [7.8, 48.0], [7.9, 48.0], [8.0, 48.0], [8.1, 48.0], [8.2, 48.0], [8.3, 48.0], [8.4, 48.0], [8.5, 48.0], [8.6, 48.0], [8.7, 48.0], [8.8, 48.0], [8.9, 48.0], [9.0, 48.0], [9.1, 48.0], [9.2, 48.0], [9.3, 48.0], [9.4, 48.0], [9.5, 48.0], [9.6, 48.0], [9.7, 48.0], [9.8, 48.0], [9.9, 48.0], [10.0, 50.0], [10.1, 50.0], [10.2, 50.0], [10.3, 50.0], [10.4, 50.0], [10.5, 50.0], [10.6, 50.0], [10.7, 50.0], [10.8, 50.0], [10.9, 50.0], [11.0, 50.0], [11.1, 50.0], [11.2, 50.0], [11.3, 50.0], [11.4, 50.0], [11.5, 50.0], [11.6, 50.0], [11.7, 50.0], [11.8, 50.0], [11.9, 50.0], [12.0, 50.0], [12.1, 50.0], [12.2, 50.0], [12.3, 50.0], [12.4, 50.0], [12.5, 50.0], [12.6, 50.0], [12.7, 50.0], [12.8, 50.0], [12.9, 50.0], [13.0, 50.0], [13.1, 50.0], [13.2, 50.0], [13.3, 50.0], [13.4, 50.0], [13.5, 50.0], [13.6, 50.0], [13.7, 50.0], [13.8, 50.0], [13.9, 50.0], [14.0, 50.0], [14.1, 50.0], [14.2, 50.0], [14.3, 50.0], [14.4, 50.0], [14.5, 50.0], [14.6, 50.0], [14.7, 50.0], [14.8, 50.0], [14.9, 50.0], [15.0, 50.0], [15.1, 50.0], [15.2, 50.0], [15.3, 50.0], [15.4, 50.0], [15.5, 50.0], [15.6, 50.0], [15.7, 50.0], [15.8, 50.0], [15.9, 50.0], [16.0, 50.0], [16.1, 50.0], [16.2, 50.0], [16.3, 50.0], [16.4, 50.0], [16.5, 50.0], [16.6, 50.0], [16.7, 50.0], [16.8, 50.0], [16.9, 50.0], [17.0, 50.0], [17.1, 50.0], [17.2, 50.0], [17.3, 50.0], [17.4, 50.0], [17.5, 50.0], [17.6, 50.0], [17.7, 50.0], [17.8, 50.0], [17.9, 50.0], [18.0, 50.0], [18.1, 50.0], [18.2, 50.0], [18.3, 50.0], [18.4, 50.0], [18.5, 50.0], [18.6, 50.0], [18.7, 50.0], [18.8, 50.0], [18.9, 50.0], [19.0, 50.0], [19.1, 50.0], [19.2, 50.0], [19.3, 50.0], [19.4, 50.0], [19.5, 50.0], [19.6, 50.0], [19.7, 50.0], [19.8, 50.0], [19.9, 50.0], [20.0, 53.0], [20.1, 53.0], [20.2, 53.0], [20.3, 53.0], [20.4, 53.0], [20.5, 53.0], [20.6, 53.0], [20.7, 53.0], [20.8, 53.0], [20.9, 53.0], [21.0, 53.0], [21.1, 53.0], [21.2, 53.0], [21.3, 53.0], [21.4, 53.0], [21.5, 53.0], [21.6, 53.0], [21.7, 53.0], [21.8, 53.0], [21.9, 53.0], [22.0, 53.0], [22.1, 53.0], [22.2, 53.0], [22.3, 53.0], [22.4, 53.0], [22.5, 53.0], [22.6, 53.0], [22.7, 53.0], [22.8, 53.0], [22.9, 53.0], [23.0, 53.0], [23.1, 53.0], [23.2, 53.0], [23.3, 53.0], [23.4, 53.0], [23.5, 53.0], [23.6, 53.0], [23.7, 53.0], [23.8, 53.0], [23.9, 53.0], [24.0, 53.0], [24.1, 53.0], [24.2, 53.0], [24.3, 53.0], [24.4, 53.0], [24.5, 53.0], [24.6, 53.0], [24.7, 53.0], [24.8, 53.0], [24.9, 53.0], [25.0, 53.0], [25.1, 53.0], [25.2, 53.0], [25.3, 53.0], [25.4, 53.0], [25.5, 53.0], [25.6, 53.0], [25.7, 53.0], [25.8, 53.0], [25.9, 53.0], [26.0, 53.0], [26.1, 53.0], [26.2, 53.0], [26.3, 53.0], [26.4, 53.0], [26.5, 53.0], [26.6, 53.0], [26.7, 53.0], [26.8, 53.0], [26.9, 53.0], [27.0, 53.0], [27.1, 53.0], [27.2, 53.0], [27.3, 53.0], [27.4, 53.0], [27.5, 53.0], [27.6, 53.0], [27.7, 53.0], [27.8, 53.0], [27.9, 53.0], [28.0, 53.0], [28.1, 53.0], [28.2, 53.0], [28.3, 53.0], [28.4, 53.0], [28.5, 53.0], [28.6, 53.0], [28.7, 53.0], [28.8, 53.0], [28.9, 53.0], [29.0, 53.0], [29.1, 53.0], [29.2, 53.0], [29.3, 53.0], [29.4, 53.0], [29.5, 53.0], [29.6, 53.0], [29.7, 53.0], [29.8, 53.0], [29.9, 53.0], [30.0, 53.0], [30.1, 53.0], [30.2, 53.0], [30.3, 53.0], [30.4, 53.0], [30.5, 53.0], [30.6, 53.0], [30.7, 53.0], [30.8, 53.0], [30.9, 53.0], [31.0, 53.0], [31.1, 53.0], [31.2, 53.0], [31.3, 53.0], [31.4, 53.0], [31.5, 53.0], [31.6, 53.0], [31.7, 53.0], [31.8, 53.0], [31.9, 53.0], [32.0, 53.0], [32.1, 53.0], [32.2, 53.0], [32.3, 53.0], [32.4, 53.0], [32.5, 53.0], [32.6, 53.0], [32.7, 53.0], [32.8, 53.0], [32.9, 53.0], [33.0, 53.0], [33.1, 53.0], [33.2, 53.0], [33.3, 53.0], [33.4, 53.0], [33.5, 53.0], [33.6, 53.0], [33.7, 53.0], [33.8, 53.0], [33.9, 53.0], [34.0, 53.0], [34.1, 53.0], [34.2, 53.0], [34.3, 53.0], [34.4, 53.0], [34.5, 53.0], [34.6, 53.0], [34.7, 53.0], [34.8, 53.0], [34.9, 53.0], [35.0, 53.0], [35.1, 53.0], [35.2, 53.0], [35.3, 53.0], [35.4, 53.0], [35.5, 53.0], [35.6, 53.0], [35.7, 53.0], [35.8, 53.0], [35.9, 53.0], [36.0, 53.0], [36.1, 53.0], [36.2, 53.0], [36.3, 53.0], [36.4, 53.0], [36.5, 53.0], [36.6, 53.0], [36.7, 53.0], [36.8, 53.0], [36.9, 53.0], [37.0, 53.0], [37.1, 53.0], [37.2, 53.0], [37.3, 53.0], [37.4, 53.0], [37.5, 53.0], [37.6, 53.0], [37.7, 53.0], [37.8, 53.0], [37.9, 53.0], [38.0, 53.0], [38.1, 53.0], [38.2, 53.0], [38.3, 53.0], [38.4, 53.0], [38.5, 53.0], [38.6, 53.0], [38.7, 53.0], [38.8, 53.0], [38.9, 53.0], [39.0, 53.0], [39.1, 53.0], [39.2, 53.0], [39.3, 53.0], [39.4, 53.0], [39.5, 53.0], [39.6, 53.0], [39.7, 53.0], [39.8, 53.0], [39.9, 53.0], [40.0, 54.0], [40.1, 54.0], [40.2, 54.0], [40.3, 54.0], [40.4, 54.0], [40.5, 54.0], [40.6, 54.0], [40.7, 54.0], [40.8, 54.0], [40.9, 54.0], [41.0, 54.0], [41.1, 54.0], [41.2, 54.0], [41.3, 54.0], [41.4, 54.0], [41.5, 54.0], [41.6, 54.0], [41.7, 54.0], [41.8, 54.0], [41.9, 54.0], [42.0, 54.0], [42.1, 54.0], [42.2, 54.0], [42.3, 54.0], [42.4, 54.0], [42.5, 54.0], [42.6, 54.0], [42.7, 54.0], [42.8, 54.0], [42.9, 54.0], [43.0, 54.0], [43.1, 54.0], [43.2, 54.0], [43.3, 54.0], [43.4, 54.0], [43.5, 54.0], [43.6, 54.0], [43.7, 54.0], [43.8, 54.0], [43.9, 54.0], [44.0, 54.0], [44.1, 54.0], [44.2, 54.0], [44.3, 54.0], [44.4, 54.0], [44.5, 54.0], [44.6, 54.0], [44.7, 54.0], [44.8, 54.0], [44.9, 54.0], [45.0, 54.0], [45.1, 54.0], [45.2, 54.0], [45.3, 54.0], [45.4, 54.0], [45.5, 54.0], [45.6, 54.0], [45.7, 54.0], [45.8, 54.0], [45.9, 54.0], [46.0, 54.0], [46.1, 54.0], [46.2, 54.0], [46.3, 54.0], [46.4, 54.0], [46.5, 54.0], [46.6, 54.0], [46.7, 54.0], [46.8, 54.0], [46.9, 54.0], [47.0, 54.0], [47.1, 54.0], [47.2, 54.0], [47.3, 54.0], [47.4, 54.0], [47.5, 54.0], [47.6, 54.0], [47.7, 54.0], [47.8, 54.0], [47.9, 54.0], [48.0, 54.0], [48.1, 54.0], [48.2, 54.0], [48.3, 54.0], [48.4, 54.0], [48.5, 54.0], [48.6, 54.0], [48.7, 54.0], [48.8, 54.0], [48.9, 54.0], [49.0, 54.0], [49.1, 54.0], [49.2, 54.0], [49.3, 54.0], [49.4, 54.0], [49.5, 54.0], [49.6, 54.0], [49.7, 54.0], [49.8, 54.0], [49.9, 54.0], [50.0, 55.0], [50.1, 55.0], [50.2, 55.0], [50.3, 55.0], [50.4, 55.0], [50.5, 55.0], [50.6, 55.0], [50.7, 55.0], [50.8, 55.0], [50.9, 55.0], [51.0, 55.0], [51.1, 55.0], [51.2, 55.0], [51.3, 55.0], [51.4, 55.0], [51.5, 55.0], [51.6, 55.0], [51.7, 55.0], [51.8, 55.0], [51.9, 55.0], [52.0, 55.0], [52.1, 55.0], [52.2, 55.0], [52.3, 55.0], [52.4, 55.0], [52.5, 55.0], [52.6, 55.0], [52.7, 55.0], [52.8, 55.0], [52.9, 55.0], [53.0, 55.0], [53.1, 55.0], [53.2, 55.0], [53.3, 55.0], [53.4, 55.0], [53.5, 55.0], [53.6, 55.0], [53.7, 55.0], [53.8, 55.0], [53.9, 55.0], [54.0, 55.0], [54.1, 55.0], [54.2, 55.0], [54.3, 55.0], [54.4, 55.0], [54.5, 55.0], [54.6, 55.0], [54.7, 55.0], [54.8, 55.0], [54.9, 55.0], [55.0, 55.0], [55.1, 55.0], [55.2, 55.0], [55.3, 55.0], [55.4, 55.0], [55.5, 55.0], [55.6, 55.0], [55.7, 55.0], [55.8, 55.0], [55.9, 55.0], [56.0, 55.0], [56.1, 55.0], [56.2, 55.0], [56.3, 55.0], [56.4, 55.0], [56.5, 55.0], [56.6, 55.0], [56.7, 55.0], [56.8, 55.0], [56.9, 55.0], [57.0, 55.0], [57.1, 55.0], [57.2, 55.0], [57.3, 55.0], [57.4, 55.0], [57.5, 55.0], [57.6, 55.0], [57.7, 55.0], [57.8, 55.0], [57.9, 55.0], [58.0, 55.0], [58.1, 55.0], [58.2, 55.0], [58.3, 55.0], [58.4, 55.0], [58.5, 55.0], [58.6, 55.0], [58.7, 55.0], [58.8, 55.0], [58.9, 55.0], [59.0, 55.0], [59.1, 55.0], [59.2, 55.0], [59.3, 55.0], [59.4, 55.0], [59.5, 55.0], [59.6, 55.0], [59.7, 55.0], [59.8, 55.0], [59.9, 55.0], [60.0, 55.0], [60.1, 55.0], [60.2, 55.0], [60.3, 55.0], [60.4, 55.0], [60.5, 55.0], [60.6, 55.0], [60.7, 55.0], [60.8, 55.0], [60.9, 55.0], [61.0, 55.0], [61.1, 55.0], [61.2, 55.0], [61.3, 55.0], [61.4, 55.0], [61.5, 55.0], [61.6, 55.0], [61.7, 55.0], [61.8, 55.0], [61.9, 55.0], [62.0, 55.0], [62.1, 55.0], [62.2, 55.0], [62.3, 55.0], [62.4, 55.0], [62.5, 55.0], [62.6, 55.0], [62.7, 55.0], [62.8, 55.0], [62.9, 55.0], [63.0, 55.0], [63.1, 55.0], [63.2, 55.0], [63.3, 55.0], [63.4, 55.0], [63.5, 55.0], [63.6, 55.0], [63.7, 55.0], [63.8, 55.0], [63.9, 55.0], [64.0, 55.0], [64.1, 55.0], [64.2, 55.0], [64.3, 55.0], [64.4, 55.0], [64.5, 55.0], [64.6, 55.0], [64.7, 55.0], [64.8, 55.0], [64.9, 55.0], [65.0, 55.0], [65.1, 55.0], [65.2, 55.0], [65.3, 55.0], [65.4, 55.0], [65.5, 55.0], [65.6, 55.0], [65.7, 55.0], [65.8, 55.0], [65.9, 55.0], [66.0, 55.0], [66.1, 55.0], [66.2, 55.0], [66.3, 55.0], [66.4, 55.0], [66.5, 55.0], [66.6, 55.0], [66.7, 55.0], [66.8, 55.0], [66.9, 55.0], [67.0, 55.0], [67.1, 55.0], [67.2, 55.0], [67.3, 55.0], [67.4, 55.0], [67.5, 55.0], [67.6, 55.0], [67.7, 55.0], [67.8, 55.0], [67.9, 55.0], [68.0, 55.0], [68.1, 55.0], [68.2, 55.0], [68.3, 55.0], [68.4, 55.0], [68.5, 55.0], [68.6, 55.0], [68.7, 55.0], [68.8, 55.0], [68.9, 55.0], [69.0, 55.0], [69.1, 55.0], [69.2, 55.0], [69.3, 55.0], [69.4, 55.0], [69.5, 55.0], [69.6, 55.0], [69.7, 55.0], [69.8, 55.0], [69.9, 55.0], [70.0, 56.0], [70.1, 56.0], [70.2, 56.0], [70.3, 56.0], [70.4, 56.0], [70.5, 56.0], [70.6, 56.0], [70.7, 56.0], [70.8, 56.0], [70.9, 56.0], [71.0, 56.0], [71.1, 56.0], [71.2, 56.0], [71.3, 56.0], [71.4, 56.0], [71.5, 56.0], [71.6, 56.0], [71.7, 56.0], [71.8, 56.0], [71.9, 56.0], [72.0, 56.0], [72.1, 56.0], [72.2, 56.0], [72.3, 56.0], [72.4, 56.0], [72.5, 56.0], [72.6, 56.0], [72.7, 56.0], [72.8, 56.0], [72.9, 56.0], [73.0, 56.0], [73.1, 56.0], [73.2, 56.0], [73.3, 56.0], [73.4, 56.0], [73.5, 56.0], [73.6, 56.0], [73.7, 56.0], [73.8, 56.0], [73.9, 56.0], [74.0, 56.0], [74.1, 56.0], [74.2, 56.0], [74.3, 56.0], [74.4, 56.0], [74.5, 56.0], [74.6, 56.0], [74.7, 56.0], [74.8, 56.0], [74.9, 56.0], [75.0, 56.0], [75.1, 56.0], [75.2, 56.0], [75.3, 56.0], [75.4, 56.0], [75.5, 56.0], [75.6, 56.0], [75.7, 56.0], [75.8, 56.0], [75.9, 56.0], [76.0, 56.0], [76.1, 56.0], [76.2, 56.0], [76.3, 56.0], [76.4, 56.0], [76.5, 56.0], [76.6, 56.0], [76.7, 56.0], [76.8, 56.0], [76.9, 56.0], [77.0, 56.0], [77.1, 56.0], [77.2, 56.0], [77.3, 56.0], [77.4, 56.0], [77.5, 56.0], [77.6, 56.0], [77.7, 56.0], [77.8, 56.0], [77.9, 56.0], [78.0, 56.0], [78.1, 56.0], [78.2, 56.0], [78.3, 56.0], [78.4, 56.0], [78.5, 56.0], [78.6, 56.0], [78.7, 56.0], [78.8, 56.0], [78.9, 56.0], [79.0, 56.0], [79.1, 56.0], [79.2, 56.0], [79.3, 56.0], [79.4, 56.0], [79.5, 56.0], [79.6, 56.0], [79.7, 56.0], [79.8, 56.0], [79.9, 56.0], [80.0, 56.0], [80.1, 56.0], [80.2, 56.0], [80.3, 56.0], [80.4, 56.0], [80.5, 56.0], [80.6, 56.0], [80.7, 56.0], [80.8, 56.0], [80.9, 56.0], [81.0, 56.0], [81.1, 56.0], [81.2, 56.0], [81.3, 56.0], [81.4, 56.0], [81.5, 56.0], [81.6, 56.0], [81.7, 56.0], [81.8, 56.0], [81.9, 56.0], [82.0, 56.0], [82.1, 56.0], [82.2, 56.0], [82.3, 56.0], [82.4, 56.0], [82.5, 56.0], [82.6, 56.0], [82.7, 56.0], [82.8, 56.0], [82.9, 56.0], [83.0, 56.0], [83.1, 56.0], [83.2, 56.0], [83.3, 56.0], [83.4, 56.0], [83.5, 56.0], [83.6, 56.0], [83.7, 56.0], [83.8, 56.0], [83.9, 56.0], [84.0, 56.0], [84.1, 56.0], [84.2, 56.0], [84.3, 56.0], [84.4, 56.0], [84.5, 56.0], [84.6, 56.0], [84.7, 56.0], [84.8, 56.0], [84.9, 56.0], [85.0, 56.0], [85.1, 56.0], [85.2, 56.0], [85.3, 56.0], [85.4, 56.0], [85.5, 56.0], [85.6, 56.0], [85.7, 56.0], [85.8, 56.0], [85.9, 56.0], [86.0, 56.0], [86.1, 56.0], [86.2, 56.0], [86.3, 56.0], [86.4, 56.0], [86.5, 56.0], [86.6, 56.0], [86.7, 56.0], [86.8, 56.0], [86.9, 56.0], [87.0, 56.0], [87.1, 56.0], [87.2, 56.0], [87.3, 56.0], [87.4, 56.0], [87.5, 56.0], [87.6, 56.0], [87.7, 56.0], [87.8, 56.0], [87.9, 56.0], [88.0, 56.0], [88.1, 56.0], [88.2, 56.0], [88.3, 56.0], [88.4, 56.0], [88.5, 56.0], [88.6, 56.0], [88.7, 56.0], [88.8, 56.0], [88.9, 56.0], [89.0, 56.0], [89.1, 56.0], [89.2, 56.0], [89.3, 56.0], [89.4, 56.0], [89.5, 56.0], [89.6, 56.0], [89.7, 56.0], [89.8, 56.0], [89.9, 56.0], [90.0, 60.0], [90.1, 60.0], [90.2, 60.0], [90.3, 60.0], [90.4, 60.0], [90.5, 60.0], [90.6, 60.0], [90.7, 60.0], [90.8, 60.0], [90.9, 60.0], [91.0, 60.0], [91.1, 60.0], [91.2, 60.0], [91.3, 60.0], [91.4, 60.0], [91.5, 60.0], [91.6, 60.0], [91.7, 60.0], [91.8, 60.0], [91.9, 60.0], [92.0, 60.0], [92.1, 60.0], [92.2, 60.0], [92.3, 60.0], [92.4, 60.0], [92.5, 60.0], [92.6, 60.0], [92.7, 60.0], [92.8, 60.0], [92.9, 60.0], [93.0, 60.0], [93.1, 60.0], [93.2, 60.0], [93.3, 60.0], [93.4, 60.0], [93.5, 60.0], [93.6, 60.0], [93.7, 60.0], [93.8, 60.0], [93.9, 60.0], [94.0, 60.0], [94.1, 60.0], [94.2, 60.0], [94.3, 60.0], [94.4, 60.0], [94.5, 60.0], [94.6, 60.0], [94.7, 60.0], [94.8, 60.0], [94.9, 60.0], [95.0, 60.0], [95.1, 60.0], [95.2, 60.0], [95.3, 60.0], [95.4, 60.0], [95.5, 60.0], [95.6, 60.0], [95.7, 60.0], [95.8, 60.0], [95.9, 60.0], [96.0, 60.0], [96.1, 60.0], [96.2, 60.0], [96.3, 60.0], [96.4, 60.0], [96.5, 60.0], [96.6, 60.0], [96.7, 60.0], [96.8, 60.0], [96.9, 60.0], [97.0, 60.0], [97.1, 60.0], [97.2, 60.0], [97.3, 60.0], [97.4, 60.0], [97.5, 60.0], [97.6, 60.0], [97.7, 60.0], [97.8, 60.0], [97.9, 60.0], [98.0, 60.0], [98.1, 60.0], [98.2, 60.0], [98.3, 60.0], [98.4, 60.0], [98.5, 60.0], [98.6, 60.0], [98.7, 60.0], [98.8, 60.0], [98.9, 60.0], [99.0, 60.0], [99.1, 60.0], [99.2, 60.0], [99.3, 60.0], [99.4, 60.0], [99.5, 60.0], [99.6, 60.0], [99.7, 60.0], [99.8, 60.0], [99.9, 60.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[0.0, 448.0], [0.1, 448.0], [0.2, 448.0], [0.3, 448.0], [0.4, 448.0], [0.5, 448.0], [0.6, 448.0], [0.7, 448.0], [0.8, 448.0], [0.9, 448.0], [1.0, 448.0], [1.1, 448.0], [1.2, 448.0], [1.3, 448.0], [1.4, 448.0], [1.5, 448.0], [1.6, 448.0], [1.7, 448.0], [1.8, 448.0], [1.9, 448.0], [2.0, 448.0], [2.1, 448.0], [2.2, 448.0], [2.3, 448.0], [2.4, 448.0], [2.5, 448.0], [2.6, 448.0], [2.7, 448.0], [2.8, 448.0], [2.9, 448.0], [3.0, 448.0], [3.1, 448.0], [3.2, 448.0], [3.3, 448.0], [3.4, 448.0], [3.5, 448.0], [3.6, 448.0], [3.7, 448.0], [3.8, 448.0], [3.9, 448.0], [4.0, 448.0], [4.1, 448.0], [4.2, 448.0], [4.3, 448.0], [4.4, 448.0], [4.5, 448.0], [4.6, 448.0], [4.7, 448.0], [4.8, 448.0], [4.9, 448.0], [5.0, 448.0], [5.1, 448.0], [5.2, 448.0], [5.3, 448.0], [5.4, 448.0], [5.5, 448.0], [5.6, 448.0], [5.7, 448.0], [5.8, 448.0], [5.9, 448.0], [6.0, 448.0], [6.1, 448.0], [6.2, 448.0], [6.3, 448.0], [6.4, 448.0], [6.5, 448.0], [6.6, 448.0], [6.7, 448.0], [6.8, 448.0], [6.9, 448.0], [7.0, 448.0], [7.1, 448.0], [7.2, 448.0], [7.3, 448.0], [7.4, 448.0], [7.5, 448.0], [7.6, 448.0], [7.7, 448.0], [7.8, 448.0], [7.9, 448.0], [8.0, 448.0], [8.1, 448.0], [8.2, 448.0], [8.3, 448.0], [8.4, 448.0], [8.5, 448.0], [8.6, 448.0], [8.7, 448.0], [8.8, 448.0], [8.9, 448.0], [9.0, 448.0], [9.1, 448.0], [9.2, 448.0], [9.3, 448.0], [9.4, 448.0], [9.5, 448.0], [9.6, 448.0], [9.7, 448.0], [9.8, 448.0], [9.9, 448.0], [10.0, 454.0], [10.1, 454.0], [10.2, 454.0], [10.3, 454.0], [10.4, 454.0], [10.5, 454.0], [10.6, 454.0], [10.7, 454.0], [10.8, 454.0], [10.9, 454.0], [11.0, 454.0], [11.1, 454.0], [11.2, 454.0], [11.3, 454.0], [11.4, 454.0], [11.5, 454.0], [11.6, 454.0], [11.7, 454.0], [11.8, 454.0], [11.9, 454.0], [12.0, 454.0], [12.1, 454.0], [12.2, 454.0], [12.3, 454.0], [12.4, 454.0], [12.5, 454.0], [12.6, 454.0], [12.7, 454.0], [12.8, 454.0], [12.9, 454.0], [13.0, 454.0], [13.1, 454.0], [13.2, 454.0], [13.3, 454.0], [13.4, 454.0], [13.5, 454.0], [13.6, 454.0], [13.7, 454.0], [13.8, 454.0], [13.9, 454.0], [14.0, 454.0], [14.1, 454.0], [14.2, 454.0], [14.3, 454.0], [14.4, 454.0], [14.5, 454.0], [14.6, 454.0], [14.7, 454.0], [14.8, 454.0], [14.9, 454.0], [15.0, 454.0], [15.1, 454.0], [15.2, 454.0], [15.3, 454.0], [15.4, 454.0], [15.5, 454.0], [15.6, 454.0], [15.7, 454.0], [15.8, 454.0], [15.9, 454.0], [16.0, 454.0], [16.1, 454.0], [16.2, 454.0], [16.3, 454.0], [16.4, 454.0], [16.5, 454.0], [16.6, 454.0], [16.7, 454.0], [16.8, 454.0], [16.9, 454.0], [17.0, 454.0], [17.1, 454.0], [17.2, 454.0], [17.3, 454.0], [17.4, 454.0], [17.5, 454.0], [17.6, 454.0], [17.7, 454.0], [17.8, 454.0], [17.9, 454.0], [18.0, 454.0], [18.1, 454.0], [18.2, 454.0], [18.3, 454.0], [18.4, 454.0], [18.5, 454.0], [18.6, 454.0], [18.7, 454.0], [18.8, 454.0], [18.9, 454.0], [19.0, 454.0], [19.1, 454.0], [19.2, 454.0], [19.3, 454.0], [19.4, 454.0], [19.5, 454.0], [19.6, 454.0], [19.7, 454.0], [19.8, 454.0], [19.9, 454.0], [20.0, 457.0], [20.1, 457.0], [20.2, 457.0], [20.3, 457.0], [20.4, 457.0], [20.5, 457.0], [20.6, 457.0], [20.7, 457.0], [20.8, 457.0], [20.9, 457.0], [21.0, 457.0], [21.1, 457.0], [21.2, 457.0], [21.3, 457.0], [21.4, 457.0], [21.5, 457.0], [21.6, 457.0], [21.7, 457.0], [21.8, 457.0], [21.9, 457.0], [22.0, 457.0], [22.1, 457.0], [22.2, 457.0], [22.3, 457.0], [22.4, 457.0], [22.5, 457.0], [22.6, 457.0], [22.7, 457.0], [22.8, 457.0], [22.9, 457.0], [23.0, 457.0], [23.1, 457.0], [23.2, 457.0], [23.3, 457.0], [23.4, 457.0], [23.5, 457.0], [23.6, 457.0], [23.7, 457.0], [23.8, 457.0], [23.9, 457.0], [24.0, 457.0], [24.1, 457.0], [24.2, 457.0], [24.3, 457.0], [24.4, 457.0], [24.5, 457.0], [24.6, 457.0], [24.7, 457.0], [24.8, 457.0], [24.9, 457.0], [25.0, 457.0], [25.1, 457.0], [25.2, 457.0], [25.3, 457.0], [25.4, 457.0], [25.5, 457.0], [25.6, 457.0], [25.7, 457.0], [25.8, 457.0], [25.9, 457.0], [26.0, 457.0], [26.1, 457.0], [26.2, 457.0], [26.3, 457.0], [26.4, 457.0], [26.5, 457.0], [26.6, 457.0], [26.7, 457.0], [26.8, 457.0], [26.9, 457.0], [27.0, 457.0], [27.1, 457.0], [27.2, 457.0], [27.3, 457.0], [27.4, 457.0], [27.5, 457.0], [27.6, 457.0], [27.7, 457.0], [27.8, 457.0], [27.9, 457.0], [28.0, 457.0], [28.1, 457.0], [28.2, 457.0], [28.3, 457.0], [28.4, 457.0], [28.5, 457.0], [28.6, 457.0], [28.7, 457.0], [28.8, 457.0], [28.9, 457.0], [29.0, 457.0], [29.1, 457.0], [29.2, 457.0], [29.3, 457.0], [29.4, 457.0], [29.5, 457.0], [29.6, 457.0], [29.7, 457.0], [29.8, 457.0], [29.9, 457.0], [30.0, 458.0], [30.1, 458.0], [30.2, 458.0], [30.3, 458.0], [30.4, 458.0], [30.5, 458.0], [30.6, 458.0], [30.7, 458.0], [30.8, 458.0], [30.9, 458.0], [31.0, 458.0], [31.1, 458.0], [31.2, 458.0], [31.3, 458.0], [31.4, 458.0], [31.5, 458.0], [31.6, 458.0], [31.7, 458.0], [31.8, 458.0], [31.9, 458.0], [32.0, 458.0], [32.1, 458.0], [32.2, 458.0], [32.3, 458.0], [32.4, 458.0], [32.5, 458.0], [32.6, 458.0], [32.7, 458.0], [32.8, 458.0], [32.9, 458.0], [33.0, 458.0], [33.1, 458.0], [33.2, 458.0], [33.3, 458.0], [33.4, 458.0], [33.5, 458.0], [33.6, 458.0], [33.7, 458.0], [33.8, 458.0], [33.9, 458.0], [34.0, 458.0], [34.1, 458.0], [34.2, 458.0], [34.3, 458.0], [34.4, 458.0], [34.5, 458.0], [34.6, 458.0], [34.7, 458.0], [34.8, 458.0], [34.9, 458.0], [35.0, 458.0], [35.1, 458.0], [35.2, 458.0], [35.3, 458.0], [35.4, 458.0], [35.5, 458.0], [35.6, 458.0], [35.7, 458.0], [35.8, 458.0], [35.9, 458.0], [36.0, 458.0], [36.1, 458.0], [36.2, 458.0], [36.3, 458.0], [36.4, 458.0], [36.5, 458.0], [36.6, 458.0], [36.7, 458.0], [36.8, 458.0], [36.9, 458.0], [37.0, 458.0], [37.1, 458.0], [37.2, 458.0], [37.3, 458.0], [37.4, 458.0], [37.5, 458.0], [37.6, 458.0], [37.7, 458.0], [37.8, 458.0], [37.9, 458.0], [38.0, 458.0], [38.1, 458.0], [38.2, 458.0], [38.3, 458.0], [38.4, 458.0], [38.5, 458.0], [38.6, 458.0], [38.7, 458.0], [38.8, 458.0], [38.9, 458.0], [39.0, 458.0], [39.1, 458.0], [39.2, 458.0], [39.3, 458.0], [39.4, 458.0], [39.5, 458.0], [39.6, 458.0], [39.7, 458.0], [39.8, 458.0], [39.9, 458.0], [40.0, 459.0], [40.1, 459.0], [40.2, 459.0], [40.3, 459.0], [40.4, 459.0], [40.5, 459.0], [40.6, 459.0], [40.7, 459.0], [40.8, 459.0], [40.9, 459.0], [41.0, 459.0], [41.1, 459.0], [41.2, 459.0], [41.3, 459.0], [41.4, 459.0], [41.5, 459.0], [41.6, 459.0], [41.7, 459.0], [41.8, 459.0], [41.9, 459.0], [42.0, 459.0], [42.1, 459.0], [42.2, 459.0], [42.3, 459.0], [42.4, 459.0], [42.5, 459.0], [42.6, 459.0], [42.7, 459.0], [42.8, 459.0], [42.9, 459.0], [43.0, 459.0], [43.1, 459.0], [43.2, 459.0], [43.3, 459.0], [43.4, 459.0], [43.5, 459.0], [43.6, 459.0], [43.7, 459.0], [43.8, 459.0], [43.9, 459.0], [44.0, 459.0], [44.1, 459.0], [44.2, 459.0], [44.3, 459.0], [44.4, 459.0], [44.5, 459.0], [44.6, 459.0], [44.7, 459.0], [44.8, 459.0], [44.9, 459.0], [45.0, 459.0], [45.1, 459.0], [45.2, 459.0], [45.3, 459.0], [45.4, 459.0], [45.5, 459.0], [45.6, 459.0], [45.7, 459.0], [45.8, 459.0], [45.9, 459.0], [46.0, 459.0], [46.1, 459.0], [46.2, 459.0], [46.3, 459.0], [46.4, 459.0], [46.5, 459.0], [46.6, 459.0], [46.7, 459.0], [46.8, 459.0], [46.9, 459.0], [47.0, 459.0], [47.1, 459.0], [47.2, 459.0], [47.3, 459.0], [47.4, 459.0], [47.5, 459.0], [47.6, 459.0], [47.7, 459.0], [47.8, 459.0], [47.9, 459.0], [48.0, 459.0], [48.1, 459.0], [48.2, 459.0], [48.3, 459.0], [48.4, 459.0], [48.5, 459.0], [48.6, 459.0], [48.7, 459.0], [48.8, 459.0], [48.9, 459.0], [49.0, 459.0], [49.1, 459.0], [49.2, 459.0], [49.3, 459.0], [49.4, 459.0], [49.5, 459.0], [49.6, 459.0], [49.7, 459.0], [49.8, 459.0], [49.9, 459.0], [50.0, 461.0], [50.1, 461.0], [50.2, 461.0], [50.3, 461.0], [50.4, 461.0], [50.5, 461.0], [50.6, 461.0], [50.7, 461.0], [50.8, 461.0], [50.9, 461.0], [51.0, 461.0], [51.1, 461.0], [51.2, 461.0], [51.3, 461.0], [51.4, 461.0], [51.5, 461.0], [51.6, 461.0], [51.7, 461.0], [51.8, 461.0], [51.9, 461.0], [52.0, 461.0], [52.1, 461.0], [52.2, 461.0], [52.3, 461.0], [52.4, 461.0], [52.5, 461.0], [52.6, 461.0], [52.7, 461.0], [52.8, 461.0], [52.9, 461.0], [53.0, 461.0], [53.1, 461.0], [53.2, 461.0], [53.3, 461.0], [53.4, 461.0], [53.5, 461.0], [53.6, 461.0], [53.7, 461.0], [53.8, 461.0], [53.9, 461.0], [54.0, 461.0], [54.1, 461.0], [54.2, 461.0], [54.3, 461.0], [54.4, 461.0], [54.5, 461.0], [54.6, 461.0], [54.7, 461.0], [54.8, 461.0], [54.9, 461.0], [55.0, 461.0], [55.1, 461.0], [55.2, 461.0], [55.3, 461.0], [55.4, 461.0], [55.5, 461.0], [55.6, 461.0], [55.7, 461.0], [55.8, 461.0], [55.9, 461.0], [56.0, 461.0], [56.1, 461.0], [56.2, 461.0], [56.3, 461.0], [56.4, 461.0], [56.5, 461.0], [56.6, 461.0], [56.7, 461.0], [56.8, 461.0], [56.9, 461.0], [57.0, 461.0], [57.1, 461.0], [57.2, 461.0], [57.3, 461.0], [57.4, 461.0], [57.5, 461.0], [57.6, 461.0], [57.7, 461.0], [57.8, 461.0], [57.9, 461.0], [58.0, 461.0], [58.1, 461.0], [58.2, 461.0], [58.3, 461.0], [58.4, 461.0], [58.5, 461.0], [58.6, 461.0], [58.7, 461.0], [58.8, 461.0], [58.9, 461.0], [59.0, 461.0], [59.1, 461.0], [59.2, 461.0], [59.3, 461.0], [59.4, 461.0], [59.5, 461.0], [59.6, 461.0], [59.7, 461.0], [59.8, 461.0], [59.9, 461.0], [60.0, 461.0], [60.1, 461.0], [60.2, 461.0], [60.3, 461.0], [60.4, 461.0], [60.5, 461.0], [60.6, 461.0], [60.7, 461.0], [60.8, 461.0], [60.9, 461.0], [61.0, 461.0], [61.1, 461.0], [61.2, 461.0], [61.3, 461.0], [61.4, 461.0], [61.5, 461.0], [61.6, 461.0], [61.7, 461.0], [61.8, 461.0], [61.9, 461.0], [62.0, 461.0], [62.1, 461.0], [62.2, 461.0], [62.3, 461.0], [62.4, 461.0], [62.5, 461.0], [62.6, 461.0], [62.7, 461.0], [62.8, 461.0], [62.9, 461.0], [63.0, 461.0], [63.1, 461.0], [63.2, 461.0], [63.3, 461.0], [63.4, 461.0], [63.5, 461.0], [63.6, 461.0], [63.7, 461.0], [63.8, 461.0], [63.9, 461.0], [64.0, 461.0], [64.1, 461.0], [64.2, 461.0], [64.3, 461.0], [64.4, 461.0], [64.5, 461.0], [64.6, 461.0], [64.7, 461.0], [64.8, 461.0], [64.9, 461.0], [65.0, 461.0], [65.1, 461.0], [65.2, 461.0], [65.3, 461.0], [65.4, 461.0], [65.5, 461.0], [65.6, 461.0], [65.7, 461.0], [65.8, 461.0], [65.9, 461.0], [66.0, 461.0], [66.1, 461.0], [66.2, 461.0], [66.3, 461.0], [66.4, 461.0], [66.5, 461.0], [66.6, 461.0], [66.7, 461.0], [66.8, 461.0], [66.9, 461.0], [67.0, 461.0], [67.1, 461.0], [67.2, 461.0], [67.3, 461.0], [67.4, 461.0], [67.5, 461.0], [67.6, 461.0], [67.7, 461.0], [67.8, 461.0], [67.9, 461.0], [68.0, 461.0], [68.1, 461.0], [68.2, 461.0], [68.3, 461.0], [68.4, 461.0], [68.5, 461.0], [68.6, 461.0], [68.7, 461.0], [68.8, 461.0], [68.9, 461.0], [69.0, 461.0], [69.1, 461.0], [69.2, 461.0], [69.3, 461.0], [69.4, 461.0], [69.5, 461.0], [69.6, 461.0], [69.7, 461.0], [69.8, 461.0], [69.9, 461.0], [70.0, 461.0], [70.1, 461.0], [70.2, 461.0], [70.3, 461.0], [70.4, 461.0], [70.5, 461.0], [70.6, 461.0], [70.7, 461.0], [70.8, 461.0], [70.9, 461.0], [71.0, 461.0], [71.1, 461.0], [71.2, 461.0], [71.3, 461.0], [71.4, 461.0], [71.5, 461.0], [71.6, 461.0], [71.7, 461.0], [71.8, 461.0], [71.9, 461.0], [72.0, 461.0], [72.1, 461.0], [72.2, 461.0], [72.3, 461.0], [72.4, 461.0], [72.5, 461.0], [72.6, 461.0], [72.7, 461.0], [72.8, 461.0], [72.9, 461.0], [73.0, 461.0], [73.1, 461.0], [73.2, 461.0], [73.3, 461.0], [73.4, 461.0], [73.5, 461.0], [73.6, 461.0], [73.7, 461.0], [73.8, 461.0], [73.9, 461.0], [74.0, 461.0], [74.1, 461.0], [74.2, 461.0], [74.3, 461.0], [74.4, 461.0], [74.5, 461.0], [74.6, 461.0], [74.7, 461.0], [74.8, 461.0], [74.9, 461.0], [75.0, 461.0], [75.1, 461.0], [75.2, 461.0], [75.3, 461.0], [75.4, 461.0], [75.5, 461.0], [75.6, 461.0], [75.7, 461.0], [75.8, 461.0], [75.9, 461.0], [76.0, 461.0], [76.1, 461.0], [76.2, 461.0], [76.3, 461.0], [76.4, 461.0], [76.5, 461.0], [76.6, 461.0], [76.7, 461.0], [76.8, 461.0], [76.9, 461.0], [77.0, 461.0], [77.1, 461.0], [77.2, 461.0], [77.3, 461.0], [77.4, 461.0], [77.5, 461.0], [77.6, 461.0], [77.7, 461.0], [77.8, 461.0], [77.9, 461.0], [78.0, 461.0], [78.1, 461.0], [78.2, 461.0], [78.3, 461.0], [78.4, 461.0], [78.5, 461.0], [78.6, 461.0], [78.7, 461.0], [78.8, 461.0], [78.9, 461.0], [79.0, 461.0], [79.1, 461.0], [79.2, 461.0], [79.3, 461.0], [79.4, 461.0], [79.5, 461.0], [79.6, 461.0], [79.7, 461.0], [79.8, 461.0], [79.9, 461.0], [80.0, 462.0], [80.1, 462.0], [80.2, 462.0], [80.3, 462.0], [80.4, 462.0], [80.5, 462.0], [80.6, 462.0], [80.7, 462.0], [80.8, 462.0], [80.9, 462.0], [81.0, 462.0], [81.1, 462.0], [81.2, 462.0], [81.3, 462.0], [81.4, 462.0], [81.5, 462.0], [81.6, 462.0], [81.7, 462.0], [81.8, 462.0], [81.9, 462.0], [82.0, 462.0], [82.1, 462.0], [82.2, 462.0], [82.3, 462.0], [82.4, 462.0], [82.5, 462.0], [82.6, 462.0], [82.7, 462.0], [82.8, 462.0], [82.9, 462.0], [83.0, 462.0], [83.1, 462.0], [83.2, 462.0], [83.3, 462.0], [83.4, 462.0], [83.5, 462.0], [83.6, 462.0], [83.7, 462.0], [83.8, 462.0], [83.9, 462.0], [84.0, 462.0], [84.1, 462.0], [84.2, 462.0], [84.3, 462.0], [84.4, 462.0], [84.5, 462.0], [84.6, 462.0], [84.7, 462.0], [84.8, 462.0], [84.9, 462.0], [85.0, 462.0], [85.1, 462.0], [85.2, 462.0], [85.3, 462.0], [85.4, 462.0], [85.5, 462.0], [85.6, 462.0], [85.7, 462.0], [85.8, 462.0], [85.9, 462.0], [86.0, 462.0], [86.1, 462.0], [86.2, 462.0], [86.3, 462.0], [86.4, 462.0], [86.5, 462.0], [86.6, 462.0], [86.7, 462.0], [86.8, 462.0], [86.9, 462.0], [87.0, 462.0], [87.1, 462.0], [87.2, 462.0], [87.3, 462.0], [87.4, 462.0], [87.5, 462.0], [87.6, 462.0], [87.7, 462.0], [87.8, 462.0], [87.9, 462.0], [88.0, 462.0], [88.1, 462.0], [88.2, 462.0], [88.3, 462.0], [88.4, 462.0], [88.5, 462.0], [88.6, 462.0], [88.7, 462.0], [88.8, 462.0], [88.9, 462.0], [89.0, 462.0], [89.1, 462.0], [89.2, 462.0], [89.3, 462.0], [89.4, 462.0], [89.5, 462.0], [89.6, 462.0], [89.7, 462.0], [89.8, 462.0], [89.9, 462.0], [90.0, 491.0], [90.1, 491.0], [90.2, 491.0], [90.3, 491.0], [90.4, 491.0], [90.5, 491.0], [90.6, 491.0], [90.7, 491.0], [90.8, 491.0], [90.9, 491.0], [91.0, 491.0], [91.1, 491.0], [91.2, 491.0], [91.3, 491.0], [91.4, 491.0], [91.5, 491.0], [91.6, 491.0], [91.7, 491.0], [91.8, 491.0], [91.9, 491.0], [92.0, 491.0], [92.1, 491.0], [92.2, 491.0], [92.3, 491.0], [92.4, 491.0], [92.5, 491.0], [92.6, 491.0], [92.7, 491.0], [92.8, 491.0], [92.9, 491.0], [93.0, 491.0], [93.1, 491.0], [93.2, 491.0], [93.3, 491.0], [93.4, 491.0], [93.5, 491.0], [93.6, 491.0], [93.7, 491.0], [93.8, 491.0], [93.9, 491.0], [94.0, 491.0], [94.1, 491.0], [94.2, 491.0], [94.3, 491.0], [94.4, 491.0], [94.5, 491.0], [94.6, 491.0], [94.7, 491.0], [94.8, 491.0], [94.9, 491.0], [95.0, 491.0], [95.1, 491.0], [95.2, 491.0], [95.3, 491.0], [95.4, 491.0], [95.5, 491.0], [95.6, 491.0], [95.7, 491.0], [95.8, 491.0], [95.9, 491.0], [96.0, 491.0], [96.1, 491.0], [96.2, 491.0], [96.3, 491.0], [96.4, 491.0], [96.5, 491.0], [96.6, 491.0], [96.7, 491.0], [96.8, 491.0], [96.9, 491.0], [97.0, 491.0], [97.1, 491.0], [97.2, 491.0], [97.3, 491.0], [97.4, 491.0], [97.5, 491.0], [97.6, 491.0], [97.7, 491.0], [97.8, 491.0], [97.9, 491.0], [98.0, 491.0], [98.1, 491.0], [98.2, 491.0], [98.3, 491.0], [98.4, 491.0], [98.5, 491.0], [98.6, 491.0], [98.7, 491.0], [98.8, 491.0], [98.9, 491.0], [99.0, 491.0], [99.1, 491.0], [99.2, 491.0], [99.3, 491.0], [99.4, 491.0], [99.5, 491.0], [99.6, 491.0], [99.7, 491.0], [99.8, 491.0], [99.9, 491.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[0.0, 8.0], [0.1, 8.0], [0.2, 8.0], [0.3, 8.0], [0.4, 8.0], [0.5, 8.0], [0.6, 8.0], [0.7, 8.0], [0.8, 8.0], [0.9, 8.0], [1.0, 8.0], [1.1, 8.0], [1.2, 8.0], [1.3, 8.0], [1.4, 8.0], [1.5, 8.0], [1.6, 8.0], [1.7, 8.0], [1.8, 8.0], [1.9, 8.0], [2.0, 8.0], [2.1, 8.0], [2.2, 8.0], [2.3, 8.0], [2.4, 8.0], [2.5, 8.0], [2.6, 8.0], [2.7, 8.0], [2.8, 8.0], [2.9, 8.0], [3.0, 8.0], [3.1, 8.0], [3.2, 8.0], [3.3, 8.0], [3.4, 8.0], [3.5, 8.0], [3.6, 8.0], [3.7, 8.0], [3.8, 8.0], [3.9, 8.0], [4.0, 8.0], [4.1, 8.0], [4.2, 8.0], [4.3, 8.0], [4.4, 8.0], [4.5, 8.0], [4.6, 8.0], [4.7, 8.0], [4.8, 8.0], [4.9, 8.0], [5.0, 8.0], [5.1, 8.0], [5.2, 8.0], [5.3, 8.0], [5.4, 8.0], [5.5, 8.0], [5.6, 8.0], [5.7, 8.0], [5.8, 8.0], [5.9, 8.0], [6.0, 8.0], [6.1, 8.0], [6.2, 8.0], [6.3, 8.0], [6.4, 8.0], [6.5, 8.0], [6.6, 8.0], [6.7, 8.0], [6.8, 8.0], [6.9, 8.0], [7.0, 8.0], [7.1, 8.0], [7.2, 8.0], [7.3, 8.0], [7.4, 8.0], [7.5, 8.0], [7.6, 8.0], [7.7, 8.0], [7.8, 8.0], [7.9, 8.0], [8.0, 8.0], [8.1, 8.0], [8.2, 8.0], [8.3, 8.0], [8.4, 8.0], [8.5, 8.0], [8.6, 8.0], [8.7, 8.0], [8.8, 8.0], [8.9, 8.0], [9.0, 8.0], [9.1, 8.0], [9.2, 8.0], [9.3, 8.0], [9.4, 8.0], [9.5, 8.0], [9.6, 8.0], [9.7, 8.0], [9.8, 8.0], [9.9, 8.0], [10.0, 8.0], [10.1, 8.0], [10.2, 8.0], [10.3, 8.0], [10.4, 8.0], [10.5, 8.0], [10.6, 8.0], [10.7, 8.0], [10.8, 8.0], [10.9, 8.0], [11.0, 8.0], [11.1, 8.0], [11.2, 8.0], [11.3, 8.0], [11.4, 8.0], [11.5, 8.0], [11.6, 8.0], [11.7, 8.0], [11.8, 8.0], [11.9, 8.0], [12.0, 8.0], [12.1, 8.0], [12.2, 8.0], [12.3, 8.0], [12.4, 8.0], [12.5, 8.0], [12.6, 8.0], [12.7, 8.0], [12.8, 8.0], [12.9, 8.0], [13.0, 8.0], [13.1, 8.0], [13.2, 8.0], [13.3, 8.0], [13.4, 8.0], [13.5, 8.0], [13.6, 8.0], [13.7, 8.0], [13.8, 8.0], [13.9, 8.0], [14.0, 8.0], [14.1, 8.0], [14.2, 8.0], [14.3, 8.0], [14.4, 8.0], [14.5, 8.0], [14.6, 8.0], [14.7, 8.0], [14.8, 8.0], [14.9, 8.0], [15.0, 8.0], [15.1, 8.0], [15.2, 8.0], [15.3, 8.0], [15.4, 8.0], [15.5, 8.0], [15.6, 8.0], [15.7, 8.0], [15.8, 8.0], [15.9, 8.0], [16.0, 8.0], [16.1, 8.0], [16.2, 8.0], [16.3, 8.0], [16.4, 8.0], [16.5, 8.0], [16.6, 8.0], [16.7, 8.0], [16.8, 8.0], [16.9, 8.0], [17.0, 8.0], [17.1, 8.0], [17.2, 8.0], [17.3, 8.0], [17.4, 8.0], [17.5, 8.0], [17.6, 8.0], [17.7, 8.0], [17.8, 8.0], [17.9, 8.0], [18.0, 8.0], [18.1, 8.0], [18.2, 8.0], [18.3, 8.0], [18.4, 8.0], [18.5, 8.0], [18.6, 8.0], [18.7, 8.0], [18.8, 8.0], [18.9, 8.0], [19.0, 8.0], [19.1, 8.0], [19.2, 8.0], [19.3, 8.0], [19.4, 8.0], [19.5, 8.0], [19.6, 8.0], [19.7, 8.0], [19.8, 8.0], [19.9, 8.0], [20.0, 8.0], [20.1, 8.0], [20.2, 8.0], [20.3, 8.0], [20.4, 8.0], [20.5, 8.0], [20.6, 8.0], [20.7, 8.0], [20.8, 8.0], [20.9, 8.0], [21.0, 8.0], [21.1, 8.0], [21.2, 8.0], [21.3, 8.0], [21.4, 8.0], [21.5, 8.0], [21.6, 8.0], [21.7, 8.0], [21.8, 8.0], [21.9, 8.0], [22.0, 8.0], [22.1, 8.0], [22.2, 8.0], [22.3, 8.0], [22.4, 8.0], [22.5, 8.0], [22.6, 8.0], [22.7, 8.0], [22.8, 8.0], [22.9, 8.0], [23.0, 8.0], [23.1, 8.0], [23.2, 8.0], [23.3, 8.0], [23.4, 8.0], [23.5, 8.0], [23.6, 8.0], [23.7, 8.0], [23.8, 8.0], [23.9, 8.0], [24.0, 8.0], [24.1, 8.0], [24.2, 8.0], [24.3, 8.0], [24.4, 8.0], [24.5, 8.0], [24.6, 8.0], [24.7, 8.0], [24.8, 8.0], [24.9, 8.0], [25.0, 8.0], [25.1, 8.0], [25.2, 8.0], [25.3, 8.0], [25.4, 8.0], [25.5, 8.0], [25.6, 8.0], [25.7, 8.0], [25.8, 8.0], [25.9, 8.0], [26.0, 8.0], [26.1, 8.0], [26.2, 8.0], [26.3, 8.0], [26.4, 8.0], [26.5, 8.0], [26.6, 8.0], [26.7, 8.0], [26.8, 8.0], [26.9, 8.0], [27.0, 8.0], [27.1, 8.0], [27.2, 8.0], [27.3, 8.0], [27.4, 8.0], [27.5, 8.0], [27.6, 8.0], [27.7, 8.0], [27.8, 8.0], [27.9, 8.0], [28.0, 8.0], [28.1, 8.0], [28.2, 8.0], [28.3, 8.0], [28.4, 8.0], [28.5, 8.0], [28.6, 8.0], [28.7, 8.0], [28.8, 8.0], [28.9, 8.0], [29.0, 8.0], [29.1, 8.0], [29.2, 8.0], [29.3, 8.0], [29.4, 8.0], [29.5, 8.0], [29.6, 8.0], [29.7, 8.0], [29.8, 8.0], [29.9, 8.0], [30.0, 8.0], [30.1, 8.0], [30.2, 8.0], [30.3, 8.0], [30.4, 8.0], [30.5, 8.0], [30.6, 8.0], [30.7, 8.0], [30.8, 8.0], [30.9, 8.0], [31.0, 8.0], [31.1, 8.0], [31.2, 8.0], [31.3, 8.0], [31.4, 8.0], [31.5, 8.0], [31.6, 8.0], [31.7, 8.0], [31.8, 8.0], [31.9, 8.0], [32.0, 8.0], [32.1, 8.0], [32.2, 8.0], [32.3, 8.0], [32.4, 8.0], [32.5, 8.0], [32.6, 8.0], [32.7, 8.0], [32.8, 8.0], [32.9, 8.0], [33.0, 8.0], [33.1, 8.0], [33.2, 8.0], [33.3, 8.0], [33.4, 8.0], [33.5, 8.0], [33.6, 8.0], [33.7, 8.0], [33.8, 8.0], [33.9, 8.0], [34.0, 8.0], [34.1, 8.0], [34.2, 8.0], [34.3, 8.0], [34.4, 8.0], [34.5, 8.0], [34.6, 8.0], [34.7, 8.0], [34.8, 8.0], [34.9, 8.0], [35.0, 8.0], [35.1, 8.0], [35.2, 8.0], [35.3, 8.0], [35.4, 8.0], [35.5, 8.0], [35.6, 8.0], [35.7, 8.0], [35.8, 8.0], [35.9, 8.0], [36.0, 8.0], [36.1, 8.0], [36.2, 8.0], [36.3, 8.0], [36.4, 8.0], [36.5, 8.0], [36.6, 8.0], [36.7, 8.0], [36.8, 8.0], [36.9, 8.0], [37.0, 8.0], [37.1, 8.0], [37.2, 8.0], [37.3, 8.0], [37.4, 8.0], [37.5, 8.0], [37.6, 8.0], [37.7, 8.0], [37.8, 8.0], [37.9, 8.0], [38.0, 8.0], [38.1, 8.0], [38.2, 8.0], [38.3, 8.0], [38.4, 8.0], [38.5, 8.0], [38.6, 8.0], [38.7, 8.0], [38.8, 8.0], [38.9, 8.0], [39.0, 8.0], [39.1, 8.0], [39.2, 8.0], [39.3, 8.0], [39.4, 8.0], [39.5, 8.0], [39.6, 8.0], [39.7, 8.0], [39.8, 8.0], [39.9, 8.0], [40.0, 9.0], [40.1, 9.0], [40.2, 9.0], [40.3, 9.0], [40.4, 9.0], [40.5, 9.0], [40.6, 9.0], [40.7, 9.0], [40.8, 9.0], [40.9, 9.0], [41.0, 9.0], [41.1, 9.0], [41.2, 9.0], [41.3, 9.0], [41.4, 9.0], [41.5, 9.0], [41.6, 9.0], [41.7, 9.0], [41.8, 9.0], [41.9, 9.0], [42.0, 9.0], [42.1, 9.0], [42.2, 9.0], [42.3, 9.0], [42.4, 9.0], [42.5, 9.0], [42.6, 9.0], [42.7, 9.0], [42.8, 9.0], [42.9, 9.0], [43.0, 9.0], [43.1, 9.0], [43.2, 9.0], [43.3, 9.0], [43.4, 9.0], [43.5, 9.0], [43.6, 9.0], [43.7, 9.0], [43.8, 9.0], [43.9, 9.0], [44.0, 9.0], [44.1, 9.0], [44.2, 9.0], [44.3, 9.0], [44.4, 9.0], [44.5, 9.0], [44.6, 9.0], [44.7, 9.0], [44.8, 9.0], [44.9, 9.0], [45.0, 9.0], [45.1, 9.0], [45.2, 9.0], [45.3, 9.0], [45.4, 9.0], [45.5, 9.0], [45.6, 9.0], [45.7, 9.0], [45.8, 9.0], [45.9, 9.0], [46.0, 9.0], [46.1, 9.0], [46.2, 9.0], [46.3, 9.0], [46.4, 9.0], [46.5, 9.0], [46.6, 9.0], [46.7, 9.0], [46.8, 9.0], [46.9, 9.0], [47.0, 9.0], [47.1, 9.0], [47.2, 9.0], [47.3, 9.0], [47.4, 9.0], [47.5, 9.0], [47.6, 9.0], [47.7, 9.0], [47.8, 9.0], [47.9, 9.0], [48.0, 9.0], [48.1, 9.0], [48.2, 9.0], [48.3, 9.0], [48.4, 9.0], [48.5, 9.0], [48.6, 9.0], [48.7, 9.0], [48.8, 9.0], [48.9, 9.0], [49.0, 9.0], [49.1, 9.0], [49.2, 9.0], [49.3, 9.0], [49.4, 9.0], [49.5, 9.0], [49.6, 9.0], [49.7, 9.0], [49.8, 9.0], [49.9, 9.0], [50.0, 9.0], [50.1, 9.0], [50.2, 9.0], [50.3, 9.0], [50.4, 9.0], [50.5, 9.0], [50.6, 9.0], [50.7, 9.0], [50.8, 9.0], [50.9, 9.0], [51.0, 9.0], [51.1, 9.0], [51.2, 9.0], [51.3, 9.0], [51.4, 9.0], [51.5, 9.0], [51.6, 9.0], [51.7, 9.0], [51.8, 9.0], [51.9, 9.0], [52.0, 9.0], [52.1, 9.0], [52.2, 9.0], [52.3, 9.0], [52.4, 9.0], [52.5, 9.0], [52.6, 9.0], [52.7, 9.0], [52.8, 9.0], [52.9, 9.0], [53.0, 9.0], [53.1, 9.0], [53.2, 9.0], [53.3, 9.0], [53.4, 9.0], [53.5, 9.0], [53.6, 9.0], [53.7, 9.0], [53.8, 9.0], [53.9, 9.0], [54.0, 9.0], [54.1, 9.0], [54.2, 9.0], [54.3, 9.0], [54.4, 9.0], [54.5, 9.0], [54.6, 9.0], [54.7, 9.0], [54.8, 9.0], [54.9, 9.0], [55.0, 9.0], [55.1, 9.0], [55.2, 9.0], [55.3, 9.0], [55.4, 9.0], [55.5, 9.0], [55.6, 9.0], [55.7, 9.0], [55.8, 9.0], [55.9, 9.0], [56.0, 9.0], [56.1, 9.0], [56.2, 9.0], [56.3, 9.0], [56.4, 9.0], [56.5, 9.0], [56.6, 9.0], [56.7, 9.0], [56.8, 9.0], [56.9, 9.0], [57.0, 9.0], [57.1, 9.0], [57.2, 9.0], [57.3, 9.0], [57.4, 9.0], [57.5, 9.0], [57.6, 9.0], [57.7, 9.0], [57.8, 9.0], [57.9, 9.0], [58.0, 9.0], [58.1, 9.0], [58.2, 9.0], [58.3, 9.0], [58.4, 9.0], [58.5, 9.0], [58.6, 9.0], [58.7, 9.0], [58.8, 9.0], [58.9, 9.0], [59.0, 9.0], [59.1, 9.0], [59.2, 9.0], [59.3, 9.0], [59.4, 9.0], [59.5, 9.0], [59.6, 9.0], [59.7, 9.0], [59.8, 9.0], [59.9, 9.0], [60.0, 9.0], [60.1, 9.0], [60.2, 9.0], [60.3, 9.0], [60.4, 9.0], [60.5, 9.0], [60.6, 9.0], [60.7, 9.0], [60.8, 9.0], [60.9, 9.0], [61.0, 9.0], [61.1, 9.0], [61.2, 9.0], [61.3, 9.0], [61.4, 9.0], [61.5, 9.0], [61.6, 9.0], [61.7, 9.0], [61.8, 9.0], [61.9, 9.0], [62.0, 9.0], [62.1, 9.0], [62.2, 9.0], [62.3, 9.0], [62.4, 9.0], [62.5, 9.0], [62.6, 9.0], [62.7, 9.0], [62.8, 9.0], [62.9, 9.0], [63.0, 9.0], [63.1, 9.0], [63.2, 9.0], [63.3, 9.0], [63.4, 9.0], [63.5, 9.0], [63.6, 9.0], [63.7, 9.0], [63.8, 9.0], [63.9, 9.0], [64.0, 9.0], [64.1, 9.0], [64.2, 9.0], [64.3, 9.0], [64.4, 9.0], [64.5, 9.0], [64.6, 9.0], [64.7, 9.0], [64.8, 9.0], [64.9, 9.0], [65.0, 9.0], [65.1, 9.0], [65.2, 9.0], [65.3, 9.0], [65.4, 9.0], [65.5, 9.0], [65.6, 9.0], [65.7, 9.0], [65.8, 9.0], [65.9, 9.0], [66.0, 9.0], [66.1, 9.0], [66.2, 9.0], [66.3, 9.0], [66.4, 9.0], [66.5, 9.0], [66.6, 9.0], [66.7, 9.0], [66.8, 9.0], [66.9, 9.0], [67.0, 9.0], [67.1, 9.0], [67.2, 9.0], [67.3, 9.0], [67.4, 9.0], [67.5, 9.0], [67.6, 9.0], [67.7, 9.0], [67.8, 9.0], [67.9, 9.0], [68.0, 9.0], [68.1, 9.0], [68.2, 9.0], [68.3, 9.0], [68.4, 9.0], [68.5, 9.0], [68.6, 9.0], [68.7, 9.0], [68.8, 9.0], [68.9, 9.0], [69.0, 9.0], [69.1, 9.0], [69.2, 9.0], [69.3, 9.0], [69.4, 9.0], [69.5, 9.0], [69.6, 9.0], [69.7, 9.0], [69.8, 9.0], [69.9, 9.0], [70.0, 9.0], [70.1, 9.0], [70.2, 9.0], [70.3, 9.0], [70.4, 9.0], [70.5, 9.0], [70.6, 9.0], [70.7, 9.0], [70.8, 9.0], [70.9, 9.0], [71.0, 9.0], [71.1, 9.0], [71.2, 9.0], [71.3, 9.0], [71.4, 9.0], [71.5, 9.0], [71.6, 9.0], [71.7, 9.0], [71.8, 9.0], [71.9, 9.0], [72.0, 9.0], [72.1, 9.0], [72.2, 9.0], [72.3, 9.0], [72.4, 9.0], [72.5, 9.0], [72.6, 9.0], [72.7, 9.0], [72.8, 9.0], [72.9, 9.0], [73.0, 9.0], [73.1, 9.0], [73.2, 9.0], [73.3, 9.0], [73.4, 9.0], [73.5, 9.0], [73.6, 9.0], [73.7, 9.0], [73.8, 9.0], [73.9, 9.0], [74.0, 9.0], [74.1, 9.0], [74.2, 9.0], [74.3, 9.0], [74.4, 9.0], [74.5, 9.0], [74.6, 9.0], [74.7, 9.0], [74.8, 9.0], [74.9, 9.0], [75.0, 9.0], [75.1, 9.0], [75.2, 9.0], [75.3, 9.0], [75.4, 9.0], [75.5, 9.0], [75.6, 9.0], [75.7, 9.0], [75.8, 9.0], [75.9, 9.0], [76.0, 9.0], [76.1, 9.0], [76.2, 9.0], [76.3, 9.0], [76.4, 9.0], [76.5, 9.0], [76.6, 9.0], [76.7, 9.0], [76.8, 9.0], [76.9, 9.0], [77.0, 9.0], [77.1, 9.0], [77.2, 9.0], [77.3, 9.0], [77.4, 9.0], [77.5, 9.0], [77.6, 9.0], [77.7, 9.0], [77.8, 9.0], [77.9, 9.0], [78.0, 9.0], [78.1, 9.0], [78.2, 9.0], [78.3, 9.0], [78.4, 9.0], [78.5, 9.0], [78.6, 9.0], [78.7, 9.0], [78.8, 9.0], [78.9, 9.0], [79.0, 9.0], [79.1, 9.0], [79.2, 9.0], [79.3, 9.0], [79.4, 9.0], [79.5, 9.0], [79.6, 9.0], [79.7, 9.0], [79.8, 9.0], [79.9, 9.0], [80.0, 10.0], [80.1, 10.0], [80.2, 10.0], [80.3, 10.0], [80.4, 10.0], [80.5, 10.0], [80.6, 10.0], [80.7, 10.0], [80.8, 10.0], [80.9, 10.0], [81.0, 10.0], [81.1, 10.0], [81.2, 10.0], [81.3, 10.0], [81.4, 10.0], [81.5, 10.0], [81.6, 10.0], [81.7, 10.0], [81.8, 10.0], [81.9, 10.0], [82.0, 10.0], [82.1, 10.0], [82.2, 10.0], [82.3, 10.0], [82.4, 10.0], [82.5, 10.0], [82.6, 10.0], [82.7, 10.0], [82.8, 10.0], [82.9, 10.0], [83.0, 10.0], [83.1, 10.0], [83.2, 10.0], [83.3, 10.0], [83.4, 10.0], [83.5, 10.0], [83.6, 10.0], [83.7, 10.0], [83.8, 10.0], [83.9, 10.0], [84.0, 10.0], [84.1, 10.0], [84.2, 10.0], [84.3, 10.0], [84.4, 10.0], [84.5, 10.0], [84.6, 10.0], [84.7, 10.0], [84.8, 10.0], [84.9, 10.0], [85.0, 10.0], [85.1, 10.0], [85.2, 10.0], [85.3, 10.0], [85.4, 10.0], [85.5, 10.0], [85.6, 10.0], [85.7, 10.0], [85.8, 10.0], [85.9, 10.0], [86.0, 10.0], [86.1, 10.0], [86.2, 10.0], [86.3, 10.0], [86.4, 10.0], [86.5, 10.0], [86.6, 10.0], [86.7, 10.0], [86.8, 10.0], [86.9, 10.0], [87.0, 10.0], [87.1, 10.0], [87.2, 10.0], [87.3, 10.0], [87.4, 10.0], [87.5, 10.0], [87.6, 10.0], [87.7, 10.0], [87.8, 10.0], [87.9, 10.0], [88.0, 10.0], [88.1, 10.0], [88.2, 10.0], [88.3, 10.0], [88.4, 10.0], [88.5, 10.0], [88.6, 10.0], [88.7, 10.0], [88.8, 10.0], [88.9, 10.0], [89.0, 10.0], [89.1, 10.0], [89.2, 10.0], [89.3, 10.0], [89.4, 10.0], [89.5, 10.0], [89.6, 10.0], [89.7, 10.0], [89.8, 10.0], [89.9, 10.0], [90.0, 11.0], [90.1, 11.0], [90.2, 11.0], [90.3, 11.0], [90.4, 11.0], [90.5, 11.0], [90.6, 11.0], [90.7, 11.0], [90.8, 11.0], [90.9, 11.0], [91.0, 11.0], [91.1, 11.0], [91.2, 11.0], [91.3, 11.0], [91.4, 11.0], [91.5, 11.0], [91.6, 11.0], [91.7, 11.0], [91.8, 11.0], [91.9, 11.0], [92.0, 11.0], [92.1, 11.0], [92.2, 11.0], [92.3, 11.0], [92.4, 11.0], [92.5, 11.0], [92.6, 11.0], [92.7, 11.0], [92.8, 11.0], [92.9, 11.0], [93.0, 11.0], [93.1, 11.0], [93.2, 11.0], [93.3, 11.0], [93.4, 11.0], [93.5, 11.0], [93.6, 11.0], [93.7, 11.0], [93.8, 11.0], [93.9, 11.0], [94.0, 11.0], [94.1, 11.0], [94.2, 11.0], [94.3, 11.0], [94.4, 11.0], [94.5, 11.0], [94.6, 11.0], [94.7, 11.0], [94.8, 11.0], [94.9, 11.0], [95.0, 11.0], [95.1, 11.0], [95.2, 11.0], [95.3, 11.0], [95.4, 11.0], [95.5, 11.0], [95.6, 11.0], [95.7, 11.0], [95.8, 11.0], [95.9, 11.0], [96.0, 11.0], [96.1, 11.0], [96.2, 11.0], [96.3, 11.0], [96.4, 11.0], [96.5, 11.0], [96.6, 11.0], [96.7, 11.0], [96.8, 11.0], [96.9, 11.0], [97.0, 11.0], [97.1, 11.0], [97.2, 11.0], [97.3, 11.0], [97.4, 11.0], [97.5, 11.0], [97.6, 11.0], [97.7, 11.0], [97.8, 11.0], [97.9, 11.0], [98.0, 11.0], [98.1, 11.0], [98.2, 11.0], [98.3, 11.0], [98.4, 11.0], [98.5, 11.0], [98.6, 11.0], [98.7, 11.0], [98.8, 11.0], [98.9, 11.0], [99.0, 11.0], [99.1, 11.0], [99.2, 11.0], [99.3, 11.0], [99.4, 11.0], [99.5, 11.0], [99.6, 11.0], [99.7, 11.0], [99.8, 11.0], [99.9, 11.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[0.0, 415.0], [0.1, 415.0], [0.2, 415.0], [0.3, 415.0], [0.4, 415.0], [0.5, 415.0], [0.6, 415.0], [0.7, 415.0], [0.8, 415.0], [0.9, 415.0], [1.0, 415.0], [1.1, 415.0], [1.2, 415.0], [1.3, 415.0], [1.4, 415.0], [1.5, 415.0], [1.6, 415.0], [1.7, 415.0], [1.8, 415.0], [1.9, 415.0], [2.0, 415.0], [2.1, 415.0], [2.2, 415.0], [2.3, 415.0], [2.4, 415.0], [2.5, 415.0], [2.6, 415.0], [2.7, 415.0], [2.8, 415.0], [2.9, 415.0], [3.0, 415.0], [3.1, 415.0], [3.2, 415.0], [3.3, 415.0], [3.4, 415.0], [3.5, 415.0], [3.6, 415.0], [3.7, 415.0], [3.8, 415.0], [3.9, 415.0], [4.0, 415.0], [4.1, 415.0], [4.2, 415.0], [4.3, 415.0], [4.4, 415.0], [4.5, 415.0], [4.6, 415.0], [4.7, 415.0], [4.8, 415.0], [4.9, 415.0], [5.0, 415.0], [5.1, 415.0], [5.2, 415.0], [5.3, 415.0], [5.4, 415.0], [5.5, 415.0], [5.6, 415.0], [5.7, 415.0], [5.8, 415.0], [5.9, 415.0], [6.0, 415.0], [6.1, 415.0], [6.2, 415.0], [6.3, 415.0], [6.4, 415.0], [6.5, 415.0], [6.6, 415.0], [6.7, 415.0], [6.8, 415.0], [6.9, 415.0], [7.0, 415.0], [7.1, 415.0], [7.2, 415.0], [7.3, 415.0], [7.4, 415.0], [7.5, 415.0], [7.6, 415.0], [7.7, 415.0], [7.8, 415.0], [7.9, 415.0], [8.0, 415.0], [8.1, 415.0], [8.2, 415.0], [8.3, 415.0], [8.4, 415.0], [8.5, 415.0], [8.6, 415.0], [8.7, 415.0], [8.8, 415.0], [8.9, 415.0], [9.0, 415.0], [9.1, 415.0], [9.2, 415.0], [9.3, 415.0], [9.4, 415.0], [9.5, 415.0], [9.6, 415.0], [9.7, 415.0], [9.8, 415.0], [9.9, 415.0], [10.0, 422.0], [10.1, 422.0], [10.2, 422.0], [10.3, 422.0], [10.4, 422.0], [10.5, 422.0], [10.6, 422.0], [10.7, 422.0], [10.8, 422.0], [10.9, 422.0], [11.0, 422.0], [11.1, 422.0], [11.2, 422.0], [11.3, 422.0], [11.4, 422.0], [11.5, 422.0], [11.6, 422.0], [11.7, 422.0], [11.8, 422.0], [11.9, 422.0], [12.0, 422.0], [12.1, 422.0], [12.2, 422.0], [12.3, 422.0], [12.4, 422.0], [12.5, 422.0], [12.6, 422.0], [12.7, 422.0], [12.8, 422.0], [12.9, 422.0], [13.0, 422.0], [13.1, 422.0], [13.2, 422.0], [13.3, 422.0], [13.4, 422.0], [13.5, 422.0], [13.6, 422.0], [13.7, 422.0], [13.8, 422.0], [13.9, 422.0], [14.0, 422.0], [14.1, 422.0], [14.2, 422.0], [14.3, 422.0], [14.4, 422.0], [14.5, 422.0], [14.6, 422.0], [14.7, 422.0], [14.8, 422.0], [14.9, 422.0], [15.0, 422.0], [15.1, 422.0], [15.2, 422.0], [15.3, 422.0], [15.4, 422.0], [15.5, 422.0], [15.6, 422.0], [15.7, 422.0], [15.8, 422.0], [15.9, 422.0], [16.0, 422.0], [16.1, 422.0], [16.2, 422.0], [16.3, 422.0], [16.4, 422.0], [16.5, 422.0], [16.6, 422.0], [16.7, 422.0], [16.8, 422.0], [16.9, 422.0], [17.0, 422.0], [17.1, 422.0], [17.2, 422.0], [17.3, 422.0], [17.4, 422.0], [17.5, 422.0], [17.6, 422.0], [17.7, 422.0], [17.8, 422.0], [17.9, 422.0], [18.0, 422.0], [18.1, 422.0], [18.2, 422.0], [18.3, 422.0], [18.4, 422.0], [18.5, 422.0], [18.6, 422.0], [18.7, 422.0], [18.8, 422.0], [18.9, 422.0], [19.0, 422.0], [19.1, 422.0], [19.2, 422.0], [19.3, 422.0], [19.4, 422.0], [19.5, 422.0], [19.6, 422.0], [19.7, 422.0], [19.8, 422.0], [19.9, 422.0], [20.0, 422.0], [20.1, 422.0], [20.2, 422.0], [20.3, 422.0], [20.4, 422.0], [20.5, 422.0], [20.6, 422.0], [20.7, 422.0], [20.8, 422.0], [20.9, 422.0], [21.0, 422.0], [21.1, 422.0], [21.2, 422.0], [21.3, 422.0], [21.4, 422.0], [21.5, 422.0], [21.6, 422.0], [21.7, 422.0], [21.8, 422.0], [21.9, 422.0], [22.0, 422.0], [22.1, 422.0], [22.2, 422.0], [22.3, 422.0], [22.4, 422.0], [22.5, 422.0], [22.6, 422.0], [22.7, 422.0], [22.8, 422.0], [22.9, 422.0], [23.0, 422.0], [23.1, 422.0], [23.2, 422.0], [23.3, 422.0], [23.4, 422.0], [23.5, 422.0], [23.6, 422.0], [23.7, 422.0], [23.8, 422.0], [23.9, 422.0], [24.0, 422.0], [24.1, 422.0], [24.2, 422.0], [24.3, 422.0], [24.4, 422.0], [24.5, 422.0], [24.6, 422.0], [24.7, 422.0], [24.8, 422.0], [24.9, 422.0], [25.0, 422.0], [25.1, 422.0], [25.2, 422.0], [25.3, 422.0], [25.4, 422.0], [25.5, 422.0], [25.6, 422.0], [25.7, 422.0], [25.8, 422.0], [25.9, 422.0], [26.0, 422.0], [26.1, 422.0], [26.2, 422.0], [26.3, 422.0], [26.4, 422.0], [26.5, 422.0], [26.6, 422.0], [26.7, 422.0], [26.8, 422.0], [26.9, 422.0], [27.0, 422.0], [27.1, 422.0], [27.2, 422.0], [27.3, 422.0], [27.4, 422.0], [27.5, 422.0], [27.6, 422.0], [27.7, 422.0], [27.8, 422.0], [27.9, 422.0], [28.0, 422.0], [28.1, 422.0], [28.2, 422.0], [28.3, 422.0], [28.4, 422.0], [28.5, 422.0], [28.6, 422.0], [28.7, 422.0], [28.8, 422.0], [28.9, 422.0], [29.0, 422.0], [29.1, 422.0], [29.2, 422.0], [29.3, 422.0], [29.4, 422.0], [29.5, 422.0], [29.6, 422.0], [29.7, 422.0], [29.8, 422.0], [29.9, 422.0], [30.0, 425.0], [30.1, 425.0], [30.2, 425.0], [30.3, 425.0], [30.4, 425.0], [30.5, 425.0], [30.6, 425.0], [30.7, 425.0], [30.8, 425.0], [30.9, 425.0], [31.0, 425.0], [31.1, 425.0], [31.2, 425.0], [31.3, 425.0], [31.4, 425.0], [31.5, 425.0], [31.6, 425.0], [31.7, 425.0], [31.8, 425.0], [31.9, 425.0], [32.0, 425.0], [32.1, 425.0], [32.2, 425.0], [32.3, 425.0], [32.4, 425.0], [32.5, 425.0], [32.6, 425.0], [32.7, 425.0], [32.8, 425.0], [32.9, 425.0], [33.0, 425.0], [33.1, 425.0], [33.2, 425.0], [33.3, 425.0], [33.4, 425.0], [33.5, 425.0], [33.6, 425.0], [33.7, 425.0], [33.8, 425.0], [33.9, 425.0], [34.0, 425.0], [34.1, 425.0], [34.2, 425.0], [34.3, 425.0], [34.4, 425.0], [34.5, 425.0], [34.6, 425.0], [34.7, 425.0], [34.8, 425.0], [34.9, 425.0], [35.0, 425.0], [35.1, 425.0], [35.2, 425.0], [35.3, 425.0], [35.4, 425.0], [35.5, 425.0], [35.6, 425.0], [35.7, 425.0], [35.8, 425.0], [35.9, 425.0], [36.0, 425.0], [36.1, 425.0], [36.2, 425.0], [36.3, 425.0], [36.4, 425.0], [36.5, 425.0], [36.6, 425.0], [36.7, 425.0], [36.8, 425.0], [36.9, 425.0], [37.0, 425.0], [37.1, 425.0], [37.2, 425.0], [37.3, 425.0], [37.4, 425.0], [37.5, 425.0], [37.6, 425.0], [37.7, 425.0], [37.8, 425.0], [37.9, 425.0], [38.0, 425.0], [38.1, 425.0], [38.2, 425.0], [38.3, 425.0], [38.4, 425.0], [38.5, 425.0], [38.6, 425.0], [38.7, 425.0], [38.8, 425.0], [38.9, 425.0], [39.0, 425.0], [39.1, 425.0], [39.2, 425.0], [39.3, 425.0], [39.4, 425.0], [39.5, 425.0], [39.6, 425.0], [39.7, 425.0], [39.8, 425.0], [39.9, 425.0], [40.0, 425.0], [40.1, 425.0], [40.2, 425.0], [40.3, 425.0], [40.4, 425.0], [40.5, 425.0], [40.6, 425.0], [40.7, 425.0], [40.8, 425.0], [40.9, 425.0], [41.0, 425.0], [41.1, 425.0], [41.2, 425.0], [41.3, 425.0], [41.4, 425.0], [41.5, 425.0], [41.6, 425.0], [41.7, 425.0], [41.8, 425.0], [41.9, 425.0], [42.0, 425.0], [42.1, 425.0], [42.2, 425.0], [42.3, 425.0], [42.4, 425.0], [42.5, 425.0], [42.6, 425.0], [42.7, 425.0], [42.8, 425.0], [42.9, 425.0], [43.0, 425.0], [43.1, 425.0], [43.2, 425.0], [43.3, 425.0], [43.4, 425.0], [43.5, 425.0], [43.6, 425.0], [43.7, 425.0], [43.8, 425.0], [43.9, 425.0], [44.0, 425.0], [44.1, 425.0], [44.2, 425.0], [44.3, 425.0], [44.4, 425.0], [44.5, 425.0], [44.6, 425.0], [44.7, 425.0], [44.8, 425.0], [44.9, 425.0], [45.0, 425.0], [45.1, 425.0], [45.2, 425.0], [45.3, 425.0], [45.4, 425.0], [45.5, 425.0], [45.6, 425.0], [45.7, 425.0], [45.8, 425.0], [45.9, 425.0], [46.0, 425.0], [46.1, 425.0], [46.2, 425.0], [46.3, 425.0], [46.4, 425.0], [46.5, 425.0], [46.6, 425.0], [46.7, 425.0], [46.8, 425.0], [46.9, 425.0], [47.0, 425.0], [47.1, 425.0], [47.2, 425.0], [47.3, 425.0], [47.4, 425.0], [47.5, 425.0], [47.6, 425.0], [47.7, 425.0], [47.8, 425.0], [47.9, 425.0], [48.0, 425.0], [48.1, 425.0], [48.2, 425.0], [48.3, 425.0], [48.4, 425.0], [48.5, 425.0], [48.6, 425.0], [48.7, 425.0], [48.8, 425.0], [48.9, 425.0], [49.0, 425.0], [49.1, 425.0], [49.2, 425.0], [49.3, 425.0], [49.4, 425.0], [49.5, 425.0], [49.6, 425.0], [49.7, 425.0], [49.8, 425.0], [49.9, 425.0], [50.0, 429.0], [50.1, 429.0], [50.2, 429.0], [50.3, 429.0], [50.4, 429.0], [50.5, 429.0], [50.6, 429.0], [50.7, 429.0], [50.8, 429.0], [50.9, 429.0], [51.0, 429.0], [51.1, 429.0], [51.2, 429.0], [51.3, 429.0], [51.4, 429.0], [51.5, 429.0], [51.6, 429.0], [51.7, 429.0], [51.8, 429.0], [51.9, 429.0], [52.0, 429.0], [52.1, 429.0], [52.2, 429.0], [52.3, 429.0], [52.4, 429.0], [52.5, 429.0], [52.6, 429.0], [52.7, 429.0], [52.8, 429.0], [52.9, 429.0], [53.0, 429.0], [53.1, 429.0], [53.2, 429.0], [53.3, 429.0], [53.4, 429.0], [53.5, 429.0], [53.6, 429.0], [53.7, 429.0], [53.8, 429.0], [53.9, 429.0], [54.0, 429.0], [54.1, 429.0], [54.2, 429.0], [54.3, 429.0], [54.4, 429.0], [54.5, 429.0], [54.6, 429.0], [54.7, 429.0], [54.8, 429.0], [54.9, 429.0], [55.0, 429.0], [55.1, 429.0], [55.2, 429.0], [55.3, 429.0], [55.4, 429.0], [55.5, 429.0], [55.6, 429.0], [55.7, 429.0], [55.8, 429.0], [55.9, 429.0], [56.0, 429.0], [56.1, 429.0], [56.2, 429.0], [56.3, 429.0], [56.4, 429.0], [56.5, 429.0], [56.6, 429.0], [56.7, 429.0], [56.8, 429.0], [56.9, 429.0], [57.0, 429.0], [57.1, 429.0], [57.2, 429.0], [57.3, 429.0], [57.4, 429.0], [57.5, 429.0], [57.6, 429.0], [57.7, 429.0], [57.8, 429.0], [57.9, 429.0], [58.0, 429.0], [58.1, 429.0], [58.2, 429.0], [58.3, 429.0], [58.4, 429.0], [58.5, 429.0], [58.6, 429.0], [58.7, 429.0], [58.8, 429.0], [58.9, 429.0], [59.0, 429.0], [59.1, 429.0], [59.2, 429.0], [59.3, 429.0], [59.4, 429.0], [59.5, 429.0], [59.6, 429.0], [59.7, 429.0], [59.8, 429.0], [59.9, 429.0], [60.0, 430.0], [60.1, 430.0], [60.2, 430.0], [60.3, 430.0], [60.4, 430.0], [60.5, 430.0], [60.6, 430.0], [60.7, 430.0], [60.8, 430.0], [60.9, 430.0], [61.0, 430.0], [61.1, 430.0], [61.2, 430.0], [61.3, 430.0], [61.4, 430.0], [61.5, 430.0], [61.6, 430.0], [61.7, 430.0], [61.8, 430.0], [61.9, 430.0], [62.0, 430.0], [62.1, 430.0], [62.2, 430.0], [62.3, 430.0], [62.4, 430.0], [62.5, 430.0], [62.6, 430.0], [62.7, 430.0], [62.8, 430.0], [62.9, 430.0], [63.0, 430.0], [63.1, 430.0], [63.2, 430.0], [63.3, 430.0], [63.4, 430.0], [63.5, 430.0], [63.6, 430.0], [63.7, 430.0], [63.8, 430.0], [63.9, 430.0], [64.0, 430.0], [64.1, 430.0], [64.2, 430.0], [64.3, 430.0], [64.4, 430.0], [64.5, 430.0], [64.6, 430.0], [64.7, 430.0], [64.8, 430.0], [64.9, 430.0], [65.0, 430.0], [65.1, 430.0], [65.2, 430.0], [65.3, 430.0], [65.4, 430.0], [65.5, 430.0], [65.6, 430.0], [65.7, 430.0], [65.8, 430.0], [65.9, 430.0], [66.0, 430.0], [66.1, 430.0], [66.2, 430.0], [66.3, 430.0], [66.4, 430.0], [66.5, 430.0], [66.6, 430.0], [66.7, 430.0], [66.8, 430.0], [66.9, 430.0], [67.0, 430.0], [67.1, 430.0], [67.2, 430.0], [67.3, 430.0], [67.4, 430.0], [67.5, 430.0], [67.6, 430.0], [67.7, 430.0], [67.8, 430.0], [67.9, 430.0], [68.0, 430.0], [68.1, 430.0], [68.2, 430.0], [68.3, 430.0], [68.4, 430.0], [68.5, 430.0], [68.6, 430.0], [68.7, 430.0], [68.8, 430.0], [68.9, 430.0], [69.0, 430.0], [69.1, 430.0], [69.2, 430.0], [69.3, 430.0], [69.4, 430.0], [69.5, 430.0], [69.6, 430.0], [69.7, 430.0], [69.8, 430.0], [69.9, 430.0], [70.0, 434.0], [70.1, 434.0], [70.2, 434.0], [70.3, 434.0], [70.4, 434.0], [70.5, 434.0], [70.6, 434.0], [70.7, 434.0], [70.8, 434.0], [70.9, 434.0], [71.0, 434.0], [71.1, 434.0], [71.2, 434.0], [71.3, 434.0], [71.4, 434.0], [71.5, 434.0], [71.6, 434.0], [71.7, 434.0], [71.8, 434.0], [71.9, 434.0], [72.0, 434.0], [72.1, 434.0], [72.2, 434.0], [72.3, 434.0], [72.4, 434.0], [72.5, 434.0], [72.6, 434.0], [72.7, 434.0], [72.8, 434.0], [72.9, 434.0], [73.0, 434.0], [73.1, 434.0], [73.2, 434.0], [73.3, 434.0], [73.4, 434.0], [73.5, 434.0], [73.6, 434.0], [73.7, 434.0], [73.8, 434.0], [73.9, 434.0], [74.0, 434.0], [74.1, 434.0], [74.2, 434.0], [74.3, 434.0], [74.4, 434.0], [74.5, 434.0], [74.6, 434.0], [74.7, 434.0], [74.8, 434.0], [74.9, 434.0], [75.0, 434.0], [75.1, 434.0], [75.2, 434.0], [75.3, 434.0], [75.4, 434.0], [75.5, 434.0], [75.6, 434.0], [75.7, 434.0], [75.8, 434.0], [75.9, 434.0], [76.0, 434.0], [76.1, 434.0], [76.2, 434.0], [76.3, 434.0], [76.4, 434.0], [76.5, 434.0], [76.6, 434.0], [76.7, 434.0], [76.8, 434.0], [76.9, 434.0], [77.0, 434.0], [77.1, 434.0], [77.2, 434.0], [77.3, 434.0], [77.4, 434.0], [77.5, 434.0], [77.6, 434.0], [77.7, 434.0], [77.8, 434.0], [77.9, 434.0], [78.0, 434.0], [78.1, 434.0], [78.2, 434.0], [78.3, 434.0], [78.4, 434.0], [78.5, 434.0], [78.6, 434.0], [78.7, 434.0], [78.8, 434.0], [78.9, 434.0], [79.0, 434.0], [79.1, 434.0], [79.2, 434.0], [79.3, 434.0], [79.4, 434.0], [79.5, 434.0], [79.6, 434.0], [79.7, 434.0], [79.8, 434.0], [79.9, 434.0], [80.0, 436.0], [80.1, 436.0], [80.2, 436.0], [80.3, 436.0], [80.4, 436.0], [80.5, 436.0], [80.6, 436.0], [80.7, 436.0], [80.8, 436.0], [80.9, 436.0], [81.0, 436.0], [81.1, 436.0], [81.2, 436.0], [81.3, 436.0], [81.4, 436.0], [81.5, 436.0], [81.6, 436.0], [81.7, 436.0], [81.8, 436.0], [81.9, 436.0], [82.0, 436.0], [82.1, 436.0], [82.2, 436.0], [82.3, 436.0], [82.4, 436.0], [82.5, 436.0], [82.6, 436.0], [82.7, 436.0], [82.8, 436.0], [82.9, 436.0], [83.0, 436.0], [83.1, 436.0], [83.2, 436.0], [83.3, 436.0], [83.4, 436.0], [83.5, 436.0], [83.6, 436.0], [83.7, 436.0], [83.8, 436.0], [83.9, 436.0], [84.0, 436.0], [84.1, 436.0], [84.2, 436.0], [84.3, 436.0], [84.4, 436.0], [84.5, 436.0], [84.6, 436.0], [84.7, 436.0], [84.8, 436.0], [84.9, 436.0], [85.0, 436.0], [85.1, 436.0], [85.2, 436.0], [85.3, 436.0], [85.4, 436.0], [85.5, 436.0], [85.6, 436.0], [85.7, 436.0], [85.8, 436.0], [85.9, 436.0], [86.0, 436.0], [86.1, 436.0], [86.2, 436.0], [86.3, 436.0], [86.4, 436.0], [86.5, 436.0], [86.6, 436.0], [86.7, 436.0], [86.8, 436.0], [86.9, 436.0], [87.0, 436.0], [87.1, 436.0], [87.2, 436.0], [87.3, 436.0], [87.4, 436.0], [87.5, 436.0], [87.6, 436.0], [87.7, 436.0], [87.8, 436.0], [87.9, 436.0], [88.0, 436.0], [88.1, 436.0], [88.2, 436.0], [88.3, 436.0], [88.4, 436.0], [88.5, 436.0], [88.6, 436.0], [88.7, 436.0], [88.8, 436.0], [88.9, 436.0], [89.0, 436.0], [89.1, 436.0], [89.2, 436.0], [89.3, 436.0], [89.4, 436.0], [89.5, 436.0], [89.6, 436.0], [89.7, 436.0], [89.8, 436.0], [89.9, 436.0], [90.0, 436.0], [90.1, 436.0], [90.2, 436.0], [90.3, 436.0], [90.4, 436.0], [90.5, 436.0], [90.6, 436.0], [90.7, 436.0], [90.8, 436.0], [90.9, 436.0], [91.0, 436.0], [91.1, 436.0], [91.2, 436.0], [91.3, 436.0], [91.4, 436.0], [91.5, 436.0], [91.6, 436.0], [91.7, 436.0], [91.8, 436.0], [91.9, 436.0], [92.0, 436.0], [92.1, 436.0], [92.2, 436.0], [92.3, 436.0], [92.4, 436.0], [92.5, 436.0], [92.6, 436.0], [92.7, 436.0], [92.8, 436.0], [92.9, 436.0], [93.0, 436.0], [93.1, 436.0], [93.2, 436.0], [93.3, 436.0], [93.4, 436.0], [93.5, 436.0], [93.6, 436.0], [93.7, 436.0], [93.8, 436.0], [93.9, 436.0], [94.0, 436.0], [94.1, 436.0], [94.2, 436.0], [94.3, 436.0], [94.4, 436.0], [94.5, 436.0], [94.6, 436.0], [94.7, 436.0], [94.8, 436.0], [94.9, 436.0], [95.0, 436.0], [95.1, 436.0], [95.2, 436.0], [95.3, 436.0], [95.4, 436.0], [95.5, 436.0], [95.6, 436.0], [95.7, 436.0], [95.8, 436.0], [95.9, 436.0], [96.0, 436.0], [96.1, 436.0], [96.2, 436.0], [96.3, 436.0], [96.4, 436.0], [96.5, 436.0], [96.6, 436.0], [96.7, 436.0], [96.8, 436.0], [96.9, 436.0], [97.0, 436.0], [97.1, 436.0], [97.2, 436.0], [97.3, 436.0], [97.4, 436.0], [97.5, 436.0], [97.6, 436.0], [97.7, 436.0], [97.8, 436.0], [97.9, 436.0], [98.0, 436.0], [98.1, 436.0], [98.2, 436.0], [98.3, 436.0], [98.4, 436.0], [98.5, 436.0], [98.6, 436.0], [98.7, 436.0], [98.8, 436.0], [98.9, 436.0], [99.0, 436.0], [99.1, 436.0], [99.2, 436.0], [99.3, 436.0], [99.4, 436.0], [99.5, 436.0], [99.6, 436.0], [99.7, 436.0], [99.8, 436.0], [99.9, 436.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[0.0, 302.0], [0.1, 302.0], [0.2, 302.0], [0.3, 302.0], [0.4, 302.0], [0.5, 302.0], [0.6, 302.0], [0.7, 302.0], [0.8, 302.0], [0.9, 302.0], [1.0, 302.0], [1.1, 302.0], [1.2, 302.0], [1.3, 302.0], [1.4, 302.0], [1.5, 302.0], [1.6, 302.0], [1.7, 302.0], [1.8, 302.0], [1.9, 302.0], [2.0, 302.0], [2.1, 302.0], [2.2, 302.0], [2.3, 302.0], [2.4, 302.0], [2.5, 302.0], [2.6, 302.0], [2.7, 302.0], [2.8, 302.0], [2.9, 302.0], [3.0, 302.0], [3.1, 302.0], [3.2, 302.0], [3.3, 302.0], [3.4, 302.0], [3.5, 302.0], [3.6, 302.0], [3.7, 302.0], [3.8, 302.0], [3.9, 302.0], [4.0, 302.0], [4.1, 302.0], [4.2, 302.0], [4.3, 302.0], [4.4, 302.0], [4.5, 302.0], [4.6, 302.0], [4.7, 302.0], [4.8, 302.0], [4.9, 302.0], [5.0, 302.0], [5.1, 302.0], [5.2, 302.0], [5.3, 302.0], [5.4, 302.0], [5.5, 302.0], [5.6, 302.0], [5.7, 302.0], [5.8, 302.0], [5.9, 302.0], [6.0, 302.0], [6.1, 302.0], [6.2, 302.0], [6.3, 302.0], [6.4, 302.0], [6.5, 302.0], [6.6, 302.0], [6.7, 302.0], [6.8, 302.0], [6.9, 302.0], [7.0, 302.0], [7.1, 302.0], [7.2, 302.0], [7.3, 302.0], [7.4, 302.0], [7.5, 302.0], [7.6, 302.0], [7.7, 302.0], [7.8, 302.0], [7.9, 302.0], [8.0, 302.0], [8.1, 302.0], [8.2, 302.0], [8.3, 302.0], [8.4, 302.0], [8.5, 302.0], [8.6, 302.0], [8.7, 302.0], [8.8, 302.0], [8.9, 302.0], [9.0, 302.0], [9.1, 302.0], [9.2, 302.0], [9.3, 302.0], [9.4, 302.0], [9.5, 302.0], [9.6, 302.0], [9.7, 302.0], [9.8, 302.0], [9.9, 302.0], [10.0, 302.0], [10.1, 302.0], [10.2, 302.0], [10.3, 302.0], [10.4, 302.0], [10.5, 302.0], [10.6, 302.0], [10.7, 302.0], [10.8, 302.0], [10.9, 302.0], [11.0, 302.0], [11.1, 302.0], [11.2, 302.0], [11.3, 302.0], [11.4, 302.0], [11.5, 302.0], [11.6, 302.0], [11.7, 302.0], [11.8, 302.0], [11.9, 302.0], [12.0, 302.0], [12.1, 302.0], [12.2, 302.0], [12.3, 302.0], [12.4, 302.0], [12.5, 302.0], [12.6, 302.0], [12.7, 302.0], [12.8, 302.0], [12.9, 302.0], [13.0, 302.0], [13.1, 302.0], [13.2, 302.0], [13.3, 302.0], [13.4, 302.0], [13.5, 302.0], [13.6, 302.0], [13.7, 302.0], [13.8, 302.0], [13.9, 302.0], [14.0, 302.0], [14.1, 302.0], [14.2, 302.0], [14.3, 302.0], [14.4, 302.0], [14.5, 302.0], [14.6, 302.0], [14.7, 302.0], [14.8, 302.0], [14.9, 302.0], [15.0, 302.0], [15.1, 302.0], [15.2, 302.0], [15.3, 302.0], [15.4, 302.0], [15.5, 302.0], [15.6, 302.0], [15.7, 302.0], [15.8, 302.0], [15.9, 302.0], [16.0, 302.0], [16.1, 302.0], [16.2, 302.0], [16.3, 302.0], [16.4, 302.0], [16.5, 302.0], [16.6, 302.0], [16.7, 302.0], [16.8, 302.0], [16.9, 302.0], [17.0, 302.0], [17.1, 302.0], [17.2, 302.0], [17.3, 302.0], [17.4, 302.0], [17.5, 302.0], [17.6, 302.0], [17.7, 302.0], [17.8, 302.0], [17.9, 302.0], [18.0, 302.0], [18.1, 302.0], [18.2, 302.0], [18.3, 302.0], [18.4, 302.0], [18.5, 302.0], [18.6, 302.0], [18.7, 302.0], [18.8, 302.0], [18.9, 302.0], [19.0, 302.0], [19.1, 302.0], [19.2, 302.0], [19.3, 302.0], [19.4, 302.0], [19.5, 302.0], [19.6, 302.0], [19.7, 302.0], [19.8, 302.0], [19.9, 302.0], [20.0, 304.0], [20.1, 304.0], [20.2, 304.0], [20.3, 304.0], [20.4, 304.0], [20.5, 304.0], [20.6, 304.0], [20.7, 304.0], [20.8, 304.0], [20.9, 304.0], [21.0, 304.0], [21.1, 304.0], [21.2, 304.0], [21.3, 304.0], [21.4, 304.0], [21.5, 304.0], [21.6, 304.0], [21.7, 304.0], [21.8, 304.0], [21.9, 304.0], [22.0, 304.0], [22.1, 304.0], [22.2, 304.0], [22.3, 304.0], [22.4, 304.0], [22.5, 304.0], [22.6, 304.0], [22.7, 304.0], [22.8, 304.0], [22.9, 304.0], [23.0, 304.0], [23.1, 304.0], [23.2, 304.0], [23.3, 304.0], [23.4, 304.0], [23.5, 304.0], [23.6, 304.0], [23.7, 304.0], [23.8, 304.0], [23.9, 304.0], [24.0, 304.0], [24.1, 304.0], [24.2, 304.0], [24.3, 304.0], [24.4, 304.0], [24.5, 304.0], [24.6, 304.0], [24.7, 304.0], [24.8, 304.0], [24.9, 304.0], [25.0, 304.0], [25.1, 304.0], [25.2, 304.0], [25.3, 304.0], [25.4, 304.0], [25.5, 304.0], [25.6, 304.0], [25.7, 304.0], [25.8, 304.0], [25.9, 304.0], [26.0, 304.0], [26.1, 304.0], [26.2, 304.0], [26.3, 304.0], [26.4, 304.0], [26.5, 304.0], [26.6, 304.0], [26.7, 304.0], [26.8, 304.0], [26.9, 304.0], [27.0, 304.0], [27.1, 304.0], [27.2, 304.0], [27.3, 304.0], [27.4, 304.0], [27.5, 304.0], [27.6, 304.0], [27.7, 304.0], [27.8, 304.0], [27.9, 304.0], [28.0, 304.0], [28.1, 304.0], [28.2, 304.0], [28.3, 304.0], [28.4, 304.0], [28.5, 304.0], [28.6, 304.0], [28.7, 304.0], [28.8, 304.0], [28.9, 304.0], [29.0, 304.0], [29.1, 304.0], [29.2, 304.0], [29.3, 304.0], [29.4, 304.0], [29.5, 304.0], [29.6, 304.0], [29.7, 304.0], [29.8, 304.0], [29.9, 304.0], [30.0, 308.0], [30.1, 308.0], [30.2, 308.0], [30.3, 308.0], [30.4, 308.0], [30.5, 308.0], [30.6, 308.0], [30.7, 308.0], [30.8, 308.0], [30.9, 308.0], [31.0, 308.0], [31.1, 308.0], [31.2, 308.0], [31.3, 308.0], [31.4, 308.0], [31.5, 308.0], [31.6, 308.0], [31.7, 308.0], [31.8, 308.0], [31.9, 308.0], [32.0, 308.0], [32.1, 308.0], [32.2, 308.0], [32.3, 308.0], [32.4, 308.0], [32.5, 308.0], [32.6, 308.0], [32.7, 308.0], [32.8, 308.0], [32.9, 308.0], [33.0, 308.0], [33.1, 308.0], [33.2, 308.0], [33.3, 308.0], [33.4, 308.0], [33.5, 308.0], [33.6, 308.0], [33.7, 308.0], [33.8, 308.0], [33.9, 308.0], [34.0, 308.0], [34.1, 308.0], [34.2, 308.0], [34.3, 308.0], [34.4, 308.0], [34.5, 308.0], [34.6, 308.0], [34.7, 308.0], [34.8, 308.0], [34.9, 308.0], [35.0, 308.0], [35.1, 308.0], [35.2, 308.0], [35.3, 308.0], [35.4, 308.0], [35.5, 308.0], [35.6, 308.0], [35.7, 308.0], [35.8, 308.0], [35.9, 308.0], [36.0, 308.0], [36.1, 308.0], [36.2, 308.0], [36.3, 308.0], [36.4, 308.0], [36.5, 308.0], [36.6, 308.0], [36.7, 308.0], [36.8, 308.0], [36.9, 308.0], [37.0, 308.0], [37.1, 308.0], [37.2, 308.0], [37.3, 308.0], [37.4, 308.0], [37.5, 308.0], [37.6, 308.0], [37.7, 308.0], [37.8, 308.0], [37.9, 308.0], [38.0, 308.0], [38.1, 308.0], [38.2, 308.0], [38.3, 308.0], [38.4, 308.0], [38.5, 308.0], [38.6, 308.0], [38.7, 308.0], [38.8, 308.0], [38.9, 308.0], [39.0, 308.0], [39.1, 308.0], [39.2, 308.0], [39.3, 308.0], [39.4, 308.0], [39.5, 308.0], [39.6, 308.0], [39.7, 308.0], [39.8, 308.0], [39.9, 308.0], [40.0, 308.0], [40.1, 308.0], [40.2, 308.0], [40.3, 308.0], [40.4, 308.0], [40.5, 308.0], [40.6, 308.0], [40.7, 308.0], [40.8, 308.0], [40.9, 308.0], [41.0, 308.0], [41.1, 308.0], [41.2, 308.0], [41.3, 308.0], [41.4, 308.0], [41.5, 308.0], [41.6, 308.0], [41.7, 308.0], [41.8, 308.0], [41.9, 308.0], [42.0, 308.0], [42.1, 308.0], [42.2, 308.0], [42.3, 308.0], [42.4, 308.0], [42.5, 308.0], [42.6, 308.0], [42.7, 308.0], [42.8, 308.0], [42.9, 308.0], [43.0, 308.0], [43.1, 308.0], [43.2, 308.0], [43.3, 308.0], [43.4, 308.0], [43.5, 308.0], [43.6, 308.0], [43.7, 308.0], [43.8, 308.0], [43.9, 308.0], [44.0, 308.0], [44.1, 308.0], [44.2, 308.0], [44.3, 308.0], [44.4, 308.0], [44.5, 308.0], [44.6, 308.0], [44.7, 308.0], [44.8, 308.0], [44.9, 308.0], [45.0, 308.0], [45.1, 308.0], [45.2, 308.0], [45.3, 308.0], [45.4, 308.0], [45.5, 308.0], [45.6, 308.0], [45.7, 308.0], [45.8, 308.0], [45.9, 308.0], [46.0, 308.0], [46.1, 308.0], [46.2, 308.0], [46.3, 308.0], [46.4, 308.0], [46.5, 308.0], [46.6, 308.0], [46.7, 308.0], [46.8, 308.0], [46.9, 308.0], [47.0, 308.0], [47.1, 308.0], [47.2, 308.0], [47.3, 308.0], [47.4, 308.0], [47.5, 308.0], [47.6, 308.0], [47.7, 308.0], [47.8, 308.0], [47.9, 308.0], [48.0, 308.0], [48.1, 308.0], [48.2, 308.0], [48.3, 308.0], [48.4, 308.0], [48.5, 308.0], [48.6, 308.0], [48.7, 308.0], [48.8, 308.0], [48.9, 308.0], [49.0, 308.0], [49.1, 308.0], [49.2, 308.0], [49.3, 308.0], [49.4, 308.0], [49.5, 308.0], [49.6, 308.0], [49.7, 308.0], [49.8, 308.0], [49.9, 308.0], [50.0, 310.0], [50.1, 310.0], [50.2, 310.0], [50.3, 310.0], [50.4, 310.0], [50.5, 310.0], [50.6, 310.0], [50.7, 310.0], [50.8, 310.0], [50.9, 310.0], [51.0, 310.0], [51.1, 310.0], [51.2, 310.0], [51.3, 310.0], [51.4, 310.0], [51.5, 310.0], [51.6, 310.0], [51.7, 310.0], [51.8, 310.0], [51.9, 310.0], [52.0, 310.0], [52.1, 310.0], [52.2, 310.0], [52.3, 310.0], [52.4, 310.0], [52.5, 310.0], [52.6, 310.0], [52.7, 310.0], [52.8, 310.0], [52.9, 310.0], [53.0, 310.0], [53.1, 310.0], [53.2, 310.0], [53.3, 310.0], [53.4, 310.0], [53.5, 310.0], [53.6, 310.0], [53.7, 310.0], [53.8, 310.0], [53.9, 310.0], [54.0, 310.0], [54.1, 310.0], [54.2, 310.0], [54.3, 310.0], [54.4, 310.0], [54.5, 310.0], [54.6, 310.0], [54.7, 310.0], [54.8, 310.0], [54.9, 310.0], [55.0, 310.0], [55.1, 310.0], [55.2, 310.0], [55.3, 310.0], [55.4, 310.0], [55.5, 310.0], [55.6, 310.0], [55.7, 310.0], [55.8, 310.0], [55.9, 310.0], [56.0, 310.0], [56.1, 310.0], [56.2, 310.0], [56.3, 310.0], [56.4, 310.0], [56.5, 310.0], [56.6, 310.0], [56.7, 310.0], [56.8, 310.0], [56.9, 310.0], [57.0, 310.0], [57.1, 310.0], [57.2, 310.0], [57.3, 310.0], [57.4, 310.0], [57.5, 310.0], [57.6, 310.0], [57.7, 310.0], [57.8, 310.0], [57.9, 310.0], [58.0, 310.0], [58.1, 310.0], [58.2, 310.0], [58.3, 310.0], [58.4, 310.0], [58.5, 310.0], [58.6, 310.0], [58.7, 310.0], [58.8, 310.0], [58.9, 310.0], [59.0, 310.0], [59.1, 310.0], [59.2, 310.0], [59.3, 310.0], [59.4, 310.0], [59.5, 310.0], [59.6, 310.0], [59.7, 310.0], [59.8, 310.0], [59.9, 310.0], [60.0, 311.0], [60.1, 311.0], [60.2, 311.0], [60.3, 311.0], [60.4, 311.0], [60.5, 311.0], [60.6, 311.0], [60.7, 311.0], [60.8, 311.0], [60.9, 311.0], [61.0, 311.0], [61.1, 311.0], [61.2, 311.0], [61.3, 311.0], [61.4, 311.0], [61.5, 311.0], [61.6, 311.0], [61.7, 311.0], [61.8, 311.0], [61.9, 311.0], [62.0, 311.0], [62.1, 311.0], [62.2, 311.0], [62.3, 311.0], [62.4, 311.0], [62.5, 311.0], [62.6, 311.0], [62.7, 311.0], [62.8, 311.0], [62.9, 311.0], [63.0, 311.0], [63.1, 311.0], [63.2, 311.0], [63.3, 311.0], [63.4, 311.0], [63.5, 311.0], [63.6, 311.0], [63.7, 311.0], [63.8, 311.0], [63.9, 311.0], [64.0, 311.0], [64.1, 311.0], [64.2, 311.0], [64.3, 311.0], [64.4, 311.0], [64.5, 311.0], [64.6, 311.0], [64.7, 311.0], [64.8, 311.0], [64.9, 311.0], [65.0, 311.0], [65.1, 311.0], [65.2, 311.0], [65.3, 311.0], [65.4, 311.0], [65.5, 311.0], [65.6, 311.0], [65.7, 311.0], [65.8, 311.0], [65.9, 311.0], [66.0, 311.0], [66.1, 311.0], [66.2, 311.0], [66.3, 311.0], [66.4, 311.0], [66.5, 311.0], [66.6, 311.0], [66.7, 311.0], [66.8, 311.0], [66.9, 311.0], [67.0, 311.0], [67.1, 311.0], [67.2, 311.0], [67.3, 311.0], [67.4, 311.0], [67.5, 311.0], [67.6, 311.0], [67.7, 311.0], [67.8, 311.0], [67.9, 311.0], [68.0, 311.0], [68.1, 311.0], [68.2, 311.0], [68.3, 311.0], [68.4, 311.0], [68.5, 311.0], [68.6, 311.0], [68.7, 311.0], [68.8, 311.0], [68.9, 311.0], [69.0, 311.0], [69.1, 311.0], [69.2, 311.0], [69.3, 311.0], [69.4, 311.0], [69.5, 311.0], [69.6, 311.0], [69.7, 311.0], [69.8, 311.0], [69.9, 311.0], [70.0, 314.0], [70.1, 314.0], [70.2, 314.0], [70.3, 314.0], [70.4, 314.0], [70.5, 314.0], [70.6, 314.0], [70.7, 314.0], [70.8, 314.0], [70.9, 314.0], [71.0, 314.0], [71.1, 314.0], [71.2, 314.0], [71.3, 314.0], [71.4, 314.0], [71.5, 314.0], [71.6, 314.0], [71.7, 314.0], [71.8, 314.0], [71.9, 314.0], [72.0, 314.0], [72.1, 314.0], [72.2, 314.0], [72.3, 314.0], [72.4, 314.0], [72.5, 314.0], [72.6, 314.0], [72.7, 314.0], [72.8, 314.0], [72.9, 314.0], [73.0, 314.0], [73.1, 314.0], [73.2, 314.0], [73.3, 314.0], [73.4, 314.0], [73.5, 314.0], [73.6, 314.0], [73.7, 314.0], [73.8, 314.0], [73.9, 314.0], [74.0, 314.0], [74.1, 314.0], [74.2, 314.0], [74.3, 314.0], [74.4, 314.0], [74.5, 314.0], [74.6, 314.0], [74.7, 314.0], [74.8, 314.0], [74.9, 314.0], [75.0, 314.0], [75.1, 314.0], [75.2, 314.0], [75.3, 314.0], [75.4, 314.0], [75.5, 314.0], [75.6, 314.0], [75.7, 314.0], [75.8, 314.0], [75.9, 314.0], [76.0, 314.0], [76.1, 314.0], [76.2, 314.0], [76.3, 314.0], [76.4, 314.0], [76.5, 314.0], [76.6, 314.0], [76.7, 314.0], [76.8, 314.0], [76.9, 314.0], [77.0, 314.0], [77.1, 314.0], [77.2, 314.0], [77.3, 314.0], [77.4, 314.0], [77.5, 314.0], [77.6, 314.0], [77.7, 314.0], [77.8, 314.0], [77.9, 314.0], [78.0, 314.0], [78.1, 314.0], [78.2, 314.0], [78.3, 314.0], [78.4, 314.0], [78.5, 314.0], [78.6, 314.0], [78.7, 314.0], [78.8, 314.0], [78.9, 314.0], [79.0, 314.0], [79.1, 314.0], [79.2, 314.0], [79.3, 314.0], [79.4, 314.0], [79.5, 314.0], [79.6, 314.0], [79.7, 314.0], [79.8, 314.0], [79.9, 314.0], [80.0, 318.0], [80.1, 318.0], [80.2, 318.0], [80.3, 318.0], [80.4, 318.0], [80.5, 318.0], [80.6, 318.0], [80.7, 318.0], [80.8, 318.0], [80.9, 318.0], [81.0, 318.0], [81.1, 318.0], [81.2, 318.0], [81.3, 318.0], [81.4, 318.0], [81.5, 318.0], [81.6, 318.0], [81.7, 318.0], [81.8, 318.0], [81.9, 318.0], [82.0, 318.0], [82.1, 318.0], [82.2, 318.0], [82.3, 318.0], [82.4, 318.0], [82.5, 318.0], [82.6, 318.0], [82.7, 318.0], [82.8, 318.0], [82.9, 318.0], [83.0, 318.0], [83.1, 318.0], [83.2, 318.0], [83.3, 318.0], [83.4, 318.0], [83.5, 318.0], [83.6, 318.0], [83.7, 318.0], [83.8, 318.0], [83.9, 318.0], [84.0, 318.0], [84.1, 318.0], [84.2, 318.0], [84.3, 318.0], [84.4, 318.0], [84.5, 318.0], [84.6, 318.0], [84.7, 318.0], [84.8, 318.0], [84.9, 318.0], [85.0, 318.0], [85.1, 318.0], [85.2, 318.0], [85.3, 318.0], [85.4, 318.0], [85.5, 318.0], [85.6, 318.0], [85.7, 318.0], [85.8, 318.0], [85.9, 318.0], [86.0, 318.0], [86.1, 318.0], [86.2, 318.0], [86.3, 318.0], [86.4, 318.0], [86.5, 318.0], [86.6, 318.0], [86.7, 318.0], [86.8, 318.0], [86.9, 318.0], [87.0, 318.0], [87.1, 318.0], [87.2, 318.0], [87.3, 318.0], [87.4, 318.0], [87.5, 318.0], [87.6, 318.0], [87.7, 318.0], [87.8, 318.0], [87.9, 318.0], [88.0, 318.0], [88.1, 318.0], [88.2, 318.0], [88.3, 318.0], [88.4, 318.0], [88.5, 318.0], [88.6, 318.0], [88.7, 318.0], [88.8, 318.0], [88.9, 318.0], [89.0, 318.0], [89.1, 318.0], [89.2, 318.0], [89.3, 318.0], [89.4, 318.0], [89.5, 318.0], [89.6, 318.0], [89.7, 318.0], [89.8, 318.0], [89.9, 318.0], [90.0, 319.0], [90.1, 319.0], [90.2, 319.0], [90.3, 319.0], [90.4, 319.0], [90.5, 319.0], [90.6, 319.0], [90.7, 319.0], [90.8, 319.0], [90.9, 319.0], [91.0, 319.0], [91.1, 319.0], [91.2, 319.0], [91.3, 319.0], [91.4, 319.0], [91.5, 319.0], [91.6, 319.0], [91.7, 319.0], [91.8, 319.0], [91.9, 319.0], [92.0, 319.0], [92.1, 319.0], [92.2, 319.0], [92.3, 319.0], [92.4, 319.0], [92.5, 319.0], [92.6, 319.0], [92.7, 319.0], [92.8, 319.0], [92.9, 319.0], [93.0, 319.0], [93.1, 319.0], [93.2, 319.0], [93.3, 319.0], [93.4, 319.0], [93.5, 319.0], [93.6, 319.0], [93.7, 319.0], [93.8, 319.0], [93.9, 319.0], [94.0, 319.0], [94.1, 319.0], [94.2, 319.0], [94.3, 319.0], [94.4, 319.0], [94.5, 319.0], [94.6, 319.0], [94.7, 319.0], [94.8, 319.0], [94.9, 319.0], [95.0, 319.0], [95.1, 319.0], [95.2, 319.0], [95.3, 319.0], [95.4, 319.0], [95.5, 319.0], [95.6, 319.0], [95.7, 319.0], [95.8, 319.0], [95.9, 319.0], [96.0, 319.0], [96.1, 319.0], [96.2, 319.0], [96.3, 319.0], [96.4, 319.0], [96.5, 319.0], [96.6, 319.0], [96.7, 319.0], [96.8, 319.0], [96.9, 319.0], [97.0, 319.0], [97.1, 319.0], [97.2, 319.0], [97.3, 319.0], [97.4, 319.0], [97.5, 319.0], [97.6, 319.0], [97.7, 319.0], [97.8, 319.0], [97.9, 319.0], [98.0, 319.0], [98.1, 319.0], [98.2, 319.0], [98.3, 319.0], [98.4, 319.0], [98.5, 319.0], [98.6, 319.0], [98.7, 319.0], [98.8, 319.0], [98.9, 319.0], [99.0, 319.0], [99.1, 319.0], [99.2, 319.0], [99.3, 319.0], [99.4, 319.0], [99.5, 319.0], [99.6, 319.0], [99.7, 319.0], [99.8, 319.0], [99.9, 319.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[0.0, 5814.0], [0.1, 5814.0], [0.2, 5814.0], [0.3, 5814.0], [0.4, 5814.0], [0.5, 5814.0], [0.6, 5814.0], [0.7, 5814.0], [0.8, 5814.0], [0.9, 5814.0], [1.0, 5814.0], [1.1, 5814.0], [1.2, 5814.0], [1.3, 5814.0], [1.4, 5814.0], [1.5, 5814.0], [1.6, 5814.0], [1.7, 5814.0], [1.8, 5814.0], [1.9, 5814.0], [2.0, 5814.0], [2.1, 5814.0], [2.2, 5814.0], [2.3, 5814.0], [2.4, 5814.0], [2.5, 5814.0], [2.6, 5814.0], [2.7, 5814.0], [2.8, 5814.0], [2.9, 5814.0], [3.0, 5814.0], [3.1, 5814.0], [3.2, 5814.0], [3.3, 5814.0], [3.4, 5814.0], [3.5, 5814.0], [3.6, 5814.0], [3.7, 5814.0], [3.8, 5814.0], [3.9, 5814.0], [4.0, 5814.0], [4.1, 5814.0], [4.2, 5814.0], [4.3, 5814.0], [4.4, 5814.0], [4.5, 5814.0], [4.6, 5814.0], [4.7, 5814.0], [4.8, 5814.0], [4.9, 5814.0], [5.0, 5850.0], [5.1, 5850.0], [5.2, 5850.0], [5.3, 5850.0], [5.4, 5850.0], [5.5, 5850.0], [5.6, 5850.0], [5.7, 5850.0], [5.8, 5850.0], [5.9, 5850.0], [6.0, 5850.0], [6.1, 5850.0], [6.2, 5850.0], [6.3, 5850.0], [6.4, 5850.0], [6.5, 5850.0], [6.6, 5850.0], [6.7, 5850.0], [6.8, 5850.0], [6.9, 5850.0], [7.0, 5850.0], [7.1, 5850.0], [7.2, 5850.0], [7.3, 5850.0], [7.4, 5850.0], [7.5, 5850.0], [7.6, 5850.0], [7.7, 5850.0], [7.8, 5850.0], [7.9, 5850.0], [8.0, 5850.0], [8.1, 5850.0], [8.2, 5850.0], [8.3, 5850.0], [8.4, 5850.0], [8.5, 5850.0], [8.6, 5850.0], [8.7, 5850.0], [8.8, 5850.0], [8.9, 5850.0], [9.0, 5850.0], [9.1, 5850.0], [9.2, 5850.0], [9.3, 5850.0], [9.4, 5850.0], [9.5, 5850.0], [9.6, 5850.0], [9.7, 5850.0], [9.8, 5850.0], [9.9, 5850.0], [10.0, 5876.0], [10.1, 5876.0], [10.2, 5876.0], [10.3, 5876.0], [10.4, 5876.0], [10.5, 5876.0], [10.6, 5876.0], [10.7, 5876.0], [10.8, 5876.0], [10.9, 5876.0], [11.0, 5876.0], [11.1, 5876.0], [11.2, 5876.0], [11.3, 5876.0], [11.4, 5876.0], [11.5, 5876.0], [11.6, 5876.0], [11.7, 5876.0], [11.8, 5876.0], [11.9, 5876.0], [12.0, 5876.0], [12.1, 5876.0], [12.2, 5876.0], [12.3, 5876.0], [12.4, 5876.0], [12.5, 5876.0], [12.6, 5876.0], [12.7, 5876.0], [12.8, 5876.0], [12.9, 5876.0], [13.0, 5876.0], [13.1, 5876.0], [13.2, 5876.0], [13.3, 5876.0], [13.4, 5876.0], [13.5, 5876.0], [13.6, 5876.0], [13.7, 5876.0], [13.8, 5876.0], [13.9, 5876.0], [14.0, 5876.0], [14.1, 5876.0], [14.2, 5876.0], [14.3, 5876.0], [14.4, 5876.0], [14.5, 5876.0], [14.6, 5876.0], [14.7, 5876.0], [14.8, 5876.0], [14.9, 5876.0], [15.0, 5899.0], [15.1, 5899.0], [15.2, 5899.0], [15.3, 5899.0], [15.4, 5899.0], [15.5, 5899.0], [15.6, 5899.0], [15.7, 5899.0], [15.8, 5899.0], [15.9, 5899.0], [16.0, 5899.0], [16.1, 5899.0], [16.2, 5899.0], [16.3, 5899.0], [16.4, 5899.0], [16.5, 5899.0], [16.6, 5899.0], [16.7, 5899.0], [16.8, 5899.0], [16.9, 5899.0], [17.0, 5899.0], [17.1, 5899.0], [17.2, 5899.0], [17.3, 5899.0], [17.4, 5899.0], [17.5, 5899.0], [17.6, 5899.0], [17.7, 5899.0], [17.8, 5899.0], [17.9, 5899.0], [18.0, 5899.0], [18.1, 5899.0], [18.2, 5899.0], [18.3, 5899.0], [18.4, 5899.0], [18.5, 5899.0], [18.6, 5899.0], [18.7, 5899.0], [18.8, 5899.0], [18.9, 5899.0], [19.0, 5899.0], [19.1, 5899.0], [19.2, 5899.0], [19.3, 5899.0], [19.4, 5899.0], [19.5, 5899.0], [19.6, 5899.0], [19.7, 5899.0], [19.8, 5899.0], [19.9, 5899.0], [20.0, 5926.0], [20.1, 5926.0], [20.2, 5926.0], [20.3, 5926.0], [20.4, 5926.0], [20.5, 5926.0], [20.6, 5926.0], [20.7, 5926.0], [20.8, 5926.0], [20.9, 5926.0], [21.0, 5926.0], [21.1, 5926.0], [21.2, 5926.0], [21.3, 5926.0], [21.4, 5926.0], [21.5, 5926.0], [21.6, 5926.0], [21.7, 5926.0], [21.8, 5926.0], [21.9, 5926.0], [22.0, 5926.0], [22.1, 5926.0], [22.2, 5926.0], [22.3, 5926.0], [22.4, 5926.0], [22.5, 5926.0], [22.6, 5926.0], [22.7, 5926.0], [22.8, 5926.0], [22.9, 5926.0], [23.0, 5926.0], [23.1, 5926.0], [23.2, 5926.0], [23.3, 5926.0], [23.4, 5926.0], [23.5, 5926.0], [23.6, 5926.0], [23.7, 5926.0], [23.8, 5926.0], [23.9, 5926.0], [24.0, 5926.0], [24.1, 5926.0], [24.2, 5926.0], [24.3, 5926.0], [24.4, 5926.0], [24.5, 5926.0], [24.6, 5926.0], [24.7, 5926.0], [24.8, 5926.0], [24.9, 5926.0], [25.0, 5972.0], [25.1, 5972.0], [25.2, 5972.0], [25.3, 5972.0], [25.4, 5972.0], [25.5, 5972.0], [25.6, 5972.0], [25.7, 5972.0], [25.8, 5972.0], [25.9, 5972.0], [26.0, 5972.0], [26.1, 5972.0], [26.2, 5972.0], [26.3, 5972.0], [26.4, 5972.0], [26.5, 5972.0], [26.6, 5972.0], [26.7, 5972.0], [26.8, 5972.0], [26.9, 5972.0], [27.0, 5972.0], [27.1, 5972.0], [27.2, 5972.0], [27.3, 5972.0], [27.4, 5972.0], [27.5, 5972.0], [27.6, 5972.0], [27.7, 5972.0], [27.8, 5972.0], [27.9, 5972.0], [28.0, 5972.0], [28.1, 5972.0], [28.2, 5972.0], [28.3, 5972.0], [28.4, 5972.0], [28.5, 5972.0], [28.6, 5972.0], [28.7, 5972.0], [28.8, 5972.0], [28.9, 5972.0], [29.0, 5972.0], [29.1, 5972.0], [29.2, 5972.0], [29.3, 5972.0], [29.4, 5972.0], [29.5, 5972.0], [29.6, 5972.0], [29.7, 5972.0], [29.8, 5972.0], [29.9, 5972.0], [30.0, 5979.0], [30.1, 5979.0], [30.2, 5979.0], [30.3, 5979.0], [30.4, 5979.0], [30.5, 5979.0], [30.6, 5979.0], [30.7, 5979.0], [30.8, 5979.0], [30.9, 5979.0], [31.0, 5979.0], [31.1, 5979.0], [31.2, 5979.0], [31.3, 5979.0], [31.4, 5979.0], [31.5, 5979.0], [31.6, 5979.0], [31.7, 5979.0], [31.8, 5979.0], [31.9, 5979.0], [32.0, 5979.0], [32.1, 5979.0], [32.2, 5979.0], [32.3, 5979.0], [32.4, 5979.0], [32.5, 5979.0], [32.6, 5979.0], [32.7, 5979.0], [32.8, 5979.0], [32.9, 5979.0], [33.0, 5979.0], [33.1, 5979.0], [33.2, 5979.0], [33.3, 5979.0], [33.4, 5979.0], [33.5, 5979.0], [33.6, 5979.0], [33.7, 5979.0], [33.8, 5979.0], [33.9, 5979.0], [34.0, 5979.0], [34.1, 5979.0], [34.2, 5979.0], [34.3, 5979.0], [34.4, 5979.0], [34.5, 5979.0], [34.6, 5979.0], [34.7, 5979.0], [34.8, 5979.0], [34.9, 5979.0], [35.0, 5990.0], [35.1, 5990.0], [35.2, 5990.0], [35.3, 5990.0], [35.4, 5990.0], [35.5, 5990.0], [35.6, 5990.0], [35.7, 5990.0], [35.8, 5990.0], [35.9, 5990.0], [36.0, 5990.0], [36.1, 5990.0], [36.2, 5990.0], [36.3, 5990.0], [36.4, 5990.0], [36.5, 5990.0], [36.6, 5990.0], [36.7, 5990.0], [36.8, 5990.0], [36.9, 5990.0], [37.0, 5990.0], [37.1, 5990.0], [37.2, 5990.0], [37.3, 5990.0], [37.4, 5990.0], [37.5, 5990.0], [37.6, 5990.0], [37.7, 5990.0], [37.8, 5990.0], [37.9, 5990.0], [38.0, 5990.0], [38.1, 5990.0], [38.2, 5990.0], [38.3, 5990.0], [38.4, 5990.0], [38.5, 5990.0], [38.6, 5990.0], [38.7, 5990.0], [38.8, 5990.0], [38.9, 5990.0], [39.0, 5990.0], [39.1, 5990.0], [39.2, 5990.0], [39.3, 5990.0], [39.4, 5990.0], [39.5, 5990.0], [39.6, 5990.0], [39.7, 5990.0], [39.8, 5990.0], [39.9, 5990.0], [40.0, 6160.0], [40.1, 6160.0], [40.2, 6160.0], [40.3, 6160.0], [40.4, 6160.0], [40.5, 6160.0], [40.6, 6160.0], [40.7, 6160.0], [40.8, 6160.0], [40.9, 6160.0], [41.0, 6160.0], [41.1, 6160.0], [41.2, 6160.0], [41.3, 6160.0], [41.4, 6160.0], [41.5, 6160.0], [41.6, 6160.0], [41.7, 6160.0], [41.8, 6160.0], [41.9, 6160.0], [42.0, 6160.0], [42.1, 6160.0], [42.2, 6160.0], [42.3, 6160.0], [42.4, 6160.0], [42.5, 6160.0], [42.6, 6160.0], [42.7, 6160.0], [42.8, 6160.0], [42.9, 6160.0], [43.0, 6160.0], [43.1, 6160.0], [43.2, 6160.0], [43.3, 6160.0], [43.4, 6160.0], [43.5, 6160.0], [43.6, 6160.0], [43.7, 6160.0], [43.8, 6160.0], [43.9, 6160.0], [44.0, 6160.0], [44.1, 6160.0], [44.2, 6160.0], [44.3, 6160.0], [44.4, 6160.0], [44.5, 6160.0], [44.6, 6160.0], [44.7, 6160.0], [44.8, 6160.0], [44.9, 6160.0], [45.0, 6205.0], [45.1, 6205.0], [45.2, 6205.0], [45.3, 6205.0], [45.4, 6205.0], [45.5, 6205.0], [45.6, 6205.0], [45.7, 6205.0], [45.8, 6205.0], [45.9, 6205.0], [46.0, 6205.0], [46.1, 6205.0], [46.2, 6205.0], [46.3, 6205.0], [46.4, 6205.0], [46.5, 6205.0], [46.6, 6205.0], [46.7, 6205.0], [46.8, 6205.0], [46.9, 6205.0], [47.0, 6205.0], [47.1, 6205.0], [47.2, 6205.0], [47.3, 6205.0], [47.4, 6205.0], [47.5, 6205.0], [47.6, 6205.0], [47.7, 6205.0], [47.8, 6205.0], [47.9, 6205.0], [48.0, 6205.0], [48.1, 6205.0], [48.2, 6205.0], [48.3, 6205.0], [48.4, 6205.0], [48.5, 6205.0], [48.6, 6205.0], [48.7, 6205.0], [48.8, 6205.0], [48.9, 6205.0], [49.0, 6205.0], [49.1, 6205.0], [49.2, 6205.0], [49.3, 6205.0], [49.4, 6205.0], [49.5, 6205.0], [49.6, 6205.0], [49.7, 6205.0], [49.8, 6205.0], [49.9, 6205.0], [50.0, 6226.0], [50.1, 6226.0], [50.2, 6226.0], [50.3, 6226.0], [50.4, 6226.0], [50.5, 6226.0], [50.6, 6226.0], [50.7, 6226.0], [50.8, 6226.0], [50.9, 6226.0], [51.0, 6226.0], [51.1, 6226.0], [51.2, 6226.0], [51.3, 6226.0], [51.4, 6226.0], [51.5, 6226.0], [51.6, 6226.0], [51.7, 6226.0], [51.8, 6226.0], [51.9, 6226.0], [52.0, 6226.0], [52.1, 6226.0], [52.2, 6226.0], [52.3, 6226.0], [52.4, 6226.0], [52.5, 6226.0], [52.6, 6226.0], [52.7, 6226.0], [52.8, 6226.0], [52.9, 6226.0], [53.0, 6226.0], [53.1, 6226.0], [53.2, 6226.0], [53.3, 6226.0], [53.4, 6226.0], [53.5, 6226.0], [53.6, 6226.0], [53.7, 6226.0], [53.8, 6226.0], [53.9, 6226.0], [54.0, 6226.0], [54.1, 6226.0], [54.2, 6226.0], [54.3, 6226.0], [54.4, 6226.0], [54.5, 6226.0], [54.6, 6226.0], [54.7, 6226.0], [54.8, 6226.0], [54.9, 6226.0], [55.0, 6235.0], [55.1, 6235.0], [55.2, 6235.0], [55.3, 6235.0], [55.4, 6235.0], [55.5, 6235.0], [55.6, 6235.0], [55.7, 6235.0], [55.8, 6235.0], [55.9, 6235.0], [56.0, 6235.0], [56.1, 6235.0], [56.2, 6235.0], [56.3, 6235.0], [56.4, 6235.0], [56.5, 6235.0], [56.6, 6235.0], [56.7, 6235.0], [56.8, 6235.0], [56.9, 6235.0], [57.0, 6235.0], [57.1, 6235.0], [57.2, 6235.0], [57.3, 6235.0], [57.4, 6235.0], [57.5, 6235.0], [57.6, 6235.0], [57.7, 6235.0], [57.8, 6235.0], [57.9, 6235.0], [58.0, 6235.0], [58.1, 6235.0], [58.2, 6235.0], [58.3, 6235.0], [58.4, 6235.0], [58.5, 6235.0], [58.6, 6235.0], [58.7, 6235.0], [58.8, 6235.0], [58.9, 6235.0], [59.0, 6235.0], [59.1, 6235.0], [59.2, 6235.0], [59.3, 6235.0], [59.4, 6235.0], [59.5, 6235.0], [59.6, 6235.0], [59.7, 6235.0], [59.8, 6235.0], [59.9, 6235.0], [60.0, 6253.0], [60.1, 6253.0], [60.2, 6253.0], [60.3, 6253.0], [60.4, 6253.0], [60.5, 6253.0], [60.6, 6253.0], [60.7, 6253.0], [60.8, 6253.0], [60.9, 6253.0], [61.0, 6253.0], [61.1, 6253.0], [61.2, 6253.0], [61.3, 6253.0], [61.4, 6253.0], [61.5, 6253.0], [61.6, 6253.0], [61.7, 6253.0], [61.8, 6253.0], [61.9, 6253.0], [62.0, 6253.0], [62.1, 6253.0], [62.2, 6253.0], [62.3, 6253.0], [62.4, 6253.0], [62.5, 6253.0], [62.6, 6253.0], [62.7, 6253.0], [62.8, 6253.0], [62.9, 6253.0], [63.0, 6253.0], [63.1, 6253.0], [63.2, 6253.0], [63.3, 6253.0], [63.4, 6253.0], [63.5, 6253.0], [63.6, 6253.0], [63.7, 6253.0], [63.8, 6253.0], [63.9, 6253.0], [64.0, 6253.0], [64.1, 6253.0], [64.2, 6253.0], [64.3, 6253.0], [64.4, 6253.0], [64.5, 6253.0], [64.6, 6253.0], [64.7, 6253.0], [64.8, 6253.0], [64.9, 6253.0], [65.0, 6269.0], [65.1, 6269.0], [65.2, 6269.0], [65.3, 6269.0], [65.4, 6269.0], [65.5, 6269.0], [65.6, 6269.0], [65.7, 6269.0], [65.8, 6269.0], [65.9, 6269.0], [66.0, 6269.0], [66.1, 6269.0], [66.2, 6269.0], [66.3, 6269.0], [66.4, 6269.0], [66.5, 6269.0], [66.6, 6269.0], [66.7, 6269.0], [66.8, 6269.0], [66.9, 6269.0], [67.0, 6269.0], [67.1, 6269.0], [67.2, 6269.0], [67.3, 6269.0], [67.4, 6269.0], [67.5, 6269.0], [67.6, 6269.0], [67.7, 6269.0], [67.8, 6269.0], [67.9, 6269.0], [68.0, 6269.0], [68.1, 6269.0], [68.2, 6269.0], [68.3, 6269.0], [68.4, 6269.0], [68.5, 6269.0], [68.6, 6269.0], [68.7, 6269.0], [68.8, 6269.0], [68.9, 6269.0], [69.0, 6269.0], [69.1, 6269.0], [69.2, 6269.0], [69.3, 6269.0], [69.4, 6269.0], [69.5, 6269.0], [69.6, 6269.0], [69.7, 6269.0], [69.8, 6269.0], [69.9, 6269.0], [70.0, 6282.0], [70.1, 6282.0], [70.2, 6282.0], [70.3, 6282.0], [70.4, 6282.0], [70.5, 6282.0], [70.6, 6282.0], [70.7, 6282.0], [70.8, 6282.0], [70.9, 6282.0], [71.0, 6282.0], [71.1, 6282.0], [71.2, 6282.0], [71.3, 6282.0], [71.4, 6282.0], [71.5, 6282.0], [71.6, 6282.0], [71.7, 6282.0], [71.8, 6282.0], [71.9, 6282.0], [72.0, 6282.0], [72.1, 6282.0], [72.2, 6282.0], [72.3, 6282.0], [72.4, 6282.0], [72.5, 6282.0], [72.6, 6282.0], [72.7, 6282.0], [72.8, 6282.0], [72.9, 6282.0], [73.0, 6282.0], [73.1, 6282.0], [73.2, 6282.0], [73.3, 6282.0], [73.4, 6282.0], [73.5, 6282.0], [73.6, 6282.0], [73.7, 6282.0], [73.8, 6282.0], [73.9, 6282.0], [74.0, 6282.0], [74.1, 6282.0], [74.2, 6282.0], [74.3, 6282.0], [74.4, 6282.0], [74.5, 6282.0], [74.6, 6282.0], [74.7, 6282.0], [74.8, 6282.0], [74.9, 6282.0], [75.0, 6292.0], [75.1, 6292.0], [75.2, 6292.0], [75.3, 6292.0], [75.4, 6292.0], [75.5, 6292.0], [75.6, 6292.0], [75.7, 6292.0], [75.8, 6292.0], [75.9, 6292.0], [76.0, 6292.0], [76.1, 6292.0], [76.2, 6292.0], [76.3, 6292.0], [76.4, 6292.0], [76.5, 6292.0], [76.6, 6292.0], [76.7, 6292.0], [76.8, 6292.0], [76.9, 6292.0], [77.0, 6292.0], [77.1, 6292.0], [77.2, 6292.0], [77.3, 6292.0], [77.4, 6292.0], [77.5, 6292.0], [77.6, 6292.0], [77.7, 6292.0], [77.8, 6292.0], [77.9, 6292.0], [78.0, 6292.0], [78.1, 6292.0], [78.2, 6292.0], [78.3, 6292.0], [78.4, 6292.0], [78.5, 6292.0], [78.6, 6292.0], [78.7, 6292.0], [78.8, 6292.0], [78.9, 6292.0], [79.0, 6292.0], [79.1, 6292.0], [79.2, 6292.0], [79.3, 6292.0], [79.4, 6292.0], [79.5, 6292.0], [79.6, 6292.0], [79.7, 6292.0], [79.8, 6292.0], [79.9, 6292.0], [80.0, 6315.0], [80.1, 6315.0], [80.2, 6315.0], [80.3, 6315.0], [80.4, 6315.0], [80.5, 6315.0], [80.6, 6315.0], [80.7, 6315.0], [80.8, 6315.0], [80.9, 6315.0], [81.0, 6315.0], [81.1, 6315.0], [81.2, 6315.0], [81.3, 6315.0], [81.4, 6315.0], [81.5, 6315.0], [81.6, 6315.0], [81.7, 6315.0], [81.8, 6315.0], [81.9, 6315.0], [82.0, 6315.0], [82.1, 6315.0], [82.2, 6315.0], [82.3, 6315.0], [82.4, 6315.0], [82.5, 6315.0], [82.6, 6315.0], [82.7, 6315.0], [82.8, 6315.0], [82.9, 6315.0], [83.0, 6315.0], [83.1, 6315.0], [83.2, 6315.0], [83.3, 6315.0], [83.4, 6315.0], [83.5, 6315.0], [83.6, 6315.0], [83.7, 6315.0], [83.8, 6315.0], [83.9, 6315.0], [84.0, 6315.0], [84.1, 6315.0], [84.2, 6315.0], [84.3, 6315.0], [84.4, 6315.0], [84.5, 6315.0], [84.6, 6315.0], [84.7, 6315.0], [84.8, 6315.0], [84.9, 6315.0], [85.0, 6320.0], [85.1, 6320.0], [85.2, 6320.0], [85.3, 6320.0], [85.4, 6320.0], [85.5, 6320.0], [85.6, 6320.0], [85.7, 6320.0], [85.8, 6320.0], [85.9, 6320.0], [86.0, 6320.0], [86.1, 6320.0], [86.2, 6320.0], [86.3, 6320.0], [86.4, 6320.0], [86.5, 6320.0], [86.6, 6320.0], [86.7, 6320.0], [86.8, 6320.0], [86.9, 6320.0], [87.0, 6320.0], [87.1, 6320.0], [87.2, 6320.0], [87.3, 6320.0], [87.4, 6320.0], [87.5, 6320.0], [87.6, 6320.0], [87.7, 6320.0], [87.8, 6320.0], [87.9, 6320.0], [88.0, 6320.0], [88.1, 6320.0], [88.2, 6320.0], [88.3, 6320.0], [88.4, 6320.0], [88.5, 6320.0], [88.6, 6320.0], [88.7, 6320.0], [88.8, 6320.0], [88.9, 6320.0], [89.0, 6320.0], [89.1, 6320.0], [89.2, 6320.0], [89.3, 6320.0], [89.4, 6320.0], [89.5, 6320.0], [89.6, 6320.0], [89.7, 6320.0], [89.8, 6320.0], [89.9, 6320.0], [90.0, 6325.0], [90.1, 6325.0], [90.2, 6325.0], [90.3, 6325.0], [90.4, 6325.0], [90.5, 6325.0], [90.6, 6325.0], [90.7, 6325.0], [90.8, 6325.0], [90.9, 6325.0], [91.0, 6325.0], [91.1, 6325.0], [91.2, 6325.0], [91.3, 6325.0], [91.4, 6325.0], [91.5, 6325.0], [91.6, 6325.0], [91.7, 6325.0], [91.8, 6325.0], [91.9, 6325.0], [92.0, 6325.0], [92.1, 6325.0], [92.2, 6325.0], [92.3, 6325.0], [92.4, 6325.0], [92.5, 6325.0], [92.6, 6325.0], [92.7, 6325.0], [92.8, 6325.0], [92.9, 6325.0], [93.0, 6325.0], [93.1, 6325.0], [93.2, 6325.0], [93.3, 6325.0], [93.4, 6325.0], [93.5, 6325.0], [93.6, 6325.0], [93.7, 6325.0], [93.8, 6325.0], [93.9, 6325.0], [94.0, 6325.0], [94.1, 6325.0], [94.2, 6325.0], [94.3, 6325.0], [94.4, 6325.0], [94.5, 6325.0], [94.6, 6325.0], [94.7, 6325.0], [94.8, 6325.0], [94.9, 6325.0], [95.0, 6362.0], [95.1, 6362.0], [95.2, 6362.0], [95.3, 6362.0], [95.4, 6362.0], [95.5, 6362.0], [95.6, 6362.0], [95.7, 6362.0], [95.8, 6362.0], [95.9, 6362.0], [96.0, 6362.0], [96.1, 6362.0], [96.2, 6362.0], [96.3, 6362.0], [96.4, 6362.0], [96.5, 6362.0], [96.6, 6362.0], [96.7, 6362.0], [96.8, 6362.0], [96.9, 6362.0], [97.0, 6362.0], [97.1, 6362.0], [97.2, 6362.0], [97.3, 6362.0], [97.4, 6362.0], [97.5, 6362.0], [97.6, 6362.0], [97.7, 6362.0], [97.8, 6362.0], [97.9, 6362.0], [98.0, 6362.0], [98.1, 6362.0], [98.2, 6362.0], [98.3, 6362.0], [98.4, 6362.0], [98.5, 6362.0], [98.6, 6362.0], [98.7, 6362.0], [98.8, 6362.0], [98.9, 6362.0], [99.0, 6362.0], [99.1, 6362.0], [99.2, 6362.0], [99.3, 6362.0], [99.4, 6362.0], [99.5, 6362.0], [99.6, 6362.0], [99.7, 6362.0], [99.8, 6362.0], [99.9, 6362.0]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[0.0, 340.0], [0.1, 340.0], [0.2, 340.0], [0.3, 340.0], [0.4, 340.0], [0.5, 340.0], [0.6, 340.0], [0.7, 340.0], [0.8, 340.0], [0.9, 340.0], [1.0, 340.0], [1.1, 340.0], [1.2, 340.0], [1.3, 340.0], [1.4, 340.0], [1.5, 340.0], [1.6, 340.0], [1.7, 340.0], [1.8, 340.0], [1.9, 340.0], [2.0, 340.0], [2.1, 340.0], [2.2, 340.0], [2.3, 340.0], [2.4, 340.0], [2.5, 340.0], [2.6, 340.0], [2.7, 340.0], [2.8, 340.0], [2.9, 340.0], [3.0, 340.0], [3.1, 340.0], [3.2, 340.0], [3.3, 340.0], [3.4, 340.0], [3.5, 340.0], [3.6, 340.0], [3.7, 340.0], [3.8, 340.0], [3.9, 340.0], [4.0, 340.0], [4.1, 340.0], [4.2, 340.0], [4.3, 340.0], [4.4, 340.0], [4.5, 340.0], [4.6, 340.0], [4.7, 340.0], [4.8, 340.0], [4.9, 340.0], [5.0, 340.0], [5.1, 340.0], [5.2, 340.0], [5.3, 340.0], [5.4, 340.0], [5.5, 340.0], [5.6, 340.0], [5.7, 340.0], [5.8, 340.0], [5.9, 340.0], [6.0, 340.0], [6.1, 340.0], [6.2, 340.0], [6.3, 340.0], [6.4, 340.0], [6.5, 340.0], [6.6, 340.0], [6.7, 340.0], [6.8, 340.0], [6.9, 340.0], [7.0, 340.0], [7.1, 340.0], [7.2, 340.0], [7.3, 340.0], [7.4, 340.0], [7.5, 340.0], [7.6, 340.0], [7.7, 340.0], [7.8, 340.0], [7.9, 340.0], [8.0, 340.0], [8.1, 340.0], [8.2, 340.0], [8.3, 340.0], [8.4, 340.0], [8.5, 340.0], [8.6, 340.0], [8.7, 340.0], [8.8, 340.0], [8.9, 340.0], [9.0, 340.0], [9.1, 340.0], [9.2, 340.0], [9.3, 340.0], [9.4, 340.0], [9.5, 340.0], [9.6, 340.0], [9.7, 340.0], [9.8, 340.0], [9.9, 340.0], [10.0, 340.0], [10.1, 340.0], [10.2, 340.0], [10.3, 340.0], [10.4, 340.0], [10.5, 340.0], [10.6, 340.0], [10.7, 340.0], [10.8, 340.0], [10.9, 340.0], [11.0, 340.0], [11.1, 340.0], [11.2, 340.0], [11.3, 340.0], [11.4, 340.0], [11.5, 340.0], [11.6, 340.0], [11.7, 340.0], [11.8, 340.0], [11.9, 340.0], [12.0, 340.0], [12.1, 340.0], [12.2, 340.0], [12.3, 340.0], [12.4, 340.0], [12.5, 340.0], [12.6, 340.0], [12.7, 340.0], [12.8, 340.0], [12.9, 340.0], [13.0, 340.0], [13.1, 340.0], [13.2, 340.0], [13.3, 340.0], [13.4, 340.0], [13.5, 340.0], [13.6, 340.0], [13.7, 340.0], [13.8, 340.0], [13.9, 340.0], [14.0, 340.0], [14.1, 340.0], [14.2, 340.0], [14.3, 340.0], [14.4, 340.0], [14.5, 340.0], [14.6, 340.0], [14.7, 340.0], [14.8, 340.0], [14.9, 340.0], [15.0, 340.0], [15.1, 340.0], [15.2, 340.0], [15.3, 340.0], [15.4, 340.0], [15.5, 340.0], [15.6, 340.0], [15.7, 340.0], [15.8, 340.0], [15.9, 340.0], [16.0, 340.0], [16.1, 340.0], [16.2, 340.0], [16.3, 340.0], [16.4, 340.0], [16.5, 340.0], [16.6, 340.0], [16.7, 340.0], [16.8, 340.0], [16.9, 340.0], [17.0, 340.0], [17.1, 340.0], [17.2, 340.0], [17.3, 340.0], [17.4, 340.0], [17.5, 340.0], [17.6, 340.0], [17.7, 340.0], [17.8, 340.0], [17.9, 340.0], [18.0, 340.0], [18.1, 340.0], [18.2, 340.0], [18.3, 340.0], [18.4, 340.0], [18.5, 340.0], [18.6, 340.0], [18.7, 340.0], [18.8, 340.0], [18.9, 340.0], [19.0, 340.0], [19.1, 340.0], [19.2, 340.0], [19.3, 340.0], [19.4, 340.0], [19.5, 340.0], [19.6, 340.0], [19.7, 340.0], [19.8, 340.0], [19.9, 340.0], [20.0, 340.0], [20.1, 340.0], [20.2, 340.0], [20.3, 340.0], [20.4, 340.0], [20.5, 340.0], [20.6, 340.0], [20.7, 340.0], [20.8, 340.0], [20.9, 340.0], [21.0, 340.0], [21.1, 340.0], [21.2, 340.0], [21.3, 340.0], [21.4, 340.0], [21.5, 340.0], [21.6, 340.0], [21.7, 340.0], [21.8, 340.0], [21.9, 340.0], [22.0, 340.0], [22.1, 340.0], [22.2, 340.0], [22.3, 340.0], [22.4, 340.0], [22.5, 340.0], [22.6, 340.0], [22.7, 340.0], [22.8, 340.0], [22.9, 340.0], [23.0, 340.0], [23.1, 340.0], [23.2, 340.0], [23.3, 340.0], [23.4, 340.0], [23.5, 340.0], [23.6, 340.0], [23.7, 340.0], [23.8, 340.0], [23.9, 340.0], [24.0, 340.0], [24.1, 340.0], [24.2, 340.0], [24.3, 340.0], [24.4, 340.0], [24.5, 340.0], [24.6, 340.0], [24.7, 340.0], [24.8, 340.0], [24.9, 340.0], [25.0, 340.0], [25.1, 340.0], [25.2, 340.0], [25.3, 340.0], [25.4, 340.0], [25.5, 340.0], [25.6, 340.0], [25.7, 340.0], [25.8, 340.0], [25.9, 340.0], [26.0, 340.0], [26.1, 340.0], [26.2, 340.0], [26.3, 340.0], [26.4, 340.0], [26.5, 340.0], [26.6, 340.0], [26.7, 340.0], [26.8, 340.0], [26.9, 340.0], [27.0, 340.0], [27.1, 340.0], [27.2, 340.0], [27.3, 340.0], [27.4, 340.0], [27.5, 340.0], [27.6, 340.0], [27.7, 340.0], [27.8, 340.0], [27.9, 340.0], [28.0, 340.0], [28.1, 340.0], [28.2, 340.0], [28.3, 340.0], [28.4, 340.0], [28.5, 340.0], [28.6, 340.0], [28.7, 340.0], [28.8, 340.0], [28.9, 340.0], [29.0, 340.0], [29.1, 340.0], [29.2, 340.0], [29.3, 340.0], [29.4, 340.0], [29.5, 340.0], [29.6, 340.0], [29.7, 340.0], [29.8, 340.0], [29.9, 340.0], [30.0, 344.0], [30.1, 344.0], [30.2, 344.0], [30.3, 344.0], [30.4, 344.0], [30.5, 344.0], [30.6, 344.0], [30.7, 344.0], [30.8, 344.0], [30.9, 344.0], [31.0, 344.0], [31.1, 344.0], [31.2, 344.0], [31.3, 344.0], [31.4, 344.0], [31.5, 344.0], [31.6, 344.0], [31.7, 344.0], [31.8, 344.0], [31.9, 344.0], [32.0, 344.0], [32.1, 344.0], [32.2, 344.0], [32.3, 344.0], [32.4, 344.0], [32.5, 344.0], [32.6, 344.0], [32.7, 344.0], [32.8, 344.0], [32.9, 344.0], [33.0, 344.0], [33.1, 344.0], [33.2, 344.0], [33.3, 344.0], [33.4, 344.0], [33.5, 344.0], [33.6, 344.0], [33.7, 344.0], [33.8, 344.0], [33.9, 344.0], [34.0, 344.0], [34.1, 344.0], [34.2, 344.0], [34.3, 344.0], [34.4, 344.0], [34.5, 344.0], [34.6, 344.0], [34.7, 344.0], [34.8, 344.0], [34.9, 344.0], [35.0, 344.0], [35.1, 344.0], [35.2, 344.0], [35.3, 344.0], [35.4, 344.0], [35.5, 344.0], [35.6, 344.0], [35.7, 344.0], [35.8, 344.0], [35.9, 344.0], [36.0, 344.0], [36.1, 344.0], [36.2, 344.0], [36.3, 344.0], [36.4, 344.0], [36.5, 344.0], [36.6, 344.0], [36.7, 344.0], [36.8, 344.0], [36.9, 344.0], [37.0, 344.0], [37.1, 344.0], [37.2, 344.0], [37.3, 344.0], [37.4, 344.0], [37.5, 344.0], [37.6, 344.0], [37.7, 344.0], [37.8, 344.0], [37.9, 344.0], [38.0, 344.0], [38.1, 344.0], [38.2, 344.0], [38.3, 344.0], [38.4, 344.0], [38.5, 344.0], [38.6, 344.0], [38.7, 344.0], [38.8, 344.0], [38.9, 344.0], [39.0, 344.0], [39.1, 344.0], [39.2, 344.0], [39.3, 344.0], [39.4, 344.0], [39.5, 344.0], [39.6, 344.0], [39.7, 344.0], [39.8, 344.0], [39.9, 344.0], [40.0, 347.0], [40.1, 347.0], [40.2, 347.0], [40.3, 347.0], [40.4, 347.0], [40.5, 347.0], [40.6, 347.0], [40.7, 347.0], [40.8, 347.0], [40.9, 347.0], [41.0, 347.0], [41.1, 347.0], [41.2, 347.0], [41.3, 347.0], [41.4, 347.0], [41.5, 347.0], [41.6, 347.0], [41.7, 347.0], [41.8, 347.0], [41.9, 347.0], [42.0, 347.0], [42.1, 347.0], [42.2, 347.0], [42.3, 347.0], [42.4, 347.0], [42.5, 347.0], [42.6, 347.0], [42.7, 347.0], [42.8, 347.0], [42.9, 347.0], [43.0, 347.0], [43.1, 347.0], [43.2, 347.0], [43.3, 347.0], [43.4, 347.0], [43.5, 347.0], [43.6, 347.0], [43.7, 347.0], [43.8, 347.0], [43.9, 347.0], [44.0, 347.0], [44.1, 347.0], [44.2, 347.0], [44.3, 347.0], [44.4, 347.0], [44.5, 347.0], [44.6, 347.0], [44.7, 347.0], [44.8, 347.0], [44.9, 347.0], [45.0, 347.0], [45.1, 347.0], [45.2, 347.0], [45.3, 347.0], [45.4, 347.0], [45.5, 347.0], [45.6, 347.0], [45.7, 347.0], [45.8, 347.0], [45.9, 347.0], [46.0, 347.0], [46.1, 347.0], [46.2, 347.0], [46.3, 347.0], [46.4, 347.0], [46.5, 347.0], [46.6, 347.0], [46.7, 347.0], [46.8, 347.0], [46.9, 347.0], [47.0, 347.0], [47.1, 347.0], [47.2, 347.0], [47.3, 347.0], [47.4, 347.0], [47.5, 347.0], [47.6, 347.0], [47.7, 347.0], [47.8, 347.0], [47.9, 347.0], [48.0, 347.0], [48.1, 347.0], [48.2, 347.0], [48.3, 347.0], [48.4, 347.0], [48.5, 347.0], [48.6, 347.0], [48.7, 347.0], [48.8, 347.0], [48.9, 347.0], [49.0, 347.0], [49.1, 347.0], [49.2, 347.0], [49.3, 347.0], [49.4, 347.0], [49.5, 347.0], [49.6, 347.0], [49.7, 347.0], [49.8, 347.0], [49.9, 347.0], [50.0, 350.0], [50.1, 350.0], [50.2, 350.0], [50.3, 350.0], [50.4, 350.0], [50.5, 350.0], [50.6, 350.0], [50.7, 350.0], [50.8, 350.0], [50.9, 350.0], [51.0, 350.0], [51.1, 350.0], [51.2, 350.0], [51.3, 350.0], [51.4, 350.0], [51.5, 350.0], [51.6, 350.0], [51.7, 350.0], [51.8, 350.0], [51.9, 350.0], [52.0, 350.0], [52.1, 350.0], [52.2, 350.0], [52.3, 350.0], [52.4, 350.0], [52.5, 350.0], [52.6, 350.0], [52.7, 350.0], [52.8, 350.0], [52.9, 350.0], [53.0, 350.0], [53.1, 350.0], [53.2, 350.0], [53.3, 350.0], [53.4, 350.0], [53.5, 350.0], [53.6, 350.0], [53.7, 350.0], [53.8, 350.0], [53.9, 350.0], [54.0, 350.0], [54.1, 350.0], [54.2, 350.0], [54.3, 350.0], [54.4, 350.0], [54.5, 350.0], [54.6, 350.0], [54.7, 350.0], [54.8, 350.0], [54.9, 350.0], [55.0, 350.0], [55.1, 350.0], [55.2, 350.0], [55.3, 350.0], [55.4, 350.0], [55.5, 350.0], [55.6, 350.0], [55.7, 350.0], [55.8, 350.0], [55.9, 350.0], [56.0, 350.0], [56.1, 350.0], [56.2, 350.0], [56.3, 350.0], [56.4, 350.0], [56.5, 350.0], [56.6, 350.0], [56.7, 350.0], [56.8, 350.0], [56.9, 350.0], [57.0, 350.0], [57.1, 350.0], [57.2, 350.0], [57.3, 350.0], [57.4, 350.0], [57.5, 350.0], [57.6, 350.0], [57.7, 350.0], [57.8, 350.0], [57.9, 350.0], [58.0, 350.0], [58.1, 350.0], [58.2, 350.0], [58.3, 350.0], [58.4, 350.0], [58.5, 350.0], [58.6, 350.0], [58.7, 350.0], [58.8, 350.0], [58.9, 350.0], [59.0, 350.0], [59.1, 350.0], [59.2, 350.0], [59.3, 350.0], [59.4, 350.0], [59.5, 350.0], [59.6, 350.0], [59.7, 350.0], [59.8, 350.0], [59.9, 350.0], [60.0, 351.0], [60.1, 351.0], [60.2, 351.0], [60.3, 351.0], [60.4, 351.0], [60.5, 351.0], [60.6, 351.0], [60.7, 351.0], [60.8, 351.0], [60.9, 351.0], [61.0, 351.0], [61.1, 351.0], [61.2, 351.0], [61.3, 351.0], [61.4, 351.0], [61.5, 351.0], [61.6, 351.0], [61.7, 351.0], [61.8, 351.0], [61.9, 351.0], [62.0, 351.0], [62.1, 351.0], [62.2, 351.0], [62.3, 351.0], [62.4, 351.0], [62.5, 351.0], [62.6, 351.0], [62.7, 351.0], [62.8, 351.0], [62.9, 351.0], [63.0, 351.0], [63.1, 351.0], [63.2, 351.0], [63.3, 351.0], [63.4, 351.0], [63.5, 351.0], [63.6, 351.0], [63.7, 351.0], [63.8, 351.0], [63.9, 351.0], [64.0, 351.0], [64.1, 351.0], [64.2, 351.0], [64.3, 351.0], [64.4, 351.0], [64.5, 351.0], [64.6, 351.0], [64.7, 351.0], [64.8, 351.0], [64.9, 351.0], [65.0, 351.0], [65.1, 351.0], [65.2, 351.0], [65.3, 351.0], [65.4, 351.0], [65.5, 351.0], [65.6, 351.0], [65.7, 351.0], [65.8, 351.0], [65.9, 351.0], [66.0, 351.0], [66.1, 351.0], [66.2, 351.0], [66.3, 351.0], [66.4, 351.0], [66.5, 351.0], [66.6, 351.0], [66.7, 351.0], [66.8, 351.0], [66.9, 351.0], [67.0, 351.0], [67.1, 351.0], [67.2, 351.0], [67.3, 351.0], [67.4, 351.0], [67.5, 351.0], [67.6, 351.0], [67.7, 351.0], [67.8, 351.0], [67.9, 351.0], [68.0, 351.0], [68.1, 351.0], [68.2, 351.0], [68.3, 351.0], [68.4, 351.0], [68.5, 351.0], [68.6, 351.0], [68.7, 351.0], [68.8, 351.0], [68.9, 351.0], [69.0, 351.0], [69.1, 351.0], [69.2, 351.0], [69.3, 351.0], [69.4, 351.0], [69.5, 351.0], [69.6, 351.0], [69.7, 351.0], [69.8, 351.0], [69.9, 351.0], [70.0, 352.0], [70.1, 352.0], [70.2, 352.0], [70.3, 352.0], [70.4, 352.0], [70.5, 352.0], [70.6, 352.0], [70.7, 352.0], [70.8, 352.0], [70.9, 352.0], [71.0, 352.0], [71.1, 352.0], [71.2, 352.0], [71.3, 352.0], [71.4, 352.0], [71.5, 352.0], [71.6, 352.0], [71.7, 352.0], [71.8, 352.0], [71.9, 352.0], [72.0, 352.0], [72.1, 352.0], [72.2, 352.0], [72.3, 352.0], [72.4, 352.0], [72.5, 352.0], [72.6, 352.0], [72.7, 352.0], [72.8, 352.0], [72.9, 352.0], [73.0, 352.0], [73.1, 352.0], [73.2, 352.0], [73.3, 352.0], [73.4, 352.0], [73.5, 352.0], [73.6, 352.0], [73.7, 352.0], [73.8, 352.0], [73.9, 352.0], [74.0, 352.0], [74.1, 352.0], [74.2, 352.0], [74.3, 352.0], [74.4, 352.0], [74.5, 352.0], [74.6, 352.0], [74.7, 352.0], [74.8, 352.0], [74.9, 352.0], [75.0, 352.0], [75.1, 352.0], [75.2, 352.0], [75.3, 352.0], [75.4, 352.0], [75.5, 352.0], [75.6, 352.0], [75.7, 352.0], [75.8, 352.0], [75.9, 352.0], [76.0, 352.0], [76.1, 352.0], [76.2, 352.0], [76.3, 352.0], [76.4, 352.0], [76.5, 352.0], [76.6, 352.0], [76.7, 352.0], [76.8, 352.0], [76.9, 352.0], [77.0, 352.0], [77.1, 352.0], [77.2, 352.0], [77.3, 352.0], [77.4, 352.0], [77.5, 352.0], [77.6, 352.0], [77.7, 352.0], [77.8, 352.0], [77.9, 352.0], [78.0, 352.0], [78.1, 352.0], [78.2, 352.0], [78.3, 352.0], [78.4, 352.0], [78.5, 352.0], [78.6, 352.0], [78.7, 352.0], [78.8, 352.0], [78.9, 352.0], [79.0, 352.0], [79.1, 352.0], [79.2, 352.0], [79.3, 352.0], [79.4, 352.0], [79.5, 352.0], [79.6, 352.0], [79.7, 352.0], [79.8, 352.0], [79.9, 352.0], [80.0, 354.0], [80.1, 354.0], [80.2, 354.0], [80.3, 354.0], [80.4, 354.0], [80.5, 354.0], [80.6, 354.0], [80.7, 354.0], [80.8, 354.0], [80.9, 354.0], [81.0, 354.0], [81.1, 354.0], [81.2, 354.0], [81.3, 354.0], [81.4, 354.0], [81.5, 354.0], [81.6, 354.0], [81.7, 354.0], [81.8, 354.0], [81.9, 354.0], [82.0, 354.0], [82.1, 354.0], [82.2, 354.0], [82.3, 354.0], [82.4, 354.0], [82.5, 354.0], [82.6, 354.0], [82.7, 354.0], [82.8, 354.0], [82.9, 354.0], [83.0, 354.0], [83.1, 354.0], [83.2, 354.0], [83.3, 354.0], [83.4, 354.0], [83.5, 354.0], [83.6, 354.0], [83.7, 354.0], [83.8, 354.0], [83.9, 354.0], [84.0, 354.0], [84.1, 354.0], [84.2, 354.0], [84.3, 354.0], [84.4, 354.0], [84.5, 354.0], [84.6, 354.0], [84.7, 354.0], [84.8, 354.0], [84.9, 354.0], [85.0, 354.0], [85.1, 354.0], [85.2, 354.0], [85.3, 354.0], [85.4, 354.0], [85.5, 354.0], [85.6, 354.0], [85.7, 354.0], [85.8, 354.0], [85.9, 354.0], [86.0, 354.0], [86.1, 354.0], [86.2, 354.0], [86.3, 354.0], [86.4, 354.0], [86.5, 354.0], [86.6, 354.0], [86.7, 354.0], [86.8, 354.0], [86.9, 354.0], [87.0, 354.0], [87.1, 354.0], [87.2, 354.0], [87.3, 354.0], [87.4, 354.0], [87.5, 354.0], [87.6, 354.0], [87.7, 354.0], [87.8, 354.0], [87.9, 354.0], [88.0, 354.0], [88.1, 354.0], [88.2, 354.0], [88.3, 354.0], [88.4, 354.0], [88.5, 354.0], [88.6, 354.0], [88.7, 354.0], [88.8, 354.0], [88.9, 354.0], [89.0, 354.0], [89.1, 354.0], [89.2, 354.0], [89.3, 354.0], [89.4, 354.0], [89.5, 354.0], [89.6, 354.0], [89.7, 354.0], [89.8, 354.0], [89.9, 354.0], [90.0, 362.0], [90.1, 362.0], [90.2, 362.0], [90.3, 362.0], [90.4, 362.0], [90.5, 362.0], [90.6, 362.0], [90.7, 362.0], [90.8, 362.0], [90.9, 362.0], [91.0, 362.0], [91.1, 362.0], [91.2, 362.0], [91.3, 362.0], [91.4, 362.0], [91.5, 362.0], [91.6, 362.0], [91.7, 362.0], [91.8, 362.0], [91.9, 362.0], [92.0, 362.0], [92.1, 362.0], [92.2, 362.0], [92.3, 362.0], [92.4, 362.0], [92.5, 362.0], [92.6, 362.0], [92.7, 362.0], [92.8, 362.0], [92.9, 362.0], [93.0, 362.0], [93.1, 362.0], [93.2, 362.0], [93.3, 362.0], [93.4, 362.0], [93.5, 362.0], [93.6, 362.0], [93.7, 362.0], [93.8, 362.0], [93.9, 362.0], [94.0, 362.0], [94.1, 362.0], [94.2, 362.0], [94.3, 362.0], [94.4, 362.0], [94.5, 362.0], [94.6, 362.0], [94.7, 362.0], [94.8, 362.0], [94.9, 362.0], [95.0, 362.0], [95.1, 362.0], [95.2, 362.0], [95.3, 362.0], [95.4, 362.0], [95.5, 362.0], [95.6, 362.0], [95.7, 362.0], [95.8, 362.0], [95.9, 362.0], [96.0, 362.0], [96.1, 362.0], [96.2, 362.0], [96.3, 362.0], [96.4, 362.0], [96.5, 362.0], [96.6, 362.0], [96.7, 362.0], [96.8, 362.0], [96.9, 362.0], [97.0, 362.0], [97.1, 362.0], [97.2, 362.0], [97.3, 362.0], [97.4, 362.0], [97.5, 362.0], [97.6, 362.0], [97.7, 362.0], [97.8, 362.0], [97.9, 362.0], [98.0, 362.0], [98.1, 362.0], [98.2, 362.0], [98.3, 362.0], [98.4, 362.0], [98.5, 362.0], [98.6, 362.0], [98.7, 362.0], [98.8, 362.0], [98.9, 362.0], [99.0, 362.0], [99.1, 362.0], [99.2, 362.0], [99.3, 362.0], [99.4, 362.0], [99.5, 362.0], [99.6, 362.0], [99.7, 362.0], [99.8, 362.0], [99.9, 362.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[0.0, 8.0], [0.1, 8.0], [0.2, 8.0], [0.3, 8.0], [0.4, 8.0], [0.5, 8.0], [0.6, 8.0], [0.7, 8.0], [0.8, 8.0], [0.9, 8.0], [1.0, 8.0], [1.1, 8.0], [1.2, 8.0], [1.3, 8.0], [1.4, 8.0], [1.5, 8.0], [1.6, 8.0], [1.7, 8.0], [1.8, 8.0], [1.9, 8.0], [2.0, 8.0], [2.1, 8.0], [2.2, 8.0], [2.3, 8.0], [2.4, 8.0], [2.5, 8.0], [2.6, 8.0], [2.7, 8.0], [2.8, 8.0], [2.9, 8.0], [3.0, 8.0], [3.1, 8.0], [3.2, 8.0], [3.3, 8.0], [3.4, 8.0], [3.5, 8.0], [3.6, 8.0], [3.7, 8.0], [3.8, 8.0], [3.9, 8.0], [4.0, 8.0], [4.1, 8.0], [4.2, 8.0], [4.3, 8.0], [4.4, 8.0], [4.5, 8.0], [4.6, 8.0], [4.7, 8.0], [4.8, 8.0], [4.9, 8.0], [5.0, 8.0], [5.1, 8.0], [5.2, 8.0], [5.3, 8.0], [5.4, 8.0], [5.5, 8.0], [5.6, 8.0], [5.7, 8.0], [5.8, 8.0], [5.9, 8.0], [6.0, 8.0], [6.1, 8.0], [6.2, 8.0], [6.3, 8.0], [6.4, 8.0], [6.5, 8.0], [6.6, 8.0], [6.7, 8.0], [6.8, 8.0], [6.9, 8.0], [7.0, 8.0], [7.1, 8.0], [7.2, 8.0], [7.3, 8.0], [7.4, 8.0], [7.5, 8.0], [7.6, 8.0], [7.7, 8.0], [7.8, 8.0], [7.9, 8.0], [8.0, 8.0], [8.1, 8.0], [8.2, 8.0], [8.3, 8.0], [8.4, 8.0], [8.5, 8.0], [8.6, 8.0], [8.7, 8.0], [8.8, 8.0], [8.9, 8.0], [9.0, 8.0], [9.1, 8.0], [9.2, 8.0], [9.3, 8.0], [9.4, 8.0], [9.5, 8.0], [9.6, 8.0], [9.7, 8.0], [9.8, 8.0], [9.9, 8.0], [10.0, 8.0], [10.1, 8.0], [10.2, 8.0], [10.3, 8.0], [10.4, 8.0], [10.5, 8.0], [10.6, 8.0], [10.7, 8.0], [10.8, 8.0], [10.9, 8.0], [11.0, 8.0], [11.1, 8.0], [11.2, 8.0], [11.3, 8.0], [11.4, 8.0], [11.5, 8.0], [11.6, 8.0], [11.7, 8.0], [11.8, 8.0], [11.9, 8.0], [12.0, 8.0], [12.1, 8.0], [12.2, 8.0], [12.3, 8.0], [12.4, 8.0], [12.5, 8.0], [12.6, 8.0], [12.7, 8.0], [12.8, 8.0], [12.9, 8.0], [13.0, 8.0], [13.1, 8.0], [13.2, 8.0], [13.3, 8.0], [13.4, 8.0], [13.5, 8.0], [13.6, 8.0], [13.7, 8.0], [13.8, 8.0], [13.9, 8.0], [14.0, 8.0], [14.1, 8.0], [14.2, 8.0], [14.3, 8.0], [14.4, 8.0], [14.5, 8.0], [14.6, 8.0], [14.7, 8.0], [14.8, 8.0], [14.9, 8.0], [15.0, 8.0], [15.1, 8.0], [15.2, 8.0], [15.3, 8.0], [15.4, 8.0], [15.5, 8.0], [15.6, 8.0], [15.7, 8.0], [15.8, 8.0], [15.9, 8.0], [16.0, 8.0], [16.1, 8.0], [16.2, 8.0], [16.3, 8.0], [16.4, 8.0], [16.5, 8.0], [16.6, 8.0], [16.7, 8.0], [16.8, 8.0], [16.9, 8.0], [17.0, 8.0], [17.1, 8.0], [17.2, 8.0], [17.3, 8.0], [17.4, 8.0], [17.5, 8.0], [17.6, 8.0], [17.7, 8.0], [17.8, 8.0], [17.9, 8.0], [18.0, 8.0], [18.1, 8.0], [18.2, 8.0], [18.3, 8.0], [18.4, 8.0], [18.5, 8.0], [18.6, 8.0], [18.7, 8.0], [18.8, 8.0], [18.9, 8.0], [19.0, 8.0], [19.1, 8.0], [19.2, 8.0], [19.3, 8.0], [19.4, 8.0], [19.5, 8.0], [19.6, 8.0], [19.7, 8.0], [19.8, 8.0], [19.9, 8.0], [20.0, 8.0], [20.1, 8.0], [20.2, 8.0], [20.3, 8.0], [20.4, 8.0], [20.5, 8.0], [20.6, 8.0], [20.7, 8.0], [20.8, 8.0], [20.9, 8.0], [21.0, 8.0], [21.1, 8.0], [21.2, 8.0], [21.3, 8.0], [21.4, 8.0], [21.5, 8.0], [21.6, 8.0], [21.7, 8.0], [21.8, 8.0], [21.9, 8.0], [22.0, 8.0], [22.1, 8.0], [22.2, 8.0], [22.3, 8.0], [22.4, 8.0], [22.5, 8.0], [22.6, 8.0], [22.7, 8.0], [22.8, 8.0], [22.9, 8.0], [23.0, 8.0], [23.1, 8.0], [23.2, 8.0], [23.3, 8.0], [23.4, 8.0], [23.5, 8.0], [23.6, 8.0], [23.7, 8.0], [23.8, 8.0], [23.9, 8.0], [24.0, 8.0], [24.1, 8.0], [24.2, 8.0], [24.3, 8.0], [24.4, 8.0], [24.5, 8.0], [24.6, 8.0], [24.7, 8.0], [24.8, 8.0], [24.9, 8.0], [25.0, 8.0], [25.1, 8.0], [25.2, 8.0], [25.3, 8.0], [25.4, 8.0], [25.5, 8.0], [25.6, 8.0], [25.7, 8.0], [25.8, 8.0], [25.9, 8.0], [26.0, 8.0], [26.1, 8.0], [26.2, 8.0], [26.3, 8.0], [26.4, 8.0], [26.5, 8.0], [26.6, 8.0], [26.7, 9.0], [26.8, 9.0], [26.9, 9.0], [27.0, 9.0], [27.1, 9.0], [27.2, 9.0], [27.3, 9.0], [27.4, 9.0], [27.5, 9.0], [27.6, 9.0], [27.7, 9.0], [27.8, 9.0], [27.9, 9.0], [28.0, 9.0], [28.1, 9.0], [28.2, 9.0], [28.3, 9.0], [28.4, 9.0], [28.5, 9.0], [28.6, 9.0], [28.7, 9.0], [28.8, 9.0], [28.9, 9.0], [29.0, 9.0], [29.1, 9.0], [29.2, 9.0], [29.3, 9.0], [29.4, 9.0], [29.5, 9.0], [29.6, 9.0], [29.7, 9.0], [29.8, 9.0], [29.9, 9.0], [30.0, 9.0], [30.1, 9.0], [30.2, 9.0], [30.3, 9.0], [30.4, 9.0], [30.5, 9.0], [30.6, 9.0], [30.7, 9.0], [30.8, 9.0], [30.9, 9.0], [31.0, 9.0], [31.1, 9.0], [31.2, 9.0], [31.3, 9.0], [31.4, 9.0], [31.5, 9.0], [31.6, 9.0], [31.7, 9.0], [31.8, 9.0], [31.9, 9.0], [32.0, 9.0], [32.1, 9.0], [32.2, 9.0], [32.3, 9.0], [32.4, 9.0], [32.5, 9.0], [32.6, 9.0], [32.7, 9.0], [32.8, 9.0], [32.9, 9.0], [33.0, 9.0], [33.1, 9.0], [33.2, 9.0], [33.3, 9.0], [33.4, 9.0], [33.5, 9.0], [33.6, 9.0], [33.7, 9.0], [33.8, 9.0], [33.9, 9.0], [34.0, 9.0], [34.1, 9.0], [34.2, 9.0], [34.3, 9.0], [34.4, 9.0], [34.5, 9.0], [34.6, 9.0], [34.7, 9.0], [34.8, 9.0], [34.9, 9.0], [35.0, 9.0], [35.1, 9.0], [35.2, 9.0], [35.3, 9.0], [35.4, 9.0], [35.5, 9.0], [35.6, 9.0], [35.7, 9.0], [35.8, 9.0], [35.9, 9.0], [36.0, 9.0], [36.1, 9.0], [36.2, 9.0], [36.3, 9.0], [36.4, 9.0], [36.5, 9.0], [36.6, 9.0], [36.7, 9.0], [36.8, 9.0], [36.9, 9.0], [37.0, 9.0], [37.1, 9.0], [37.2, 9.0], [37.3, 9.0], [37.4, 9.0], [37.5, 9.0], [37.6, 9.0], [37.7, 9.0], [37.8, 9.0], [37.9, 9.0], [38.0, 9.0], [38.1, 9.0], [38.2, 9.0], [38.3, 9.0], [38.4, 9.0], [38.5, 9.0], [38.6, 9.0], [38.7, 9.0], [38.8, 9.0], [38.9, 9.0], [39.0, 9.0], [39.1, 9.0], [39.2, 9.0], [39.3, 9.0], [39.4, 9.0], [39.5, 9.0], [39.6, 9.0], [39.7, 9.0], [39.8, 9.0], [39.9, 9.0], [40.0, 9.0], [40.1, 9.0], [40.2, 9.0], [40.3, 9.0], [40.4, 9.0], [40.5, 9.0], [40.6, 9.0], [40.7, 9.0], [40.8, 9.0], [40.9, 9.0], [41.0, 9.0], [41.1, 9.0], [41.2, 9.0], [41.3, 9.0], [41.4, 9.0], [41.5, 9.0], [41.6, 9.0], [41.7, 9.0], [41.8, 9.0], [41.9, 9.0], [42.0, 9.0], [42.1, 9.0], [42.2, 9.0], [42.3, 9.0], [42.4, 9.0], [42.5, 9.0], [42.6, 9.0], [42.7, 9.0], [42.8, 9.0], [42.9, 9.0], [43.0, 9.0], [43.1, 9.0], [43.2, 9.0], [43.3, 9.0], [43.4, 9.0], [43.5, 9.0], [43.6, 9.0], [43.7, 9.0], [43.8, 9.0], [43.9, 9.0], [44.0, 9.0], [44.1, 9.0], [44.2, 9.0], [44.3, 9.0], [44.4, 9.0], [44.5, 9.0], [44.6, 9.0], [44.7, 9.0], [44.8, 9.0], [44.9, 9.0], [45.0, 9.0], [45.1, 9.0], [45.2, 9.0], [45.3, 9.0], [45.4, 9.0], [45.5, 9.0], [45.6, 9.0], [45.7, 9.0], [45.8, 9.0], [45.9, 9.0], [46.0, 9.0], [46.1, 9.0], [46.2, 9.0], [46.3, 9.0], [46.4, 9.0], [46.5, 9.0], [46.6, 9.0], [46.7, 9.0], [46.8, 9.0], [46.9, 9.0], [47.0, 9.0], [47.1, 9.0], [47.2, 9.0], [47.3, 9.0], [47.4, 9.0], [47.5, 9.0], [47.6, 9.0], [47.7, 9.0], [47.8, 9.0], [47.9, 9.0], [48.0, 9.0], [48.1, 9.0], [48.2, 9.0], [48.3, 9.0], [48.4, 9.0], [48.5, 9.0], [48.6, 9.0], [48.7, 9.0], [48.8, 9.0], [48.9, 9.0], [49.0, 9.0], [49.1, 9.0], [49.2, 9.0], [49.3, 9.0], [49.4, 9.0], [49.5, 9.0], [49.6, 9.0], [49.7, 9.0], [49.8, 9.0], [49.9, 9.0], [50.0, 9.0], [50.1, 9.0], [50.2, 9.0], [50.3, 9.0], [50.4, 9.0], [50.5, 9.0], [50.6, 9.0], [50.7, 9.0], [50.8, 9.0], [50.9, 9.0], [51.0, 9.0], [51.1, 9.0], [51.2, 9.0], [51.3, 9.0], [51.4, 9.0], [51.5, 9.0], [51.6, 9.0], [51.7, 9.0], [51.8, 9.0], [51.9, 9.0], [52.0, 9.0], [52.1, 9.0], [52.2, 9.0], [52.3, 9.0], [52.4, 9.0], [52.5, 9.0], [52.6, 9.0], [52.7, 9.0], [52.8, 9.0], [52.9, 9.0], [53.0, 9.0], [53.1, 9.0], [53.2, 9.0], [53.3, 9.0], [53.4, 9.0], [53.5, 9.0], [53.6, 9.0], [53.7, 9.0], [53.8, 9.0], [53.9, 9.0], [54.0, 9.0], [54.1, 9.0], [54.2, 9.0], [54.3, 9.0], [54.4, 9.0], [54.5, 9.0], [54.6, 9.0], [54.7, 9.0], [54.8, 9.0], [54.9, 9.0], [55.0, 9.0], [55.1, 9.0], [55.2, 9.0], [55.3, 9.0], [55.4, 9.0], [55.5, 9.0], [55.6, 9.0], [55.7, 9.0], [55.8, 9.0], [55.9, 9.0], [56.0, 9.0], [56.1, 9.0], [56.2, 9.0], [56.3, 9.0], [56.4, 9.0], [56.5, 9.0], [56.6, 9.0], [56.7, 9.0], [56.8, 9.0], [56.9, 9.0], [57.0, 9.0], [57.1, 9.0], [57.2, 9.0], [57.3, 9.0], [57.4, 9.0], [57.5, 9.0], [57.6, 9.0], [57.7, 9.0], [57.8, 9.0], [57.9, 9.0], [58.0, 9.0], [58.1, 9.0], [58.2, 9.0], [58.3, 9.0], [58.4, 9.0], [58.5, 9.0], [58.6, 9.0], [58.7, 9.0], [58.8, 9.0], [58.9, 9.0], [59.0, 9.0], [59.1, 9.0], [59.2, 9.0], [59.3, 9.0], [59.4, 9.0], [59.5, 9.0], [59.6, 9.0], [59.7, 9.0], [59.8, 9.0], [59.9, 9.0], [60.0, 9.0], [60.1, 9.0], [60.2, 9.0], [60.3, 9.0], [60.4, 9.0], [60.5, 9.0], [60.6, 9.0], [60.7, 9.0], [60.8, 9.0], [60.9, 9.0], [61.0, 9.0], [61.1, 9.0], [61.2, 9.0], [61.3, 9.0], [61.4, 9.0], [61.5, 9.0], [61.6, 9.0], [61.7, 9.0], [61.8, 9.0], [61.9, 9.0], [62.0, 9.0], [62.1, 9.0], [62.2, 9.0], [62.3, 9.0], [62.4, 9.0], [62.5, 9.0], [62.6, 9.0], [62.7, 9.0], [62.8, 9.0], [62.9, 9.0], [63.0, 9.0], [63.1, 9.0], [63.2, 9.0], [63.3, 9.0], [63.4, 9.0], [63.5, 9.0], [63.6, 9.0], [63.7, 9.0], [63.8, 9.0], [63.9, 9.0], [64.0, 9.0], [64.1, 9.0], [64.2, 9.0], [64.3, 9.0], [64.4, 9.0], [64.5, 9.0], [64.6, 9.0], [64.7, 9.0], [64.8, 9.0], [64.9, 9.0], [65.0, 9.0], [65.1, 9.0], [65.2, 9.0], [65.3, 9.0], [65.4, 9.0], [65.5, 9.0], [65.6, 9.0], [65.7, 9.0], [65.8, 9.0], [65.9, 9.0], [66.0, 9.0], [66.1, 9.0], [66.2, 9.0], [66.3, 9.0], [66.4, 9.0], [66.5, 9.0], [66.6, 9.0], [66.7, 9.0], [66.8, 9.0], [66.9, 9.0], [67.0, 9.0], [67.1, 9.0], [67.2, 9.0], [67.3, 9.0], [67.4, 9.0], [67.5, 9.0], [67.6, 9.0], [67.7, 9.0], [67.8, 9.0], [67.9, 9.0], [68.0, 9.0], [68.1, 9.0], [68.2, 9.0], [68.3, 9.0], [68.4, 9.0], [68.5, 9.0], [68.6, 9.0], [68.7, 9.0], [68.8, 9.0], [68.9, 9.0], [69.0, 9.0], [69.1, 9.0], [69.2, 9.0], [69.3, 9.0], [69.4, 9.0], [69.5, 9.0], [69.6, 9.0], [69.7, 9.0], [69.8, 9.0], [69.9, 9.0], [70.0, 9.0], [70.1, 9.0], [70.2, 9.0], [70.3, 9.0], [70.4, 9.0], [70.5, 9.0], [70.6, 9.0], [70.7, 9.0], [70.8, 9.0], [70.9, 9.0], [71.0, 9.0], [71.1, 9.0], [71.2, 9.0], [71.3, 9.0], [71.4, 9.0], [71.5, 9.0], [71.6, 9.0], [71.7, 9.0], [71.8, 9.0], [71.9, 9.0], [72.0, 9.0], [72.1, 9.0], [72.2, 9.0], [72.3, 9.0], [72.4, 9.0], [72.5, 9.0], [72.6, 9.0], [72.7, 9.0], [72.8, 9.0], [72.9, 9.0], [73.0, 9.0], [73.1, 9.0], [73.2, 9.0], [73.3, 9.0], [73.4, 9.0], [73.5, 9.0], [73.6, 9.0], [73.7, 9.0], [73.8, 9.0], [73.9, 9.0], [74.0, 9.0], [74.1, 9.0], [74.2, 9.0], [74.3, 9.0], [74.4, 9.0], [74.5, 9.0], [74.6, 9.0], [74.7, 9.0], [74.8, 9.0], [74.9, 9.0], [75.0, 9.0], [75.1, 9.0], [75.2, 9.0], [75.3, 9.0], [75.4, 9.0], [75.5, 9.0], [75.6, 9.0], [75.7, 9.0], [75.8, 9.0], [75.9, 9.0], [76.0, 9.0], [76.1, 9.0], [76.2, 9.0], [76.3, 9.0], [76.4, 9.0], [76.5, 9.0], [76.6, 9.0], [76.7, 9.0], [76.8, 9.0], [76.9, 9.0], [77.0, 9.0], [77.1, 9.0], [77.2, 9.0], [77.3, 9.0], [77.4, 9.0], [77.5, 9.0], [77.6, 9.0], [77.7, 9.0], [77.8, 9.0], [77.9, 9.0], [78.0, 9.0], [78.1, 9.0], [78.2, 9.0], [78.3, 9.0], [78.4, 9.0], [78.5, 9.0], [78.6, 9.0], [78.7, 9.0], [78.8, 9.0], [78.9, 9.0], [79.0, 9.0], [79.1, 9.0], [79.2, 9.0], [79.3, 9.0], [79.4, 9.0], [79.5, 9.0], [79.6, 9.0], [79.7, 9.0], [79.8, 9.0], [79.9, 9.0], [80.0, 9.0], [80.1, 9.0], [80.2, 9.0], [80.3, 9.0], [80.4, 9.0], [80.5, 9.0], [80.6, 9.0], [80.7, 9.0], [80.8, 9.0], [80.9, 9.0], [81.0, 9.0], [81.1, 9.0], [81.2, 9.0], [81.3, 9.0], [81.4, 9.0], [81.5, 9.0], [81.6, 9.0], [81.7, 9.0], [81.8, 9.0], [81.9, 9.0], [82.0, 9.0], [82.1, 9.0], [82.2, 9.0], [82.3, 9.0], [82.4, 9.0], [82.5, 9.0], [82.6, 9.0], [82.7, 9.0], [82.8, 9.0], [82.9, 9.0], [83.0, 9.0], [83.1, 9.0], [83.2, 9.0], [83.3, 9.0], [83.4, 10.0], [83.5, 10.0], [83.6, 10.0], [83.7, 10.0], [83.8, 10.0], [83.9, 10.0], [84.0, 10.0], [84.1, 10.0], [84.2, 10.0], [84.3, 10.0], [84.4, 10.0], [84.5, 10.0], [84.6, 10.0], [84.7, 10.0], [84.8, 10.0], [84.9, 10.0], [85.0, 10.0], [85.1, 10.0], [85.2, 10.0], [85.3, 10.0], [85.4, 10.0], [85.5, 10.0], [85.6, 10.0], [85.7, 10.0], [85.8, 10.0], [85.9, 10.0], [86.0, 10.0], [86.1, 10.0], [86.2, 10.0], [86.3, 10.0], [86.4, 10.0], [86.5, 10.0], [86.6, 10.0], [86.7, 10.0], [86.8, 10.0], [86.9, 10.0], [87.0, 10.0], [87.1, 10.0], [87.2, 10.0], [87.3, 10.0], [87.4, 10.0], [87.5, 10.0], [87.6, 10.0], [87.7, 10.0], [87.8, 10.0], [87.9, 10.0], [88.0, 10.0], [88.1, 10.0], [88.2, 10.0], [88.3, 10.0], [88.4, 10.0], [88.5, 10.0], [88.6, 10.0], [88.7, 10.0], [88.8, 10.0], [88.9, 10.0], [89.0, 10.0], [89.1, 10.0], [89.2, 10.0], [89.3, 10.0], [89.4, 10.0], [89.5, 10.0], [89.6, 10.0], [89.7, 10.0], [89.8, 10.0], [89.9, 10.0], [90.0, 10.0], [90.1, 10.0], [90.2, 10.0], [90.3, 10.0], [90.4, 10.0], [90.5, 10.0], [90.6, 10.0], [90.7, 10.0], [90.8, 10.0], [90.9, 10.0], [91.0, 10.0], [91.1, 10.0], [91.2, 10.0], [91.3, 10.0], [91.4, 10.0], [91.5, 10.0], [91.6, 10.0], [91.7, 10.0], [91.8, 10.0], [91.9, 10.0], [92.0, 10.0], [92.1, 10.0], [92.2, 10.0], [92.3, 10.0], [92.4, 10.0], [92.5, 10.0], [92.6, 10.0], [92.7, 10.0], [92.8, 10.0], [92.9, 10.0], [93.0, 10.0], [93.1, 10.0], [93.2, 10.0], [93.3, 10.0], [93.4, 12.0], [93.5, 12.0], [93.6, 12.0], [93.7, 12.0], [93.8, 12.0], [93.9, 12.0], [94.0, 12.0], [94.1, 12.0], [94.2, 12.0], [94.3, 12.0], [94.4, 12.0], [94.5, 12.0], [94.6, 12.0], [94.7, 12.0], [94.8, 12.0], [94.9, 12.0], [95.0, 12.0], [95.1, 12.0], [95.2, 12.0], [95.3, 12.0], [95.4, 12.0], [95.5, 12.0], [95.6, 12.0], [95.7, 12.0], [95.8, 12.0], [95.9, 12.0], [96.0, 12.0], [96.1, 12.0], [96.2, 12.0], [96.3, 12.0], [96.4, 12.0], [96.5, 12.0], [96.6, 12.0], [96.7, 20.0], [96.8, 20.0], [96.9, 20.0], [97.0, 20.0], [97.1, 20.0], [97.2, 20.0], [97.3, 20.0], [97.4, 20.0], [97.5, 20.0], [97.6, 20.0], [97.7, 20.0], [97.8, 20.0], [97.9, 20.0], [98.0, 20.0], [98.1, 20.0], [98.2, 20.0], [98.3, 20.0], [98.4, 20.0], [98.5, 20.0], [98.6, 20.0], [98.7, 20.0], [98.8, 20.0], [98.9, 20.0], [99.0, 20.0], [99.1, 20.0], [99.2, 20.0], [99.3, 20.0], [99.4, 20.0], [99.5, 20.0], [99.6, 20.0], [99.7, 20.0], [99.8, 20.0], [99.9, 20.0]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[0.0, 6551.0], [0.1, 6551.0], [0.2, 6551.0], [0.3, 6551.0], [0.4, 6551.0], [0.5, 6551.0], [0.6, 6551.0], [0.7, 6551.0], [0.8, 6551.0], [0.9, 6551.0], [1.0, 6551.0], [1.1, 6551.0], [1.2, 6551.0], [1.3, 6551.0], [1.4, 6551.0], [1.5, 6551.0], [1.6, 6551.0], [1.7, 6551.0], [1.8, 6551.0], [1.9, 6551.0], [2.0, 6551.0], [2.1, 6551.0], [2.2, 6551.0], [2.3, 6551.0], [2.4, 6551.0], [2.5, 6551.0], [2.6, 6551.0], [2.7, 6551.0], [2.8, 6551.0], [2.9, 6551.0], [3.0, 6551.0], [3.1, 6551.0], [3.2, 6551.0], [3.3, 6551.0], [3.4, 6551.0], [3.5, 6551.0], [3.6, 6551.0], [3.7, 6551.0], [3.8, 6551.0], [3.9, 6551.0], [4.0, 6551.0], [4.1, 6551.0], [4.2, 6551.0], [4.3, 6551.0], [4.4, 6551.0], [4.5, 6551.0], [4.6, 6551.0], [4.7, 6551.0], [4.8, 6551.0], [4.9, 6551.0], [5.0, 6551.0], [5.1, 6551.0], [5.2, 6551.0], [5.3, 6551.0], [5.4, 6551.0], [5.5, 6551.0], [5.6, 6551.0], [5.7, 6551.0], [5.8, 6551.0], [5.9, 6551.0], [6.0, 6551.0], [6.1, 6551.0], [6.2, 6551.0], [6.3, 6551.0], [6.4, 6551.0], [6.5, 6551.0], [6.6, 6551.0], [6.7, 6551.0], [6.8, 6551.0], [6.9, 6551.0], [7.0, 6551.0], [7.1, 6551.0], [7.2, 6551.0], [7.3, 6551.0], [7.4, 6551.0], [7.5, 6551.0], [7.6, 6551.0], [7.7, 6551.0], [7.8, 6551.0], [7.9, 6551.0], [8.0, 6551.0], [8.1, 6551.0], [8.2, 6551.0], [8.3, 6551.0], [8.4, 6551.0], [8.5, 6551.0], [8.6, 6551.0], [8.7, 6551.0], [8.8, 6551.0], [8.9, 6551.0], [9.0, 6551.0], [9.1, 6551.0], [9.2, 6551.0], [9.3, 6551.0], [9.4, 6551.0], [9.5, 6551.0], [9.6, 6551.0], [9.7, 6551.0], [9.8, 6551.0], [9.9, 6551.0], [10.0, 6561.0], [10.1, 6561.0], [10.2, 6561.0], [10.3, 6561.0], [10.4, 6561.0], [10.5, 6561.0], [10.6, 6561.0], [10.7, 6561.0], [10.8, 6561.0], [10.9, 6561.0], [11.0, 6561.0], [11.1, 6561.0], [11.2, 6561.0], [11.3, 6561.0], [11.4, 6561.0], [11.5, 6561.0], [11.6, 6561.0], [11.7, 6561.0], [11.8, 6561.0], [11.9, 6561.0], [12.0, 6561.0], [12.1, 6561.0], [12.2, 6561.0], [12.3, 6561.0], [12.4, 6561.0], [12.5, 6561.0], [12.6, 6561.0], [12.7, 6561.0], [12.8, 6561.0], [12.9, 6561.0], [13.0, 6561.0], [13.1, 6561.0], [13.2, 6561.0], [13.3, 6561.0], [13.4, 6561.0], [13.5, 6561.0], [13.6, 6561.0], [13.7, 6561.0], [13.8, 6561.0], [13.9, 6561.0], [14.0, 6561.0], [14.1, 6561.0], [14.2, 6561.0], [14.3, 6561.0], [14.4, 6561.0], [14.5, 6561.0], [14.6, 6561.0], [14.7, 6561.0], [14.8, 6561.0], [14.9, 6561.0], [15.0, 6561.0], [15.1, 6561.0], [15.2, 6561.0], [15.3, 6561.0], [15.4, 6561.0], [15.5, 6561.0], [15.6, 6561.0], [15.7, 6561.0], [15.8, 6561.0], [15.9, 6561.0], [16.0, 6561.0], [16.1, 6561.0], [16.2, 6561.0], [16.3, 6561.0], [16.4, 6561.0], [16.5, 6561.0], [16.6, 6561.0], [16.7, 6561.0], [16.8, 6561.0], [16.9, 6561.0], [17.0, 6561.0], [17.1, 6561.0], [17.2, 6561.0], [17.3, 6561.0], [17.4, 6561.0], [17.5, 6561.0], [17.6, 6561.0], [17.7, 6561.0], [17.8, 6561.0], [17.9, 6561.0], [18.0, 6561.0], [18.1, 6561.0], [18.2, 6561.0], [18.3, 6561.0], [18.4, 6561.0], [18.5, 6561.0], [18.6, 6561.0], [18.7, 6561.0], [18.8, 6561.0], [18.9, 6561.0], [19.0, 6561.0], [19.1, 6561.0], [19.2, 6561.0], [19.3, 6561.0], [19.4, 6561.0], [19.5, 6561.0], [19.6, 6561.0], [19.7, 6561.0], [19.8, 6561.0], [19.9, 6561.0], [20.0, 6584.0], [20.1, 6584.0], [20.2, 6584.0], [20.3, 6584.0], [20.4, 6584.0], [20.5, 6584.0], [20.6, 6584.0], [20.7, 6584.0], [20.8, 6584.0], [20.9, 6584.0], [21.0, 6584.0], [21.1, 6584.0], [21.2, 6584.0], [21.3, 6584.0], [21.4, 6584.0], [21.5, 6584.0], [21.6, 6584.0], [21.7, 6584.0], [21.8, 6584.0], [21.9, 6584.0], [22.0, 6584.0], [22.1, 6584.0], [22.2, 6584.0], [22.3, 6584.0], [22.4, 6584.0], [22.5, 6584.0], [22.6, 6584.0], [22.7, 6584.0], [22.8, 6584.0], [22.9, 6584.0], [23.0, 6584.0], [23.1, 6584.0], [23.2, 6584.0], [23.3, 6584.0], [23.4, 6584.0], [23.5, 6584.0], [23.6, 6584.0], [23.7, 6584.0], [23.8, 6584.0], [23.9, 6584.0], [24.0, 6584.0], [24.1, 6584.0], [24.2, 6584.0], [24.3, 6584.0], [24.4, 6584.0], [24.5, 6584.0], [24.6, 6584.0], [24.7, 6584.0], [24.8, 6584.0], [24.9, 6584.0], [25.0, 6584.0], [25.1, 6584.0], [25.2, 6584.0], [25.3, 6584.0], [25.4, 6584.0], [25.5, 6584.0], [25.6, 6584.0], [25.7, 6584.0], [25.8, 6584.0], [25.9, 6584.0], [26.0, 6584.0], [26.1, 6584.0], [26.2, 6584.0], [26.3, 6584.0], [26.4, 6584.0], [26.5, 6584.0], [26.6, 6584.0], [26.7, 6584.0], [26.8, 6584.0], [26.9, 6584.0], [27.0, 6584.0], [27.1, 6584.0], [27.2, 6584.0], [27.3, 6584.0], [27.4, 6584.0], [27.5, 6584.0], [27.6, 6584.0], [27.7, 6584.0], [27.8, 6584.0], [27.9, 6584.0], [28.0, 6584.0], [28.1, 6584.0], [28.2, 6584.0], [28.3, 6584.0], [28.4, 6584.0], [28.5, 6584.0], [28.6, 6584.0], [28.7, 6584.0], [28.8, 6584.0], [28.9, 6584.0], [29.0, 6584.0], [29.1, 6584.0], [29.2, 6584.0], [29.3, 6584.0], [29.4, 6584.0], [29.5, 6584.0], [29.6, 6584.0], [29.7, 6584.0], [29.8, 6584.0], [29.9, 6584.0], [30.0, 6614.0], [30.1, 6614.0], [30.2, 6614.0], [30.3, 6614.0], [30.4, 6614.0], [30.5, 6614.0], [30.6, 6614.0], [30.7, 6614.0], [30.8, 6614.0], [30.9, 6614.0], [31.0, 6614.0], [31.1, 6614.0], [31.2, 6614.0], [31.3, 6614.0], [31.4, 6614.0], [31.5, 6614.0], [31.6, 6614.0], [31.7, 6614.0], [31.8, 6614.0], [31.9, 6614.0], [32.0, 6614.0], [32.1, 6614.0], [32.2, 6614.0], [32.3, 6614.0], [32.4, 6614.0], [32.5, 6614.0], [32.6, 6614.0], [32.7, 6614.0], [32.8, 6614.0], [32.9, 6614.0], [33.0, 6614.0], [33.1, 6614.0], [33.2, 6614.0], [33.3, 6614.0], [33.4, 6614.0], [33.5, 6614.0], [33.6, 6614.0], [33.7, 6614.0], [33.8, 6614.0], [33.9, 6614.0], [34.0, 6614.0], [34.1, 6614.0], [34.2, 6614.0], [34.3, 6614.0], [34.4, 6614.0], [34.5, 6614.0], [34.6, 6614.0], [34.7, 6614.0], [34.8, 6614.0], [34.9, 6614.0], [35.0, 6614.0], [35.1, 6614.0], [35.2, 6614.0], [35.3, 6614.0], [35.4, 6614.0], [35.5, 6614.0], [35.6, 6614.0], [35.7, 6614.0], [35.8, 6614.0], [35.9, 6614.0], [36.0, 6614.0], [36.1, 6614.0], [36.2, 6614.0], [36.3, 6614.0], [36.4, 6614.0], [36.5, 6614.0], [36.6, 6614.0], [36.7, 6614.0], [36.8, 6614.0], [36.9, 6614.0], [37.0, 6614.0], [37.1, 6614.0], [37.2, 6614.0], [37.3, 6614.0], [37.4, 6614.0], [37.5, 6614.0], [37.6, 6614.0], [37.7, 6614.0], [37.8, 6614.0], [37.9, 6614.0], [38.0, 6614.0], [38.1, 6614.0], [38.2, 6614.0], [38.3, 6614.0], [38.4, 6614.0], [38.5, 6614.0], [38.6, 6614.0], [38.7, 6614.0], [38.8, 6614.0], [38.9, 6614.0], [39.0, 6614.0], [39.1, 6614.0], [39.2, 6614.0], [39.3, 6614.0], [39.4, 6614.0], [39.5, 6614.0], [39.6, 6614.0], [39.7, 6614.0], [39.8, 6614.0], [39.9, 6614.0], [40.0, 6621.0], [40.1, 6621.0], [40.2, 6621.0], [40.3, 6621.0], [40.4, 6621.0], [40.5, 6621.0], [40.6, 6621.0], [40.7, 6621.0], [40.8, 6621.0], [40.9, 6621.0], [41.0, 6621.0], [41.1, 6621.0], [41.2, 6621.0], [41.3, 6621.0], [41.4, 6621.0], [41.5, 6621.0], [41.6, 6621.0], [41.7, 6621.0], [41.8, 6621.0], [41.9, 6621.0], [42.0, 6621.0], [42.1, 6621.0], [42.2, 6621.0], [42.3, 6621.0], [42.4, 6621.0], [42.5, 6621.0], [42.6, 6621.0], [42.7, 6621.0], [42.8, 6621.0], [42.9, 6621.0], [43.0, 6621.0], [43.1, 6621.0], [43.2, 6621.0], [43.3, 6621.0], [43.4, 6621.0], [43.5, 6621.0], [43.6, 6621.0], [43.7, 6621.0], [43.8, 6621.0], [43.9, 6621.0], [44.0, 6621.0], [44.1, 6621.0], [44.2, 6621.0], [44.3, 6621.0], [44.4, 6621.0], [44.5, 6621.0], [44.6, 6621.0], [44.7, 6621.0], [44.8, 6621.0], [44.9, 6621.0], [45.0, 6621.0], [45.1, 6621.0], [45.2, 6621.0], [45.3, 6621.0], [45.4, 6621.0], [45.5, 6621.0], [45.6, 6621.0], [45.7, 6621.0], [45.8, 6621.0], [45.9, 6621.0], [46.0, 6621.0], [46.1, 6621.0], [46.2, 6621.0], [46.3, 6621.0], [46.4, 6621.0], [46.5, 6621.0], [46.6, 6621.0], [46.7, 6621.0], [46.8, 6621.0], [46.9, 6621.0], [47.0, 6621.0], [47.1, 6621.0], [47.2, 6621.0], [47.3, 6621.0], [47.4, 6621.0], [47.5, 6621.0], [47.6, 6621.0], [47.7, 6621.0], [47.8, 6621.0], [47.9, 6621.0], [48.0, 6621.0], [48.1, 6621.0], [48.2, 6621.0], [48.3, 6621.0], [48.4, 6621.0], [48.5, 6621.0], [48.6, 6621.0], [48.7, 6621.0], [48.8, 6621.0], [48.9, 6621.0], [49.0, 6621.0], [49.1, 6621.0], [49.2, 6621.0], [49.3, 6621.0], [49.4, 6621.0], [49.5, 6621.0], [49.6, 6621.0], [49.7, 6621.0], [49.8, 6621.0], [49.9, 6621.0], [50.0, 6622.0], [50.1, 6622.0], [50.2, 6622.0], [50.3, 6622.0], [50.4, 6622.0], [50.5, 6622.0], [50.6, 6622.0], [50.7, 6622.0], [50.8, 6622.0], [50.9, 6622.0], [51.0, 6622.0], [51.1, 6622.0], [51.2, 6622.0], [51.3, 6622.0], [51.4, 6622.0], [51.5, 6622.0], [51.6, 6622.0], [51.7, 6622.0], [51.8, 6622.0], [51.9, 6622.0], [52.0, 6622.0], [52.1, 6622.0], [52.2, 6622.0], [52.3, 6622.0], [52.4, 6622.0], [52.5, 6622.0], [52.6, 6622.0], [52.7, 6622.0], [52.8, 6622.0], [52.9, 6622.0], [53.0, 6622.0], [53.1, 6622.0], [53.2, 6622.0], [53.3, 6622.0], [53.4, 6622.0], [53.5, 6622.0], [53.6, 6622.0], [53.7, 6622.0], [53.8, 6622.0], [53.9, 6622.0], [54.0, 6622.0], [54.1, 6622.0], [54.2, 6622.0], [54.3, 6622.0], [54.4, 6622.0], [54.5, 6622.0], [54.6, 6622.0], [54.7, 6622.0], [54.8, 6622.0], [54.9, 6622.0], [55.0, 6622.0], [55.1, 6622.0], [55.2, 6622.0], [55.3, 6622.0], [55.4, 6622.0], [55.5, 6622.0], [55.6, 6622.0], [55.7, 6622.0], [55.8, 6622.0], [55.9, 6622.0], [56.0, 6622.0], [56.1, 6622.0], [56.2, 6622.0], [56.3, 6622.0], [56.4, 6622.0], [56.5, 6622.0], [56.6, 6622.0], [56.7, 6622.0], [56.8, 6622.0], [56.9, 6622.0], [57.0, 6622.0], [57.1, 6622.0], [57.2, 6622.0], [57.3, 6622.0], [57.4, 6622.0], [57.5, 6622.0], [57.6, 6622.0], [57.7, 6622.0], [57.8, 6622.0], [57.9, 6622.0], [58.0, 6622.0], [58.1, 6622.0], [58.2, 6622.0], [58.3, 6622.0], [58.4, 6622.0], [58.5, 6622.0], [58.6, 6622.0], [58.7, 6622.0], [58.8, 6622.0], [58.9, 6622.0], [59.0, 6622.0], [59.1, 6622.0], [59.2, 6622.0], [59.3, 6622.0], [59.4, 6622.0], [59.5, 6622.0], [59.6, 6622.0], [59.7, 6622.0], [59.8, 6622.0], [59.9, 6622.0], [60.0, 6688.0], [60.1, 6688.0], [60.2, 6688.0], [60.3, 6688.0], [60.4, 6688.0], [60.5, 6688.0], [60.6, 6688.0], [60.7, 6688.0], [60.8, 6688.0], [60.9, 6688.0], [61.0, 6688.0], [61.1, 6688.0], [61.2, 6688.0], [61.3, 6688.0], [61.4, 6688.0], [61.5, 6688.0], [61.6, 6688.0], [61.7, 6688.0], [61.8, 6688.0], [61.9, 6688.0], [62.0, 6688.0], [62.1, 6688.0], [62.2, 6688.0], [62.3, 6688.0], [62.4, 6688.0], [62.5, 6688.0], [62.6, 6688.0], [62.7, 6688.0], [62.8, 6688.0], [62.9, 6688.0], [63.0, 6688.0], [63.1, 6688.0], [63.2, 6688.0], [63.3, 6688.0], [63.4, 6688.0], [63.5, 6688.0], [63.6, 6688.0], [63.7, 6688.0], [63.8, 6688.0], [63.9, 6688.0], [64.0, 6688.0], [64.1, 6688.0], [64.2, 6688.0], [64.3, 6688.0], [64.4, 6688.0], [64.5, 6688.0], [64.6, 6688.0], [64.7, 6688.0], [64.8, 6688.0], [64.9, 6688.0], [65.0, 6688.0], [65.1, 6688.0], [65.2, 6688.0], [65.3, 6688.0], [65.4, 6688.0], [65.5, 6688.0], [65.6, 6688.0], [65.7, 6688.0], [65.8, 6688.0], [65.9, 6688.0], [66.0, 6688.0], [66.1, 6688.0], [66.2, 6688.0], [66.3, 6688.0], [66.4, 6688.0], [66.5, 6688.0], [66.6, 6688.0], [66.7, 6688.0], [66.8, 6688.0], [66.9, 6688.0], [67.0, 6688.0], [67.1, 6688.0], [67.2, 6688.0], [67.3, 6688.0], [67.4, 6688.0], [67.5, 6688.0], [67.6, 6688.0], [67.7, 6688.0], [67.8, 6688.0], [67.9, 6688.0], [68.0, 6688.0], [68.1, 6688.0], [68.2, 6688.0], [68.3, 6688.0], [68.4, 6688.0], [68.5, 6688.0], [68.6, 6688.0], [68.7, 6688.0], [68.8, 6688.0], [68.9, 6688.0], [69.0, 6688.0], [69.1, 6688.0], [69.2, 6688.0], [69.3, 6688.0], [69.4, 6688.0], [69.5, 6688.0], [69.6, 6688.0], [69.7, 6688.0], [69.8, 6688.0], [69.9, 6688.0], [70.0, 6777.0], [70.1, 6777.0], [70.2, 6777.0], [70.3, 6777.0], [70.4, 6777.0], [70.5, 6777.0], [70.6, 6777.0], [70.7, 6777.0], [70.8, 6777.0], [70.9, 6777.0], [71.0, 6777.0], [71.1, 6777.0], [71.2, 6777.0], [71.3, 6777.0], [71.4, 6777.0], [71.5, 6777.0], [71.6, 6777.0], [71.7, 6777.0], [71.8, 6777.0], [71.9, 6777.0], [72.0, 6777.0], [72.1, 6777.0], [72.2, 6777.0], [72.3, 6777.0], [72.4, 6777.0], [72.5, 6777.0], [72.6, 6777.0], [72.7, 6777.0], [72.8, 6777.0], [72.9, 6777.0], [73.0, 6777.0], [73.1, 6777.0], [73.2, 6777.0], [73.3, 6777.0], [73.4, 6777.0], [73.5, 6777.0], [73.6, 6777.0], [73.7, 6777.0], [73.8, 6777.0], [73.9, 6777.0], [74.0, 6777.0], [74.1, 6777.0], [74.2, 6777.0], [74.3, 6777.0], [74.4, 6777.0], [74.5, 6777.0], [74.6, 6777.0], [74.7, 6777.0], [74.8, 6777.0], [74.9, 6777.0], [75.0, 6777.0], [75.1, 6777.0], [75.2, 6777.0], [75.3, 6777.0], [75.4, 6777.0], [75.5, 6777.0], [75.6, 6777.0], [75.7, 6777.0], [75.8, 6777.0], [75.9, 6777.0], [76.0, 6777.0], [76.1, 6777.0], [76.2, 6777.0], [76.3, 6777.0], [76.4, 6777.0], [76.5, 6777.0], [76.6, 6777.0], [76.7, 6777.0], [76.8, 6777.0], [76.9, 6777.0], [77.0, 6777.0], [77.1, 6777.0], [77.2, 6777.0], [77.3, 6777.0], [77.4, 6777.0], [77.5, 6777.0], [77.6, 6777.0], [77.7, 6777.0], [77.8, 6777.0], [77.9, 6777.0], [78.0, 6777.0], [78.1, 6777.0], [78.2, 6777.0], [78.3, 6777.0], [78.4, 6777.0], [78.5, 6777.0], [78.6, 6777.0], [78.7, 6777.0], [78.8, 6777.0], [78.9, 6777.0], [79.0, 6777.0], [79.1, 6777.0], [79.2, 6777.0], [79.3, 6777.0], [79.4, 6777.0], [79.5, 6777.0], [79.6, 6777.0], [79.7, 6777.0], [79.8, 6777.0], [79.9, 6777.0], [80.0, 6799.0], [80.1, 6799.0], [80.2, 6799.0], [80.3, 6799.0], [80.4, 6799.0], [80.5, 6799.0], [80.6, 6799.0], [80.7, 6799.0], [80.8, 6799.0], [80.9, 6799.0], [81.0, 6799.0], [81.1, 6799.0], [81.2, 6799.0], [81.3, 6799.0], [81.4, 6799.0], [81.5, 6799.0], [81.6, 6799.0], [81.7, 6799.0], [81.8, 6799.0], [81.9, 6799.0], [82.0, 6799.0], [82.1, 6799.0], [82.2, 6799.0], [82.3, 6799.0], [82.4, 6799.0], [82.5, 6799.0], [82.6, 6799.0], [82.7, 6799.0], [82.8, 6799.0], [82.9, 6799.0], [83.0, 6799.0], [83.1, 6799.0], [83.2, 6799.0], [83.3, 6799.0], [83.4, 6799.0], [83.5, 6799.0], [83.6, 6799.0], [83.7, 6799.0], [83.8, 6799.0], [83.9, 6799.0], [84.0, 6799.0], [84.1, 6799.0], [84.2, 6799.0], [84.3, 6799.0], [84.4, 6799.0], [84.5, 6799.0], [84.6, 6799.0], [84.7, 6799.0], [84.8, 6799.0], [84.9, 6799.0], [85.0, 6799.0], [85.1, 6799.0], [85.2, 6799.0], [85.3, 6799.0], [85.4, 6799.0], [85.5, 6799.0], [85.6, 6799.0], [85.7, 6799.0], [85.8, 6799.0], [85.9, 6799.0], [86.0, 6799.0], [86.1, 6799.0], [86.2, 6799.0], [86.3, 6799.0], [86.4, 6799.0], [86.5, 6799.0], [86.6, 6799.0], [86.7, 6799.0], [86.8, 6799.0], [86.9, 6799.0], [87.0, 6799.0], [87.1, 6799.0], [87.2, 6799.0], [87.3, 6799.0], [87.4, 6799.0], [87.5, 6799.0], [87.6, 6799.0], [87.7, 6799.0], [87.8, 6799.0], [87.9, 6799.0], [88.0, 6799.0], [88.1, 6799.0], [88.2, 6799.0], [88.3, 6799.0], [88.4, 6799.0], [88.5, 6799.0], [88.6, 6799.0], [88.7, 6799.0], [88.8, 6799.0], [88.9, 6799.0], [89.0, 6799.0], [89.1, 6799.0], [89.2, 6799.0], [89.3, 6799.0], [89.4, 6799.0], [89.5, 6799.0], [89.6, 6799.0], [89.7, 6799.0], [89.8, 6799.0], [89.9, 6799.0], [90.0, 7099.0], [90.1, 7099.0], [90.2, 7099.0], [90.3, 7099.0], [90.4, 7099.0], [90.5, 7099.0], [90.6, 7099.0], [90.7, 7099.0], [90.8, 7099.0], [90.9, 7099.0], [91.0, 7099.0], [91.1, 7099.0], [91.2, 7099.0], [91.3, 7099.0], [91.4, 7099.0], [91.5, 7099.0], [91.6, 7099.0], [91.7, 7099.0], [91.8, 7099.0], [91.9, 7099.0], [92.0, 7099.0], [92.1, 7099.0], [92.2, 7099.0], [92.3, 7099.0], [92.4, 7099.0], [92.5, 7099.0], [92.6, 7099.0], [92.7, 7099.0], [92.8, 7099.0], [92.9, 7099.0], [93.0, 7099.0], [93.1, 7099.0], [93.2, 7099.0], [93.3, 7099.0], [93.4, 7099.0], [93.5, 7099.0], [93.6, 7099.0], [93.7, 7099.0], [93.8, 7099.0], [93.9, 7099.0], [94.0, 7099.0], [94.1, 7099.0], [94.2, 7099.0], [94.3, 7099.0], [94.4, 7099.0], [94.5, 7099.0], [94.6, 7099.0], [94.7, 7099.0], [94.8, 7099.0], [94.9, 7099.0], [95.0, 7099.0], [95.1, 7099.0], [95.2, 7099.0], [95.3, 7099.0], [95.4, 7099.0], [95.5, 7099.0], [95.6, 7099.0], [95.7, 7099.0], [95.8, 7099.0], [95.9, 7099.0], [96.0, 7099.0], [96.1, 7099.0], [96.2, 7099.0], [96.3, 7099.0], [96.4, 7099.0], [96.5, 7099.0], [96.6, 7099.0], [96.7, 7099.0], [96.8, 7099.0], [96.9, 7099.0], [97.0, 7099.0], [97.1, 7099.0], [97.2, 7099.0], [97.3, 7099.0], [97.4, 7099.0], [97.5, 7099.0], [97.6, 7099.0], [97.7, 7099.0], [97.8, 7099.0], [97.9, 7099.0], [98.0, 7099.0], [98.1, 7099.0], [98.2, 7099.0], [98.3, 7099.0], [98.4, 7099.0], [98.5, 7099.0], [98.6, 7099.0], [98.7, 7099.0], [98.8, 7099.0], [98.9, 7099.0], [99.0, 7099.0], [99.1, 7099.0], [99.2, 7099.0], [99.3, 7099.0], [99.4, 7099.0], [99.5, 7099.0], [99.6, 7099.0], [99.7, 7099.0], [99.8, 7099.0], [99.9, 7099.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[0.0, 7.0], [0.1, 7.0], [0.2, 7.0], [0.3, 7.0], [0.4, 7.0], [0.5, 7.0], [0.6, 7.0], [0.7, 7.0], [0.8, 7.0], [0.9, 7.0], [1.0, 7.0], [1.1, 7.0], [1.2, 7.0], [1.3, 7.0], [1.4, 7.0], [1.5, 7.0], [1.6, 7.0], [1.7, 7.0], [1.8, 7.0], [1.9, 7.0], [2.0, 7.0], [2.1, 7.0], [2.2, 7.0], [2.3, 7.0], [2.4, 7.0], [2.5, 7.0], [2.6, 7.0], [2.7, 7.0], [2.8, 7.0], [2.9, 7.0], [3.0, 7.0], [3.1, 7.0], [3.2, 7.0], [3.3, 7.0], [3.4, 7.0], [3.5, 7.0], [3.6, 7.0], [3.7, 7.0], [3.8, 7.0], [3.9, 7.0], [4.0, 7.0], [4.1, 7.0], [4.2, 7.0], [4.3, 7.0], [4.4, 7.0], [4.5, 7.0], [4.6, 7.0], [4.7, 7.0], [4.8, 7.0], [4.9, 7.0], [5.0, 7.0], [5.1, 7.0], [5.2, 7.0], [5.3, 7.0], [5.4, 7.0], [5.5, 7.0], [5.6, 7.0], [5.7, 7.0], [5.8, 7.0], [5.9, 7.0], [6.0, 7.0], [6.1, 7.0], [6.2, 7.0], [6.3, 7.0], [6.4, 7.0], [6.5, 7.0], [6.6, 7.0], [6.7, 7.0], [6.8, 7.0], [6.9, 7.0], [7.0, 7.0], [7.1, 7.0], [7.2, 7.0], [7.3, 7.0], [7.4, 7.0], [7.5, 7.0], [7.6, 7.0], [7.7, 7.0], [7.8, 7.0], [7.9, 7.0], [8.0, 7.0], [8.1, 7.0], [8.2, 7.0], [8.3, 7.0], [8.4, 7.0], [8.5, 7.0], [8.6, 7.0], [8.7, 7.0], [8.8, 7.0], [8.9, 7.0], [9.0, 7.0], [9.1, 7.0], [9.2, 7.0], [9.3, 7.0], [9.4, 7.0], [9.5, 7.0], [9.6, 7.0], [9.7, 7.0], [9.8, 7.0], [9.9, 7.0], [10.0, 7.0], [10.1, 7.0], [10.2, 7.0], [10.3, 7.0], [10.4, 7.0], [10.5, 7.0], [10.6, 7.0], [10.7, 7.0], [10.8, 7.0], [10.9, 7.0], [11.0, 7.0], [11.1, 7.0], [11.2, 7.0], [11.3, 7.0], [11.4, 7.0], [11.5, 7.0], [11.6, 7.0], [11.7, 7.0], [11.8, 7.0], [11.9, 7.0], [12.0, 7.0], [12.1, 7.0], [12.2, 7.0], [12.3, 7.0], [12.4, 7.0], [12.5, 7.0], [12.6, 7.0], [12.7, 7.0], [12.8, 7.0], [12.9, 7.0], [13.0, 7.0], [13.1, 7.0], [13.2, 7.0], [13.3, 7.0], [13.4, 7.0], [13.5, 7.0], [13.6, 7.0], [13.7, 7.0], [13.8, 7.0], [13.9, 7.0], [14.0, 7.0], [14.1, 7.0], [14.2, 7.0], [14.3, 7.0], [14.4, 7.0], [14.5, 7.0], [14.6, 7.0], [14.7, 7.0], [14.8, 7.0], [14.9, 7.0], [15.0, 7.0], [15.1, 7.0], [15.2, 7.0], [15.3, 7.0], [15.4, 7.0], [15.5, 7.0], [15.6, 7.0], [15.7, 7.0], [15.8, 7.0], [15.9, 7.0], [16.0, 7.0], [16.1, 7.0], [16.2, 7.0], [16.3, 7.0], [16.4, 7.0], [16.5, 7.0], [16.6, 7.0], [16.7, 7.0], [16.8, 7.0], [16.9, 7.0], [17.0, 7.0], [17.1, 7.0], [17.2, 7.0], [17.3, 7.0], [17.4, 7.0], [17.5, 7.0], [17.6, 7.0], [17.7, 7.0], [17.8, 7.0], [17.9, 7.0], [18.0, 8.0], [18.1, 8.0], [18.2, 8.0], [18.3, 8.0], [18.4, 8.0], [18.5, 8.0], [18.6, 8.0], [18.7, 8.0], [18.8, 8.0], [18.9, 8.0], [19.0, 8.0], [19.1, 8.0], [19.2, 8.0], [19.3, 8.0], [19.4, 8.0], [19.5, 8.0], [19.6, 8.0], [19.7, 8.0], [19.8, 8.0], [19.9, 8.0], [20.0, 8.0], [20.1, 8.0], [20.2, 8.0], [20.3, 8.0], [20.4, 8.0], [20.5, 8.0], [20.6, 8.0], [20.7, 8.0], [20.8, 8.0], [20.9, 8.0], [21.0, 8.0], [21.1, 8.0], [21.2, 8.0], [21.3, 8.0], [21.4, 8.0], [21.5, 8.0], [21.6, 8.0], [21.7, 8.0], [21.8, 8.0], [21.9, 8.0], [22.0, 8.0], [22.1, 8.0], [22.2, 8.0], [22.3, 8.0], [22.4, 8.0], [22.5, 8.0], [22.6, 8.0], [22.7, 8.0], [22.8, 8.0], [22.9, 8.0], [23.0, 8.0], [23.1, 8.0], [23.2, 8.0], [23.3, 8.0], [23.4, 8.0], [23.5, 8.0], [23.6, 8.0], [23.7, 8.0], [23.8, 8.0], [23.9, 8.0], [24.0, 8.0], [24.1, 8.0], [24.2, 8.0], [24.3, 8.0], [24.4, 8.0], [24.5, 8.0], [24.6, 8.0], [24.7, 8.0], [24.8, 8.0], [24.9, 8.0], [25.0, 8.0], [25.1, 8.0], [25.2, 8.0], [25.3, 8.0], [25.4, 8.0], [25.5, 8.0], [25.6, 8.0], [25.7, 8.0], [25.8, 8.0], [25.9, 8.0], [26.0, 8.0], [26.1, 8.0], [26.2, 8.0], [26.3, 8.0], [26.4, 8.0], [26.5, 8.0], [26.6, 8.0], [26.7, 8.0], [26.8, 8.0], [26.9, 8.0], [27.0, 8.0], [27.1, 8.0], [27.2, 8.0], [27.3, 8.0], [27.4, 8.0], [27.5, 8.0], [27.6, 8.0], [27.7, 8.0], [27.8, 8.0], [27.9, 8.0], [28.0, 8.0], [28.1, 8.0], [28.2, 8.0], [28.3, 8.0], [28.4, 8.0], [28.5, 8.0], [28.6, 8.0], [28.7, 8.0], [28.8, 8.0], [28.9, 8.0], [29.0, 8.0], [29.1, 8.0], [29.2, 8.0], [29.3, 8.0], [29.4, 8.0], [29.5, 8.0], [29.6, 8.0], [29.7, 8.0], [29.8, 8.0], [29.9, 8.0], [30.0, 8.0], [30.1, 8.0], [30.2, 8.0], [30.3, 8.0], [30.4, 8.0], [30.5, 8.0], [30.6, 8.0], [30.7, 8.0], [30.8, 8.0], [30.9, 8.0], [31.0, 8.0], [31.1, 8.0], [31.2, 8.0], [31.3, 8.0], [31.4, 8.0], [31.5, 8.0], [31.6, 8.0], [31.7, 8.0], [31.8, 8.0], [31.9, 8.0], [32.0, 8.0], [32.1, 8.0], [32.2, 8.0], [32.3, 8.0], [32.4, 8.0], [32.5, 8.0], [32.6, 8.0], [32.7, 8.0], [32.8, 8.0], [32.9, 8.0], [33.0, 8.0], [33.1, 8.0], [33.2, 8.0], [33.3, 8.0], [33.4, 8.0], [33.5, 8.0], [33.6, 8.0], [33.7, 8.0], [33.8, 8.0], [33.9, 8.0], [34.0, 8.0], [34.1, 8.0], [34.2, 8.0], [34.3, 8.0], [34.4, 8.0], [34.5, 8.0], [34.6, 8.0], [34.7, 8.0], [34.8, 8.0], [34.9, 8.0], [35.0, 8.0], [35.1, 8.0], [35.2, 8.0], [35.3, 8.0], [35.4, 8.0], [35.5, 8.0], [35.6, 8.0], [35.7, 8.0], [35.8, 8.0], [35.9, 8.0], [36.0, 8.0], [36.1, 8.0], [36.2, 8.0], [36.3, 8.0], [36.4, 8.0], [36.5, 8.0], [36.6, 8.0], [36.7, 8.0], [36.8, 8.0], [36.9, 8.0], [37.0, 8.0], [37.1, 8.0], [37.2, 8.0], [37.3, 8.0], [37.4, 8.0], [37.5, 8.0], [37.6, 8.0], [37.7, 8.0], [37.8, 8.0], [37.9, 8.0], [38.0, 8.0], [38.1, 8.0], [38.2, 8.0], [38.3, 8.0], [38.4, 8.0], [38.5, 8.0], [38.6, 8.0], [38.7, 8.0], [38.8, 8.0], [38.9, 8.0], [39.0, 8.0], [39.1, 8.0], [39.2, 8.0], [39.3, 8.0], [39.4, 8.0], [39.5, 8.0], [39.6, 8.0], [39.7, 8.0], [39.8, 8.0], [39.9, 8.0], [40.0, 8.0], [40.1, 8.0], [40.2, 8.0], [40.3, 8.0], [40.4, 8.0], [40.5, 8.0], [40.6, 8.0], [40.7, 8.0], [40.8, 8.0], [40.9, 8.0], [41.0, 8.0], [41.1, 8.0], [41.2, 8.0], [41.3, 8.0], [41.4, 8.0], [41.5, 8.0], [41.6, 8.0], [41.7, 8.0], [41.8, 8.0], [41.9, 8.0], [42.0, 8.0], [42.1, 8.0], [42.2, 8.0], [42.3, 8.0], [42.4, 8.0], [42.5, 8.0], [42.6, 8.0], [42.7, 8.0], [42.8, 8.0], [42.9, 8.0], [43.0, 8.0], [43.1, 8.0], [43.2, 8.0], [43.3, 8.0], [43.4, 8.0], [43.5, 8.0], [43.6, 8.0], [43.7, 8.0], [43.8, 8.0], [43.9, 8.0], [44.0, 8.0], [44.1, 8.0], [44.2, 8.0], [44.3, 8.0], [44.4, 8.0], [44.5, 8.0], [44.6, 8.0], [44.7, 8.0], [44.8, 8.0], [44.9, 8.0], [45.0, 8.0], [45.1, 8.0], [45.2, 8.0], [45.3, 8.0], [45.4, 8.0], [45.5, 8.0], [45.6, 8.0], [45.7, 8.0], [45.8, 8.0], [45.9, 8.0], [46.0, 8.0], [46.1, 8.0], [46.2, 8.0], [46.3, 8.0], [46.4, 8.0], [46.5, 8.0], [46.6, 8.0], [46.7, 8.0], [46.8, 8.0], [46.9, 8.0], [47.0, 8.0], [47.1, 8.0], [47.2, 8.0], [47.3, 8.0], [47.4, 8.0], [47.5, 8.0], [47.6, 8.0], [47.7, 8.0], [47.8, 8.0], [47.9, 8.0], [48.0, 8.0], [48.1, 8.0], [48.2, 8.0], [48.3, 8.0], [48.4, 8.0], [48.5, 8.0], [48.6, 8.0], [48.7, 8.0], [48.8, 8.0], [48.9, 8.0], [49.0, 8.0], [49.1, 8.0], [49.2, 8.0], [49.3, 8.0], [49.4, 8.0], [49.5, 8.0], [49.6, 8.0], [49.7, 8.0], [49.8, 8.0], [49.9, 8.0], [50.0, 8.0], [50.1, 8.0], [50.2, 8.0], [50.3, 8.0], [50.4, 8.0], [50.5, 8.0], [50.6, 8.0], [50.7, 8.0], [50.8, 8.0], [50.9, 8.0], [51.0, 8.0], [51.1, 8.0], [51.2, 8.0], [51.3, 8.0], [51.4, 8.0], [51.5, 8.0], [51.6, 8.0], [51.7, 8.0], [51.8, 8.0], [51.9, 8.0], [52.0, 8.0], [52.1, 8.0], [52.2, 8.0], [52.3, 8.0], [52.4, 8.0], [52.5, 8.0], [52.6, 8.0], [52.7, 8.0], [52.8, 8.0], [52.9, 8.0], [53.0, 8.0], [53.1, 8.0], [53.2, 8.0], [53.3, 8.0], [53.4, 8.0], [53.5, 8.0], [53.6, 8.0], [53.7, 8.0], [53.8, 8.0], [53.9, 8.0], [54.0, 8.0], [54.1, 8.0], [54.2, 8.0], [54.3, 8.0], [54.4, 8.0], [54.5, 8.0], [54.6, 8.0], [54.7, 8.0], [54.8, 8.0], [54.9, 8.0], [55.0, 8.0], [55.1, 8.0], [55.2, 8.0], [55.3, 8.0], [55.4, 8.0], [55.5, 8.0], [55.6, 8.0], [55.7, 8.0], [55.8, 8.0], [55.9, 8.0], [56.0, 8.0], [56.1, 8.0], [56.2, 8.0], [56.3, 8.0], [56.4, 8.0], [56.5, 8.0], [56.6, 8.0], [56.7, 8.0], [56.8, 8.0], [56.9, 8.0], [57.0, 8.0], [57.1, 8.0], [57.2, 8.0], [57.3, 8.0], [57.4, 8.0], [57.5, 8.0], [57.6, 8.0], [57.7, 8.0], [57.8, 8.0], [57.9, 8.0], [58.0, 8.0], [58.1, 8.0], [58.2, 8.0], [58.3, 8.0], [58.4, 8.0], [58.5, 8.0], [58.6, 8.0], [58.7, 8.0], [58.8, 8.0], [58.9, 8.0], [59.0, 8.0], [59.1, 8.0], [59.2, 8.0], [59.3, 8.0], [59.4, 8.0], [59.5, 8.0], [59.6, 8.0], [59.7, 8.0], [59.8, 8.0], [59.9, 8.0], [60.0, 8.0], [60.1, 8.0], [60.2, 8.0], [60.3, 8.0], [60.4, 8.0], [60.5, 8.0], [60.6, 8.0], [60.7, 8.0], [60.8, 8.0], [60.9, 8.0], [61.0, 8.0], [61.1, 8.0], [61.2, 8.0], [61.3, 8.0], [61.4, 8.0], [61.5, 8.0], [61.6, 8.0], [61.7, 8.0], [61.8, 8.0], [61.9, 8.0], [62.0, 8.0], [62.1, 8.0], [62.2, 8.0], [62.3, 8.0], [62.4, 8.0], [62.5, 8.0], [62.6, 8.0], [62.7, 8.0], [62.8, 8.0], [62.9, 8.0], [63.0, 8.0], [63.1, 8.0], [63.2, 8.0], [63.3, 8.0], [63.4, 8.0], [63.5, 8.0], [63.6, 8.0], [63.7, 8.0], [63.8, 8.0], [63.9, 8.0], [64.0, 9.0], [64.1, 9.0], [64.2, 9.0], [64.3, 9.0], [64.4, 9.0], [64.5, 9.0], [64.6, 9.0], [64.7, 9.0], [64.8, 9.0], [64.9, 9.0], [65.0, 9.0], [65.1, 9.0], [65.2, 9.0], [65.3, 9.0], [65.4, 9.0], [65.5, 9.0], [65.6, 9.0], [65.7, 9.0], [65.8, 9.0], [65.9, 9.0], [66.0, 9.0], [66.1, 9.0], [66.2, 9.0], [66.3, 9.0], [66.4, 9.0], [66.5, 9.0], [66.6, 9.0], [66.7, 9.0], [66.8, 9.0], [66.9, 9.0], [67.0, 9.0], [67.1, 9.0], [67.2, 9.0], [67.3, 9.0], [67.4, 9.0], [67.5, 9.0], [67.6, 9.0], [67.7, 9.0], [67.8, 9.0], [67.9, 9.0], [68.0, 9.0], [68.1, 9.0], [68.2, 9.0], [68.3, 9.0], [68.4, 9.0], [68.5, 9.0], [68.6, 9.0], [68.7, 9.0], [68.8, 9.0], [68.9, 9.0], [69.0, 9.0], [69.1, 9.0], [69.2, 9.0], [69.3, 9.0], [69.4, 9.0], [69.5, 9.0], [69.6, 9.0], [69.7, 9.0], [69.8, 9.0], [69.9, 9.0], [70.0, 9.0], [70.1, 9.0], [70.2, 9.0], [70.3, 9.0], [70.4, 9.0], [70.5, 9.0], [70.6, 9.0], [70.7, 9.0], [70.8, 9.0], [70.9, 9.0], [71.0, 9.0], [71.1, 9.0], [71.2, 9.0], [71.3, 9.0], [71.4, 9.0], [71.5, 9.0], [71.6, 9.0], [71.7, 9.0], [71.8, 9.0], [71.9, 9.0], [72.0, 9.0], [72.1, 9.0], [72.2, 9.0], [72.3, 9.0], [72.4, 9.0], [72.5, 9.0], [72.6, 9.0], [72.7, 9.0], [72.8, 9.0], [72.9, 9.0], [73.0, 9.0], [73.1, 9.0], [73.2, 9.0], [73.3, 9.0], [73.4, 9.0], [73.5, 9.0], [73.6, 9.0], [73.7, 9.0], [73.8, 9.0], [73.9, 9.0], [74.0, 9.0], [74.1, 9.0], [74.2, 9.0], [74.3, 9.0], [74.4, 9.0], [74.5, 9.0], [74.6, 9.0], [74.7, 9.0], [74.8, 9.0], [74.9, 9.0], [75.0, 9.0], [75.1, 9.0], [75.2, 9.0], [75.3, 9.0], [75.4, 9.0], [75.5, 9.0], [75.6, 9.0], [75.7, 9.0], [75.8, 9.0], [75.9, 9.0], [76.0, 9.0], [76.1, 9.0], [76.2, 9.0], [76.3, 9.0], [76.4, 9.0], [76.5, 9.0], [76.6, 9.0], [76.7, 9.0], [76.8, 9.0], [76.9, 9.0], [77.0, 9.0], [77.1, 9.0], [77.2, 9.0], [77.3, 9.0], [77.4, 9.0], [77.5, 9.0], [77.6, 9.0], [77.7, 9.0], [77.8, 9.0], [77.9, 9.0], [78.0, 9.0], [78.1, 9.0], [78.2, 9.0], [78.3, 9.0], [78.4, 9.0], [78.5, 9.0], [78.6, 9.0], [78.7, 9.0], [78.8, 9.0], [78.9, 9.0], [79.0, 9.0], [79.1, 9.0], [79.2, 9.0], [79.3, 9.0], [79.4, 9.0], [79.5, 9.0], [79.6, 9.0], [79.7, 9.0], [79.8, 9.0], [79.9, 9.0], [80.0, 9.0], [80.1, 9.0], [80.2, 9.0], [80.3, 9.0], [80.4, 9.0], [80.5, 9.0], [80.6, 9.0], [80.7, 9.0], [80.8, 9.0], [80.9, 9.0], [81.0, 9.0], [81.1, 9.0], [81.2, 9.0], [81.3, 9.0], [81.4, 9.0], [81.5, 9.0], [81.6, 9.0], [81.7, 9.0], [81.8, 9.0], [81.9, 9.0], [82.0, 9.0], [82.1, 9.0], [82.2, 9.0], [82.3, 9.0], [82.4, 9.0], [82.5, 9.0], [82.6, 9.0], [82.7, 9.0], [82.8, 9.0], [82.9, 9.0], [83.0, 9.0], [83.1, 9.0], [83.2, 9.0], [83.3, 9.0], [83.4, 9.0], [83.5, 9.0], [83.6, 9.0], [83.7, 9.0], [83.8, 9.0], [83.9, 9.0], [84.0, 9.0], [84.1, 9.0], [84.2, 9.0], [84.3, 9.0], [84.4, 9.0], [84.5, 9.0], [84.6, 9.0], [84.7, 9.0], [84.8, 9.0], [84.9, 9.0], [85.0, 9.0], [85.1, 9.0], [85.2, 9.0], [85.3, 9.0], [85.4, 9.0], [85.5, 9.0], [85.6, 9.0], [85.7, 9.0], [85.8, 9.0], [85.9, 9.0], [86.0, 10.0], [86.1, 10.0], [86.2, 10.0], [86.3, 10.0], [86.4, 10.0], [86.5, 10.0], [86.6, 10.0], [86.7, 10.0], [86.8, 10.0], [86.9, 10.0], [87.0, 10.0], [87.1, 10.0], [87.2, 10.0], [87.3, 10.0], [87.4, 10.0], [87.5, 10.0], [87.6, 10.0], [87.7, 10.0], [87.8, 10.0], [87.9, 10.0], [88.0, 10.0], [88.1, 10.0], [88.2, 10.0], [88.3, 10.0], [88.4, 10.0], [88.5, 10.0], [88.6, 10.0], [88.7, 10.0], [88.8, 10.0], [88.9, 10.0], [89.0, 10.0], [89.1, 10.0], [89.2, 10.0], [89.3, 10.0], [89.4, 10.0], [89.5, 10.0], [89.6, 10.0], [89.7, 10.0], [89.8, 10.0], [89.9, 10.0], [90.0, 10.0], [90.1, 10.0], [90.2, 10.0], [90.3, 10.0], [90.4, 10.0], [90.5, 10.0], [90.6, 10.0], [90.7, 10.0], [90.8, 10.0], [90.9, 10.0], [91.0, 10.0], [91.1, 10.0], [91.2, 10.0], [91.3, 10.0], [91.4, 10.0], [91.5, 10.0], [91.6, 10.0], [91.7, 10.0], [91.8, 10.0], [91.9, 10.0], [92.0, 10.0], [92.1, 10.0], [92.2, 10.0], [92.3, 10.0], [92.4, 10.0], [92.5, 10.0], [92.6, 10.0], [92.7, 10.0], [92.8, 10.0], [92.9, 10.0], [93.0, 10.0], [93.1, 10.0], [93.2, 10.0], [93.3, 10.0], [93.4, 10.0], [93.5, 10.0], [93.6, 10.0], [93.7, 10.0], [93.8, 10.0], [93.9, 10.0], [94.0, 10.0], [94.1, 10.0], [94.2, 10.0], [94.3, 10.0], [94.4, 10.0], [94.5, 10.0], [94.6, 10.0], [94.7, 10.0], [94.8, 10.0], [94.9, 10.0], [95.0, 10.0], [95.1, 10.0], [95.2, 10.0], [95.3, 10.0], [95.4, 10.0], [95.5, 10.0], [95.6, 10.0], [95.7, 10.0], [95.8, 10.0], [95.9, 10.0], [96.0, 11.0], [96.1, 11.0], [96.2, 11.0], [96.3, 11.0], [96.4, 11.0], [96.5, 11.0], [96.6, 11.0], [96.7, 11.0], [96.8, 11.0], [96.9, 11.0], [97.0, 11.0], [97.1, 11.0], [97.2, 11.0], [97.3, 11.0], [97.4, 11.0], [97.5, 11.0], [97.6, 11.0], [97.7, 11.0], [97.8, 11.0], [97.9, 11.0], [98.0, 12.0], [98.1, 12.0], [98.2, 12.0], [98.3, 12.0], [98.4, 12.0], [98.5, 12.0], [98.6, 12.0], [98.7, 12.0], [98.8, 12.0], [98.9, 12.0], [99.0, 12.0], [99.1, 12.0], [99.2, 12.0], [99.3, 12.0], [99.4, 12.0], [99.5, 12.0], [99.6, 12.0], [99.7, 12.0], [99.8, 12.0], [99.9, 12.0]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 100.0, "title": "Response Time Percentiles"}},
        getOptions: function() {
            return {
                series: {
                    points: { show: false }
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentiles'
                },
                xaxis: {
                    tickDecimals: 1,
                    axisLabel: "Percentiles",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Percentile value in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : %x.2 percentile was %y ms"
                },
                selection: { mode: "xy" },
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentiles"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesPercentiles"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesPercentiles"), dataset, prepareOverviewOptions(options));
        }
};

/**
 * @param elementId Id of element where we display message
 */
function setEmptyGraph(elementId) {
    $(function() {
        $(elementId).text("No graph series with filter="+seriesFilter);
    });
}

// Response times percentiles
function refreshResponseTimePercentiles() {
    var infos = responseTimePercentilesInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimePercentiles");
        return;
    }
    if (isGraph($("#flotResponseTimesPercentiles"))){
        infos.createGraph();
    } else {
        var choiceContainer = $("#choicesResponseTimePercentiles");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesPercentiles", "#overviewResponseTimesPercentiles");
        $('#bodyResponseTimePercentiles .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimeDistributionInfos = {
        data: {"result": {"minY": 1.0, "minX": 0.0, "maxY": 50.0, "series": [{"data": [[0.0, 10.0]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[8200.0, 2.0], [8300.0, 2.0], [8600.0, 1.0], [8100.0, 5.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[8300.0, 2.0], [5100.0, 1.0], [5200.0, 1.0], [5500.0, 2.0], [5800.0, 2.0], [6100.0, 3.0], [6000.0, 1.0], [6300.0, 3.0], [6200.0, 2.0], [6500.0, 1.0], [6800.0, 1.0], [6700.0, 1.0], [7400.0, 3.0], [7800.0, 2.0], [7700.0, 1.0], [7900.0, 3.0], [8100.0, 1.0]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[0.0, 10.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[0.0, 10.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[400.0, 10.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[0.0, 10.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[400.0, 10.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[300.0, 10.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[5800.0, 4.0], [6100.0, 1.0], [5900.0, 4.0], [6300.0, 4.0], [6200.0, 7.0]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[300.0, 10.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[0.0, 30.0]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[6600.0, 4.0], [6500.0, 3.0], [6700.0, 2.0], [7000.0, 1.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[0.0, 50.0]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 100, "maxX": 8600.0, "title": "Response Time Distribution"}},
        getOptions: function() {
            var granularity = this.data.result.granularity;
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    barWidth: this.data.result.granularity
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " responses for " + label + " were between " + xval + " and " + (xval + granularity) + " ms";
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimeDistribution"), prepareData(data.result.series, $("#choicesResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshResponseTimeDistribution() {
    var infos = responseTimeDistributionInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeDistribution");
        return;
    }
    if (isGraph($("#flotResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var syntheticResponseTimeDistributionInfos = {
        data: {"result": {"minY": 10.0, "minX": 0.0, "ticks": [[0, "Requests having \nresponse time <= 500ms"], [1, "Requests having \nresponse time > 500ms and <= 2 000ms"], [2, "Requests having \nresponse time > 2 000ms"], [3, "Requests in error"]], "maxY": 140.0, "series": [{"data": [[0.0, 140.0]], "color": "#9ACD32", "isOverall": false, "label": "Requests having \nresponse time <= 500ms", "isController": false}, {"data": [], "color": "yellow", "isOverall": false, "label": "Requests having \nresponse time > 500ms and <= 2 000ms", "isController": false}, {"data": [[2.0, 60.0]], "color": "orange", "isOverall": false, "label": "Requests having \nresponse time > 2 000ms", "isController": false}, {"data": [[3.0, 10.0]], "color": "#FF6347", "isOverall": false, "label": "Requests in error", "isController": false}], "supportsControllersDiscrimination": false, "maxX": 3.0, "title": "Synthetic Response Times Distribution"}},
        getOptions: function() {
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendSyntheticResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times ranges",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                    tickLength:0,
                    min:-0.5,
                    max:3.5
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    align: "center",
                    barWidth: 0.25,
                    fill:.75
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " " + label;
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            options.xaxis.ticks = data.result.ticks;
            $.plot($("#flotSyntheticResponseTimeDistribution"), prepareData(data.result.series, $("#choicesSyntheticResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshSyntheticResponseTimeDistribution() {
    var infos = syntheticResponseTimeDistributionInfos;
    prepareSeries(infos.data, true);
    if (isGraph($("#flotSyntheticResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerSyntheticResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var activeThreadsOverTimeInfos = {
        data: {"result": {"minY": 1.0, "minX": 1.74734556E12, "maxY": 1.0, "series": [{"data": [[1.74734598E12, 1.0], [1.74734778E12, 1.0], [1.74734658E12, 1.0], [1.74734718E12, 1.0], [1.74734736E12, 1.0], [1.747347E12, 1.0], [1.74734898E12, 1.0], [1.74734838E12, 1.0], [1.74734856E12, 1.0], [1.7473458E12, 1.0], [1.74734754E12, 1.0], [1.74734976E12, 1.0], [1.74734682E12, 1.0], [1.74734916E12, 1.0], [1.74734694E12, 1.0], [1.74734904E12, 1.0], [1.74734592E12, 1.0], [1.74734802E12, 1.0], [1.74734634E12, 1.0], [1.74734742E12, 1.0], [1.74734964E12, 1.0], [1.7473476E12, 1.0], [1.74734922E12, 1.0], [1.74734862E12, 1.0], [1.7473488E12, 1.0], [1.74734556E12, 1.0], [1.74734616E12, 1.0], [1.74734808E12, 1.0], [1.7473497E12, 1.0], [1.74734628E12, 1.0], [1.74734748E12, 1.0], [1.74734688E12, 1.0], [1.7473467E12, 1.0], [1.74734868E12, 1.0], [1.74734826E12, 1.0], [1.7473461E12, 1.0], [1.74734766E12, 1.0], [1.74734946E12, 1.0], [1.74734724E12, 1.0], [1.74734886E12, 1.0], [1.74734712E12, 1.0], [1.74734874E12, 1.0], [1.74734562E12, 1.0], [1.74734814E12, 1.0], [1.74734832E12, 1.0], [1.74734604E12, 1.0], [1.74734772E12, 1.0], [1.74734934E12, 1.0], [1.74734664E12, 1.0], [1.74734646E12, 1.0], [1.74734952E12, 1.0], [1.74734892E12, 1.0], [1.7473485E12, 1.0], [1.74734586E12, 1.0], [1.7473479E12, 1.0]], "isOverall": false, "label": "Thread Group", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.74734976E12, "title": "Active Threads Over Time"}},
        getOptions: function() {
            return {
                series: {
                    stack: true,
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 6,
                    show: true,
                    container: '#legendActiveThreadsOverTime'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                selection: {
                    mode: 'xy'
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : At %x there were %y active threads"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesActiveThreadsOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotActiveThreadsOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewActiveThreadsOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Active Threads Over Time
function refreshActiveThreadsOverTime(fixTimestamps) {
    var infos = activeThreadsOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotActiveThreadsOverTime"))) {
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesActiveThreadsOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotActiveThreadsOverTime", "#overviewActiveThreadsOverTime");
        $('#footerActiveThreadsOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var timeVsThreadsInfos = {
        data: {"result": {"minY": 8.379999999999999, "minX": 1.0, "maxY": 8262.199999999999, "series": [{"data": [[1.0, 12.1]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[1.0, 12.1]], "isOverall": false, "label": "Get all albums-Aggregated", "isController": false}, {"data": [[1.0, 8262.199999999999]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[1.0, 8262.199999999999]], "isOverall": false, "label": "Home page-Aggregated", "isController": true}, {"data": [[1.0, 6791.599999999999]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[1.0, 6791.599999999999]], "isOverall": false, "label": "Play songs from home page-Aggregated", "isController": false}, {"data": [[1.0, 15.5]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[1.0, 15.5]], "isOverall": false, "label": "Trending playlist-Aggregated", "isController": false}, {"data": [[1.0, 54.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[1.0, 54.0]], "isOverall": false, "label": "Albums page-Aggregated", "isController": true}, {"data": [[1.0, 461.2]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[1.0, 461.2]], "isOverall": false, "label": "Cover 2-Aggregated", "isController": false}, {"data": [[1.0, 8.9]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[1.0, 8.9]], "isOverall": false, "label": "Cover 1-Aggregated", "isController": false}, {"data": [[1.0, 427.4]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[1.0, 427.4]], "isOverall": false, "label": "Cover 4-Aggregated", "isController": false}, {"data": [[1.0, 309.6]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[1.0, 309.6]], "isOverall": false, "label": "Cover 3-Aggregated", "isController": false}, {"data": [[1.0, 6127.5]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[1.0, 6127.5]], "isOverall": false, "label": "Play songs from albums-Aggregated", "isController": false}, {"data": [[1.0, 348.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[1.0, 348.0]], "isOverall": false, "label": "Cover 5-Aggregated", "isController": false}, {"data": [[1.0, 9.3]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[1.0, 9.3]], "isOverall": false, "label": "Search-Aggregated", "isController": false}, {"data": [[1.0, 6691.5999999999985]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[1.0, 6691.5999999999985]], "isOverall": false, "label": "Initial song-Aggregated", "isController": false}, {"data": [[1.0, 8.379999999999999]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}, {"data": [[1.0, 8.379999999999999]], "isOverall": false, "label": "Get paths to songs from each album-Aggregated", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 1.0, "title": "Time VS Threads"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: { noColumns: 2,show: true, container: '#legendTimeVsThreads' },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s: At %x.2 active threads, Average response time was %y.2 ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesTimeVsThreads"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotTimesVsThreads"), dataset, options);
            // setup overview
            $.plot($("#overviewTimesVsThreads"), dataset, prepareOverviewOptions(options));
        }
};

// Time vs threads
function refreshTimeVsThreads(){
    var infos = timeVsThreadsInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTimeVsThreads");
        return;
    }
    if(isGraph($("#flotTimesVsThreads"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTimeVsThreads");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTimesVsThreads", "#overviewTimesVsThreads");
        $('#footerTimeVsThreads .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var bytesThroughputOverTimeInfos = {
        data : {"result": {"minY": 2.2333333333333334, "minX": 1.74734556E12, "maxY": 281426.95, "series": [{"data": [[1.74734598E12, 109424.53333333334], [1.74734778E12, 272394.73333333334], [1.74734658E12, 110869.96666666666], [1.74734718E12, 250.11666666666667], [1.74734736E12, 116.08333333333333], [1.747347E12, 103712.41666666667], [1.74734898E12, 142879.0], [1.74734838E12, 103712.41666666667], [1.74734856E12, 250.11666666666667], [1.7473458E12, 95945.35], [1.74734754E12, 110869.96666666666], [1.74734976E12, 218849.06666666668], [1.74734682E12, 103712.41666666667], [1.74734916E12, 138315.95], [1.74734694E12, 109470.45], [1.74734904E12, 281426.95], [1.74734592E12, 110869.96666666666], [1.74734802E12, 129869.7], [1.74734634E12, 219203.65], [1.74734742E12, 272394.73333333334], [1.74734964E12, 139848.46666666667], [1.7473476E12, 250.11666666666667], [1.74734922E12, 103457.5], [1.74734862E12, 204638.43333333332], [1.7473488E12, 204028.91666666666], [1.74734556E12, 142879.0], [1.74734616E12, 129869.7], [1.74734808E12, 105233.83333333333], [1.7473497E12, 250.11666666666667], [1.74734628E12, 145316.76666666666], [1.74734748E12, 103712.41666666667], [1.74734688E12, 143048.0], [1.7473467E12, 109424.53333333334], [1.74734868E12, 143093.91666666666], [1.74734826E12, 143048.0], [1.7473461E12, 109639.45], [1.74734766E12, 109424.53333333334], [1.74734946E12, 143048.0], [1.74734724E12, 214408.25], [1.74734886E12, 101681.16666666667], [1.74734712E12, 108083.56666666667], [1.74734874E12, 110869.96666666666], [1.74734562E12, 212063.15], [1.74734814E12, 101431.05], [1.74734832E12, 135941.18333333332], [1.74734604E12, 142879.0], [1.74734772E12, 109540.61666666667], [1.74734934E12, 109424.53333333334], [1.74734664E12, 109674.65], [1.74734646E12, 278989.18333333335], [1.74734952E12, 201688.98333333334], [1.74734892E12, 108083.56666666667], [1.7473485E12, 116077.05], [1.74734586E12, 250.11666666666667], [1.7473479E12, 139848.46666666667]], "isOverall": false, "label": "Bytes received per second", "isController": false}, {"data": [[1.74734598E12, 2.7666666666666666], [1.74734778E12, 21.583333333333332], [1.74734658E12, 2.9], [1.74734718E12, 15.183333333333334], [1.74734736E12, 2.2333333333333334], [1.747347E12, 2.8], [1.74734898E12, 14.366666666666667], [1.74734838E12, 2.8], [1.74734856E12, 15.183333333333334], [1.7473458E12, 3.2333333333333334], [1.74734754E12, 2.9], [1.74734976E12, 5.6], [1.74734682E12, 2.8], [1.74734916E12, 2.6333333333333333], [1.74734694E12, 5.083333333333333], [1.74734904E12, 12.366666666666667], [1.74734592E12, 2.9], [1.74734802E12, 2.7666666666666666], [1.74734634E12, 21.016666666666666], [1.74734742E12, 21.583333333333332], [1.74734964E12, 2.8666666666666667], [1.7473476E12, 15.183333333333334], [1.74734922E12, 17.983333333333334], [1.74734862E12, 5.65], [1.7473488E12, 6.166666666666667], [1.74734556E12, 14.366666666666667], [1.74734616E12, 2.7666666666666666], [1.74734808E12, 18.083333333333332], [1.7473497E12, 15.183333333333334], [1.74734628E12, 2.816666666666667], [1.74734748E12, 2.8], [1.74734688E12, 18.866666666666667], [1.7473467E12, 2.8333333333333335], [1.74734868E12, 21.116666666666667], [1.74734826E12, 18.866666666666667], [1.7473461E12, 9.583333333333334], [1.74734766E12, 2.7666666666666666], [1.74734946E12, 18.866666666666667], [1.74734724E12, 5.666666666666667], [1.74734886E12, 18.033333333333335], [1.74734712E12, 2.933333333333333], [1.74734874E12, 2.9], [1.74734562E12, 12.333333333333334], [1.74734814E12, 2.85], [1.74734832E12, 5.05], [1.74734604E12, 14.366666666666667], [1.74734772E12, 5.066666666666666], [1.74734934E12, 2.8333333333333335], [1.74734664E12, 18.016666666666666], [1.74734646E12, 23.916666666666668], [1.74734952E12, 7.966666666666667], [1.74734892E12, 2.933333333333333], [1.7473485E12, 2.8666666666666667], [1.74734586E12, 15.183333333333334], [1.7473479E12, 2.8666666666666667]], "isOverall": false, "label": "Bytes sent per second", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.74734976E12, "title": "Bytes Throughput Over Time"}},
        getOptions : function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity) ,
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Bytes / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendBytesThroughputOverTime'
                },
                selection: {
                    mode: "xy"
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y"
                }
            };
        },
        createGraph : function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesBytesThroughputOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotBytesThroughputOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewBytesThroughputOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Bytes throughput Over Time
function refreshBytesThroughputOverTime(fixTimestamps) {
    var infos = bytesThroughputOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotBytesThroughputOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesBytesThroughputOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotBytesThroughputOverTime", "#overviewBytesThroughputOverTime");
        $('#footerBytesThroughputOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimesOverTimeInfos = {
        data: {"result": {"minY": 7.6, "minX": 1.74734556E12, "maxY": 8648.0, "series": [{"data": [[1.74734856E12, 14.0], [1.7473476E12, 12.0], [1.74734808E12, 13.0], [1.74734922E12, 8.0], [1.7473497E12, 9.0], [1.74734718E12, 14.0], [1.74734634E12, 13.0], [1.74734586E12, 16.0], [1.74734664E12, 13.0], [1.74734886E12, 9.0]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[1.74734646E12, 8192.0], [1.74734826E12, 8387.0], [1.74734778E12, 8269.0], [1.74734688E12, 8103.0], [1.74734946E12, 8169.0], [1.74734604E12, 8347.0], [1.74734898E12, 8144.0], [1.74734556E12, 8212.0], [1.74734868E12, 8151.0], [1.74734742E12, 8648.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[1.74734628E12, 8337.0], [1.74734778E12, 7416.0], [1.74734658E12, 6334.0], [1.74734748E12, 5866.0], [1.747347E12, 5899.0], [1.74734838E12, 6126.0], [1.7473458E12, 5530.0], [1.7473461E12, 6327.0], [1.74734754E12, 6508.0], [1.74734916E12, 7902.0], [1.74734694E12, 6203.0], [1.74734904E12, 8117.0], [1.74734712E12, 6118.0], [1.74734874E12, 6351.0], [1.74734562E12, 6061.0], [1.74734832E12, 7869.0], [1.74734802E12, 7721.0], [1.74734664E12, 6274.0], [1.74734742E12, 7480.0], [1.74734964E12, 7943.0], [1.74734646E12, 7890.0], [1.74734952E12, 5641.5], [1.7473488E12, 5823.0], [1.7473485E12, 6764.0], [1.74734616E12, 7464.0], [1.7473479E12, 8141.0]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[1.74734646E12, 11.0], [1.74734826E12, 18.0], [1.74734688E12, 9.0], [1.74734736E12, 11.0], [1.74734946E12, 15.0], [1.74734604E12, 9.0], [1.74734898E12, 26.0], [1.74734556E12, 37.0], [1.74734868E12, 10.0], [1.74734772E12, 9.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[1.74734856E12, 56.0], [1.7473476E12, 50.0], [1.74734808E12, 54.0], [1.74734922E12, 55.0], [1.7473497E12, 53.0], [1.74734718E12, 55.0], [1.74734634E12, 53.0], [1.74734586E12, 60.0], [1.74734664E12, 56.0], [1.74734886E12, 48.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[1.74734646E12, 457.0], [1.74734826E12, 454.0], [1.74734778E12, 461.0], [1.74734688E12, 448.0], [1.74734946E12, 459.0], [1.74734604E12, 461.0], [1.74734898E12, 461.0], [1.74734556E12, 458.0], [1.74734868E12, 491.0], [1.74734742E12, 462.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[1.74734646E12, 9.0], [1.74734826E12, 10.0], [1.74734778E12, 9.0], [1.74734688E12, 9.0], [1.74734946E12, 9.0], [1.74734604E12, 8.0], [1.74734898E12, 8.0], [1.74734556E12, 11.0], [1.74734868E12, 8.0], [1.74734742E12, 8.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[1.74734646E12, 429.0], [1.74734826E12, 436.0], [1.74734778E12, 436.0], [1.74734688E12, 425.0], [1.74734946E12, 422.0], [1.74734604E12, 434.0], [1.74734898E12, 415.0], [1.74734556E12, 430.0], [1.74734868E12, 425.0], [1.74734742E12, 422.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[1.74734646E12, 318.0], [1.74734826E12, 319.0], [1.74734778E12, 304.0], [1.74734688E12, 311.0], [1.74734946E12, 302.0], [1.74734604E12, 308.0], [1.74734898E12, 310.0], [1.74734556E12, 308.0], [1.74734868E12, 314.0], [1.74734742E12, 302.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[1.74734598E12, 6315.0], [1.74734808E12, 5972.0], [1.74734814E12, 5814.0], [1.74734592E12, 6362.0], [1.7473467E12, 6282.0], [1.74734634E12, 6242.5], [1.74734772E12, 6269.0], [1.74734934E12, 6205.0], [1.74734922E12, 5979.0], [1.74734892E12, 6226.0], [1.74734862E12, 5863.0], [1.74734766E12, 6320.0], [1.74734976E12, 6263.5], [1.74734682E12, 5926.0], [1.74734724E12, 6121.5], [1.74734886E12, 5899.0]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[1.74734646E12, 354.0], [1.74734826E12, 351.0], [1.74734778E12, 362.0], [1.74734688E12, 340.0], [1.74734946E12, 340.0], [1.74734604E12, 350.0], [1.74734898E12, 340.0], [1.74734556E12, 347.0], [1.74734868E12, 352.0], [1.74734742E12, 344.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[1.74734694E12, 8.0], [1.74734904E12, 8.333333333333334], [1.74734778E12, 8.333333333333334], [1.74734562E12, 9.333333333333334], [1.74734688E12, 10.0], [1.74734832E12, 9.0], [1.74734868E12, 9.666666666666666], [1.74734742E12, 9.0], [1.74734646E12, 9.0], [1.74734826E12, 9.0], [1.74734952E12, 8.0], [1.7473461E12, 9.0], [1.74734946E12, 14.0]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[1.74734646E12, 6614.0], [1.74734826E12, 6799.0], [1.74734778E12, 6688.0], [1.74734688E12, 6561.0], [1.74734946E12, 6622.0], [1.74734604E12, 6777.0], [1.74734898E12, 6584.0], [1.74734556E12, 6621.0], [1.74734868E12, 6551.0], [1.74734742E12, 7099.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[1.74734856E12, 8.4], [1.7473476E12, 7.6], [1.74734808E12, 8.2], [1.74734922E12, 9.4], [1.7473497E12, 8.8], [1.74734718E12, 8.2], [1.74734634E12, 8.0], [1.74734586E12, 8.8], [1.74734664E12, 8.6], [1.74734886E12, 7.8]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.74734976E12, "title": "Response Time Over Time"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average response time was %y ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Times Over Time
function refreshResponseTimeOverTime(fixTimestamps) {
    var infos = responseTimesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotResponseTimesOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesOverTime", "#overviewResponseTimesOverTime");
        $('#footerResponseTimesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var latenciesOverTimeInfos = {
        data: {"result": {"minY": 0.0, "minX": 1.74734556E12, "maxY": 80.0, "series": [{"data": [[1.74734856E12, 14.0], [1.7473476E12, 12.0], [1.74734808E12, 13.0], [1.74734922E12, 8.0], [1.7473497E12, 9.0], [1.74734718E12, 14.0], [1.74734634E12, 13.0], [1.74734586E12, 16.0], [1.74734664E12, 13.0], [1.74734886E12, 9.0]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[1.74734646E12, 52.0], [1.74734826E12, 59.0], [1.74734778E12, 51.0], [1.74734688E12, 50.0], [1.74734946E12, 70.0], [1.74734604E12, 50.0], [1.74734898E12, 67.0], [1.74734556E12, 80.0], [1.74734868E12, 51.0], [1.74734742E12, 53.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[1.74734628E12, 10.0], [1.74734778E12, 10.0], [1.74734658E12, 10.0], [1.74734748E12, 9.0], [1.747347E12, 10.0], [1.74734838E12, 10.0], [1.7473458E12, 9.0], [1.7473461E12, 16.0], [1.74734754E12, 9.0], [1.74734916E12, 47.0], [1.74734694E12, 10.0], [1.74734904E12, 12.0], [1.74734712E12, 9.0], [1.74734874E12, 9.0], [1.74734562E12, 10.0], [1.74734832E12, 8.0], [1.74734802E12, 10.0], [1.74734664E12, 12.0], [1.74734742E12, 8.0], [1.74734964E12, 11.0], [1.74734646E12, 12.0], [1.74734952E12, 13.0], [1.7473488E12, 12.5], [1.7473485E12, 10.0], [1.74734616E12, 16.0], [1.7473479E12, 9.0]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[1.74734646E12, 11.0], [1.74734826E12, 18.0], [1.74734688E12, 9.0], [1.74734736E12, 11.0], [1.74734946E12, 15.0], [1.74734604E12, 9.0], [1.74734898E12, 26.0], [1.74734556E12, 37.0], [1.74734868E12, 10.0], [1.74734772E12, 9.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[1.74734856E12, 56.0], [1.7473476E12, 49.0], [1.74734808E12, 54.0], [1.74734922E12, 55.0], [1.7473497E12, 53.0], [1.74734718E12, 55.0], [1.74734634E12, 53.0], [1.74734586E12, 60.0], [1.74734664E12, 55.0], [1.74734886E12, 47.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[1.74734646E12, 9.0], [1.74734826E12, 9.0], [1.74734778E12, 9.0], [1.74734688E12, 9.0], [1.74734946E12, 21.0], [1.74734604E12, 8.0], [1.74734898E12, 8.0], [1.74734556E12, 9.0], [1.74734868E12, 9.0], [1.74734742E12, 10.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[1.74734646E12, 0.0], [1.74734826E12, 0.0], [1.74734778E12, 0.0], [1.74734688E12, 0.0], [1.74734946E12, 0.0], [1.74734604E12, 0.0], [1.74734898E12, 0.0], [1.74734556E12, 0.0], [1.74734868E12, 0.0], [1.74734742E12, 0.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[1.74734646E12, 7.0], [1.74734826E12, 8.0], [1.74734778E12, 8.0], [1.74734688E12, 8.0], [1.74734946E12, 9.0], [1.74734604E12, 8.0], [1.74734898E12, 8.0], [1.74734556E12, 8.0], [1.74734868E12, 8.0], [1.74734742E12, 8.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[1.74734646E12, 9.0], [1.74734826E12, 8.0], [1.74734778E12, 8.0], [1.74734688E12, 8.0], [1.74734946E12, 7.0], [1.74734604E12, 8.0], [1.74734898E12, 8.0], [1.74734556E12, 8.0], [1.74734868E12, 8.0], [1.74734742E12, 7.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[1.74734598E12, 19.0], [1.74734808E12, 15.0], [1.74734814E12, 9.0], [1.74734592E12, 9.0], [1.7473467E12, 10.0], [1.74734634E12, 12.5], [1.74734772E12, 10.0], [1.74734934E12, 10.0], [1.74734922E12, 9.0], [1.74734892E12, 16.0], [1.74734862E12, 9.0], [1.74734766E12, 11.0], [1.74734976E12, 12.0], [1.74734682E12, 10.0], [1.74734724E12, 13.5], [1.74734886E12, 10.0]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[1.74734646E12, 8.0], [1.74734826E12, 8.0], [1.74734778E12, 9.0], [1.74734688E12, 8.0], [1.74734946E12, 9.0], [1.74734604E12, 9.0], [1.74734898E12, 8.0], [1.74734556E12, 9.0], [1.74734868E12, 8.0], [1.74734742E12, 8.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[1.74734694E12, 8.0], [1.74734904E12, 8.333333333333334], [1.74734778E12, 8.333333333333334], [1.74734562E12, 9.333333333333334], [1.74734688E12, 9.0], [1.74734832E12, 9.0], [1.74734868E12, 9.666666666666666], [1.74734742E12, 9.0], [1.74734646E12, 9.0], [1.74734826E12, 9.0], [1.74734952E12, 8.0], [1.7473461E12, 9.0], [1.74734946E12, 14.0]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[1.74734646E12, 8.0], [1.74734826E12, 8.0], [1.74734778E12, 8.0], [1.74734688E12, 8.0], [1.74734946E12, 9.0], [1.74734604E12, 8.0], [1.74734898E12, 9.0], [1.74734556E12, 9.0], [1.74734868E12, 8.0], [1.74734742E12, 9.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[1.74734856E12, 8.4], [1.7473476E12, 7.4], [1.74734808E12, 8.2], [1.74734922E12, 9.4], [1.7473497E12, 8.8], [1.74734718E12, 8.2], [1.74734634E12, 8.0], [1.74734586E12, 8.8], [1.74734664E12, 8.4], [1.74734886E12, 7.6]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.74734976E12, "title": "Latencies Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response latencies in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendLatenciesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average latency was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesLatenciesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotLatenciesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewLatenciesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Latencies Over Time
function refreshLatenciesOverTime(fixTimestamps) {
    var infos = latenciesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyLatenciesOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotLatenciesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesLatenciesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotLatenciesOverTime", "#overviewLatenciesOverTime");
        $('#footerLatenciesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var connectTimeOverTimeInfos = {
        data: {"result": {"minY": 0.0, "minX": 1.74734556E12, "maxY": 24.0, "series": [{"data": [[1.74734856E12, 1.0], [1.7473476E12, 2.0], [1.74734808E12, 2.0], [1.74734922E12, 1.0], [1.7473497E12, 1.0], [1.74734718E12, 1.0], [1.74734634E12, 2.0], [1.74734586E12, 2.0], [1.74734664E12, 1.0], [1.74734886E12, 2.0]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[1.74734646E12, 3.0], [1.74734826E12, 2.0], [1.74734778E12, 2.0], [1.74734688E12, 3.0], [1.74734946E12, 3.0], [1.74734604E12, 2.0], [1.74734898E12, 2.0], [1.74734556E12, 24.0], [1.74734868E12, 2.0], [1.74734742E12, 3.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[1.74734628E12, 1.0], [1.74734778E12, 1.0], [1.74734658E12, 1.0], [1.74734748E12, 1.0], [1.747347E12, 2.0], [1.74734838E12, 2.0], [1.7473458E12, 1.0], [1.7473461E12, 1.0], [1.74734754E12, 1.0], [1.74734916E12, 1.0], [1.74734694E12, 1.0], [1.74734904E12, 1.0], [1.74734712E12, 2.0], [1.74734874E12, 1.0], [1.74734562E12, 2.0], [1.74734832E12, 0.0], [1.74734802E12, 2.0], [1.74734664E12, 2.0], [1.74734742E12, 0.0], [1.74734964E12, 2.0], [1.74734646E12, 0.0], [1.74734952E12, 1.5], [1.7473488E12, 1.5], [1.7473485E12, 1.0], [1.74734616E12, 1.0], [1.7473479E12, 1.0]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[1.74734646E12, 2.0], [1.74734826E12, 1.0], [1.74734688E12, 2.0], [1.74734736E12, 2.0], [1.74734946E12, 2.0], [1.74734604E12, 2.0], [1.74734898E12, 1.0], [1.74734556E12, 23.0], [1.74734868E12, 1.0], [1.74734772E12, 1.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[1.74734856E12, 1.0], [1.7473476E12, 2.0], [1.74734808E12, 2.0], [1.74734922E12, 1.0], [1.7473497E12, 1.0], [1.74734718E12, 1.0], [1.74734634E12, 2.0], [1.74734586E12, 2.0], [1.74734664E12, 1.0], [1.74734886E12, 2.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[1.74734646E12, 1.0], [1.74734826E12, 1.0], [1.74734778E12, 1.0], [1.74734688E12, 1.0], [1.74734946E12, 1.0], [1.74734604E12, 0.0], [1.74734898E12, 1.0], [1.74734556E12, 1.0], [1.74734868E12, 1.0], [1.74734742E12, 1.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[1.74734646E12, 0.0], [1.74734826E12, 0.0], [1.74734778E12, 0.0], [1.74734688E12, 0.0], [1.74734946E12, 0.0], [1.74734604E12, 0.0], [1.74734898E12, 0.0], [1.74734556E12, 0.0], [1.74734868E12, 0.0], [1.74734742E12, 0.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[1.74734646E12, 0.0], [1.74734826E12, 0.0], [1.74734778E12, 0.0], [1.74734688E12, 0.0], [1.74734946E12, 0.0], [1.74734604E12, 0.0], [1.74734898E12, 0.0], [1.74734556E12, 0.0], [1.74734868E12, 0.0], [1.74734742E12, 0.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[1.74734646E12, 0.0], [1.74734826E12, 0.0], [1.74734778E12, 0.0], [1.74734688E12, 0.0], [1.74734946E12, 0.0], [1.74734604E12, 0.0], [1.74734898E12, 0.0], [1.74734556E12, 0.0], [1.74734868E12, 0.0], [1.74734742E12, 0.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[1.74734598E12, 2.0], [1.74734808E12, 1.0], [1.74734814E12, 1.0], [1.74734592E12, 1.0], [1.7473467E12, 2.0], [1.74734634E12, 1.5], [1.74734772E12, 2.0], [1.74734934E12, 1.0], [1.74734922E12, 1.0], [1.74734892E12, 2.0], [1.74734862E12, 1.0], [1.74734766E12, 2.0], [1.74734976E12, 1.0], [1.74734682E12, 1.0], [1.74734724E12, 1.5], [1.74734886E12, 1.0]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[1.74734646E12, 0.0], [1.74734826E12, 0.0], [1.74734778E12, 0.0], [1.74734688E12, 0.0], [1.74734946E12, 0.0], [1.74734604E12, 0.0], [1.74734898E12, 0.0], [1.74734556E12, 0.0], [1.74734868E12, 0.0], [1.74734742E12, 0.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[1.74734694E12, 1.0], [1.74734904E12, 0.6666666666666667], [1.74734778E12, 0.33333333333333337], [1.74734562E12, 1.6666666666666667], [1.74734688E12, 1.5], [1.74734832E12, 1.0], [1.74734868E12, 1.6666666666666667], [1.74734742E12, 1.0], [1.74734646E12, 1.3333333333333333], [1.74734826E12, 1.0], [1.74734952E12, 0.0], [1.7473461E12, 1.0], [1.74734946E12, 0.5]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[1.74734646E12, 0.0], [1.74734826E12, 0.0], [1.74734778E12, 0.0], [1.74734688E12, 0.0], [1.74734946E12, 0.0], [1.74734604E12, 0.0], [1.74734898E12, 0.0], [1.74734556E12, 0.0], [1.74734868E12, 0.0], [1.74734742E12, 0.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[1.74734856E12, 0.0], [1.7473476E12, 0.0], [1.74734808E12, 0.0], [1.74734922E12, 0.0], [1.7473497E12, 0.0], [1.74734718E12, 0.0], [1.74734634E12, 0.0], [1.74734586E12, 0.0], [1.74734664E12, 0.0], [1.74734886E12, 0.0]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.74734976E12, "title": "Connect Time Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getConnectTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average Connect Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendConnectTimeOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average connect time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesConnectTimeOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotConnectTimeOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewConnectTimeOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Connect Time Over Time
function refreshConnectTimeOverTime(fixTimestamps) {
    var infos = connectTimeOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyConnectTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotConnectTimeOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesConnectTimeOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotConnectTimeOverTime", "#overviewConnectTimeOverTime");
        $('#footerConnectTimeOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var responseTimePercentilesOverTimeInfos = {
        data: {"result": {"minY": 7.0, "minX": 1.74734556E12, "maxY": 8337.0, "series": [{"data": [[1.74734598E12, 6315.0], [1.74734778E12, 7416.0], [1.74734658E12, 6334.0], [1.74734718E12, 14.0], [1.74734736E12, 11.0], [1.747347E12, 5899.0], [1.74734898E12, 6584.0], [1.74734838E12, 6126.0], [1.74734856E12, 14.0], [1.7473458E12, 5530.0], [1.74734754E12, 6508.0], [1.74734976E12, 6292.0], [1.74734682E12, 5926.0], [1.74734916E12, 7902.0], [1.74734694E12, 6203.0], [1.74734904E12, 8332.0], [1.74734592E12, 6362.0], [1.74734802E12, 7721.0], [1.74734634E12, 6325.0], [1.74734742E12, 7480.0], [1.74734964E12, 7943.0], [1.7473476E12, 12.0], [1.74734922E12, 5979.0], [1.74734862E12, 5876.0], [1.7473488E12, 6120.0], [1.74734556E12, 6621.0], [1.74734616E12, 7464.0], [1.74734808E12, 5972.0], [1.7473497E12, 11.0], [1.74734628E12, 8337.0], [1.74734748E12, 5866.0], [1.74734688E12, 6561.0], [1.7473467E12, 6282.0], [1.74734868E12, 6551.0], [1.74734826E12, 6799.0], [1.7473461E12, 6327.0], [1.74734766E12, 6320.0], [1.74734946E12, 6622.0], [1.74734724E12, 6253.0], [1.74734886E12, 5899.0], [1.74734712E12, 6118.0], [1.74734874E12, 6351.0], [1.74734562E12, 6867.0], [1.74734814E12, 5814.0], [1.74734832E12, 7869.0], [1.74734604E12, 6777.0], [1.74734772E12, 6269.0], [1.74734934E12, 6205.0], [1.74734664E12, 6274.0], [1.74734646E12, 7890.0], [1.74734952E12, 6092.0], [1.74734892E12, 6226.0], [1.7473485E12, 6764.0], [1.74734586E12, 16.0], [1.7473479E12, 8141.0]], "isOverall": false, "label": "Max", "isController": false}, {"data": [[1.74734598E12, 6315.0], [1.74734778E12, 8.0], [1.74734658E12, 6334.0], [1.74734718E12, 8.0], [1.74734736E12, 11.0], [1.747347E12, 5899.0], [1.74734898E12, 26.0], [1.74734838E12, 6126.0], [1.74734856E12, 8.0], [1.7473458E12, 5530.0], [1.74734754E12, 6508.0], [1.74734976E12, 6235.0], [1.74734682E12, 5926.0], [1.74734916E12, 7902.0], [1.74734694E12, 8.0], [1.74734904E12, 8.0], [1.74734592E12, 6362.0], [1.74734802E12, 7721.0], [1.74734634E12, 7.0], [1.74734742E12, 9.0], [1.74734964E12, 7943.0], [1.7473476E12, 7.0], [1.74734922E12, 8.0], [1.74734862E12, 5850.0], [1.7473488E12, 5526.0], [1.74734556E12, 37.0], [1.74734616E12, 7464.0], [1.74734808E12, 8.0], [1.7473497E12, 7.0], [1.74734628E12, 8337.0], [1.74734748E12, 5866.0], [1.74734688E12, 9.0], [1.7473467E12, 6282.0], [1.74734868E12, 8.0], [1.74734826E12, 9.0], [1.7473461E12, 9.0], [1.74734766E12, 6320.0], [1.74734946E12, 8.0], [1.74734724E12, 5990.0], [1.74734886E12, 7.0], [1.74734712E12, 6118.0], [1.74734874E12, 6351.0], [1.74734562E12, 9.0], [1.74734814E12, 5814.0], [1.74734832E12, 9.0], [1.74734604E12, 9.0], [1.74734772E12, 9.0], [1.74734934E12, 6205.0], [1.74734664E12, 7.0], [1.74734646E12, 9.0], [1.74734952E12, 8.0], [1.74734892E12, 6226.0], [1.7473485E12, 6764.0], [1.74734586E12, 8.0], [1.7473479E12, 8141.0]], "isOverall": false, "label": "Min", "isController": false}, {"data": [[1.74734598E12, 6315.0], [1.74734778E12, 7416.0], [1.74734658E12, 6334.0], [1.74734718E12, 14.0], [1.74734736E12, 11.0], [1.747347E12, 5899.0], [1.74734898E12, 6584.0], [1.74734838E12, 6126.0], [1.74734856E12, 14.0], [1.7473458E12, 5530.0], [1.74734754E12, 6508.0], [1.74734976E12, 6292.0], [1.74734682E12, 5926.0], [1.74734916E12, 7902.0], [1.74734694E12, 6203.0], [1.74734904E12, 8332.0], [1.74734592E12, 6362.0], [1.74734802E12, 7721.0], [1.74734634E12, 6325.0], [1.74734742E12, 7480.0], [1.74734964E12, 7943.0], [1.7473476E12, 12.0], [1.74734922E12, 5979.0], [1.74734862E12, 5876.0], [1.7473488E12, 6120.0], [1.74734556E12, 6621.0], [1.74734616E12, 7464.0], [1.74734808E12, 5972.0], [1.7473497E12, 11.0], [1.74734628E12, 8337.0], [1.74734748E12, 5866.0], [1.74734688E12, 6561.0], [1.7473467E12, 6282.0], [1.74734868E12, 6551.0], [1.74734826E12, 6799.0], [1.7473461E12, 6327.0], [1.74734766E12, 6320.0], [1.74734946E12, 6622.0], [1.74734724E12, 6253.0], [1.74734886E12, 5899.0], [1.74734712E12, 6118.0], [1.74734874E12, 6351.0], [1.74734562E12, 6867.0], [1.74734814E12, 5814.0], [1.74734832E12, 7869.0], [1.74734604E12, 6777.0], [1.74734772E12, 6269.0], [1.74734934E12, 6205.0], [1.74734664E12, 6274.0], [1.74734646E12, 7762.400000000001], [1.74734952E12, 6092.0], [1.74734892E12, 6226.0], [1.7473485E12, 6764.0], [1.74734586E12, 16.0], [1.7473479E12, 8141.0]], "isOverall": false, "label": "90th percentile", "isController": false}, {"data": [[1.74734598E12, 6315.0], [1.74734778E12, 7416.0], [1.74734658E12, 6334.0], [1.74734718E12, 14.0], [1.74734736E12, 11.0], [1.747347E12, 5899.0], [1.74734898E12, 6584.0], [1.74734838E12, 6126.0], [1.74734856E12, 14.0], [1.7473458E12, 5530.0], [1.74734754E12, 6508.0], [1.74734976E12, 6292.0], [1.74734682E12, 5926.0], [1.74734916E12, 7902.0], [1.74734694E12, 6203.0], [1.74734904E12, 8332.0], [1.74734592E12, 6362.0], [1.74734802E12, 7721.0], [1.74734634E12, 6325.0], [1.74734742E12, 7480.0], [1.74734964E12, 7943.0], [1.7473476E12, 12.0], [1.74734922E12, 5979.0], [1.74734862E12, 5876.0], [1.7473488E12, 6120.0], [1.74734556E12, 6621.0], [1.74734616E12, 7464.0], [1.74734808E12, 5972.0], [1.7473497E12, 11.0], [1.74734628E12, 8337.0], [1.74734748E12, 5866.0], [1.74734688E12, 6561.0], [1.7473467E12, 6282.0], [1.74734868E12, 6551.0], [1.74734826E12, 6799.0], [1.7473461E12, 6327.0], [1.74734766E12, 6320.0], [1.74734946E12, 6622.0], [1.74734724E12, 6253.0], [1.74734886E12, 5899.0], [1.74734712E12, 6118.0], [1.74734874E12, 6351.0], [1.74734562E12, 6867.0], [1.74734814E12, 5814.0], [1.74734832E12, 7869.0], [1.74734604E12, 6777.0], [1.74734772E12, 6269.0], [1.74734934E12, 6205.0], [1.74734664E12, 6274.0], [1.74734646E12, 7890.0], [1.74734952E12, 6092.0], [1.74734892E12, 6226.0], [1.7473485E12, 6764.0], [1.74734586E12, 16.0], [1.7473479E12, 8141.0]], "isOverall": false, "label": "99th percentile", "isController": false}, {"data": [[1.74734598E12, 6315.0], [1.74734778E12, 362.0], [1.74734658E12, 6334.0], [1.74734718E12, 8.0], [1.74734736E12, 11.0], [1.747347E12, 5899.0], [1.74734898E12, 377.5], [1.74734838E12, 6126.0], [1.74734856E12, 8.5], [1.7473458E12, 5530.0], [1.74734754E12, 6508.0], [1.74734976E12, 6263.5], [1.74734682E12, 5926.0], [1.74734916E12, 7902.0], [1.74734694E12, 3105.5], [1.74734904E12, 9.0], [1.74734592E12, 6362.0], [1.74734802E12, 7721.0], [1.74734634E12, 9.5], [1.74734742E12, 344.0], [1.74734964E12, 7943.0], [1.7473476E12, 8.0], [1.74734922E12, 9.0], [1.74734862E12, 5863.0], [1.7473488E12, 5823.0], [1.74734556E12, 388.5], [1.74734616E12, 7464.0], [1.74734808E12, 8.0], [1.7473497E12, 9.0], [1.74734628E12, 8337.0], [1.74734748E12, 5866.0], [1.74734688E12, 325.5], [1.7473467E12, 6282.0], [1.74734868E12, 314.0], [1.74734826E12, 335.0], [1.7473461E12, 9.0], [1.74734766E12, 6320.0], [1.74734946E12, 321.0], [1.74734724E12, 6121.5], [1.74734886E12, 8.0], [1.74734712E12, 6118.0], [1.74734874E12, 6351.0], [1.74734562E12, 10.0], [1.74734814E12, 5814.0], [1.74734832E12, 3939.0], [1.74734604E12, 392.0], [1.74734772E12, 3139.0], [1.74734934E12, 6205.0], [1.74734664E12, 10.0], [1.74734646E12, 336.0], [1.74734952E12, 5191.0], [1.74734892E12, 6226.0], [1.7473485E12, 6764.0], [1.74734586E12, 9.0], [1.7473479E12, 8141.0]], "isOverall": false, "label": "Median", "isController": false}, {"data": [[1.74734598E12, 6315.0], [1.74734778E12, 7416.0], [1.74734658E12, 6334.0], [1.74734718E12, 14.0], [1.74734736E12, 11.0], [1.747347E12, 5899.0], [1.74734898E12, 6584.0], [1.74734838E12, 6126.0], [1.74734856E12, 14.0], [1.7473458E12, 5530.0], [1.74734754E12, 6508.0], [1.74734976E12, 6292.0], [1.74734682E12, 5926.0], [1.74734916E12, 7902.0], [1.74734694E12, 6203.0], [1.74734904E12, 8332.0], [1.74734592E12, 6362.0], [1.74734802E12, 7721.0], [1.74734634E12, 6325.0], [1.74734742E12, 7480.0], [1.74734964E12, 7943.0], [1.7473476E12, 12.0], [1.74734922E12, 5979.0], [1.74734862E12, 5876.0], [1.7473488E12, 6120.0], [1.74734556E12, 6621.0], [1.74734616E12, 7464.0], [1.74734808E12, 5972.0], [1.7473497E12, 11.0], [1.74734628E12, 8337.0], [1.74734748E12, 5866.0], [1.74734688E12, 6561.0], [1.7473467E12, 6282.0], [1.74734868E12, 6551.0], [1.74734826E12, 6799.0], [1.7473461E12, 6327.0], [1.74734766E12, 6320.0], [1.74734946E12, 6622.0], [1.74734724E12, 6253.0], [1.74734886E12, 5899.0], [1.74734712E12, 6118.0], [1.74734874E12, 6351.0], [1.74734562E12, 6867.0], [1.74734814E12, 5814.0], [1.74734832E12, 7869.0], [1.74734604E12, 6777.0], [1.74734772E12, 6269.0], [1.74734934E12, 6205.0], [1.74734664E12, 6274.0], [1.74734646E12, 7890.0], [1.74734952E12, 6092.0], [1.74734892E12, 6226.0], [1.7473485E12, 6764.0], [1.74734586E12, 16.0], [1.7473479E12, 8141.0]], "isOverall": false, "label": "95th percentile", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.74734976E12, "title": "Response Time Percentiles Over Time (successful requests only)"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Response Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentilesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Response time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentilesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimePercentilesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimePercentilesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Time Percentiles Over Time
function refreshResponseTimePercentilesOverTime(fixTimestamps) {
    var infos = responseTimePercentilesOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotResponseTimePercentilesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimePercentilesOverTime", "#overviewResponseTimePercentilesOverTime");
        $('#footerResponseTimePercentilesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var responseTimeVsRequestInfos = {
    data: {"result": {"minY": 8.0, "minX": 1.0, "maxY": 5814.0, "series": [{"data": [[1.0, 5814.0], [2.0, 418.5], [4.0, 306.0], [6.0, 8.0], [3.0, 434.0]], "isOverall": false, "label": "Successes", "isController": false}, {"data": [[2.0, 9.5], [4.0, 8.5], [3.0, 8.5]], "isOverall": false, "label": "Failures", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 6.0, "title": "Response Time Vs Request"}},
    getOptions: function() {
        return {
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Response Time in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: {
                noColumns: 2,
                show: true,
                container: '#legendResponseTimeVsRequest'
            },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median response time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesResponseTimeVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotResponseTimeVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewResponseTimeVsRequest"), dataset, prepareOverviewOptions(options));

    }
};

// Response Time vs Request
function refreshResponseTimeVsRequest() {
    var infos = responseTimeVsRequestInfos;
    prepareSeries(infos.data);
    if (isGraph($("#flotResponseTimeVsRequest"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeVsRequest");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimeVsRequest", "#overviewResponseTimeVsRequest");
        $('#footerResponseRimeVsRequest .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var latenciesVsRequestInfos = {
    data: {"result": {"minY": 0.0, "minX": 1.0, "maxY": 9.0, "series": [{"data": [[1.0, 9.0], [2.0, 8.0], [4.0, 9.0], [6.0, 8.0], [3.0, 8.0]], "isOverall": false, "label": "Successes", "isController": false}, {"data": [[2.0, 0.0], [4.0, 0.0], [3.0, 0.0]], "isOverall": false, "label": "Failures", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 6.0, "title": "Latencies Vs Request"}},
    getOptions: function() {
        return{
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Latency in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: { noColumns: 2,show: true, container: '#legendLatencyVsRequest' },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median Latency time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesLatencyVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotLatenciesVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewLatenciesVsRequest"), dataset, prepareOverviewOptions(options));
    }
};

// Latencies vs Request
function refreshLatenciesVsRequest() {
        var infos = latenciesVsRequestInfos;
        prepareSeries(infos.data);
        if(isGraph($("#flotLatenciesVsRequest"))){
            infos.createGraph();
        }else{
            var choiceContainer = $("#choicesLatencyVsRequest");
            createLegend(choiceContainer, infos);
            infos.createGraph();
            setGraphZoomable("#flotLatenciesVsRequest", "#overviewLatenciesVsRequest");
            $('#footerLatenciesVsRequest .legendColorBox > div').each(function(i){
                $(this).clone().prependTo(choiceContainer.find("li").eq(i));
            });
        }
};

var hitsPerSecondInfos = {
        data: {"result": {"minY": 0.016666666666666666, "minX": 1.74734556E12, "maxY": 0.18333333333333332, "series": [{"data": [[1.74734598E12, 0.016666666666666666], [1.74734778E12, 0.15], [1.74734658E12, 0.016666666666666666], [1.74734718E12, 0.11666666666666667], [1.74734736E12, 0.03333333333333333], [1.747347E12, 0.016666666666666666], [1.74734898E12, 0.11666666666666667], [1.74734838E12, 0.016666666666666666], [1.74734856E12, 0.11666666666666667], [1.74734754E12, 0.016666666666666666], [1.74734976E12, 0.03333333333333333], [1.74734682E12, 0.016666666666666666], [1.74734916E12, 0.016666666666666666], [1.74734694E12, 0.03333333333333333], [1.74734904E12, 0.08333333333333333], [1.74734592E12, 0.016666666666666666], [1.74734574E12, 0.016666666666666666], [1.74734802E12, 0.016666666666666666], [1.74734634E12, 0.13333333333333333], [1.74734742E12, 0.15], [1.74734964E12, 0.016666666666666666], [1.7473476E12, 0.1], [1.74734922E12, 0.11666666666666667], [1.74734862E12, 0.016666666666666666], [1.7473488E12, 0.03333333333333333], [1.74734556E12, 0.11666666666666667], [1.74734616E12, 0.016666666666666666], [1.74734808E12, 0.11666666666666667], [1.7473497E12, 0.1], [1.74734628E12, 0.016666666666666666], [1.74734748E12, 0.016666666666666666], [1.74734688E12, 0.15], [1.7473467E12, 0.016666666666666666], [1.74734868E12, 0.18333333333333332], [1.74734826E12, 0.15], [1.7473461E12, 0.06666666666666667], [1.74734766E12, 0.016666666666666666], [1.74734946E12, 0.15], [1.74734724E12, 0.016666666666666666], [1.74734886E12, 0.11666666666666667], [1.74734712E12, 0.016666666666666666], [1.74734562E12, 0.08333333333333333], [1.74734814E12, 0.016666666666666666], [1.74734832E12, 0.03333333333333333], [1.74734604E12, 0.11666666666666667], [1.74734772E12, 0.05], [1.74734934E12, 0.016666666666666666], [1.74734664E12, 0.11666666666666667], [1.74734646E12, 0.18333333333333332], [1.74734952E12, 0.05], [1.74734892E12, 0.016666666666666666], [1.7473485E12, 0.016666666666666666], [1.74734586E12, 0.1], [1.7473479E12, 0.016666666666666666]], "isOverall": false, "label": "hitsPerSecond", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.74734976E12, "title": "Hits Per Second"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of hits / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendHitsPerSecond"
                },
                selection: {
                    mode : 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y.2 hits/sec"
                }
            };
        },
        createGraph: function createGraph() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesHitsPerSecond"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotHitsPerSecond"), dataset, options);
            // setup overview
            $.plot($("#overviewHitsPerSecond"), dataset, prepareOverviewOptions(options));
        }
};

// Hits per second
function refreshHitsPerSecond(fixTimestamps) {
    var infos = hitsPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if (isGraph($("#flotHitsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesHitsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotHitsPerSecond", "#overviewHitsPerSecond");
        $('#footerHitsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var codesPerSecondInfos = {
        data: {"result": {"minY": 0.016666666666666666, "minX": 1.74734556E12, "maxY": 0.16666666666666666, "series": [{"data": [[1.74734598E12, 0.016666666666666666], [1.74734778E12, 0.15], [1.74734658E12, 0.016666666666666666], [1.74734718E12, 0.1], [1.74734736E12, 0.016666666666666666], [1.747347E12, 0.016666666666666666], [1.74734898E12, 0.1], [1.74734838E12, 0.016666666666666666], [1.74734856E12, 0.1], [1.7473458E12, 0.016666666666666666], [1.74734754E12, 0.016666666666666666], [1.74734976E12, 0.03333333333333333], [1.74734682E12, 0.016666666666666666], [1.74734916E12, 0.016666666666666666], [1.74734694E12, 0.03333333333333333], [1.74734904E12, 0.08333333333333333], [1.74734592E12, 0.016666666666666666], [1.74734802E12, 0.016666666666666666], [1.74734634E12, 0.13333333333333333], [1.74734742E12, 0.15], [1.74734964E12, 0.016666666666666666], [1.7473476E12, 0.1], [1.74734922E12, 0.11666666666666667], [1.74734862E12, 0.03333333333333333], [1.7473488E12, 0.03333333333333333], [1.74734556E12, 0.1], [1.74734616E12, 0.016666666666666666], [1.74734808E12, 0.11666666666666667], [1.7473497E12, 0.1], [1.74734628E12, 0.016666666666666666], [1.74734748E12, 0.016666666666666666], [1.74734688E12, 0.13333333333333333], [1.7473467E12, 0.016666666666666666], [1.74734868E12, 0.15], [1.74734826E12, 0.13333333333333333], [1.7473461E12, 0.06666666666666667], [1.74734766E12, 0.016666666666666666], [1.74734946E12, 0.13333333333333333], [1.74734724E12, 0.03333333333333333], [1.74734886E12, 0.11666666666666667], [1.74734712E12, 0.016666666666666666], [1.74734874E12, 0.016666666666666666], [1.74734562E12, 0.08333333333333333], [1.74734814E12, 0.016666666666666666], [1.74734832E12, 0.03333333333333333], [1.74734604E12, 0.1], [1.74734772E12, 0.03333333333333333], [1.74734934E12, 0.016666666666666666], [1.74734664E12, 0.11666666666666667], [1.74734646E12, 0.16666666666666666], [1.74734952E12, 0.05], [1.74734892E12, 0.016666666666666666], [1.7473485E12, 0.016666666666666666], [1.74734586E12, 0.1], [1.7473479E12, 0.016666666666666666]], "isOverall": false, "label": "200", "isController": false}, {"data": [[1.74734646E12, 0.016666666666666666], [1.74734826E12, 0.016666666666666666], [1.74734778E12, 0.016666666666666666], [1.74734688E12, 0.016666666666666666], [1.74734946E12, 0.016666666666666666], [1.74734604E12, 0.016666666666666666], [1.74734898E12, 0.016666666666666666], [1.74734556E12, 0.016666666666666666], [1.74734868E12, 0.016666666666666666], [1.74734742E12, 0.016666666666666666]], "isOverall": false, "label": "Non HTTP response code: org.apache.http.NoHttpResponseException", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.74734976E12, "title": "Codes Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendCodesPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "Number of Response Codes %s at %x was %y.2 responses / sec"
                }
            };
        },
    createGraph: function() {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesCodesPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotCodesPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewCodesPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Codes per second
function refreshCodesPerSecond(fixTimestamps) {
    var infos = codesPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotCodesPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesCodesPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotCodesPerSecond", "#overviewCodesPerSecond");
        $('#footerCodesPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var transactionsPerSecondInfos = {
        data: {"result": {"minY": 0.016666666666666666, "minX": 1.74734556E12, "maxY": 0.08333333333333333, "series": [{"data": [[1.74734694E12, 0.016666666666666666], [1.74734904E12, 0.05], [1.74734778E12, 0.05], [1.74734562E12, 0.05], [1.74734688E12, 0.03333333333333333], [1.74734832E12, 0.016666666666666666], [1.74734868E12, 0.05], [1.74734742E12, 0.05], [1.74734646E12, 0.05], [1.74734826E12, 0.03333333333333333], [1.74734952E12, 0.016666666666666666], [1.7473461E12, 0.05], [1.74734946E12, 0.03333333333333333]], "isOverall": false, "label": "Search-success", "isController": false}, {"data": [[1.74734646E12, 0.016666666666666666], [1.74734826E12, 0.016666666666666666], [1.74734778E12, 0.016666666666666666], [1.74734688E12, 0.016666666666666666], [1.74734946E12, 0.016666666666666666], [1.74734604E12, 0.016666666666666666], [1.74734898E12, 0.016666666666666666], [1.74734556E12, 0.016666666666666666], [1.74734868E12, 0.016666666666666666], [1.74734742E12, 0.016666666666666666]], "isOverall": false, "label": "Cover 3-success", "isController": false}, {"data": [[1.74734856E12, 0.016666666666666666], [1.7473476E12, 0.016666666666666666], [1.74734808E12, 0.016666666666666666], [1.74734922E12, 0.016666666666666666], [1.7473497E12, 0.016666666666666666], [1.74734718E12, 0.016666666666666666], [1.74734634E12, 0.016666666666666666], [1.74734586E12, 0.016666666666666666], [1.74734664E12, 0.016666666666666666], [1.74734886E12, 0.016666666666666666]], "isOverall": false, "label": "Albums page-success", "isController": true}, {"data": [[1.74734646E12, 0.016666666666666666], [1.74734826E12, 0.016666666666666666], [1.74734778E12, 0.016666666666666666], [1.74734688E12, 0.016666666666666666], [1.74734946E12, 0.016666666666666666], [1.74734604E12, 0.016666666666666666], [1.74734898E12, 0.016666666666666666], [1.74734556E12, 0.016666666666666666], [1.74734868E12, 0.016666666666666666], [1.74734742E12, 0.016666666666666666]], "isOverall": false, "label": "Cover 1-failure", "isController": false}, {"data": [[1.74734646E12, 0.016666666666666666], [1.74734826E12, 0.016666666666666666], [1.74734778E12, 0.016666666666666666], [1.74734688E12, 0.016666666666666666], [1.74734946E12, 0.016666666666666666], [1.74734604E12, 0.016666666666666666], [1.74734898E12, 0.016666666666666666], [1.74734556E12, 0.016666666666666666], [1.74734868E12, 0.016666666666666666], [1.74734742E12, 0.016666666666666666]], "isOverall": false, "label": "Cover 4-success", "isController": false}, {"data": [[1.74734856E12, 0.08333333333333333], [1.7473476E12, 0.08333333333333333], [1.74734808E12, 0.08333333333333333], [1.74734922E12, 0.08333333333333333], [1.7473497E12, 0.08333333333333333], [1.74734718E12, 0.08333333333333333], [1.74734634E12, 0.08333333333333333], [1.74734586E12, 0.08333333333333333], [1.74734664E12, 0.08333333333333333], [1.74734886E12, 0.08333333333333333]], "isOverall": false, "label": "Get paths to songs from each album-success", "isController": false}, {"data": [[1.74734628E12, 0.016666666666666666], [1.74734778E12, 0.016666666666666666], [1.74734658E12, 0.016666666666666666], [1.74734748E12, 0.016666666666666666], [1.747347E12, 0.016666666666666666], [1.74734838E12, 0.016666666666666666], [1.7473458E12, 0.016666666666666666], [1.7473461E12, 0.016666666666666666], [1.74734754E12, 0.016666666666666666], [1.74734916E12, 0.016666666666666666], [1.74734694E12, 0.016666666666666666], [1.74734904E12, 0.03333333333333333], [1.74734712E12, 0.016666666666666666], [1.74734874E12, 0.016666666666666666], [1.74734562E12, 0.03333333333333333], [1.74734832E12, 0.016666666666666666], [1.74734802E12, 0.016666666666666666], [1.74734664E12, 0.016666666666666666], [1.74734742E12, 0.016666666666666666], [1.74734964E12, 0.016666666666666666], [1.74734646E12, 0.016666666666666666], [1.74734952E12, 0.03333333333333333], [1.7473488E12, 0.03333333333333333], [1.7473485E12, 0.016666666666666666], [1.74734616E12, 0.016666666666666666], [1.7473479E12, 0.016666666666666666]], "isOverall": false, "label": "Play songs from home page-success", "isController": false}, {"data": [[1.74734646E12, 0.016666666666666666], [1.74734826E12, 0.016666666666666666], [1.74734778E12, 0.016666666666666666], [1.74734688E12, 0.016666666666666666], [1.74734946E12, 0.016666666666666666], [1.74734604E12, 0.016666666666666666], [1.74734898E12, 0.016666666666666666], [1.74734556E12, 0.016666666666666666], [1.74734868E12, 0.016666666666666666], [1.74734742E12, 0.016666666666666666]], "isOverall": false, "label": "Initial song-success", "isController": false}, {"data": [[1.74734646E12, 0.016666666666666666], [1.74734826E12, 0.016666666666666666], [1.74734778E12, 0.016666666666666666], [1.74734688E12, 0.016666666666666666], [1.74734946E12, 0.016666666666666666], [1.74734604E12, 0.016666666666666666], [1.74734898E12, 0.016666666666666666], [1.74734556E12, 0.016666666666666666], [1.74734868E12, 0.016666666666666666], [1.74734742E12, 0.016666666666666666]], "isOverall": false, "label": "Cover 5-success", "isController": false}, {"data": [[1.74734598E12, 0.016666666666666666], [1.74734808E12, 0.016666666666666666], [1.74734814E12, 0.016666666666666666], [1.74734592E12, 0.016666666666666666], [1.7473467E12, 0.016666666666666666], [1.74734634E12, 0.03333333333333333], [1.74734772E12, 0.016666666666666666], [1.74734934E12, 0.016666666666666666], [1.74734922E12, 0.016666666666666666], [1.74734892E12, 0.016666666666666666], [1.74734862E12, 0.03333333333333333], [1.74734766E12, 0.016666666666666666], [1.74734976E12, 0.03333333333333333], [1.74734682E12, 0.016666666666666666], [1.74734724E12, 0.03333333333333333], [1.74734886E12, 0.016666666666666666]], "isOverall": false, "label": "Play songs from albums-success", "isController": false}, {"data": [[1.74734646E12, 0.016666666666666666], [1.74734826E12, 0.016666666666666666], [1.74734688E12, 0.016666666666666666], [1.74734736E12, 0.016666666666666666], [1.74734946E12, 0.016666666666666666], [1.74734604E12, 0.016666666666666666], [1.74734898E12, 0.016666666666666666], [1.74734556E12, 0.016666666666666666], [1.74734868E12, 0.016666666666666666], [1.74734772E12, 0.016666666666666666]], "isOverall": false, "label": "Trending playlist-success", "isController": false}, {"data": [[1.74734646E12, 0.016666666666666666], [1.74734826E12, 0.016666666666666666], [1.74734778E12, 0.016666666666666666], [1.74734688E12, 0.016666666666666666], [1.74734946E12, 0.016666666666666666], [1.74734604E12, 0.016666666666666666], [1.74734898E12, 0.016666666666666666], [1.74734556E12, 0.016666666666666666], [1.74734868E12, 0.016666666666666666], [1.74734742E12, 0.016666666666666666]], "isOverall": false, "label": "Home page-failure", "isController": true}, {"data": [[1.74734646E12, 0.016666666666666666], [1.74734826E12, 0.016666666666666666], [1.74734778E12, 0.016666666666666666], [1.74734688E12, 0.016666666666666666], [1.74734946E12, 0.016666666666666666], [1.74734604E12, 0.016666666666666666], [1.74734898E12, 0.016666666666666666], [1.74734556E12, 0.016666666666666666], [1.74734868E12, 0.016666666666666666], [1.74734742E12, 0.016666666666666666]], "isOverall": false, "label": "Cover 2-success", "isController": false}, {"data": [[1.74734856E12, 0.016666666666666666], [1.7473476E12, 0.016666666666666666], [1.74734808E12, 0.016666666666666666], [1.74734922E12, 0.016666666666666666], [1.7473497E12, 0.016666666666666666], [1.74734718E12, 0.016666666666666666], [1.74734634E12, 0.016666666666666666], [1.74734586E12, 0.016666666666666666], [1.74734664E12, 0.016666666666666666], [1.74734886E12, 0.016666666666666666]], "isOverall": false, "label": "Get all albums-success", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.74734976E12, "title": "Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTransactionsPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                }
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTransactionsPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTransactionsPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewTransactionsPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Transactions per second
function refreshTransactionsPerSecond(fixTimestamps) {
    var infos = transactionsPerSecondInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTransactionsPerSecond");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotTransactionsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTransactionsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTransactionsPerSecond", "#overviewTransactionsPerSecond");
        $('#footerTransactionsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var totalTPSInfos = {
        data: {"result": {"minY": 0.016666666666666666, "minX": 1.74734556E12, "maxY": 0.16666666666666666, "series": [{"data": [[1.74734598E12, 0.016666666666666666], [1.74734778E12, 0.15], [1.74734658E12, 0.016666666666666666], [1.74734718E12, 0.11666666666666667], [1.74734736E12, 0.016666666666666666], [1.747347E12, 0.016666666666666666], [1.74734898E12, 0.1], [1.74734838E12, 0.016666666666666666], [1.74734856E12, 0.11666666666666667], [1.7473458E12, 0.016666666666666666], [1.74734754E12, 0.016666666666666666], [1.74734976E12, 0.03333333333333333], [1.74734682E12, 0.016666666666666666], [1.74734916E12, 0.016666666666666666], [1.74734694E12, 0.03333333333333333], [1.74734904E12, 0.08333333333333333], [1.74734592E12, 0.016666666666666666], [1.74734802E12, 0.016666666666666666], [1.74734634E12, 0.15], [1.74734742E12, 0.15], [1.74734964E12, 0.016666666666666666], [1.7473476E12, 0.11666666666666667], [1.74734922E12, 0.13333333333333333], [1.74734862E12, 0.03333333333333333], [1.7473488E12, 0.03333333333333333], [1.74734556E12, 0.1], [1.74734616E12, 0.016666666666666666], [1.74734808E12, 0.13333333333333333], [1.7473497E12, 0.11666666666666667], [1.74734628E12, 0.016666666666666666], [1.74734748E12, 0.016666666666666666], [1.74734688E12, 0.13333333333333333], [1.7473467E12, 0.016666666666666666], [1.74734868E12, 0.15], [1.74734826E12, 0.13333333333333333], [1.7473461E12, 0.06666666666666667], [1.74734766E12, 0.016666666666666666], [1.74734946E12, 0.13333333333333333], [1.74734724E12, 0.03333333333333333], [1.74734886E12, 0.13333333333333333], [1.74734712E12, 0.016666666666666666], [1.74734874E12, 0.016666666666666666], [1.74734562E12, 0.08333333333333333], [1.74734814E12, 0.016666666666666666], [1.74734832E12, 0.03333333333333333], [1.74734604E12, 0.1], [1.74734772E12, 0.03333333333333333], [1.74734934E12, 0.016666666666666666], [1.74734664E12, 0.13333333333333333], [1.74734646E12, 0.16666666666666666], [1.74734952E12, 0.05], [1.74734892E12, 0.016666666666666666], [1.7473485E12, 0.016666666666666666], [1.74734586E12, 0.11666666666666667], [1.7473479E12, 0.016666666666666666]], "isOverall": false, "label": "Transaction-success", "isController": false}, {"data": [[1.74734646E12, 0.03333333333333333], [1.74734826E12, 0.03333333333333333], [1.74734778E12, 0.03333333333333333], [1.74734688E12, 0.03333333333333333], [1.74734946E12, 0.03333333333333333], [1.74734604E12, 0.03333333333333333], [1.74734898E12, 0.03333333333333333], [1.74734556E12, 0.03333333333333333], [1.74734868E12, 0.03333333333333333], [1.74734742E12, 0.03333333333333333]], "isOverall": false, "label": "Transaction-failure", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.74734976E12, "title": "Total Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTotalTPS"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                },
                colors: ["#9ACD32", "#FF6347"]
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTotalTPS"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTotalTPS"), dataset, options);
        // setup overview
        $.plot($("#overviewTotalTPS"), dataset, prepareOverviewOptions(options));
    }
};

// Total Transactions per second
function refreshTotalTPS(fixTimestamps) {
    var infos = totalTPSInfos;
    // We want to ignore seriesFilter
    prepareSeries(infos.data, false, true);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotTotalTPS"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTotalTPS");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTotalTPS", "#overviewTotalTPS");
        $('#footerTotalTPS .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

// Collapse the graph matching the specified DOM element depending the collapsed
// status
function collapse(elem, collapsed){
    if(collapsed){
        $(elem).parent().find(".fa-chevron-up").removeClass("fa-chevron-up").addClass("fa-chevron-down");
    } else {
        $(elem).parent().find(".fa-chevron-down").removeClass("fa-chevron-down").addClass("fa-chevron-up");
        if (elem.id == "bodyBytesThroughputOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshBytesThroughputOverTime(true);
            }
            document.location.href="#bytesThroughputOverTime";
        } else if (elem.id == "bodyLatenciesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesOverTime(true);
            }
            document.location.href="#latenciesOverTime";
        } else if (elem.id == "bodyCustomGraph") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCustomGraph(true);
            }
            document.location.href="#responseCustomGraph";
        } else if (elem.id == "bodyConnectTimeOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshConnectTimeOverTime(true);
            }
            document.location.href="#connectTimeOverTime";
        } else if (elem.id == "bodyResponseTimePercentilesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimePercentilesOverTime(true);
            }
            document.location.href="#responseTimePercentilesOverTime";
        } else if (elem.id == "bodyResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeDistribution();
            }
            document.location.href="#responseTimeDistribution" ;
        } else if (elem.id == "bodySyntheticResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshSyntheticResponseTimeDistribution();
            }
            document.location.href="#syntheticResponseTimeDistribution" ;
        } else if (elem.id == "bodyActiveThreadsOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshActiveThreadsOverTime(true);
            }
            document.location.href="#activeThreadsOverTime";
        } else if (elem.id == "bodyTimeVsThreads") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTimeVsThreads();
            }
            document.location.href="#timeVsThreads" ;
        } else if (elem.id == "bodyCodesPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCodesPerSecond(true);
            }
            document.location.href="#codesPerSecond";
        } else if (elem.id == "bodyTransactionsPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTransactionsPerSecond(true);
            }
            document.location.href="#transactionsPerSecond";
        } else if (elem.id == "bodyTotalTPS") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTotalTPS(true);
            }
            document.location.href="#totalTPS";
        } else if (elem.id == "bodyResponseTimeVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeVsRequest();
            }
            document.location.href="#responseTimeVsRequest";
        } else if (elem.id == "bodyLatenciesVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesVsRequest();
            }
            document.location.href="#latencyVsRequest";
        }
    }
}

/*
 * Activates or deactivates all series of the specified graph (represented by id parameter)
 * depending on checked argument.
 */
function toggleAll(id, checked){
    var placeholder = document.getElementById(id);

    var cases = $(placeholder).find(':checkbox');
    cases.prop('checked', checked);
    $(cases).parent().children().children().toggleClass("legend-disabled", !checked);

    var choiceContainer;
    if ( id == "choicesBytesThroughputOverTime"){
        choiceContainer = $("#choicesBytesThroughputOverTime");
        refreshBytesThroughputOverTime(false);
    } else if(id == "choicesResponseTimesOverTime"){
        choiceContainer = $("#choicesResponseTimesOverTime");
        refreshResponseTimeOverTime(false);
    }else if(id == "choicesResponseCustomGraph"){
        choiceContainer = $("#choicesResponseCustomGraph");
        refreshCustomGraph(false);
    } else if ( id == "choicesLatenciesOverTime"){
        choiceContainer = $("#choicesLatenciesOverTime");
        refreshLatenciesOverTime(false);
    } else if ( id == "choicesConnectTimeOverTime"){
        choiceContainer = $("#choicesConnectTimeOverTime");
        refreshConnectTimeOverTime(false);
    } else if ( id == "choicesResponseTimePercentilesOverTime"){
        choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        refreshResponseTimePercentilesOverTime(false);
    } else if ( id == "choicesResponseTimePercentiles"){
        choiceContainer = $("#choicesResponseTimePercentiles");
        refreshResponseTimePercentiles();
    } else if(id == "choicesActiveThreadsOverTime"){
        choiceContainer = $("#choicesActiveThreadsOverTime");
        refreshActiveThreadsOverTime(false);
    } else if ( id == "choicesTimeVsThreads"){
        choiceContainer = $("#choicesTimeVsThreads");
        refreshTimeVsThreads();
    } else if ( id == "choicesSyntheticResponseTimeDistribution"){
        choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        refreshSyntheticResponseTimeDistribution();
    } else if ( id == "choicesResponseTimeDistribution"){
        choiceContainer = $("#choicesResponseTimeDistribution");
        refreshResponseTimeDistribution();
    } else if ( id == "choicesHitsPerSecond"){
        choiceContainer = $("#choicesHitsPerSecond");
        refreshHitsPerSecond(false);
    } else if(id == "choicesCodesPerSecond"){
        choiceContainer = $("#choicesCodesPerSecond");
        refreshCodesPerSecond(false);
    } else if ( id == "choicesTransactionsPerSecond"){
        choiceContainer = $("#choicesTransactionsPerSecond");
        refreshTransactionsPerSecond(false);
    } else if ( id == "choicesTotalTPS"){
        choiceContainer = $("#choicesTotalTPS");
        refreshTotalTPS(false);
    } else if ( id == "choicesResponseTimeVsRequest"){
        choiceContainer = $("#choicesResponseTimeVsRequest");
        refreshResponseTimeVsRequest();
    } else if ( id == "choicesLatencyVsRequest"){
        choiceContainer = $("#choicesLatencyVsRequest");
        refreshLatenciesVsRequest();
    }
    var color = checked ? "black" : "#818181";
    if(choiceContainer != null) {
        choiceContainer.find("label").each(function(){
            this.style.color = color;
        });
    }
}

