import {albums, genres} from "./data";
import client from "../src/client";


const seed = async () => {
  console.log("Start seeding...")

  await client.genre.createMany({data: genres});
  await client.$transaction(albums.map((album) => client.album.create({data: album})))

  const songs = await client.song.findMany({
    where: {
      title: {
        contains: "a"
      }
    }
  })

  await client.playlist.create({
    data: {
      id: "0",
      title: "Trending",
      songs: {
        connect: songs
      }
    }
  })

  console.log("Seeding finished.")
}


seed().then(async () => {
  await client.$disconnect()
}).catch(async (e) => {
  console.error(e)
  await client.$disconnect()
  process.exit(1)
});
