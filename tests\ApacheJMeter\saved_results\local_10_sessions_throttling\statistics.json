{"Get all albums": {"transaction": "Get all albums", "sampleCount": 10, "errorCount": 0, "errorPct": 0.0, "meanResTime": 12.1, "medianResTime": 13.0, "minResTime": 8.0, "maxResTime": 16.0, "pct1ResTime": 15.8, "pct2ResTime": 16.0, "pct3ResTime": 16.0, "throughput": 0.0025881698895420854, "receivedKBytesPerSec": 0.0025957524185153532, "sentKBytesPerSec": 0.0003058286685884691}, "Home page": {"transaction": "Home page", "sampleCount": 10, "errorCount": 10, "errorPct": 100.0, "meanResTime": 8262.199999999999, "medianResTime": 8202.0, "minResTime": 8103.0, "maxResTime": 8648.0, "pct1ResTime": 8621.9, "pct2ResTime": 8648.0, "pct3ResTime": 8648.0, "throughput": 0.0025732210036591204, "receivedKBytesPerSec": 21.542533815340516, "sentKBytesPerSec": 0.002166129399564611}, "Play songs from home page": {"transaction": "Play songs from home page", "sampleCount": 30, "errorCount": 0, "errorPct": 0.0, "meanResTime": 6791.599999999999, "medianResTime": 6429.5, "minResTime": 5191.0, "maxResTime": 8337.0, "pct1ResTime": 8121.200000000001, "pct2ResTime": 8334.25, "pct3ResTime": 8337.0, "throughput": 0.0074675237392579676, "receivedKBytesPerSec": 51.69732191146018, "sentKBytesPerSec": 0.0012460457906066516}, "Trending playlist": {"transaction": "Trending playlist", "sampleCount": 10, "errorCount": 0, "errorPct": 0.0, "meanResTime": 15.5, "medianResTime": 11.0, "minResTime": 9.0, "maxResTime": 37.0, "pct1ResTime": 35.900000000000006, "pct2ResTime": 37.0, "pct3ResTime": 37.0, "throughput": 0.0025786773808992723, "receivedKBytesPerSec": 0.017539539021448666, "sentKBytesPerSec": 0.00033744411039111574}, "Albums page": {"transaction": "Albums page", "sampleCount": 10, "errorCount": 0, "errorPct": 0.0, "meanResTime": 54.0, "medianResTime": 54.5, "minResTime": 48.0, "maxResTime": 60.0, "pct1ResTime": 59.6, "pct2ResTime": 60.0, "pct3ResTime": 60.0, "throughput": 0.00258814041593487, "receivedKBytesPerSec": 0.0379299054901705, "sentKBytesPerSec": 0.0023025350770670572}, "Cover 2": {"transaction": "Cover 2", "sampleCount": 10, "errorCount": 0, "errorPct": 0.0, "meanResTime": 461.2, "medianResTime": 460.0, "minResTime": 448.0, "maxResTime": 491.0, "pct1ResTime": 488.1, "pct2ResTime": 491.0, "pct3ResTime": 491.0, "throughput": 0.002578804394076383, "receivedKBytesPerSec": 1.1766021700703446, "sentKBytesPerSec": 0.0003500525495865403}, "Cover 1": {"transaction": "Cover 1", "sampleCount": 10, "errorCount": 10, "errorPct": 100.0, "meanResTime": 8.9, "medianResTime": 9.0, "minResTime": 8.0, "maxResTime": 11.0, "pct1ResTime": 10.9, "pct2ResTime": 11.0, "pct3ResTime": 11.0, "throughput": 0.0025791023588985994, "receivedKBytesPerSec": 0.005269025522281123, "sentKBytesPerSec": 0.0}, "Cover 4": {"transaction": "Cover 4", "sampleCount": 10, "errorCount": 0, "errorPct": 0.0, "meanResTime": 427.4, "medianResTime": 427.0, "minResTime": 415.0, "maxResTime": 436.0, "pct1ResTime": 436.0, "pct2ResTime": 436.0, "pct3ResTime": 436.0, "throughput": 0.002578833655429657, "receivedKBytesPerSec": 1.1327501647552352, "sentKBytesPerSec": 0.00035005652158664287}, "Cover 3": {"transaction": "Cover 3", "sampleCount": 10, "errorCount": 0, "errorPct": 0.0, "meanResTime": 309.6, "medianResTime": 309.0, "minResTime": 302.0, "maxResTime": 319.0, "pct1ResTime": 318.9, "pct2ResTime": 319.0, "pct3ResTime": 319.0, "throughput": 0.002578908806947787, "receivedKBytesPerSec": 0.8070171270491687, "sentKBytesPerSec": 0.0003500667228181078}, "Play songs from albums": {"transaction": "Play songs from albums", "sampleCount": 20, "errorCount": 0, "errorPct": 0.0, "meanResTime": 6127.5, "medianResTime": 6215.5, "minResTime": 5814.0, "maxResTime": 6362.0, "pct1ResTime": 6324.5, "pct2ResTime": 6360.15, "pct3ResTime": 6362.0, "throughput": 0.00513268767544168, "receivedKBytesPerSec": 32.14482645372471, "sentKBytesPerSec": 0.0008543619280068696}, "Cover 5": {"transaction": "Cover 5", "sampleCount": 10, "errorCount": 0, "errorPct": 0.0, "meanResTime": 348.0, "medianResTime": 348.5, "minResTime": 340.0, "maxResTime": 362.0, "pct1ResTime": 361.2, "pct2ResTime": 362.0, "pct3ResTime": 362.0, "throughput": 0.0025788935102660595, "receivedKBytesPerSec": 0.9107221398265746, "sentKBytesPerSec": 0.0003500646464130686}, "Total": {"transaction": "Total", "sampleCount": 210, "errorCount": 10, "errorPct": 4.7619047, "meanResTime": 1951.1380952380955, "medianResTime": 15.5, "minResTime": 7.0, "maxResTime": 8337.0, "pct1ResTime": 6611.0, "pct2ResTime": 7471.2, "pct3ResTime": 8310.989999999998, "throughput": 0.049849561145827774, "receivedKBytesPerSec": 98.971460338072, "sentKBytesPerSec": 0.007027480683295056}, "Search": {"transaction": "Search", "sampleCount": 30, "errorCount": 0, "errorPct": 0.0, "meanResTime": 9.3, "medianResTime": 9.0, "minResTime": 8.0, "maxResTime": 20.0, "pct1ResTime": 10.0, "pct2ResTime": 15.599999999999994, "pct3ResTime": 20.0, "throughput": 0.007733433696117301, "receivedKBytesPerSec": 0.03246179280971113, "sentKBytesPerSec": 0.0010195444814217144}, "Initial song": {"transaction": "Initial song", "sampleCount": 10, "errorCount": 0, "errorPct": 0.0, "meanResTime": 6691.5999999999985, "medianResTime": 6621.5, "minResTime": 6551.0, "maxResTime": 7099.0, "pct1ResTime": 7069.0, "pct2ResTime": 7099.0, "pct3ResTime": 7099.0, "throughput": 0.002574714348316626, "receivedKBytesPerSec": 17.511635516324205, "sentKBytesPerSec": 0.0004324715506938083}, "Get paths to songs from each album": {"transaction": "Get paths to songs from each album", "sampleCount": 50, "errorCount": 0, "errorPct": 0.0, "meanResTime": 8.379999999999999, "medianResTime": 8.0, "minResTime": 7.0, "maxResTime": 12.0, "pct1ResTime": 10.0, "pct2ResTime": 10.449999999999996, "pct3ResTime": 12.0, "throughput": 0.012940745619881126, "receivedKBytesPerSec": 0.035334301516784794, "sentKBytesPerSec": 0.001996716609317596}}