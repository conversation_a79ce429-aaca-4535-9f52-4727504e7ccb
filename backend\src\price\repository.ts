import { Result } from "@badrap/result";
import client from "../client";
import { DbResult, Price } from "../types";
import { NotFoundError } from "../utils";
import { PriceInput } from "../validationSchemas";

export const priceRepository = {
  get: async function (): DbResult<PriceInput["body"]> {
    try {
      // get the latest price and omit id from the result
      const result = await client.price.findFirst({
        orderBy: {
          id: "desc",
        },
        select: {
          car: true,
          moto: true,
          caravan: true,
          people: true,
          children: true,
          dog: true,
          tent: true,
          electricity: true,
        },
      });

      if (!result) {
        return Result.err(new Error("Price not found"));
      }
      return Result.ok(result);
    } catch (e) {
      return Result.err(new NotFoundError());
    }
  },

  create: async function (priceData: PriceInput["body"]): DbResult<Price> {
    try {
      const result = await client.price.create({
        data: priceData,
      });

      if (!result) {
        return Result.err(new Error("Price not created"));
      }
      return Result.ok(result);
    } catch (e) {
      return Result.err(new NotFoundError());
    }
  },
};
