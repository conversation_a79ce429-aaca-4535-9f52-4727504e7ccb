import client from "../client";
import {Result} from '@badrap/result';
import {NotFoundError} from "../utils";
import {Playlist} from "@prisma/client";
import {DbResult} from "../types";
import {randomUUID} from "node:crypto";

export const playlistRepository = {

  get: async function (id?: string, name?: string): DbResult<Playlist> {
    try {
      let result

      if (name) {
        const songs = await client.song.findMany(
          {
            where: {
              title: {
                contains: name,
                mode: 'insensitive'
              }
            },
            include: {
              genres: true,
            }
          }
        )
        result = {
          id: randomUUID(),
          title: name,
          songs: songs
        }
      } else if (id) {
        result = await client.playlist.findFirst({
          where: {
            id: "0",
          },
          include: {
            songs: {
              include: {
                genres: true,
              },
            },
          },
        });
      }

      if ((!id && !name) || !result) {
        return Result.err(new Error(`Playlist not found`));
      }

      return Result.ok(result);
    } catch (e) {
      return Result.err(new NotFoundError());
    }
  }
}