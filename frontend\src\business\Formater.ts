
export class Formater {
  static formatPrice(price: number): string {
    return price.toFixed(2).replace('.', ',') + ' $';
  }

  static formatTime(secs: number): string {
    const minutes = Math.floor(secs / 60) || 0;
    const seconds = (secs - minutes * 60) || 0;

    return minutes + ':' + (seconds < 10 ? '0' : '') + seconds;
  }

  static formatDuration(duration: number): string {
    const minutes = Math.floor(duration / 60) || 0;
    const seconds = (duration - minutes * 60) || 0;

    return minutes + ' min. ' + (seconds < 10 ? '0' : '') + seconds + ' s';
  }

  static formatYear(date: Date): string {
    return date.toString().slice(0, 4);
  }
}
