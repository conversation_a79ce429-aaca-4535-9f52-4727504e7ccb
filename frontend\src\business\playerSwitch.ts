import {Player} from "@/business/player.ts";
import {Album, Playlist} from "@/types/types.ts";

interface PlayerSwitchProps {
  data: Album | Playlist | undefined;
  songIndex: number;
  player: Player;
  setPlayer: (player: Player) => void;
  startPlay?: boolean;
}

export function playerSwitch({data, songIndex, player, setPlayer, startPlay = true}: PlayerSwitchProps) {

  if (!data || !data.songs || !data.songs.length) {
    return
  }

  if (player.id !== data.id) {
    // switch to another album/playlist
    player.stop();
    const newPlayer = new Player(data.songs, data.id);
    setPlayer(newPlayer);
    newPlayer.play(songIndex, startPlay)
  } else {
    if (player.songIndex === songIndex) {
      // play/pause the current song
      if (player.isPlaying) {
        player.pause();
      } else {
        player.play(player.songIndex);
      }
    } else {
      // switch to another song in playlist
      player.skipTo(songIndex);
      if (!player.isPlaying) {
        player.play(songIndex);
      }
    }
  }


  player.onSongChange(() => {
  })

  return
}