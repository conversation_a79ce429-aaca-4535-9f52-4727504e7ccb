import router from "./router/routes.tsx";
import {RouterProvider} from "react-router-dom";
import {Toaster} from "@/ui/components/toaster.tsx";
import {createContext, useState, useEffect, Dispatch, SetStateAction} from "react";
import {Player} from "@/business/player.ts";
import {usePlaylist} from "@/hooks/usePlaylist.ts";

const defaultPlayer = new Player([], "");

export const PlayerContext = createContext<{
  player: Player;
  setPlayer: Dispatch<SetStateAction<Player>>;
}>({
  player: defaultPlayer,
  setPlayer: () => {
  },
});

export const mediaPath = import.meta.env.VITE_BACKEND_URL + "/media";

function App() {
  const {data, isFetching} = usePlaylist("0", "");
  const [player, setPlayer] = useState<Player>(defaultPlayer);

  useEffect(() => {
    if (data && data.songs) {
      const newPlayer = new Player(data.songs, data.id);
      newPlayer.play(0, false);
      setPlayer(newPlayer);
    }
  }, [data]);

  if (isFetching) {
    return <div>Loading...</div>;
  }

  if (!player.playlist.length) {
    return <div>503 Service Unavailable</div>;
  }

  return (
    <PlayerContext.Provider value={{player, setPlayer}}>
      <Toaster/>
      <RouterProvider router={router}/>
    </PlayerContext.Provider>
  );
}

export default App;
