import {
  NavigationMenu,
  NavigationMenuItem, NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle
} from "../navigation-menu.tsx";
import {Link} from "react-router-dom";
import {HomeIcon, MusicalNoteIcon, UserCircleIcon} from "@heroicons/react/24/outline";

function NavbarNavigationMenu() {
  return (
    <div className="">
      <NavigationMenu>
        <NavigationMenuList>

          <NavigationMenuItem>
            <NavigationMenuLink className={navigationMenuTriggerStyle()} asChild>
              <Link to="./home">
                <HomeIcon className="h-6 w-6"/>
                <h2 className="text-2xl hidden md:inline">
                  HOME
                </h2>
              </Link>
            </NavigationMenuLink>
          </NavigationMenuItem>


          <NavigationMenuItem>
            <NavigationMenuLink className={navigationMenuTriggerStyle()} asChild>
              <Link to="./albums">
                <MusicalNoteIcon className="h-6 w-6"/>
                <h2 className="text-2xl hidden md:inline">
                  ALBUMS
                </h2>
              </Link>
            </NavigationMenuLink>
          </NavigationMenuItem>

          <NavigationMenuItem>
            <NavigationMenuLink className={navigationMenuTriggerStyle()} asChild>
              <Link to="./about">
                <UserCircleIcon className="h-6 w-6"/>
                <h2 className="text-2xl hidden md:inline">
                  ABOUT
                </h2>
              </Link>
            </NavigationMenuLink>
          </NavigationMenuItem>

        </NavigationMenuList>
      </NavigationMenu>
    </div>
  )
}

export default NavbarNavigationMenu