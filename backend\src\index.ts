// <<PERSON><PERSON>> (<6.10.2024>) *********************/iteration-02
// [Source code] https://gitlab.fi.muni.cz/pb138/*********************/-/tree/iteration-02/iteration-02?ref_type=heads

import {config} from 'dotenv';
import express from "express";
import cors from 'cors';
import {albumRouter, mediaRouter, playlistRouter} from "./router";
import {env} from "process";

config();

const app = express();
const port = env.PORT || 6001;

app.use(cors());
app.use(express.json());
app.use(express.urlencoded({extended: true}));

app.use("/playlist", playlistRouter);
app.use("/media", mediaRouter);
app.use("/album", albumRouter);


app.use((_req, res) => {
  res.status(404).send('Not found');
});

if (env.NODE_ENV !== 'test') {
  app.listen(port, () => {
    console.log(
      `[${new Date().toISOString()}] API is listening on port ${port}`,
    );
  });
}