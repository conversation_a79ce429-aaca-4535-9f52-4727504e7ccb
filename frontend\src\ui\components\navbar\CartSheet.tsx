import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetDes<PERSON>
} from "../sheet.tsx";
import {Button} from "../button.tsx";
import {toast} from "../use-toast.ts";
import {usePlaylist} from "@/hooks/usePlaylist.ts";
import SongCartPreview from "@/ui/components/page-content/SongCartPreview.tsx";


function CartSheet() {
  function submitOrder() {
    toast({
      title: "Order submitted",
      description: "Your order was submitted successfully",
    })
  }

  const {data} = usePlaylist("0")

  if (!data) {
    return <div>503 Service Unavailable</div>;
  }

  const totalPrice = data.songs.reduce((acc, song) => acc + song.price, 0).toFixed(2);

  return (
    <SheetContent>
      <SheetHeader>
        <SheetTitle>CART</SheetTitle>
        <SheetDescription asChild>
          <div>
            <SongCartPreview song={data.songs[0]}/>
            <SongCartPreview song={data.songs[1]}/>
            <SongCartPreview song={data.songs[2]}/>
            <div className="flex justify-between mt-10 text-lg font-bold">
              <h3>Subtotal:</h3>
              <h3>{totalPrice} €</h3>
            </div>
            <Button variant="default" className="w-full" onClick={submitOrder}>
              Checkout
            </Button>
          </div>
        </SheetDescription>
      </SheetHeader>
    </SheetContent>
  )
}

export default CartSheet