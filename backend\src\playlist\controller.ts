import {playlistRepository} from "./repository";
import {handleRepositoryErrors, parseRequest} from "../utils";
import {idQueryRequestSchema} from "../validationSchemas";
import {Request, Response} from "express";


const getPlaylist = async (req: Request, res: Response) => {
  const request = await parseRequest(idQueryRequestSchema, req, res);
  if (request === null) {
    return;
  }

  const id = request.params.id;
  const filter = request.query.filter;
  const playlist = await playlistRepository.get(id, filter);

  if (playlist.isErr) {
    handleRepositoryErrors(playlist.error, res);
    return;
  }

  res.status(200).send(playlist.value);
}

export const playlistController = {
  getPlaylist,
}