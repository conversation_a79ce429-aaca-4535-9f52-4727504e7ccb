/*
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
var showControllersOnly = false;
var seriesFilter = "";
var filtersOnlySampleSeries = true;

/*
 * Add header in statistics table to group metrics by category
 * format
 *
 */
function summaryTableHeader(header) {
    var newRow = header.insertRow(-1);
    newRow.className = "tablesorter-no-sort";
    var cell = document.createElement('th');
    cell.setAttribute("data-sorter", false);
    cell.colSpan = 1;
    cell.innerHTML = "Requests";
    newRow.appendChild(cell);

    cell = document.createElement('th');
    cell.setAttribute("data-sorter", false);
    cell.colSpan = 3;
    cell.innerHTML = "Executions";
    newRow.appendChild(cell);

    cell = document.createElement('th');
    cell.setAttribute("data-sorter", false);
    cell.colSpan = 7;
    cell.innerHTML = "Response Times (ms)";
    newRow.appendChild(cell);

    cell = document.createElement('th');
    cell.setAttribute("data-sorter", false);
    cell.colSpan = 1;
    cell.innerHTML = "Throughput";
    newRow.appendChild(cell);

    cell = document.createElement('th');
    cell.setAttribute("data-sorter", false);
    cell.colSpan = 2;
    cell.innerHTML = "Network (KB/sec)";
    newRow.appendChild(cell);
}

/*
 * Populates the table identified by id parameter with the specified data and
 * format
 *
 */
function createTable(table, info, formatter, defaultSorts, seriesIndex, headerCreator) {
    var tableRef = table[0];

    // Create header and populate it with data.titles array
    var header = tableRef.createTHead();

    // Call callback is available
    if(headerCreator) {
        headerCreator(header);
    }

    var newRow = header.insertRow(-1);
    for (var index = 0; index < info.titles.length; index++) {
        var cell = document.createElement('th');
        cell.innerHTML = info.titles[index];
        newRow.appendChild(cell);
    }

    var tBody;

    // Create overall body if defined
    if(info.overall){
        tBody = document.createElement('tbody');
        tBody.className = "tablesorter-no-sort";
        tableRef.appendChild(tBody);
        var newRow = tBody.insertRow(-1);
        var data = info.overall.data;
        for(var index=0;index < data.length; index++){
            var cell = newRow.insertCell(-1);
            cell.innerHTML = formatter ? formatter(index, data[index]): data[index];
        }
    }

    // Create regular body
    tBody = document.createElement('tbody');
    tableRef.appendChild(tBody);

    var regexp;
    if(seriesFilter) {
        regexp = new RegExp(seriesFilter, 'i');
    }
    // Populate body with data.items array
    for(var index=0; index < info.items.length; index++){
        var item = info.items[index];
        if((!regexp || filtersOnlySampleSeries && !info.supportsControllersDiscrimination || regexp.test(item.data[seriesIndex]))
                &&
                (!showControllersOnly || !info.supportsControllersDiscrimination || item.isController)){
            if(item.data.length > 0) {
                var newRow = tBody.insertRow(-1);
                for(var col=0; col < item.data.length; col++){
                    var cell = newRow.insertCell(-1);
                    cell.innerHTML = formatter ? formatter(col, item.data[col]) : item.data[col];
                }
            }
        }
    }

    // Add support of columns sort
    table.tablesorter({sortList : defaultSorts});
}

$(document).ready(function() {

    // Customize table sorter default options
    $.extend( $.tablesorter.defaults, {
        theme: 'blue',
        cssInfoBlock: "tablesorter-no-sort",
        widthFixed: true,
        widgets: ['zebra']
    });

    var data = {"OkPercent": 95.23809523809524, "KoPercent": 4.761904761904762};
    var dataset = [
        {
            "label" : "FAIL",
            "data" : data.KoPercent,
            "color" : "#FF6347"
        },
        {
            "label" : "PASS",
            "data" : data.OkPercent,
            "color" : "#9ACD32"
        }];
    $.plot($("#flot-requests-summary"), dataset, {
        series : {
            pie : {
                show : true,
                radius : 1,
                label : {
                    show : true,
                    radius : 3 / 4,
                    formatter : function(label, series) {
                        return '<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'
                            + label
                            + '<br/>'
                            + Math.round10(series.percent, -2)
                            + '%</div>';
                    },
                    background : {
                        opacity : 0.5,
                        color : '#000'
                    }
                }
            }
        },
        legend : {
            show : true
        }
    });

    // Creates APDEX table
    createTable($("#apdexTable"), {"supportsControllersDiscrimination": true, "overall": {"data": [0.6521739130434783, 500, 2000, "Total"], "isController": false}, "titles": ["Apdex", "T (Toleration threshold)", "F (Frustration threshold)", "Label"], "items": [{"data": [1.0, 500, 2000, "Get all albums"], "isController": false}, {"data": [0.0, 500, 2000, "Home page"], "isController": true}, {"data": [0.0, 500, 2000, "Play songs from home page"], "isController": false}, {"data": [1.0, 500, 2000, "Trending playlist"], "isController": false}, {"data": [1.0, 500, 2000, "Albums page"], "isController": true}, {"data": [1.0, 500, 2000, "Cover 2"], "isController": false}, {"data": [0.0, 500, 2000, "Cover 1"], "isController": false}, {"data": [1.0, 500, 2000, "Cover 4"], "isController": false}, {"data": [1.0, 500, 2000, "Cover 3"], "isController": false}, {"data": [0.0, 500, 2000, "Play songs from albums"], "isController": false}, {"data": [1.0, 500, 2000, "Cover 5"], "isController": false}, {"data": [1.0, 500, 2000, "Search"], "isController": false}, {"data": [0.0, 500, 2000, "Initial song"], "isController": false}, {"data": [1.0, 500, 2000, "Get paths to songs from each album"], "isController": false}]}, function(index, item){
        switch(index){
            case 0:
                item = item.toFixed(3);
                break;
            case 1:
            case 2:
                item = formatDuration(item);
                break;
        }
        return item;
    }, [[0, 0]], 3);

    // Create statistics table
    createTable($("#statisticsTable"), {"supportsControllersDiscrimination": true, "overall": {"data": ["Total", 210, 10, 4.761904761904762, 1951.1380952380955, 7, 8337, 15.5, 6611.0, 7471.2, 8310.989999999998, 0.049849561145827774, 98.971460338072, 0.007027480683295056], "isController": false}, "titles": ["Label", "#Samples", "FAIL", "Error %", "Average", "Min", "Max", "Median", "90th pct", "95th pct", "99th pct", "Transactions/s", "Received", "Sent"], "items": [{"data": ["Get all albums", 10, 0, 0.0, 12.1, 8, 16, 13.0, 15.8, 16.0, 16.0, 0.0025881698895420854, 0.0025957524185153532, 3.058286685884691E-4], "isController": false}, {"data": ["Home page", 10, 10, 100.0, 8262.199999999999, 8103, 8648, 8202.0, 8621.9, 8648.0, 8648.0, 0.0025732210036591204, 21.542533815340516, 0.002166129399564611], "isController": true}, {"data": ["Play songs from home page", 30, 0, 0.0, 6791.599999999999, 5191, 8337, 6429.5, 8121.200000000001, 8334.25, 8337.0, 0.0074675237392579676, 51.69732191146018, 0.0012460457906066516], "isController": false}, {"data": ["Trending playlist", 10, 0, 0.0, 15.5, 9, 37, 11.0, 35.900000000000006, 37.0, 37.0, 0.0025786773808992723, 0.017539539021448666, 3.3744411039111574E-4], "isController": false}, {"data": ["Albums page", 10, 0, 0.0, 54.0, 48, 60, 54.5, 59.6, 60.0, 60.0, 0.00258814041593487, 0.0379299054901705, 0.0023025350770670572], "isController": true}, {"data": ["Cover 2", 10, 0, 0.0, 461.2, 448, 491, 460.0, 488.1, 491.0, 491.0, 0.002578804394076383, 1.1766021700703446, 3.500525495865403E-4], "isController": false}, {"data": ["Cover 1", 10, 10, 100.0, 8.9, 8, 11, 9.0, 10.9, 11.0, 11.0, 0.0025791023588985994, 0.005269025522281123, 0.0], "isController": false}, {"data": ["Cover 4", 10, 0, 0.0, 427.4, 415, 436, 427.0, 436.0, 436.0, 436.0, 0.002578833655429657, 1.1327501647552352, 3.5005652158664287E-4], "isController": false}, {"data": ["Cover 3", 10, 0, 0.0, 309.6, 302, 319, 309.0, 318.9, 319.0, 319.0, 0.002578908806947787, 0.8070171270491687, 3.500667228181078E-4], "isController": false}, {"data": ["Play songs from albums", 20, 0, 0.0, 6127.5, 5814, 6362, 6215.5, 6324.5, 6360.15, 6362.0, 0.00513268767544168, 32.14482645372471, 8.543619280068696E-4], "isController": false}, {"data": ["Cover 5", 10, 0, 0.0, 348.0, 340, 362, 348.5, 361.2, 362.0, 362.0, 0.0025788935102660595, 0.9107221398265746, 3.500646464130686E-4], "isController": false}, {"data": ["Search", 30, 0, 0.0, 9.3, 8, 20, 9.0, 10.0, 15.599999999999994, 20.0, 0.007733433696117301, 0.03246179280971113, 0.0010195444814217144], "isController": false}, {"data": ["Initial song", 10, 0, 0.0, 6691.5999999999985, 6551, 7099, 6621.5, 7069.0, 7099.0, 7099.0, 0.002574714348316626, 17.511635516324205, 4.324715506938083E-4], "isController": false}, {"data": ["Get paths to songs from each album", 50, 0, 0.0, 8.379999999999999, 7, 12, 8.0, 10.0, 10.449999999999996, 12.0, 0.012940745619881126, 0.035334301516784794, 0.001996716609317596], "isController": false}]}, function(index, item){
        switch(index){
            // Errors pct
            case 3:
                item = item.toFixed(2) + '%';
                break;
            // Mean
            case 4:
            // Mean
            case 7:
            // Median
            case 8:
            // Percentile 1
            case 9:
            // Percentile 2
            case 10:
            // Percentile 3
            case 11:
            // Throughput
            case 12:
            // Kbytes/s
            case 13:
            // Sent Kbytes/s
                item = item.toFixed(2);
                break;
        }
        return item;
    }, [[0, 0]], 0, summaryTableHeader);

    // Create error table
    createTable($("#errorsTable"), {"supportsControllersDiscrimination": false, "titles": ["Type of error", "Number of errors", "% in errors", "% in all samples"], "items": [{"data": ["Non HTTP response code: org.apache.http.NoHttpResponseException/Non HTTP response message: localhost:6001 failed to respond", 10, 100.0, 4.761904761904762], "isController": false}]}, function(index, item){
        switch(index){
            case 2:
            case 3:
                item = item.toFixed(2) + '%';
                break;
        }
        return item;
    }, [[1, 1]]);

        // Create top5 errors by sampler
    createTable($("#top5ErrorsBySamplerTable"), {"supportsControllersDiscrimination": false, "overall": {"data": ["Total", 210, 10, "Non HTTP response code: org.apache.http.NoHttpResponseException/Non HTTP response message: localhost:6001 failed to respond", 10, "", "", "", "", "", "", "", ""], "isController": false}, "titles": ["Sample", "#Samples", "#Errors", "Error", "#Errors", "Error", "#Errors", "Error", "#Errors", "Error", "#Errors", "Error", "#Errors"], "items": [{"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": ["Cover 1", 10, 10, "Non HTTP response code: org.apache.http.NoHttpResponseException/Non HTTP response message: localhost:6001 failed to respond", 10, "", "", "", "", "", "", "", ""], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}]}, function(index, item){
        return item;
    }, [[0, 0]], 0);

});
