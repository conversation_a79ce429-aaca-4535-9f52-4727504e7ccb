// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// TODO add isDeleted flag to Reservation and filter them out from queries
model Reservation {
  id        Int      @id @default(autoincrement())
  startDate DateTime
  endDate   DateTime
  name      String
  surname   String
  email     String
  phone     String

  car         Int
  moto        Int
  caravan     Int
  people      Int
  children    Int
  dog         Int
  tent        Int
  electricity Boolean

  street  String
  zip     String
  city    String
  country String
  note    String?

  totalPrice Int
  createdAt DateTime @default(now())
  isDeleted Boolean @default(false)
}

model Price {
  id          Int @id @default(autoincrement())
  car         Int
  moto        Int
  caravan     Int
  people      Int
  children    Int
  dog         Int
  tent        Int
  electricity Int

  createdAt DateTime @default(now())
}
