// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Song {
  id          String   @id @default(uuid())
  title       String
  duration    Int
  bpm         Int
  price       Float
  cover       String
  src         String
  releaseDate DateTime
  key         String
  lyrics      String?

  album      Album?    @relation(fields: [albumId], references: [id])
  albumId    String?
  playlist   Playlist? @relation(fields: [playlistId], references: [id])
  playlistId String?
  genres     Genre[]
}

model Album {
  id          String   @id @default(uuid())
  title       String
  releaseDate DateTime
  cover       String

  songs Song[]
}

model Playlist {
  id    String @id @default(uuid())
  title String

  songs Song[]
}

model Genre {
  name String @id

  songs Song[]
}
