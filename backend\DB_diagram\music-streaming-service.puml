@startuml Music streaming service

skinparam Linetype ortho
hide circle
'left to right direction

entity <PERSON> {
    * id: <<uuid>>
    ---
    * title: string
    * duration: int
    * bpm: int
    * price: float
    * cover: string
    * src: string
    * releaseDate: date
    * key: int
    lyrics: string
}

entity Album {
    * id: <<uuid>>
    ---
    * title: string
    * releaseDate: date
    * cover: string
}

entity Playlist {
    * id: <<uuid>>
    ---
    * title: string
}

entity Genre {
    * name: string
    ---
}

entity SongGenre {
    * songId: <<uuid>>
    * genreName: string
}

Playlist |o..o{ Song

Album |o..o{ Song

Song ||..o{ SongGenre
SongGenre }o..|| Genre

@enduml
